{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "lib": ["ES2022"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true}, "include": ["apps/*/src/**/*", "packages/*/src/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.test.ts", "**/*.spec.ts"]}