version: '3.8'

services:
  redis:
    image: redis:7.2-alpine
    container_name: askinfosec-redis
    ports:
      - '6379:6379'
    restart: unless-stopped

  api:
    container_name: askinfosec-api
    build:
      context: .
      dockerfile: apps/api/Dockerfile
      target: production
    ports:
      - '8000:8000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - DATABASE_URL_ROOT_USER=${DATABASE_URL_ROOT_USER}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - REFRESH_TOKEN_SECRET=${REFRESH_TOKEN_SECRET}
      - INTERNAL_API_SECRET=${INTERNAL_API_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - REDIS_URL=redis://redis:6379
      - MCP_ENABLED=${MCP_ENABLED:-true}
      - MCP_SESSION_TTL=${MCP_SESSION_TTL:-1800}
      - MCP_CONTEXT_TTL=${MCP_CONTEXT_TTL:-3600}
    healthcheck:
      test:
        [
          'CMD',
          'curl',
          '-f',
          '-X',
          'POST',
          '-H',
          'Content-Type: application/json',
          '-H',
          'x-internal-secret: ${INTERNAL_API_SECRET}',
          '-d',
          '{"message":"healthcheck"}',
          'http://localhost:8000/api/v1/external/echo',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
