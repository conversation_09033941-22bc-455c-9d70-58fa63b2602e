#!/usr/bin/env node

/**
 * Investigation Script for Missing Embedding: doc-5d32ac26
 *
 * This script helps investigate why the document doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26
 * appears in Redis but doesn't have an embedding created.
 */

const Redis = require('ioredis');

// Configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const TARGET_DOC_ID = 'doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26';
const ORG_ID = '2dd032ac-2dd0-32ac-2dd032ac'; // From the Redis screenshot

async function investigateMissingEmbedding() {
  const redis = new Redis(REDIS_URL);

  console.log('🔍 Investigating Missing Embedding Issue');
  console.log('==========================================');
  console.log(`Target Document: ${TARGET_DOC_ID}`);
  console.log(`Organization ID: ${ORG_ID}`);
  console.log(`Redis URL: ${REDIS_URL}`);
  console.log('');

  try {
    // 1. Check if document exists in top-level keys
    console.log('1. Checking top-level document keys...');
    const topLevelKey = TARGET_DOC_ID;
    const topLevelData = await redis.get(topLevelKey);
    console.log(`   Top-level key "${topLevelKey}": ${topLevelData ? 'EXISTS' : 'NOT FOUND'}`);

    if (topLevelData) {
      try {
        const parsed = JSON.parse(topLevelData);
        console.log(`   Data type: ${typeof parsed}`);
        console.log(`   Has content: ${!!parsed.content}`);
        console.log(`   Content length: ${parsed.content?.length || 0}`);
      } catch (e) {
        console.log(`   Data parsing failed: ${e.message}`);
      }
    }
    console.log('');

    // 2. Check if document exists in embeddings hierarchy
    console.log('2. Checking embeddings hierarchy...');
    const embeddingPattern = `embeddings:org-${ORG_ID}:${TARGET_DOC_ID}*`;
    const embeddingKeys = await redis.keys(embeddingPattern);
    console.log(`   Embedding pattern: ${embeddingPattern}`);
    console.log(`   Found embedding keys: ${embeddingKeys.length}`);

    for (const key of embeddingKeys) {
      const data = await redis.hget(key, 'data');
      if (data) {
        try {
          const parsed = JSON.parse(data);
          console.log(`   Key: ${key}`);
          console.log(`   Has embedding: ${!!parsed.embedding}`);
          console.log(`   Embedding length: ${parsed.embedding?.length || 0}`);
          console.log(`   Is chunked: ${parsed.metadata?.isChunked || false}`);
        } catch (e) {
          console.log(`   Key: ${key} - Parse error: ${e.message}`);
        }
      }
    }
    console.log('');

    // 3. Check content hashes
    console.log('3. Checking content hashes...');
    const contentHashKey = `content_hashes:${ORG_ID}`;
    const contentHashData = await redis.get(contentHashKey);
    console.log(`   Content hash key: ${contentHashKey}`);
    console.log(`   Content hash data: ${contentHashData ? 'EXISTS' : 'NOT FOUND'}`);

    if (contentHashData) {
      try {
        const hashes = JSON.parse(contentHashData);
        const targetHash = hashes[TARGET_DOC_ID];
        console.log(`   Hash for ${TARGET_DOC_ID}: ${targetHash || 'NOT FOUND'}`);
      } catch (e) {
        console.log(`   Hash parsing failed: ${e.message}`);
      }
    }
    console.log('');

    // 4. Check document cache
    console.log('4. Checking document cache...');
    const docCacheKey = `doc_cache:${ORG_ID}`;
    const docCacheData = await redis.get(docCacheKey);
    console.log(`   Document cache key: ${docCacheKey}`);
    console.log(`   Document cache data: ${docCacheData ? 'EXISTS' : 'NOT FOUND'}`);

    if (docCacheData) {
      try {
        const cache = JSON.parse(docCacheData);
        const targetDoc = cache.documents?.find(doc => doc.id === TARGET_DOC_ID);
        console.log(`   Document in cache: ${targetDoc ? 'FOUND' : 'NOT FOUND'}`);
        if (targetDoc) {
          console.log(`   Document name: ${targetDoc.name}`);
          console.log(`   Document content length: ${targetDoc.content?.length || 0}`);
          console.log(`   Document updated at: ${targetDoc.updatedAt}`);
        }
      } catch (e) {
        console.log(`   Cache parsing failed: ${e.message}`);
      }
    }
    console.log('');

    // 5. Check all keys related to the target document
    console.log('5. Checking all keys related to target document...');
    const allPatterns = [
      `*${TARGET_DOC_ID}*`,
      `embeddings:*${TARGET_DOC_ID}*`,
      `*${TARGET_DOC_ID.replace(/^doc-/, '')}*`,
    ];

    for (const pattern of allPatterns) {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        console.log(`   Pattern "${pattern}":`);
        for (const key of keys) {
          const type = await redis.type(key);
          const ttl = await redis.ttl(key);
          console.log(`     ${key} (${type}, TTL: ${ttl}s)`);
        }
      }
    }
    console.log('');

    // 6. Generate investigation summary
    console.log('6. Investigation Summary');
    console.log('========================');

    const hasTopLevel = !!topLevelData;
    const hasEmbeddings = embeddingKeys.length > 0;
    const hasContentHash = !!contentHashData;
    const hasDocCache = !!docCacheData;

    console.log(`   Document exists in top-level: ${hasTopLevel ? 'YES' : 'NO'}`);
    console.log(`   Document has embeddings: ${hasEmbeddings ? 'YES' : 'NO'}`);
    console.log(`   Content hash exists: ${hasContentHash ? 'YES' : 'NO'}`);
    console.log(`   Document cache exists: ${hasDocCache ? 'YES' : 'NO'}`);

    if (hasTopLevel && !hasEmbeddings) {
      console.log('');
      console.log('🚨 ISSUE CONFIRMED: Document exists but has no embeddings!');
      console.log('');
      console.log('Possible causes:');
      console.log('1. Content hash check prevented re-embedding');
      console.log('2. Embedding generation failed silently');
      console.log('3. Redis client unavailable during indexing');
      console.log('4. Document chunking failed');
      console.log('5. Error in incremental indexing logic');
      console.log('');
      console.log('Recommended actions:');
      console.log('1. Check application logs for embedding errors');
      console.log('2. Verify Redis client availability');
      console.log('3. Check document content size and chunking');
      console.log('4. Manually trigger re-indexing for this document');
    }
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
  } finally {
    await redis.quit();
  }
}

// Run the investigation
if (require.main === module) {
  investigateMissingEmbedding().catch(console.error);
}

module.exports = { investigateMissingEmbedding };
