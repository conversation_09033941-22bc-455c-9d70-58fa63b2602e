# Scripts Directory

This directory contains utility scripts for managing the AskInfoSec API system.

## Available Scripts

### 1. `clear-organization-data.js`

**Purpose**: Safely clears all Redis data for a specific organization
**Usage**: `node scripts/clear-organization-data.js <organizationId>`
**Features**:

- Clears `content_hashes:{orgId}`, `doc_cache:{orgId}`, and `embeddings:{orgId}:*` keys
- Validates organization ID format
- Provides detailed logging and error handling
- Supports dry-run mode

### 2. `view-organization-data.js`

**Purpose**: Displays all Redis keys for a specific organization (read-only)
**Usage**: `node scripts/view-organization-data.js <organizationId>`
**Features**:

- Shows key metadata (TTL, type, size) without loading content
- Groups embeddings by document for better organization
- Provides size breakdown and statistics
- Safe for production use

### 3. `migrate-redis-keys.js`

**Purpose**: Migrates incorrectly stored document keys to proper embeddings hierarchy
**Usage**: `node scripts/migrate-redis-keys.js [--verify]`
**Features**:

- Fixes keys like `doc-*::*` to `embeddings:{orgId}:{docId}::{chunkNumber}`
- Handles duplicate key scenarios
- Provides verification mode
- Maintains data integrity during migration

### 4. `sync-documents-to-redis.js` ⭐ **NEW**

**Purpose**: Syncs documents from database to Redis by creating embeddings
**Usage**: `node scripts/sync-documents-to-redis.js [options]`
**Features**:

- Retrieves documents from database using MCP tools
- Processes documents (extract content, chunk if needed)
- Generates embeddings for document chunks
- Stores embeddings in Redis with proper key patterns
- Supports full sync (clear + recreate) and incremental sync
- Includes dry-run mode for testing
- Provides progress tracking and detailed logging

#### Options:

- `--clear`: Clear existing embeddings before syncing
- `--dry-run`: Show what would be done without making changes
- `--org=<orgId>`: Organization ID to sync (default: test-org-id)
- `--help`: Show help message

#### Examples:

```bash
# Basic sync
node scripts/sync-documents-to-redis.js

# Clear existing and recreate
node scripts/sync-documents-to-redis.js --clear

# Test run without making changes
node scripts/sync-documents-to-redis.js --dry-run

# Sync specific organization
node scripts/sync-documents-to-redis.js --clear --org=org-12345678-1234-1234-1234-123456789abc
```

## Environment Variables

All scripts support the following environment variables:

- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)
- `REDIS_PASSWORD`: Redis password (optional)
- `REDIS_DB`: Redis database (default: 0)
- `NODE_ENV`: Environment mode (production/development)
- `OPENAI_API_KEY`: OpenAI API key for embeddings (required for sync script)
- `LANGCHAIN_EMBEDDINGS`: Embedding model (default: text-embedding-3-large)

## Redis Key Patterns

The scripts work with the following Redis key patterns:

1. **Content Hashes**: `content_hashes:{orgId}`

   - Stores content hashes for incremental document updates
   - Prevents unnecessary re-embedding of unchanged documents

2. **Document Cache**: `doc_cache:{orgId}`

   - Caches processed document data
   - Stores `ProcessedDocument[]` with version control

3. **Embeddings**: `embeddings:{orgId}:{docId}::{chunkNumber}`
   - Stores document embeddings with chunking
   - Primary vector storage for semantic search
   - Uses Redis HASH structure with 'data' field

## Security Considerations

- All scripts use secure logging that respects `NODE_ENV`
- Sensitive data is redacted in production logs
- Input validation is performed for all user inputs
- JSON parsing includes security checks for malicious patterns
- Environment variables are validated before use

## Workflow Integration

The scripts are designed to work together in a typical workflow:

1. **View** existing data: `view-organization-data.js`
2. **Clear** if needed: `clear-organization-data.js`
3. **Sync** documents: `sync-documents-to-redis.js`
4. **Verify** results: `view-organization-data.js`

## Error Handling

All scripts include comprehensive error handling:

- Graceful failure with detailed error messages
- Statistics tracking for successful operations
- Error details logging for debugging
- Proper cleanup of Redis connections

## Performance Considerations

- Batch processing for large datasets
- Pipeline operations for Redis efficiency
- Configurable batch sizes and delays
- Memory-conscious processing for large documents
