#!/usr/bin/env node

/**
 * Redis Key Migration Script
 *
 * This script migrates incorrectly stored document keys to the proper embeddings: hierarchy.
 * Based on investigation results, some documents are stored with keys like:
 *   doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26::1
 * Instead of the expected:
 *   embeddings:org-2dd032ac-2dd0-32ac-2dd0-32ac2dd032ac:doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26::1
 */

const Redis = require('ioredis');

// Configuration with secure defaults
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0', 10),
};

// Organization ID from investigation
const ORGANIZATION_ID = 'test-org-id';

// Secure logging function that respects NODE_ENV
function secureLog(message, data = null) {
  if (process.env.NODE_ENV === 'production') {
    // In production, only log essential information without sensitive data
    const sanitizedMessage = message.replace(/🔗 Redis: .*/, '🔗 Redis: [REDACTED]');
    console.log(sanitizedMessage);
  } else {
    // In development, log full information
    console.log(message);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

// Secure JSON parsing with validation
function safeJsonParse(data) {
  if (typeof data !== 'string' || data.length === 0) {
    throw new Error('Invalid data format');
  }

  try {
    const parsed = JSON.parse(data);
    if (typeof parsed !== 'object' || parsed === null) {
      throw new Error('Parsed data is not an object');
    }
    return parsed;
  } catch (error) {
    throw new Error(`JSON parsing failed: ${error.message}`);
  }
}

class RedisKeyMigrator {
  constructor() {
    this.redis = new Redis(REDIS_CONFIG);
    this.migrationStats = {
      totalKeysFound: 0,
      keysMigrated: 0,
      keysSkipped: 0,
      errors: 0,
      migratedKeys: [],
      errorKeys: [],
    };
  }

  /**
   * Find all document keys that need migration
   */
  async findKeysToMigrate() {
    secureLog('🔍 Searching for keys that need migration...');

    // Find keys that match the incorrect pattern: doc-*::*
    const incorrectPattern = 'doc-*::*';
    const keys = await this.redis.keys(incorrectPattern);

    secureLog(`Found ${keys.length} keys matching incorrect pattern: ${incorrectPattern}`);

    // Filter out keys that already have the correct embeddings: prefix
    const keysToMigrate = keys.filter(key => !key.startsWith('embeddings:'));

    secureLog(`Keys to migrate: ${keysToMigrate.length}`);
    return keysToMigrate;
  }

  /**
   * Parse document ID from incorrect key
   */
  parseDocumentIdFromKey(key) {
    // Extract document ID from key like: doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26::1
    const match = key.match(/^(doc-[^:]+)::(\d+)$/);
    if (!match) {
      throw new Error(`Invalid key format: ${key}`);
    }

    return {
      documentId: match[1],
      chunkNumber: parseInt(match[2], 10),
    };
  }

  /**
   * Generate correct key format
   */
  generateCorrectKey(documentId, chunkNumber) {
    // Remove 'doc-' prefix if present
    const cleanDocId = documentId.replace(/^doc-/, '');
    const cleanOrgId = ORGANIZATION_ID.replace(/^org-/, '');

    return `embeddings:org-${cleanOrgId}:doc-${cleanDocId}::${chunkNumber}`;
  }

  /**
   * Migrate a single key
   */
  async migrateKey(oldKey) {
    try {
      secureLog(`🔄 Migrating key: ${oldKey}`);

      // Parse the old key
      const { documentId, chunkNumber } = this.parseDocumentIdFromKey(oldKey);

      // Generate the new key
      const newKey = this.generateCorrectKey(documentId, chunkNumber);

      if (process.env.NODE_ENV !== 'production') {
        secureLog(`  📝 Old key: ${oldKey}`);
        secureLog(`  📝 New key: ${newKey}`);
      }

      // Check if new key already exists
      const newKeyExists = await this.redis.exists(newKey);
      if (newKeyExists) {
        secureLog(`  🗑️  New key already exists, deleting old key to keep store clean`);

        // Delete the old key since the correct one already exists
        await this.redis.del(oldKey);

        this.migrationStats.keysMigrated++;
        this.migrationStats.migratedKeys.push({
          oldKey,
          newKey,
          documentId: newKey, // Use the new key as documentId since it already exists
          action: 'deleted_duplicate',
        });
        return;
      }

      // Get the data from the old key
      const data = await this.redis.hget(oldKey, 'data');
      if (!data) {
        secureLog(`  ⚠️  No data found in old key, skipping`);
        this.migrationStats.keysSkipped++;
        this.migrationStats.errorKeys.push({
          oldKey,
          newKey,
          reason: 'No data found in old key',
        });
        return;
      }

      // Parse the data to update the documentId if needed
      let documentData;
      try {
        documentData = safeJsonParse(data);
      } catch (error) {
        secureLog(`  ⚠️  Invalid JSON data in old key, skipping`);
        this.migrationStats.keysSkipped++;
        this.migrationStats.errorKeys.push({
          oldKey,
          newKey,
          reason: 'Invalid JSON data',
        });
        return;
      }

      // Update the documentId in the stored data to match the new key format
      documentData.documentId = newKey;

      // Store the data in the new key
      await this.redis.hset(newKey, 'data', JSON.stringify(documentData));

      // Copy TTL if it exists
      const ttl = await this.redis.ttl(oldKey);
      if (ttl > 0) {
        await this.redis.expire(newKey, ttl);
      }

      // Delete the old key
      await this.redis.del(oldKey);

      secureLog(`  ✅ Successfully migrated key`);
      this.migrationStats.keysMigrated++;
      this.migrationStats.migratedKeys.push({
        oldKey,
        newKey,
        documentId: documentData.documentId,
        action: 'migrated',
      });
    } catch (error) {
      secureLog(`  ❌ Error migrating key ${oldKey}: ${error.message}`);
      this.migrationStats.errors++;
      this.migrationStats.errorKeys.push({
        oldKey,
        newKey: 'unknown',
        reason: error.message,
      });
    }
  }

  /**
   * Run the migration
   */
  async runMigration() {
    secureLog('🚀 Starting Redis key migration...');
    secureLog(`📊 Organization ID: ${ORGANIZATION_ID}`);

    if (process.env.NODE_ENV !== 'production') {
      secureLog(`🔗 Redis: ${REDIS_CONFIG.host}:${REDIS_CONFIG.port}`);
    }

    try {
      // Test Redis connection
      await this.redis.ping();
      secureLog('✅ Redis connection successful');

      // Find keys to migrate
      const keysToMigrate = await this.findKeysToMigrate();
      this.migrationStats.totalKeysFound = keysToMigrate.length;

      if (keysToMigrate.length === 0) {
        secureLog('✅ No keys need migration');
        return;
      }

      // Migrate each key
      secureLog(`🔄 Starting migration of ${keysToMigrate.length} keys...`);

      for (const key of keysToMigrate) {
        await this.migrateKey(key);
      }

      // Print migration summary
      this.printMigrationSummary();
    } catch (error) {
      secureLog(`❌ Migration failed: ${error.message}`);
      process.exit(1);
    } finally {
      await this.redis.quit();
    }
  }

  /**
   * Print migration summary
   */
  printMigrationSummary() {
    secureLog('\n📊 Migration Summary');
    secureLog('==================');
    secureLog(`Total keys found: ${this.migrationStats.totalKeysFound}`);
    secureLog(`Keys migrated: ${this.migrationStats.keysMigrated}`);
    secureLog(`Keys skipped: ${this.migrationStats.keysSkipped}`);
    secureLog(`Errors: ${this.migrationStats.errors}`);

    // Count different types of actions
    const migratedCount = this.migrationStats.migratedKeys.filter(
      k => k.action === 'migrated'
    ).length;
    const deletedCount = this.migrationStats.migratedKeys.filter(
      k => k.action === 'deleted_duplicate'
    ).length;

    if (migratedCount > 0) {
      secureLog(`  - Successfully migrated: ${migratedCount}`);
    }
    if (deletedCount > 0) {
      secureLog(`  - Deleted duplicates (correct keys already existed): ${deletedCount}`);
    }

    if (this.migrationStats.migratedKeys.length > 0) {
      secureLog('\n✅ Successfully processed keys:');
      this.migrationStats.migratedKeys.forEach(({ oldKey, newKey, action }) => {
        const actionText = action === 'migrated' ? '→' : '🗑️';
        secureLog(`  ${oldKey} ${actionText} ${newKey} (${action})`);
      });
    }

    if (this.migrationStats.errorKeys.length > 0) {
      secureLog('\n❌ Failed migrations:');
      this.migrationStats.errorKeys.forEach(({ oldKey, reason }) => {
        secureLog(`  ${oldKey}: ${reason}`);
      });
    }

    secureLog('\n🎉 Migration completed!');
  }

  /**
   * Verify migration results
   */
  async verifyMigration() {
    secureLog('\n🔍 Verifying migration results...');

    // Check for any remaining incorrect keys
    const remainingIncorrectKeys = await this.redis.keys('doc-*::*');
    const incorrectKeys = remainingIncorrectKeys.filter(key => !key.startsWith('embeddings:'));

    if (incorrectKeys.length > 0) {
      secureLog(`⚠️  Found ${incorrectKeys.length} keys that still need migration:`);
      incorrectKeys.forEach(key => secureLog(`  ${key}`));
    } else {
      secureLog('✅ No remaining incorrect keys found');
    }

    // Check for new correct keys
    const correctKeys = await this.redis.keys(
      `embeddings:org-${ORGANIZATION_ID.replace(/^org-/, '')}:*`
    );
    secureLog(`✅ Found ${correctKeys.length} correctly formatted keys`);

    await this.redis.quit();
  }
}

// Main execution
async function main() {
  const migrator = new RedisKeyMigrator();

  if (process.argv.includes('--verify')) {
    await migrator.verifyMigration();
  } else {
    await migrator.runMigration();
  }
}

// Handle command line arguments
if (process.argv.includes('--help')) {
  const helpText = `
Redis Key Migration Script

Usage:
  node migrate-redis-keys.js [options]

Options:
  --verify    Verify migration results without performing migration
  --help      Show this help message

Environment Variables:
  REDIS_HOST      Redis host (default: localhost)
  REDIS_PORT      Redis port (default: 6379)
  REDIS_PASSWORD  Redis password
  REDIS_DB        Redis database (default: 0)
  NODE_ENV        Environment mode (development/production)

Example:
  node migrate-redis-keys.js
  node migrate-redis-keys.js --verify
  REDIS_HOST=redis.example.com node migrate-redis-keys.js
`;
  console.log(helpText);
  process.exit(0);
}

// Run the migration
main().catch(error => {
  secureLog(`❌ Script failed: ${error.message}`);
  process.exit(1);
});
