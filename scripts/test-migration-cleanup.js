#!/usr/bin/env node

/**
 * Test script to demonstrate the migration cleanup behavior
 * This script creates some test keys to show how the migration handles duplicates
 */

const Redis = require('ioredis');

// Configuration
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0', 10),
};

// Organization ID from investigation
const ORGANIZATION_ID = 'test-org-id';

class MigrationTest {
  constructor() {
    this.redis = new Redis(REDIS_CONFIG);
  }

  /**
   * Create test data to demonstrate migration behavior
   */
  async createTestData() {
    console.log('🧪 Creating test data...');

    // Create some incorrect keys (like the ones we found)
    const incorrectKeys = ['doc-test-1::1', 'doc-test-1::2', 'doc-test-2::1'];

    // Create some correct keys (like the ones that already exist)
    const correctKeys = [
      `embeddings:org-${ORGANIZATION_ID.replace(/^org-/, '')}:doc-test-1::1`,
      `embeddings:org-${ORGANIZATION_ID.replace(/^org-/, '')}:doc-test-1::2`,
    ];

    // Create incorrect keys
    for (const key of incorrectKeys) {
      const testData = {
        documentId: key,
        content: `Test content for ${key}`,
        embedding: [0.1, 0.2, 0.3],
        metadata: { test: true, key: key },
        timestamp: Date.now(),
        organizationId: ORGANIZATION_ID,
      };
      await this.redis.hset(key, 'data', JSON.stringify(testData));
      console.log(`  ✅ Created incorrect key: ${key}`);
    }

    // Create correct keys (these will cause the migration to delete duplicates)
    for (const key of correctKeys) {
      const testData = {
        documentId: key,
        content: `Correct content for ${key}`,
        embedding: [0.4, 0.5, 0.6],
        metadata: { test: true, key: key, correct: true },
        timestamp: Date.now(),
        organizationId: ORGANIZATION_ID,
      };
      await this.redis.hset(key, 'data', JSON.stringify(testData));
      console.log(`  ✅ Created correct key: ${key}`);
    }

    console.log('\n📊 Test data created:');
    console.log(`  - Incorrect keys: ${incorrectKeys.length}`);
    console.log(`  - Correct keys: ${correctKeys.length}`);
    console.log(`  - Total keys: ${incorrectKeys.length + correctKeys.length}`);
  }

  /**
   * Show current state of keys
   */
  async showCurrentState() {
    console.log('\n🔍 Current Redis state:');

    const allKeys = await this.redis.keys('*');
    const incorrectKeys = allKeys.filter(key => key.startsWith('doc-') && key.includes('::'));
    const correctKeys = allKeys.filter(key => key.startsWith('embeddings:'));

    console.log(`  - Total keys: ${allKeys.length}`);
    console.log(`  - Incorrect keys: ${incorrectKeys.length}`);
    console.log(`  - Correct keys: ${correctKeys.length}`);

    if (incorrectKeys.length > 0) {
      console.log('  - Incorrect keys found:');
      incorrectKeys.forEach(key => console.log(`    ${key}`));
    }

    if (correctKeys.length > 0) {
      console.log('  - Correct keys found:');
      correctKeys.forEach(key => console.log(`    ${key}`));
    }
  }

  /**
   * Clean up test data
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');

    const allKeys = await this.redis.keys('*');
    const testKeys = allKeys.filter(
      key => key.startsWith('doc-test-') || key.includes('doc-test-')
    );

    if (testKeys.length > 0) {
      await Promise.all(testKeys.map(key => this.redis.del(key)));
      console.log(`  ✅ Deleted ${testKeys.length} test keys`);
    } else {
      console.log('  ℹ️  No test keys found to clean up');
    }
  }

  /**
   * Run the test
   */
  async run() {
    try {
      await this.redis.ping();
      console.log('✅ Redis connection successful');

      await this.createTestData();
      await this.showCurrentState();

      console.log('\n🚀 Now you can run the migration script:');
      console.log('  node scripts/migrate-redis-keys.js');
      console.log('\nThis will:');
      console.log('  - Migrate doc-test-2::1 to the correct structure');
      console.log('  - Delete doc-test-1::1 and doc-test-1::2 (duplicates)');
      console.log('  - Show statistics for both actions');
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.redis.quit();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new MigrationTest();
  test.run().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = MigrationTest;
