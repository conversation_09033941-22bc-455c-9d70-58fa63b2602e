#!/usr/bin/env node

/**
 * Simple Investigation Script for Missing Embedding: doc-5d32ac26
 *
 * This script provides manual investigation steps and can be run with existing Redis tools.
 */

const TARGET_DOC_ID = 'doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26';
const ORG_ID = '2dd032ac-2dd0-32ac-2dd032ac'; // From the Redis screenshot

console.log('🔍 Investigation Guide for Missing Embedding Issue');
console.log('==================================================');
console.log(`Target Document: ${TARGET_DOC_ID}`);
console.log(`Organization ID: ${ORG_ID}`);
console.log('');

console.log('📋 Manual Investigation Steps:');
console.log('');

console.log('1. Connect to Redis using redis-cli:');
console.log(`   redis-cli -h localhost -p 6379`);
console.log('');

console.log('2. Check if document exists in top-level keys:');
console.log(`   GET ${TARGET_DOC_ID}`);
console.log('');

console.log('3. Check if document exists in embeddings hierarchy:');
console.log(`   KEYS embeddings:org-${ORG_ID}:${TARGET_DOC_ID}*`);
console.log('');

console.log('4. Check content hashes:');
console.log(`   GET content_hashes:${ORG_ID}`);
console.log('');

console.log('5. Check document cache:');
console.log(`   GET doc_cache:${ORG_ID}`);
console.log('');

console.log('6. Check all keys related to the target document:');
console.log(`   KEYS *${TARGET_DOC_ID}*`);
console.log(`   KEYS embeddings:*${TARGET_DOC_ID}*`);
console.log('');

console.log('7. If embeddings exist, check their content:');
console.log(`   HGET embeddings:org-${ORG_ID}:${TARGET_DOC_ID} data`);
console.log('');

console.log('📊 Expected Results:');
console.log('- If document exists in top-level but NOT in embeddings: ISSUE CONFIRMED');
console.log('- If document exists in both: NORMAL');
console.log('- If document exists in neither: DOCUMENT NOT FOUND');
console.log('');

console.log('🔧 Troubleshooting Commands:');
console.log('');

console.log('Check Redis connection:');
console.log('   PING');
console.log('');

console.log('Check Redis info:');
console.log('   INFO');
console.log('');

console.log('Check all keys in the database:');
console.log('   KEYS *');
console.log('');

console.log('Check key types:');
console.log(`   TYPE ${TARGET_DOC_ID}`);
console.log(`   TYPE embeddings:org-${ORG_ID}:${TARGET_DOC_ID}`);
console.log('');

console.log('Check TTL (time to live):');
console.log(`   TTL ${TARGET_DOC_ID}`);
console.log(`   TTL embeddings:org-${ORG_ID}:${TARGET_DOC_ID}`);
console.log('');

console.log('🚨 If Issue is Confirmed:');
console.log('1. Check application logs for embedding generation errors');
console.log('2. Verify Redis client availability during indexing');
console.log('3. Check document content size and chunking');
console.log('4. Manually trigger re-indexing for this document');
console.log('');

console.log('📝 Next Steps:');
console.log('1. Run the above Redis commands manually');
console.log('2. Check application logs (see Step 2 below)');
console.log('3. Verify Redis client availability (see Step 3 below)');
console.log('');

console.log('==================================================');
console.log('');

// Check if we can use the existing application's Redis connection
console.log('🔗 Alternative: Use Application Redis Connection');
console.log('');
console.log('If the application is running, you can also:');
console.log('1. Add a debug endpoint to the API to check Redis data');
console.log('2. Use the existing Redis connection in the application');
console.log('3. Add logging to the embedding generation process');
console.log('');

console.log('Example API endpoint to add (temporary):');
console.log(`
// Add this to apps/api/src/app.ts or create a debug route
app.get('/debug/redis/:docId', async (request, reply) => {
  const { docId } = request.params;
  const redis = app.redis;
  
  const topLevel = await redis.get(docId);
  const embeddings = await redis.keys(\`embeddings:*\${docId}*\`);
  const contentHashes = await redis.get('content_hashes:${ORG_ID}');
  
  return {
    docId,
    topLevel: !!topLevel,
    embeddings: embeddings.length,
    contentHashes: !!contentHashes,
    embeddingKeys: embeddings
  };
});
`);

module.exports = { TARGET_DOC_ID, ORG_ID };
