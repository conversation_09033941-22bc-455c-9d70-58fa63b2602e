import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Navigate to workspace root from scripts directory
const workspaceRoot = path.resolve(__dirname, '..');
const certsDir = path.join(workspaceRoot, 'certs');

console.log('🔧 Setting up SSL certificates for local development...');

// Create certs directory if it doesn't exist
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir, { recursive: true });
  console.log(`📁 Created directory: ${certsDir}`);
}

try {
  // Check if mkcert is available
  try {
    execSync('mkcert -version', { stdio: 'ignore' });
    console.log('✅ mkcert found - generating trusted certificates');

    // Generate certificates with mkcert
    execSync(
      `mkcert -key-file ${certsDir}/key.pem -cert-file ${certsDir}/cert.pem localhost 127.0.0.1 ::1`,
      {
        stdio: 'inherit',
      }
    );

    console.log('✅ Trusted SSL certificates generated successfully!');
    console.log('🌐 Certificates are valid for: localhost, 127.0.0.1, ::1');
  } catch (mkcertError) {
    console.log('⚠️  mkcert not found - falling back to OpenSSL');

    // Fallback to OpenSSL
    execSync(`openssl genrsa -out ${certsDir}/key.pem 2048`, { stdio: 'inherit' });

    execSync(
      `openssl req -new -x509 -key ${certsDir}/key.pem -out ${certsDir}/cert.pem -days 365 -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`,
      {
        stdio: 'inherit',
      }
    );

    console.log('✅ Self-signed SSL certificates generated successfully!');
    console.log('⚠️  Note: Your browser will show security warnings for self-signed certificates');
  }

  console.log(`📂 Certificates saved to: ${certsDir}`);
  console.log('');
  console.log('🚀 You can now run HTTPS servers:');
  console.log('   API:   cd apps/api && pnpm run dev       # Fastify API with HTTPS');
  console.log('   Teams: cd apps/teams && npm run dev:https # Teams frontend with HTTPS');
  console.log('   Bot:   cd apps/teams && npm run bot:dev  # Teams bot with HTTPS');
  console.log('');
  console.log('💡 For best experience, install mkcert:');
  console.log('   macOS:    brew install mkcert');
  console.log('   Windows:  choco install mkcert');
  console.log('   Linux:    https://github.com/FiloSottile/mkcert#linux');
} catch (error) {
  console.error('❌ Error generating certificates:', error.message);
  console.log('');
  console.log('🔧 Manual setup options:');
  console.log('1. Install mkcert: https://github.com/FiloSottile/mkcert');
  console.log('2. Install OpenSSL: https://www.openssl.org/');
  console.log('3. Use ngrok for HTTPS tunneling: npm install -g ngrok');
  console.log('');
  console.log('🌐 For Teams development, you can also use:');
  console.log('   cd apps/teams && npm run tunnel:bot    # Expose bot via ngrok HTTPS');
  console.log('   cd apps/teams && npm run tunnel:app    # Expose frontend via ngrok HTTPS');
}
