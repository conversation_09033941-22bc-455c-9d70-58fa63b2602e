#!/usr/bin/env node

/**
 * Test script for clear-organization-data.js
 * This script tests the OrganizationDataClearer class without actually clearing data
 */

const { OrganizationDataClearer } = require('./clear-organization-data');

async function testOrganizationDataClearer() {
  console.log('🧪 Testing OrganizationDataClearer...\n');

  // Test 1: Valid organization ID
  try {
    const validOrgId = 'test-org-id';
    console.log(`✅ Test 1: Valid organization ID (${validOrgId})`);

    const clearer = new OrganizationDataClearer(validOrgId);
    console.log('  - Constructor created successfully');

    // Test getting keys (without clearing)
    const keysToClear = await clearer.getKeysToClear();
    console.log(`  - Found keys to clear:`);
    console.log(`    - content_hashes: ${keysToClear.contentHashes.length}`);
    console.log(`    - doc_cache: ${keysToClear.docCache.length}`);
    console.log(`    - embeddings: ${keysToClear.embeddings.length}`);

    await clearer.cleanup();
    console.log('  - Cleanup completed successfully\n');
  } catch (error) {
    console.error(`  ❌ Test 1 failed: ${error.message}\n`);
  }

  // Test 2: Invalid organization ID
  try {
    console.log('✅ Test 2: Invalid organization ID');
    const clearer = new OrganizationDataClearer('invalid-org-id');
    console.log('  ❌ Test 2 failed: Should have thrown an error');
  } catch (error) {
    console.log(`  - Correctly rejected invalid organization ID: ${error.message}\n`);
  }

  // Test 3: Missing organization ID
  try {
    console.log('✅ Test 3: Missing organization ID');
    const clearer = new OrganizationDataClearer('');
    console.log('  ❌ Test 3 failed: Should have thrown an error');
  } catch (error) {
    console.log(`  - Correctly rejected missing organization ID: ${error.message}\n`);
  }

  // Test 4: Organization ID without org- prefix
  try {
    console.log('✅ Test 4: Organization ID without org- prefix');
    const clearer = new OrganizationDataClearer('2dd032ac-2dd0-32ac-2dd0-32ac2dd032ac');
    console.log('  ❌ Test 4 failed: Should have thrown an error');
  } catch (error) {
    console.log(`  - Correctly rejected organization ID without org- prefix: ${error.message}\n`);
  }

  console.log('🎉 All tests completed!');
}

// Run the test
if (require.main === module) {
  testOrganizationDataClearer().catch(error => {
    console.error(`❌ Test failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testOrganizationDataClearer };
