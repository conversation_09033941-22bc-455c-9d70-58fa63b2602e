#!/usr/bin/env node

/**
 * View Organization Data Script
 *
 * This script displays all Redis keys for a specific organization:
 * - content_hashes:{orgId}
 * - doc_cache:{orgId}
 * - embeddings:{orgId}:*
 *
 * This is a read-only operation that only shows key information,
 * not the actual data contents which can be large.
 */

const Redis = require('ioredis');

// Configuration with secure defaults
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0', 10),
};

// Secure logging function that respects NODE_ENV
function secureLog(message, data = null) {
  if (process.env.NODE_ENV === 'production') {
    // In production, only log essential information without sensitive data
    const sanitizedMessage = message.replace(/🔗 Redis: .*/, '🔗 Redis: [REDACTED]');
    console.log(sanitizedMessage);
  } else {
    // In development, log full information
    console.log(message);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

class OrganizationDataViewer {
  constructor(organizationId) {
    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    // Validate organization ID format
    if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
      throw new Error('Invalid organization ID format. Expected format: org-{uuid}');
    }

    this.organizationId = organizationId;
    this.redis = new Redis(REDIS_CONFIG);
    this.viewStats = {
      contentHashes: [],
      docCache: [],
      embeddings: [],
      totalKeys: 0,
      totalSize: 0,
      errors: 0,
      errorKeys: [],
    };
  }

  /**
   * Get all keys for the organization
   */
  async getAllKeys() {
    const keys = {
      contentHashes: [],
      docCache: [],
      embeddings: [],
    };

    try {
      // Get content_hashes keys
      const contentHashesPattern = `content_hashes:${this.organizationId}`;
      const contentHashesKeys = await this.redis.keys(contentHashesPattern);
      keys.contentHashes = contentHashesKeys;

      // Get doc_cache keys
      const docCachePattern = `doc_cache:${this.organizationId}`;
      const docCacheKeys = await this.redis.keys(docCachePattern);
      keys.docCache = docCacheKeys;

      // Get embeddings keys
      const embeddingsPattern = `embeddings:${this.organizationId}:*`;
      const embeddingsKeys = await this.redis.keys(embeddingsPattern);
      keys.embeddings = embeddingsKeys;

      return keys;
    } catch (error) {
      throw new Error(`Failed to get keys: ${error.message}`);
    }
  }

  /**
   * Get key metadata (TTL, type, size) without loading content
   */
  async getKeyMetadata(key) {
    try {
      const [ttl, type, size] = await Promise.all([
        this.redis.ttl(key),
        this.redis.type(key),
        this.redis.memory('usage', key).catch(() => 0), // Memory usage in bytes
      ]);

      return {
        key,
        ttl: ttl === -1 ? 'no-expiry' : ttl,
        type,
        size: size || 0,
        sizeFormatted: this.formatBytes(size || 0),
      };
    } catch (error) {
      this.viewStats.errors++;
      this.viewStats.errorKeys.push({ key, error: error.message });
      return {
        key,
        ttl: 'error',
        type: 'error',
        size: 0,
        sizeFormatted: '0 B',
        error: error.message,
      };
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Analyze content_hashes keys
   */
  async analyzeContentHashes() {
    const pattern = `content_hashes:${this.organizationId}`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No content_hashes keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(
      `📝 Found ${keys.length} content_hashes keys for organization: ${this.organizationId}`
    );

    for (const key of keys) {
      const metadata = await this.getKeyMetadata(key);
      this.viewStats.contentHashes.push(metadata);
      this.viewStats.totalKeys++;
      this.viewStats.totalSize += metadata.size;

      if (metadata.error) {
        secureLog(`  ❌ ${key} - Error: ${metadata.error}`);
      } else {
        secureLog(
          `  📄 ${key} (${metadata.type}, ${metadata.sizeFormatted}, TTL: ${metadata.ttl})`
        );
      }
    }
  }

  /**
   * Analyze doc_cache keys
   */
  async analyzeDocCache() {
    const pattern = `doc_cache:${this.organizationId}`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No doc_cache keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(`📝 Found ${keys.length} doc_cache keys for organization: ${this.organizationId}`);

    for (const key of keys) {
      const metadata = await this.getKeyMetadata(key);
      this.viewStats.docCache.push(metadata);
      this.viewStats.totalKeys++;
      this.viewStats.totalSize += metadata.size;

      if (metadata.error) {
        secureLog(`  ❌ ${key} - Error: ${metadata.error}`);
      } else {
        secureLog(
          `  📄 ${key} (${metadata.type}, ${metadata.sizeFormatted}, TTL: ${metadata.ttl})`
        );
      }
    }
  }

  /**
   * Analyze embeddings keys
   */
  async analyzeEmbeddings() {
    const pattern = `embeddings:${this.organizationId}:*`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No embeddings keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(`📝 Found ${keys.length} embeddings keys for organization: ${this.organizationId}`);

    // Group embeddings by document for better organization
    const documentGroups = {};

    for (const key of keys) {
      const metadata = await this.getKeyMetadata(key);
      this.viewStats.embeddings.push(metadata);
      this.viewStats.totalKeys++;
      this.viewStats.totalSize += metadata.size;

      // Extract document ID from key for grouping
      const docMatch = key.match(/embeddings:[^:]+:([^:]+)::\d+$/);
      const docId = docMatch ? docMatch[1] : 'unknown';

      if (!documentGroups[docId]) {
        documentGroups[docId] = [];
      }
      documentGroups[docId].push(metadata);
    }

    // Display grouped by document
    Object.entries(documentGroups).forEach(([docId, metadatas]) => {
      const totalDocSize = metadatas.reduce((sum, m) => sum + m.size, 0);
      const chunkCount = metadatas.length;

      secureLog(
        `  📚 Document: ${docId} (${chunkCount} chunks, ${this.formatBytes(totalDocSize)})`
      );

      metadatas.forEach(metadata => {
        if (metadata.error) {
          secureLog(`    ❌ ${metadata.key} - Error: ${metadata.error}`);
        } else {
          secureLog(
            `    📄 ${metadata.key} (${metadata.type}, ${metadata.sizeFormatted}, TTL: ${metadata.ttl})`
          );
        }
      });
    });
  }

  /**
   * Run the complete viewing operation
   */
  async viewOrganizationData() {
    secureLog(`🔍 Viewing data for organization: ${this.organizationId}`);
    secureLog(`🔗 Redis: ${REDIS_CONFIG.host}:${REDIS_CONFIG.port} (DB: ${REDIS_CONFIG.db})`);

    try {
      // Test Redis connection
      await this.redis.ping();
      secureLog('✅ Redis connection successful');
    } catch (error) {
      throw new Error(`Failed to connect to Redis: ${error.message}`);
    }

    // Get overview of keys that exist
    const keysToView = await this.getAllKeys();
    const totalKeys =
      keysToView.contentHashes.length + keysToView.docCache.length + keysToView.embeddings.length;

    secureLog(`📊 Overview of keys found:`);
    secureLog(`  - content_hashes: ${keysToView.contentHashes.length} keys`);
    secureLog(`  - doc_cache: ${keysToView.docCache.length} keys`);
    secureLog(`  - embeddings: ${keysToView.embeddings.length} keys`);
    secureLog(`  - Total: ${totalKeys} keys`);

    if (totalKeys === 0) {
      secureLog(`ℹ️  No data found for organization: ${this.organizationId}`);
      return;
    }

    secureLog('\n📋 Detailed Analysis:');
    secureLog('==================');

    // Analyze each data type
    await this.analyzeContentHashes();
    await this.analyzeDocCache();
    await this.analyzeEmbeddings();

    // Print summary
    this.printViewingSummary();
  }

  /**
   * Print viewing summary
   */
  printViewingSummary() {
    secureLog('\n📊 Data Summary:');
    secureLog('================');
    secureLog(`Organization ID: ${this.organizationId}`);
    secureLog(`Content Hashes: ${this.viewStats.contentHashes.length} keys`);
    secureLog(`Document Cache: ${this.viewStats.docCache.length} keys`);
    secureLog(`Embeddings: ${this.viewStats.embeddings.length} keys`);
    secureLog(`Total Keys: ${this.viewStats.totalKeys}`);
    secureLog(`Total Size: ${this.formatBytes(this.viewStats.totalSize)}`);
    secureLog(`Errors: ${this.viewStats.errors}`);

    if (this.viewStats.errors > 0) {
      secureLog('\n❌ Errors encountered:');
      this.viewStats.errorKeys.forEach(({ key, error }) => {
        secureLog(`  - ${key}: ${error}`);
      });
    }

    // Show size breakdown
    const contentHashesSize = this.viewStats.contentHashes.reduce((sum, k) => sum + k.size, 0);
    const docCacheSize = this.viewStats.docCache.reduce((sum, k) => sum + k.size, 0);
    const embeddingsSize = this.viewStats.embeddings.reduce((sum, k) => sum + k.size, 0);

    secureLog('\n💾 Size Breakdown:');
    secureLog(`  - Content Hashes: ${this.formatBytes(contentHashesSize)}`);
    secureLog(`  - Document Cache: ${this.formatBytes(docCacheSize)}`);
    secureLog(`  - Embeddings: ${this.formatBytes(embeddingsSize)}`);

    if (this.viewStats.totalKeys > 0) {
      secureLog('\n✅ Organization data viewing completed successfully!');
    } else {
      secureLog('\nℹ️  No data was found for this organization.');
    }
  }

  /**
   * Clean up Redis connection
   */
  async cleanup() {
    if (this.redis) {
      await this.redis.quit();
      secureLog('🔌 Redis connection closed');
    }
  }
}

/**
 * Display usage information
 */
function showUsage() {
  console.log(`
View Organization Data Script

Usage: node scripts/view-organization-data.js <organizationId>

Arguments:
  organizationId    The organization ID to view data for (format: org-{uuid})

Examples:
  node scripts/view-organization-data.js your-organization-id
  node scripts/view-organization-data.js org-12345678-1234-1234-1234-123456789abc

Environment Variables:
  REDIS_HOST        Redis host (default: localhost)
  REDIS_PORT        Redis port (default: 6379)
  REDIS_PASSWORD    Redis password (optional)
  REDIS_DB          Redis database (default: 0)
  NODE_ENV          Environment mode (production/development)

ℹ️  This script is read-only and only displays key information, not actual data contents.
`);
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);

  // Check for help flag
  if (args.includes('--help') || args.includes('-h') || args.length === 0) {
    showUsage();
    process.exit(0);
  }

  const organizationId = args[0];

  // Validate organization ID
  if (!organizationId || !organizationId.match(/^org-[a-f0-9-]+$/)) {
    console.error('❌ Error: Invalid organization ID format.');
    console.error('Expected format: org-{uuid}');
    console.error('Example: your-organization-id');
    showUsage();
    process.exit(1);
  }

  let viewer = null;

  try {
    viewer = new OrganizationDataViewer(organizationId);
    await viewer.viewOrganizationData();
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  } finally {
    if (viewer) {
      await viewer.cleanup();
    }
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { OrganizationDataViewer };
