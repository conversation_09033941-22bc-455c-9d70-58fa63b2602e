#!/usr/bin/env node

/**
 * Document Sync to <PERSON><PERSON>ript
 *
 * This script syncs documents from the database to Redis by creating embeddings.
 * It follows this workflow:
 * 1. Retrieve documents from database using MCP tool
 * 2. Process documents (extract content, chunk if needed)
 * 3. Generate embeddings for document chunks
 * 4. Store embeddings in Redis with proper key patterns
 *
 * The script supports:
 * - Full sync (clear existing + recreate)
 * - Incremental sync (only new/changed documents)
 * - Dry run mode for testing
 * - Progress tracking and detailed logging
 */

const Redis = require('ioredis');
const { OpenAIEmbeddings } = require('langchain/embeddings/openai');
const WebSocket = require('ws');

// Secure configuration management
function getSecureConfig() {
  const config = {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.LANGCHAIN_EMBEDDINGS || 'text-embedding-3-large',
    },
    organization: {
      id: process.env.ORGANIZATION_ID || 'test-org-id',
    },
    environment: process.env.NODE_ENV || 'development',
    api: {
      baseUrl: process.env.API_BASE_URL || 'http://localhost:8000',
      jwtToken: process.env.API_JWT_TOKEN || 'your-jwt-token',
    },
  };

  // Validate required configuration
  if (!config.openai.apiKey) {
    throw new Error('OPENAI_API_KEY environment variable is required');
  }

  return config;
}

// Organization ID from investigation
const ORGANIZATION_ID = 'test-org-id';

// Secure logging function that respects NODE_ENV
function secureLog(message, data = null) {
  const config = getSecureConfig();

  if (config.environment === 'production') {
    // In production, only log essential information without sensitive data
    const sanitizedMessage = message.replace(/🔗 Redis: .*/, '🔗 Redis: [REDACTED]');
    console.log(sanitizedMessage);
  } else {
    // In development, log full information
    console.log(message);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

// Secure JSON parsing with validation
function safeJsonParse(data) {
  if (typeof data !== 'string' || data.length === 0) {
    throw new Error('Invalid data format');
  }

  // Validate input before parsing
  if (data.length > 1000000) {
    // 1MB limit
    throw new Error('Data too large for parsing');
  }

  // Check for potentially malicious patterns
  if (data.includes('__proto__') || data.includes('constructor')) {
    throw new Error('Potentially malicious data detected');
  }

  try {
    const parsed = JSON.parse(data);
    if (typeof parsed !== 'object' || parsed === null) {
      throw new Error('Parsed data is not an object');
    }
    return parsed;
  } catch (error) {
    throw new Error(`JSON parsing failed: ${error.message}`);
  }
}

/**
 * MCP Client for database document retrieval
 */
class MCPClient {
  constructor(config) {
    this.config = config;
    this.requestId = 0;
    this.pendingRequests = new Map();
    this.ws = null;
    this.session = null;
  }

  /**
   * Create MCP session
   */
  async createSession() {
    const config = getSecureConfig();
    const apiUrl = config.api?.baseUrl || 'http://localhost:8000';

    const response = await this.makeHttpRequest(`${apiUrl}/api/v1/internal/mcp/session/init`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${config.api?.jwtToken || 'your-jwt-token'}`,
        'Content-Type': 'application/json',
      },
    });

    this.session = response;
    secureLog(`MCP Session created: ${this.session.session_id}`);
    return this.session;
  }

  /**
   * Connect to WebSocket
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.session.websocket_url);

      this.ws.on('open', () => {
        secureLog('MCP WebSocket connected');
        resolve();
      });

      this.ws.on('message', data => {
        this.handleMessage(JSON.parse(data.toString()));
      });

      this.ws.on('error', error => {
        secureLog(`MCP WebSocket error: ${error.message}`);
        reject(error);
      });

      this.ws.on('close', () => {
        secureLog('MCP WebSocket closed');
      });
    });
  }

  /**
   * Initialize MCP protocol
   */
  async initializeProtocol() {
    const initRequest = {
      jsonrpc: '2.0',
      method: 'initialize',
      params: {
        protocolVersion: '2025-03-26',
        capabilities: {
          roots: { listChanged: true },
          sampling: {},
        },
        clientInfo: {
          name: 'Document Sync Client',
          version: '1.0.0',
        },
      },
      id: ++this.requestId,
    };

    return this.sendRequest(initRequest);
  }

  /**
   * Call MCP tool
   */
  async callTool(name, args) {
    const request = {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name,
        arguments: args,
      },
      id: ++this.requestId,
    };

    return this.sendRequest(request);
  }

  /**
   * Send JSON-RPC request
   */
  async sendRequest(request) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected');
    }

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(request.id, { resolve, reject });
      this.ws.send(JSON.stringify(request));

      // Set timeout
      setTimeout(() => {
        if (this.pendingRequests.has(request.id)) {
          this.pendingRequests.delete(request.id);
          reject(new Error(`Request ${request.id} timed out`));
        }
      }, 30000);
    });
  }

  /**
   * Handle WebSocket messages
   */
  handleMessage(response) {
    const pending = this.pendingRequests.get(response.id);
    if (pending) {
      this.pendingRequests.delete(response.id);
      if (response.error) {
        pending.reject(new Error(response.error.message));
      } else {
        pending.resolve(response.result);
      }
    }
  }

  /**
   * Make HTTP request
   */
  async makeHttpRequest(path, options = {}) {
    const config = getSecureConfig();
    const apiUrl = config.api?.baseUrl || 'http://localhost:8000';
    const url = `${apiUrl}${path}`;

    return new Promise((resolve, reject) => {
      const isHttps = url.startsWith('https://');
      const client = isHttps ? require('https') : require('http');

      const urlObj = new URL(url);
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {},
      };

      const req = client.request(requestOptions, res => {
        let data = '';
        res.on('data', chunk => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            resolve(jsonData);
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', error => {
        reject(error);
      });

      if (options.body) {
        req.write(options.body);
      }
      req.end();
    });
  }

  /**
   * Close connection
   */
  close() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

class DocumentSyncService {
  constructor(options = {}) {
    const config = getSecureConfig();
    this.organizationId = options.organizationId || config.organization.id;
    this.redis = new Redis(config.redis);
    this.embeddings = new OpenAIEmbeddings({
      model: config.openai.model,
      dimensions: 1536,
      openAIApiKey: config.openai.apiKey,
    });

    this.syncStats = {
      documentsRetrieved: 0,
      documentsProcessed: 0,
      chunksCreated: 0,
      embeddingsGenerated: 0,
      embeddingsStored: 0,
      errors: 0,
      errorDetails: [],
      startTime: Date.now(),
    };

    this.options = {
      clearExisting: options.clearExisting || false,
      dryRun: options.dryRun || false,
      batchSize: options.batchSize || 10,
      maxTokensPerChunk: options.maxTokensPerChunk || 8000,
      overlapTokens: options.overlapTokens || 200,
      ...options,
    };
  }

  /**
   * Estimate token count for text (rough approximation)
   */
  estimateTokenCount(text) {
    // Rough estimate: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  /**
   * Split content into chunks based on token limits
   */
  chunkContent(content, documentId, options = {}) {
    const {
      maxTokens = this.options.maxTokensPerChunk,
      overlapTokens = this.options.overlapTokens,
    } = options;

    if (!content || !content.trim()) {
      return [];
    }

    const text = content.trim();
    const estimatedTokens = this.estimateTokenCount(text);

    // If content fits within token limit, return as single chunk
    if (estimatedTokens <= maxTokens) {
      return [
        {
          id: `${documentId}::1`,
          content: text,
          chunkNumber: 1,
          totalChunks: 1,
          documentId: documentId,
          startToken: 0,
          endToken: estimatedTokens,
        },
      ];
    }

    // Split content into chunks
    const chunks = [];
    const words = text.split(/\s+/);
    let currentChunk = [];
    let currentTokens = 0;
    let chunkNumber = 1;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const wordTokens = this.estimateTokenCount(word + ' ');

      // If adding this word would exceed the limit
      if (currentTokens + wordTokens > maxTokens && currentChunk.length > 0) {
        // Save current chunk
        chunks.push({
          id: `${documentId}::${chunkNumber}`,
          content: currentChunk.join(' '),
          chunkNumber: chunkNumber,
          totalChunks: 0, // Will be set later
          documentId: documentId,
          startToken:
            currentTokens -
            currentChunk.reduce((sum, w) => sum + this.estimateTokenCount(w + ' '), 0),
          endToken: currentTokens,
        });

        // Start new chunk with overlap
        const overlapWords = Math.floor(overlapTokens / 4); // Rough word count for overlap
        const overlapStart = Math.max(0, currentChunk.length - overlapWords);
        currentChunk = currentChunk.slice(overlapStart);
        currentTokens = currentChunk.reduce((sum, w) => sum + this.estimateTokenCount(w + ' '), 0);
        chunkNumber++;
      }

      currentChunk.push(word);
      currentTokens += wordTokens;
    }

    // Add final chunk if there's content
    if (currentChunk.length > 0) {
      chunks.push({
        id: `${documentId}::${chunkNumber}`,
        content: currentChunk.join(' '),
        chunkNumber: chunkNumber,
        totalChunks: 0, // Will be set later
        documentId: documentId,
        startToken:
          currentTokens -
          currentChunk.reduce((sum, w) => sum + this.estimateTokenCount(w + ' '), 0),
        endToken: currentTokens,
      });
    }

    // Set total chunks for all chunks
    const totalChunks = chunks.length;
    chunks.forEach(chunk => {
      chunk.totalChunks = totalChunks;
    });

    return chunks;
  }

  /**
   * Extract text content from document
   */
  extractDocumentContent(document) {
    let content = '';

    // Prefer textual content fields
    if (document.content && typeof document.content === 'string') {
      content = document.content;
    }

    // If no text found but we have a binary buffer, we can't extract here
    // In a real implementation, you'd use FileTextExtractor
    if (!content && document.buffer_file) {
      secureLog(`⚠️  Binary file detected for document ${document.id}, skipping text extraction`);
      return null;
    }

    return content;
  }

  /**
   * Generate embeddings for text chunks
   */
  async generateEmbeddings(chunks) {
    if (chunks.length === 0) {
      return [];
    }

    try {
      const texts = chunks.map(chunk => chunk.content);
      const embeddings = await this.embeddings.embedDocuments(texts);

      return chunks.map((chunk, index) => ({
        ...chunk,
        embedding: embeddings[index],
      }));
    } catch (error) {
      throw new Error(`Failed to generate embeddings: ${error.message}`);
    }
  }

  /**
   * Store embeddings in Redis
   */
  async storeEmbeddings(embeddingsWithVectors) {
    if (embeddingsWithVectors.length === 0) {
      return;
    }

    const pipeline = this.redis.pipeline();

    for (const item of embeddingsWithVectors) {
      const key = `embeddings:${this.organizationId}:${item.documentId}::${item.chunkNumber}`;

      const storedDoc = {
        documentId: item.id,
        embedding: item.embedding,
        content: item.content,
        metadata: {
          originalDocumentId: item.documentId,
          chunkNumber: item.chunkNumber,
          totalChunks: item.totalChunks,
          startToken: item.startToken,
          endToken: item.endToken,
          timestamp: Date.now(),
        },
        timestamp: Date.now(),
        organizationId: this.organizationId,
      };

      if (!this.options.dryRun) {
        pipeline.hset(key, 'data', JSON.stringify(storedDoc));

        // Set TTL (24 hours)
        pipeline.expire(key, 24 * 3600);
      }

      secureLog(`  📄 ${key} (${item.content.length} chars, ${item.embedding.length} dimensions)`);
    }

    if (!this.options.dryRun) {
      try {
        await pipeline.exec();
        this.syncStats.embeddingsStored += embeddingsWithVectors.length;
      } catch (error) {
        throw new Error(`Failed to store embeddings: ${error.message}`);
      }
    } else {
      this.syncStats.embeddingsStored += embeddingsWithVectors.length;
    }
  }

  /**
   * Clear existing embeddings for the organization
   */
  async clearExistingEmbeddings() {
    if (!this.options.clearExisting) {
      return;
    }

    secureLog('🗑️  Clearing existing embeddings...');

    const pattern = `embeddings:${this.organizationId}:*`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog('  ℹ️  No existing embeddings found');
      return;
    }

    secureLog(`  🗑️  Found ${keys.length} existing embedding keys`);

    if (!this.options.dryRun) {
      const pipeline = this.redis.pipeline();
      keys.forEach(key => pipeline.del(key));
      await pipeline.exec();
      secureLog(`  ✅ Cleared ${keys.length} embedding keys`);
    } else {
      secureLog(`  🔍 Would clear ${keys.length} embedding keys (dry run)`);
    }
  }

  /**
   * Retrieve documents from database using MCP tool
   */
  async retrieveDocuments() {
    secureLog('📚 Retrieving documents from database using MCP...');

    const mcpClient = new MCPClient();
    let documents = [];

    try {
      // Create MCP session
      await mcpClient.createSession();

      // Connect WebSocket
      await mcpClient.connectWebSocket();

      // Initialize protocol
      await mcpClient.initializeProtocol();

      // Call the get_all_internal_documents tool
      const toolArgs = {
        limit: 1000,
        offset: 0,
        filter: {
          status: 'active', // Only active documents
        },
        includeIdsOnly: false, // We need full document data
      };

      secureLog('  🔧 Calling get_all_internal_documents tool...');
      const result = await mcpClient.callTool('get_all_internal_documents', toolArgs);

      // Parse the result
      if (result && result.content && result.content.length > 0) {
        const contentText = result.content[0].text;
        const parsedResult = safeJsonParse(contentText);

        if (parsedResult.success && parsedResult.documents) {
          documents = parsedResult.documents;
          secureLog(`  📄 Retrieved ${documents.length} documents from database`);

          // Log pagination info if available
          if (parsedResult.pagination) {
            secureLog(
              `  📊 Pagination: ${documents.length}/${parsedResult.pagination.total} documents`
            );
          }
        } else {
          throw new Error(`MCP tool returned error: ${parsedResult.error || 'Unknown error'}`);
        }
      } else {
        throw new Error('MCP tool returned empty or invalid response');
      }
    } catch (error) {
      secureLog(`  ❌ Failed to retrieve documents via MCP: ${error.message}`);
      secureLog('  🔄 Falling back to mock data for testing...');

      // Fallback to mock data for testing
      documents = [
        {
          id: 'doc-1',
          name: 'Sample Document 1 (Mock)',
          content:
            'This is a sample document with some content that will be processed and embedded.',
          documentType: 'text',
          fileType: 'text/plain',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'doc-2',
          name: 'Sample Document 2 (Mock)',
          content:
            'Another sample document with different content. This document is longer and will likely be chunked into multiple pieces for better embedding and retrieval.',
          documentType: 'text',
          fileType: 'text/plain',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
    } finally {
      // Close MCP connection
      mcpClient.close();
    }

    this.syncStats.documentsRetrieved = documents.length;
    secureLog(`  📄 Total documents to process: ${documents.length}`);

    return documents;
  }

  /**
   * Process a single document
   */
  async processDocument(document) {
    try {
      secureLog(`🔄 Processing document: ${document.id} (${document.name})`);

      // Extract content
      const content = this.extractDocumentContent(document);
      if (!content) {
        secureLog(`  ⚠️  No content found, skipping document ${document.id}`);
        return null;
      }

      // Chunk content
      const chunks = this.chunkContent(content, document.id);
      this.syncStats.chunksCreated += chunks.length;

      secureLog(`  📝 Created ${chunks.length} chunks from ${content.length} characters`);

      // Generate embeddings
      const embeddingsWithVectors = await this.generateEmbeddings(chunks);
      this.syncStats.embeddingsGenerated += embeddingsWithVectors.length;

      secureLog(`  🧠 Generated ${embeddingsWithVectors.length} embeddings`);

      // Store in Redis
      await this.storeEmbeddings(embeddingsWithVectors);

      this.syncStats.documentsProcessed++;
      return embeddingsWithVectors.length;
    } catch (error) {
      this.syncStats.errors++;
      this.syncStats.errorDetails.push({
        documentId: document.id,
        error: error.message,
      });
      secureLog(`  ❌ Error processing document ${document.id}: ${error.message}`);
      return null;
    }
  }

  /**
   * Run the complete sync operation
   */
  async syncDocuments() {
    const startTime = Date.now();
    const config = getSecureConfig();

    secureLog('🚀 Starting document sync to Redis...');
    secureLog(`📊 Organization ID: ${this.organizationId}`);
    secureLog(`🔧 Options:`, {
      clearExisting: this.options.clearExisting,
      dryRun: this.options.dryRun,
      batchSize: this.options.batchSize,
    });

    if (config.environment !== 'production') {
      secureLog(`🔗 Redis: ${config.redis.host}:${config.redis.port}`);
    }

    try {
      // Test Redis connection
      await this.redis.ping();
      secureLog('✅ Redis connection successful');

      // Clear existing embeddings if requested
      await this.clearExistingEmbeddings();

      // Retrieve documents
      const documents = await this.retrieveDocuments();

      if (documents.length === 0) {
        secureLog('ℹ️  No documents found to sync');
        return;
      }

      // Process documents in batches
      secureLog(
        `🔄 Processing ${documents.length} documents in batches of ${this.options.batchSize}...`
      );

      for (let i = 0; i < documents.length; i += this.options.batchSize) {
        const batch = documents.slice(i, i + this.options.batchSize);
        const batchNumber = Math.floor(i / this.options.batchSize) + 1;
        const totalBatches = Math.ceil(documents.length / this.options.batchSize);

        secureLog(`📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} documents)`);

        for (const document of batch) {
          await this.processDocument(document);
        }

        // Small delay between batches to avoid overwhelming the system
        if (i + this.options.batchSize < documents.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Print sync summary
      this.printSyncSummary(startTime);
    } catch (error) {
      secureLog(`❌ Sync failed: ${error.message}`);
      throw error;
    } finally {
      await this.redis.quit();
    }
  }

  /**
   * Print sync summary
   */
  printSyncSummary(startTime) {
    const duration = Date.now() - startTime;
    const durationSeconds = (duration / 1000).toFixed(2);

    secureLog('\n📊 Sync Summary');
    secureLog('================');
    secureLog(`Organization ID: ${this.organizationId}`);
    secureLog(`Duration: ${durationSeconds} seconds`);
    secureLog(`Documents Retrieved: ${this.syncStats.documentsRetrieved}`);
    secureLog(`Documents Processed: ${this.syncStats.documentsProcessed}`);
    secureLog(`Chunks Created: ${this.syncStats.chunksCreated}`);
    secureLog(`Embeddings Generated: ${this.syncStats.embeddingsGenerated}`);
    secureLog(`Embeddings Stored: ${this.syncStats.embeddingsStored}`);
    secureLog(`Errors: ${this.syncStats.errors}`);

    if (this.syncStats.errors > 0) {
      secureLog('\n❌ Errors encountered:');
      this.syncStats.errorDetails.forEach(({ documentId, error }) => {
        secureLog(`  - ${documentId}: ${error}`);
      });
    }

    if (this.options.dryRun) {
      secureLog('\n🔍 This was a dry run - no changes were made to Redis');
    } else {
      secureLog('\n✅ Document sync completed successfully!');
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  // Parse command line arguments
  const options = {
    clearExisting: args.includes('--clear'),
    dryRun: args.includes('--dry-run'),
    organizationId: args.find(arg => arg.startsWith('--org='))?.split('=')[1] || ORGANIZATION_ID,
  };

  if (args.includes('--help')) {
    const helpText = `
Document Sync to Redis Script

Usage:
  node sync-documents-to-redis.js [options]

Options:
  --clear           Clear existing embeddings before syncing
  --dry-run         Show what would be done without making changes
  --org=<orgId>     Organization ID to sync (default: ${ORGANIZATION_ID})
  --help            Show this help message

Examples:
  node sync-documents-to-redis.js
  node sync-documents-to-redis.js --clear
  node sync-documents-to-redis.js --dry-run
  node sync-documents-to-redis.js --clear --org=org-12345678-1234-1234-1234-123456789abc

Environment Variables:
  REDIS_HOST        Redis host (default: localhost)
  REDIS_PORT        Redis port (default: 6379)
  REDIS_PASSWORD    Redis password
  REDIS_DB          Redis database (default: 0)
  OPENAI_API_KEY    OpenAI API key for embeddings
  LANGCHAIN_EMBEDDINGS Embedding model (default: text-embedding-3-large)
  API_BASE_URL      API base URL (default: http://localhost:8000)
  API_JWT_TOKEN     JWT token for API authentication
  ORGANIZATION_ID   Organization ID to sync (default: test-org-id)
  NODE_ENV          Environment mode (development/production)
`;
    console.log(helpText);
    process.exit(0);
  }

  const syncService = new DocumentSyncService(options);

  try {
    await syncService.syncDocuments();
  } catch (error) {
    secureLog(`❌ Script failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the sync
main().catch(error => {
  secureLog(`❌ Unexpected error: ${error.message}`);
  process.exit(1);
});
