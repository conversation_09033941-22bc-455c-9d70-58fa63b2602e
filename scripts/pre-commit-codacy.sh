#!/bin/bash

# Pre-commit hook for Codacy analysis
# This script runs Codacy analysis before each commit

set -e

echo "🔍 Running Codacy pre-commit analysis..."

# Make Codacy CLI executable
chmod +x .codacy/cli.sh

# Run ESLint analysis
echo "📝 Running ESLint analysis..."
.codacy/cli.sh analyze --tool eslint

# Run Semgrep analysis
echo "🛡️ Running Semgrep security analysis..."
.codacy/cli.sh analyze --tool semgrep

# Run Trivy analysis (only for changed files)
echo "🔒 Running Trivy vulnerability scan..."
.codacy/cli.sh analyze --tool trivy

echo "✅ Codacy pre-commit analysis completed successfully!"
echo "💡 If issues were found, please fix them before committing."

# Exit with success
exit 0 