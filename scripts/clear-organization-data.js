#!/usr/bin/env node

/**
 * Clear Organization Data Script
 *
 * This script safely clears all Redis data for a specific organization:
 * - content_hashes:{orgId}
 * - doc_cache:{orgId}
 * - embeddings:{orgId}:*
 *
 * IMPORTANT: This is a destructive operation that will permanently delete data.
 * Use with extreme caution and ensure you have proper backups.
 */

const Redis = require('ioredis');

// Configuration with secure defaults
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0', 10),
};

// Secure logging function that respects NODE_ENV
function secureLog(message, data = null) {
  if (process.env.NODE_ENV === 'production') {
    // In production, only log essential information without sensitive data
    const sanitizedMessage = message.replace(/🔗 Redis: .*/, '🔗 Redis: [REDACTED]');
    console.log(sanitizedMessage);
  } else {
    // In development, log full information
    console.log(message);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

class OrganizationDataClearer {
  constructor(organizationId) {
    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    // Validate organization ID format
    if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
      throw new Error('Invalid organization ID format. Expected format: org-{uuid}');
    }

    this.organizationId = organizationId;
    this.redis = new Redis(REDIS_CONFIG);
    this.clearStats = {
      contentHashesCleared: 0,
      docCacheCleared: 0,
      embeddingsCleared: 0,
      totalKeysCleared: 0,
      errors: 0,
      errorKeys: [],
    };
  }

  /**
   * Get all keys that will be cleared for the organization
   */
  async getKeysToClear() {
    const keys = {
      contentHashes: [],
      docCache: [],
      embeddings: [],
    };

    try {
      // Get content_hashes keys
      const contentHashesPattern = `content_hashes:${this.organizationId}`;
      const contentHashesKeys = await this.redis.keys(contentHashesPattern);
      keys.contentHashes = contentHashesKeys;

      // Get doc_cache keys
      const docCachePattern = `doc_cache:${this.organizationId}`;
      const docCacheKeys = await this.redis.keys(docCachePattern);
      keys.docCache = docCacheKeys;

      // Get embeddings keys
      const embeddingsPattern = `embeddings:${this.organizationId}:*`;
      const embeddingsKeys = await this.redis.keys(embeddingsPattern);
      keys.embeddings = embeddingsKeys;

      return keys;
    } catch (error) {
      throw new Error(`Failed to get keys: ${error.message}`);
    }
  }

  /**
   * Clear content_hashes for the organization
   */
  async clearContentHashes() {
    const pattern = `content_hashes:${this.organizationId}`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No content_hashes keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(
      `🗑️  Clearing ${keys.length} content_hashes keys for organization: ${this.organizationId}`
    );

    for (const key of keys) {
      try {
        await this.redis.del(key);
        this.clearStats.contentHashesCleared++;
        this.clearStats.totalKeysCleared++;
        secureLog(`  ✅ Cleared: ${key}`);
      } catch (error) {
        this.clearStats.errors++;
        this.clearStats.errorKeys.push({ key, error: error.message });
        secureLog(`  ❌ Failed to clear: ${key} - ${error.message}`);
      }
    }
  }

  /**
   * Clear doc_cache for the organization
   */
  async clearDocCache() {
    const pattern = `doc_cache:${this.organizationId}`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No doc_cache keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(
      `🗑️  Clearing ${keys.length} doc_cache keys for organization: ${this.organizationId}`
    );

    for (const key of keys) {
      try {
        await this.redis.del(key);
        this.clearStats.docCacheCleared++;
        this.clearStats.totalKeysCleared++;
        secureLog(`  ✅ Cleared: ${key}`);
      } catch (error) {
        this.clearStats.errors++;
        this.clearStats.errorKeys.push({ key, error: error.message });
        secureLog(`  ❌ Failed to clear: ${key} - ${error.message}`);
      }
    }
  }

  /**
   * Clear embeddings for the organization
   */
  async clearEmbeddings() {
    const pattern = `embeddings:${this.organizationId}:*`;
    const keys = await this.redis.keys(pattern);

    if (keys.length === 0) {
      secureLog(`📝 No embeddings keys found for organization: ${this.organizationId}`);
      return;
    }

    secureLog(
      `🗑️  Clearing ${keys.length} embeddings keys for organization: ${this.organizationId}`
    );

    for (const key of keys) {
      try {
        await this.redis.del(key);
        this.clearStats.embeddingsCleared++;
        this.clearStats.totalKeysCleared++;
        secureLog(`  ✅ Cleared: ${key}`);
      } catch (error) {
        this.clearStats.errors++;
        this.clearStats.errorKeys.push({ key, error: error.message });
        secureLog(`  ❌ Failed to clear: ${key} - ${error.message}`);
      }
    }
  }

  /**
   * Run the complete clearing operation
   */
  async clearOrganizationData() {
    secureLog(`🚀 Starting data clearing for organization: ${this.organizationId}`);
    secureLog(`🔗 Redis: ${REDIS_CONFIG.host}:${REDIS_CONFIG.port} (DB: ${REDIS_CONFIG.db})`);

    try {
      // Test Redis connection
      await this.redis.ping();
      secureLog('✅ Redis connection successful');
    } catch (error) {
      throw new Error(`Failed to connect to Redis: ${error.message}`);
    }

    // Get keys that will be cleared (for preview)
    const keysToClear = await this.getKeysToClear();
    const totalKeys =
      keysToClear.contentHashes.length +
      keysToClear.docCache.length +
      keysToClear.embeddings.length;

    secureLog(`📊 Preview of keys to be cleared:`);
    secureLog(`  - content_hashes: ${keysToClear.contentHashes.length} keys`);
    secureLog(`  - doc_cache: ${keysToClear.docCache.length} keys`);
    secureLog(`  - embeddings: ${keysToClear.embeddings.length} keys`);
    secureLog(`  - Total: ${totalKeys} keys`);

    if (totalKeys === 0) {
      secureLog(`ℹ️  No data found for organization: ${this.organizationId}`);
      return;
    }

    // Clear each data type
    await this.clearContentHashes();
    await this.clearDocCache();
    await this.clearEmbeddings();

    // Verify clearing
    await this.verifyClearing();

    // Print summary
    this.printClearingSummary();
  }

  /**
   * Verify that all data has been cleared
   */
  async verifyClearing() {
    secureLog('🔍 Verifying that all data has been cleared...');

    const remainingKeys = await this.getKeysToClear();
    const totalRemaining =
      remainingKeys.contentHashes.length +
      remainingKeys.docCache.length +
      remainingKeys.embeddings.length;

    if (totalRemaining === 0) {
      secureLog('✅ Verification successful: All organization data has been cleared');
    } else {
      secureLog(`⚠️  Warning: ${totalRemaining} keys still remain:`);
      if (remainingKeys.contentHashes.length > 0) {
        secureLog(`  - content_hashes: ${remainingKeys.contentHashes.length} keys remaining`);
      }
      if (remainingKeys.docCache.length > 0) {
        secureLog(`  - doc_cache: ${remainingKeys.docCache.length} keys remaining`);
      }
      if (remainingKeys.embeddings.length > 0) {
        secureLog(`  - embeddings: ${remainingKeys.embeddings.length} keys remaining`);
      }
    }
  }

  /**
   * Print clearing summary
   */
  printClearingSummary() {
    secureLog('\n📊 Clearing Summary:');
    secureLog('==================');
    secureLog(`Organization ID: ${this.organizationId}`);
    secureLog(`Content Hashes Cleared: ${this.clearStats.contentHashesCleared}`);
    secureLog(`Document Cache Cleared: ${this.clearStats.docCacheCleared}`);
    secureLog(`Embeddings Cleared: ${this.clearStats.embeddingsCleared}`);
    secureLog(`Total Keys Cleared: ${this.clearStats.totalKeysCleared}`);
    secureLog(`Errors: ${this.clearStats.errors}`);

    if (this.clearStats.errors > 0) {
      secureLog('\n❌ Errors encountered:');
      this.clearStats.errorKeys.forEach(({ key, error }) => {
        secureLog(`  - ${key}: ${error}`);
      });
    }

    if (this.clearStats.totalKeysCleared > 0) {
      secureLog('\n✅ Organization data clearing completed successfully!');
    } else {
      secureLog('\nℹ️  No data was found to clear for this organization.');
    }
  }

  /**
   * Clean up Redis connection
   */
  async cleanup() {
    if (this.redis) {
      await this.redis.quit();
      secureLog('🔌 Redis connection closed');
    }
  }
}

/**
 * Display usage information
 */
function showUsage() {
  console.log(`
Clear Organization Data Script

Usage: node scripts/clear-organization-data.js <organizationId>

Arguments:
  organizationId    The organization ID to clear data for (format: org-{uuid})

Examples:
  node scripts/clear-organization-data.js your-organization-id
  node scripts/clear-organization-data.js org-12345678-1234-1234-1234-123456789abc

Environment Variables:
  REDIS_HOST        Redis host (default: localhost)
  REDIS_PORT        Redis port (default: 6379)
  REDIS_PASSWORD    Redis password (optional)
  REDIS_DB          Redis database (default: 0)
  NODE_ENV          Environment mode (production/development)

⚠️  WARNING: This script will permanently delete all Redis data for the specified organization.
   Make sure you have proper backups before running this script.
   This operation cannot be undone.
`);
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);

  // Check for help flag
  if (args.includes('--help') || args.includes('-h') || args.length === 0) {
    showUsage();
    process.exit(0);
  }

  const organizationId = args[0];

  // Validate organization ID
  if (!organizationId || !organizationId.match(/^org-[a-f0-9-]+$/)) {
    console.error('❌ Error: Invalid organization ID format.');
    console.error('Expected format: org-{uuid}');
    console.error('Example: your-organization-id');
    showUsage();
    process.exit(1);
  }

  let clearer = null;

  try {
    clearer = new OrganizationDataClearer(organizationId);
    await clearer.clearOrganizationData();
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  } finally {
    if (clearer) {
      await clearer.cleanup();
    }
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { OrganizationDataClearer };
