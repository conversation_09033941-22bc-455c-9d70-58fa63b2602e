import { test } from 'tap';
import { build } from '../helper';

test('POST /v1/external/embeddings/submit', async t => {
  const app = await build(t);

  // Test missing organization ID header
  t.test('should return 400 when X-Organization-Id header is missing', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/embeddings/submit',
      headers: {
        'X-Internal-Secret': 'test-secret',
        'Content-Type': 'application/json',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            name: 'Test Document',
            content: 'This is a test document with sufficient content for processing.',
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'MISSING_ORGANIZATION_ID');
  });

  // Test invalid organization ID format
  t.test('should return 400 when organization ID format is invalid', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/embeddings/submit',
      headers: {
        'X-Internal-Secret': 'test-secret',
        'X-Organization-Id': 'invalid-org-id',
        'Content-Type': 'application/json',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            name: 'Test Document',
            content: 'This is a test document with sufficient content for processing.',
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INVALID_ORGANIZATION_ID');
  });

  // Test missing documents array
  t.test('should return 400 when documents array is missing', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/embeddings/submit',
      headers: {
        'X-Internal-Secret': 'test-secret',
        'X-Organization-Id': 'org-12345678-1234-1234-1234-123456789012',
        'Content-Type': 'application/json',
      },
      payload: {},
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INVALID_REQUEST_BODY');
  });

  // Test invalid document structure
  t.test('should return 400 when document is missing required fields', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/embeddings/submit',
      headers: {
        'X-Internal-Secret': 'test-secret',
        'X-Organization-Id': 'org-12345678-1234-1234-1234-123456789012',
        'Content-Type': 'application/json',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            // Missing name and content
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INVALID_DOCUMENT');
  });

  // Test insufficient content
  t.test('should return 400 when document content is too short', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/embeddings/submit',
      headers: {
        'X-Internal-Secret': 'test-secret',
        'X-Organization-Id': 'org-12345678-1234-1234-1234-123456789012',
        'Content-Type': 'application/json',
      },
      payload: {
        documents: [
          {
            id: 'test-doc-1',
            name: 'Test Document',
            content: 'Too short', // Less than 20 characters
          },
        ],
      },
    });

    t.equal(response.statusCode, 400);
    const body = JSON.parse(response.body);
    t.equal(body.code, 'INSUFFICIENT_CONTENT');
  });

  // Test valid request structure (this would normally require mocking the embedding services)
  t.test('should accept valid request structure', async t => {
    // Note: This test would require mocking the MCP tools and Redis
    // For now, we just verify the request validation passes
    const validPayload = {
      documents: [
        {
          id: 'test-doc-1',
          name: 'Test Document',
          content:
            'This is a test document with sufficient content for processing and embedding generation.',
          metadata: {
            category: 'test',
            author: 'test-user',
          },
        },
      ],
      options: {
        maxTokens: 1500,
        overlapTokens: 150,
        clearExisting: false,
      },
    };

    // This test would need proper mocking to complete successfully
    // For now, we just verify the payload structure is valid
    t.ok(validPayload.documents.length > 0);
    t.ok(validPayload.documents[0].content.length >= 20);
    t.ok(validPayload.options.maxTokens >= 100);
  });
});

test('Schema validation for SubmitEmbeddingsSchema', async t => {
  // Test that the schema is properly exported and structured
  const { SubmitEmbeddingsSchema } = await import('../../src/api/v1/external/schemas/embeddings');

  t.ok(SubmitEmbeddingsSchema, 'SubmitEmbeddingsSchema should be exported');
  t.ok(SubmitEmbeddingsSchema.headers, 'Schema should have headers validation');
  t.ok(SubmitEmbeddingsSchema.body, 'Schema should have body validation');
  t.ok(SubmitEmbeddingsSchema.response, 'Schema should have response validation');

  // Verify required headers
  const headers = SubmitEmbeddingsSchema.headers as any;
  t.ok(headers.required?.includes('x-organization-id'), 'Should require x-organization-id header');

  // Verify body structure
  const body = SubmitEmbeddingsSchema.body as any;
  t.equal(body.type, 'object', 'Body should be an object');
  t.ok(body.required?.includes('documents'), 'Should require documents field');
});
