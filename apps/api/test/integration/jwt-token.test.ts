import { test } from 'tap';
import { build } from '../helper';

test('JWT Token Generation Endpoint', async t => {
  const app = await build(t);

  t.test('POST /v1/external/auth/token - should generate JWT token with valid headers', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/auth/token',
      headers: {
        'x-internal-secret': process.env.INTERNAL_API_SECRET || 'dev-secret-key-123',
        'x-organization-id': 'test-org-123',
        'content-type': 'application/json',
      },
      payload: {},
    });

    t.equal(response.statusCode, 200);

    const result = JSON.parse(response.payload);
    t.ok(result.access_token);
    t.equal(result.token_type, 'Bearer');
    t.ok(result.expires_in);
    t.equal(result.organization_id, 'test-org-123');

    // Verify the token is a valid JWT format
    const tokenParts = result.access_token.split('.');
    t.equal(tokenParts.length, 3, 'Token should have 3 parts (header.payload.signature)');
  });

  t.test('POST /v1/external/auth/token - should fail without internal secret', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/auth/token',
      headers: {
        'x-organization-id': 'test-org-123',
        'content-type': 'application/json',
      },
      payload: {},
    });

    t.equal(response.statusCode, 401);
  });

  t.test('POST /v1/external/auth/token - should fail without organization ID', async t => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/auth/token',
      headers: {
        'x-internal-secret': process.env.INTERNAL_API_SECRET || 'dev-secret-key-123',
        'content-type': 'application/json',
      },
      payload: {},
    });

    t.equal(response.statusCode, 400);

    const result = JSON.parse(response.payload);
    t.equal(result.code, 'MISSING_ORGANIZATION_ID');
  });

  t.test(
    'POST /v1/external/auth/token - should fail with invalid organization ID format',
    async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/v1/external/auth/token',
        headers: {
          'x-internal-secret': process.env.INTERNAL_API_SECRET || 'dev-secret-key-123',
          'x-organization-id': 'invalid@org#id',
          'content-type': 'application/json',
        },
        payload: {},
      });

      t.equal(response.statusCode, 400);

      const result = JSON.parse(response.payload);
      t.equal(result.code, 'INVALID_ORGANIZATION_ID');
    }
  );
});
