import { test } from 'tap';
import { build } from '../helper';

test('MCP Implementation Tests', async t => {
  const app = await build(t);

  t.test('MCP session initialization should work', async t => {
    // This test verifies that the MCP routes are properly registered and accessible
    const response = await app.inject({
      method: 'GET',
      url: '/api/v1/internal/mcp/session/test-session/status',
      headers: {
        authorization: 'Bearer fake-jwt-token',
      },
    });

    // We expect this to fail with authentication error since we don't have valid JWT
    // but the route should be accessible
    t.ok(response.statusCode === 401 || response.statusCode === 404, 'MCP route is accessible');
  });

  t.test('Redis plugin should be loaded', async t => {
    // Check if redis is available on the fastify instance
    t.ok(app.redis, 'Redis client should be available');
  });

  t.end();
});
