import { test } from 'tap';
import Fastify, { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import corsPlugin from '../../src/lib/cors';
import envPlugin from '../../src/config/env/env';

test('CORS plugin tests', async t => {
  let fastify: FastifyInstance;

  t.beforeEach(async () => {
    fastify = Fastify();
    await fastify.register(envPlugin);
    await fastify.register(corsPlugin);

    fastify.get('/', async (request: FastifyRequest, reply: FastifyReply) => {
      return { hello: 'world' };
    });
  });

  t.afterEach(async () => {
    await fastify.close();
  });

  t.test('should allow requests from an allowed origin', async st => {
    process.env.CORS_ORIGINS = 'http://localhost:8000';
    const response = await fastify.inject({
      method: 'GET',
      url: '/',
      headers: {
        origin: 'http://localhost:8000',
      },
    });

    st.equal(response.statusCode, 200, 'should return 200 OK');
    st.equal(
      response.headers['access-control-allow-origin'],
      'http://localhost:8000',
      'should have Access-Control-Allow-Origin header'
    );
    st.equal(
      response.headers['access-control-allow-credentials'],
      'true',
      'should have Access-Control-Allow-Credentials header'
    );
  });

  t.test('should not allow requests from a disallowed origin', async st => {
    process.env.CORS_ORIGINS = 'http://localhost:8000';
    const response = await fastify.inject({
      method: 'GET',
      url: '/',
      headers: {
        origin: 'http://localhost:8001',
      },
    });

    st.equal(response.statusCode, 200, 'should return 200 OK');
    st.notOk(
      response.headers['access-control-allow-origin'],
      'should not have Access-Control-Allow-Origin header'
    );
  });

  t.test('should handle preflight requests', async st => {
    process.env.CORS_ORIGINS = 'http://localhost:8000';
    const response = await fastify.inject({
      method: 'OPTIONS',
      url: '/',
      headers: {
        origin: 'http://localhost:8000',
        'access-control-request-method': 'POST',
        'access-control-request-headers': 'Content-Type,Authorization',
      },
    });

    st.equal(response.statusCode, 204, 'should return 204 No Content for preflight');
    st.equal(
      response.headers['access-control-allow-origin'],
      'http://localhost:8000',
      'should have Access-Control-Allow-Origin header'
    );
    st.equal(
      response.headers['access-control-allow-methods'],
      'GET,POST,PUT,DELETE,OPTIONS',
      'should have Access-Control-Allow-Methods header'
    );
    st.ok(
      response.headers['access-control-allow-headers'],
      'should have Access-Control-Allow-Headers header'
    );
    st.equal(
      response.headers['access-control-allow-credentials'],
      'true',
      'should have Access-Control-Allow-Credentials header'
    );
  });

  t.test('should allow all origins when CORS_ORIGINS is *', async st => {
    process.env.CORS_ORIGINS = '*';
    const response = await fastify.inject({
      method: 'GET',
      url: '/',
      headers: {
        origin: 'http://anydomain.com',
      },
    });

    st.equal(response.statusCode, 200, 'should return 200 OK');
    st.equal(
      response.headers['access-control-allow-origin'],
      '*',
      'should have Access-Control-Allow-Origin header as *'
    );
  });
});
