import { test } from 'tap';
import { build } from '../../../../helper';

test('Embeddings API', async t => {
  const app = await build(t);

  t.test('GET /v1/external/embeddings - should return embeddings', async t => {
    const response = await app.inject({
      method: 'GET',
      url: '/v1/external/embeddings',
      headers: {
        'x-api-key': 'test-api-key',
      },
    });

    t.equal(response.statusCode, 200);
  });

  await app.close();
});
