import { test } from 'tap';
import { build } from '../../../helper';
import { ApiKeyValidator } from '../../../../src/core/auth/api-key/validator';
import { API_KEY_ERRORS } from '../../../../src/types/api-key';

test('ApiKeyValidator', async t => {
  const app = await build(t);

  const validator = new ApiKeyValidator(app);

  t.test('validateApiKey - missing key', async t => {
    const result = await validator.validateApiKey('');

    t.equal(result.isValid, false);
    t.equal(result.error, API_KEY_ERRORS.MISSING_KEY.message);
    t.notOk(result.keyId);
  });

  t.test('validateApiKey - invalid key', async t => {
    // Mock Prisma to return null for invalid key
    app.prisma.api_key.findFirst = async () => null;

    const result = await validator.validateApiKey('invalid-key');

    t.equal(result.isValid, false);
    t.equal(result.error, API_KEY_ERRORS.INVALID_KEY.message);
    t.notOk(result.keyId);
  });

  t.test('validateApiKey - expired key', async t => {
    // Mock Prisma to return an expired key
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: new Date(Date.now() - 1000), // Expired 1 second ago
    });

    const result = await validator.validateApiKey('expired-key');

    t.equal(result.isValid, false);
    t.equal(result.error, API_KEY_ERRORS.EXPIRED_KEY.message);
    t.notOk(result.keyId);
  });

  t.test('validateApiKey - valid key', async t => {
    // Mock Prisma to return a valid key
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: new Date(Date.now() + 1000), // Expires in 1 second
    });

    const result = await validator.validateApiKey('valid-key');

    t.equal(result.isValid, true);
    t.equal(result.keyId, 'test-id');
    t.notOk(result.error);
  });

  t.test('validateApiKey - valid key without expiration', async t => {
    // Mock Prisma to return a valid key without expiration
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: null,
    });

    const result = await validator.validateApiKey('valid-key-no-expiry');

    t.equal(result.isValid, true);
    t.equal(result.keyId, 'test-id');
    t.notOk(result.error);
  });

  t.test('validateApiKey - database error', async t => {
    // Mock Prisma to throw an error
    app.prisma.api_key.findFirst = async () => {
      throw new Error('Database error');
    };

    const result = await validator.validateApiKey('error-key');

    t.equal(result.isValid, false);
    t.equal(result.error, API_KEY_ERRORS.INVALID_KEY.message);
    t.notOk(result.keyId);
  });
});
