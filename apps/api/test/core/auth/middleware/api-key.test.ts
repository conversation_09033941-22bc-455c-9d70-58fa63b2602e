import { test } from 'tap';
import { build } from '../../../helper';
import { apiKeyMiddleware } from '../../../../src/core/auth/middleware/service';
import { API_KEY_HEADER } from '../../../../src/types/api-key';

test('apiKeyMiddleware', async t => {
  const app = await build(t);

  t.test('missing API key', async t => {
    const request = {
      headers: {},
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.equal(code, 401);
          t.equal(data.error, 'API key is required');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.notOk(request.apiKeyId);
  });

  t.test('invalid API key', async t => {
    // Mock Prisma to return null for invalid key
    app.prisma.api_key.findFirst = async () => null;

    const request = {
      headers: {
        [API_KEY_HEADER]: 'invalid-key',
      },
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.equal(code, 401);
          t.equal(data.error, 'Invalid API key');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.notOk(request.apiKeyId);
  });

  t.test('expired API key', async t => {
    // Mock Prisma to return an expired key
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: new Date(Date.now() - 1000), // Expired 1 second ago
    });

    const request = {
      headers: {
        [API_KEY_HEADER]: 'expired-key',
      },
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.equal(code, 401);
          t.equal(data.error, 'API key has expired');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.notOk(request.apiKeyId);
  });

  t.test('valid API key', async t => {
    // Mock Prisma to return a valid key
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: new Date(Date.now() + 1000), // Expires in 1 second
    });

    const request = {
      headers: {
        [API_KEY_HEADER]: 'valid-key',
      },
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.fail('Should not send error response');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.equal(request.apiKeyId, 'test-id');
  });

  t.test('valid API key without expiration', async t => {
    // Mock Prisma to return a valid key without expiration
    app.prisma.api_key.findFirst = async () => ({
      id: 'test-id',
      expires_at: null,
    });

    const request = {
      headers: {
        [API_KEY_HEADER]: 'valid-key-no-expiry',
      },
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.fail('Should not send error response');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.equal(request.apiKeyId, 'test-id');
  });

  t.test('database error', async t => {
    // Mock Prisma to throw an error
    app.prisma.api_key.findFirst = async () => {
      throw new Error('Database error');
    };

    const request = {
      headers: {
        [API_KEY_HEADER]: 'error-key',
      },
      server: app,
    } as any;

    const reply = {
      code: (code: number) => ({
        send: (data: any) => {
          t.equal(code, 401);
          t.equal(data.error, 'Invalid API key');
        },
      }),
    } as any;

    await apiKeyMiddleware(request, reply);
    t.notOk(request.apiKeyId);
  });
});
