import { test } from 'tap';
import { FastifyInstance } from 'fastify';
import { build } from '../../helper';

test('Validation Tests', async t => {
  let app: FastifyInstance;

  t.before(async () => {
    app = await build(t);
  });

  t.teardown(async () => {
    await app.close();
  });

  t.test('Item Creation Validation', async t => {
    t.test('should validate required fields', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/items',
        payload: {
          // Missing required fields
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should validate field types and constraints', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/items',
        payload: {
          name: '', // Empty string (minLength: 1)
          description: 'a'.repeat(1001), // Too long (maxLength: 1000)
          tags: Array(11).fill('tag'), // Too many tags (maxItems: 10)
        },
      });

      t.equal(response.statusCode, 400);
      const payload = JSON.parse(response.payload);
      t.equal(payload.error, 'Validation Error');
      t.ok(payload.details);
    });

    t.test('should accept valid payload', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/items',
        payload: {
          name: 'Test Item',
          description: 'Test Description',
          tags: ['tag1', 'tag2'],
        },
      });

      t.not(response.statusCode, 400);
    });
  });

  t.test('Item Update Validation', async t => {
    t.test('should validate UUID format', async t => {
      const response = await app.inject({
        method: 'PUT',
        url: '/items/invalid-uuid',
        payload: {
          name: 'Updated Name',
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should require at least one field to update', async t => {
      const response = await app.inject({
        method: 'PUT',
        url: '/items/123e4567-e89b-12d3-a456-************',
        payload: {},
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });
  });
});
