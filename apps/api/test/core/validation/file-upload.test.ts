import { test } from 'tap';
import { FastifyInstance } from 'fastify';
import { build } from '../../helper';

test('File Upload Validation', async t => {
  let app: FastifyInstance;

  t.before(async () => {
    app = await build(t);
  });

  t.teardown(async () => {
    await app.close();
  });

  t.test('should validate file upload', async t => {
    t.test('should reject invalid file type', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/files',
        payload: {
          file: {
            filename: 'test.exe',
            mimetype: 'application/x-msdownload',
            data: 'base64data',
          },
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should reject invalid filename format', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/files',
        payload: {
          file: {
            filename: 'test file.pdf',
            mimetype: 'application/pdf',
            data: 'base64data',
          },
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should accept valid file upload', async t => {
      const response = await app.inject({
        method: 'POST',
        url: '/files',
        payload: {
          file: {
            filename: 'test.pdf',
            mimetype: 'application/pdf',
            data: 'base64data',
          },
          metadata: {
            description: 'Test file',
            tags: ['test', 'pdf'],
          },
        },
      });

      t.not(response.statusCode, 400);
    });
  });

  t.test('should validate file download', async t => {
    t.test('should reject invalid UUID', async t => {
      const response = await app.inject({
        method: 'GET',
        url: '/files/invalid-uuid',
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should accept valid UUID', async t => {
      const response = await app.inject({
        method: 'GET',
        url: '/files/123e4567-e89b-12d3-a456-************',
      });

      t.not(response.statusCode, 400);
    });
  });

  t.test('should validate metadata update', async t => {
    t.test('should reject empty metadata', async t => {
      const response = await app.inject({
        method: 'PATCH',
        url: '/files/123e4567-e89b-12d3-a456-************',
        payload: {
          metadata: {},
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should reject too many tags', async t => {
      const response = await app.inject({
        method: 'PATCH',
        url: '/files/123e4567-e89b-12d3-a456-************',
        payload: {
          metadata: {
            tags: Array(11).fill('tag'),
          },
        },
      });

      t.equal(response.statusCode, 400);
      t.match(JSON.parse(response.payload), {
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
      });
    });

    t.test('should accept valid metadata update', async t => {
      const response = await app.inject({
        method: 'PATCH',
        url: '/files/123e4567-e89b-12d3-a456-************',
        payload: {
          metadata: {
            description: 'Updated description',
            tags: ['updated', 'test'],
          },
        },
      });

      t.not(response.statusCode, 400);
    });
  });
});
