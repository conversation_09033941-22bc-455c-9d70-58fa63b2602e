import { test } from 'tap';
import {
  formatNonStreamingResponse,
  formatStreamingCompletionResponse,
  formatErrorResponse,
} from '../../../src/lib/utils/response-formatter';

test('Response Formatter', async t => {
  t.test('formatNonStreamingResponse', async st => {
    st.test('should format a basic agent result correctly', async sst => {
      const mockResult = {
        output: {
          response: 'Test response',
          metadata: {
            session_id: 'test-session',
            context_doc_ids: ['doc1', 'doc2'],
            conversation_entries: 5,
            semantic_search_average_score: 0.85,
            confidence_score: 7,
            confidence_reason: 'High confidence',
          },
          metrics: {
            tokens_used: 1000,
            embedding_calls: 3,
            rows: 2,
            tool_used: 'traditional-rag',
          },
        },
      };

      const result = formatNonStreamingResponse(mockResult, 1500, 'ask_ai', 'req-123');

      sst.equal(result.success, true);
      sst.equal(result.result.type, 'complete');
      sst.equal(result.result.content, 'Test response');
      sst.equal(result.result.metadata.session_id, 'test-session');
      sst.equal(result.result.metadata.agent_name, 'ask_ai');
      sst.equal(result.result.metadata.request_id, 'req-123');
      sst.equal(result.result.metadata.semantic_search.results_count, 2);
      sst.equal(result.result.metadata.semantic_search.average_score, 0.85);
      sst.same(result.result.metadata.semantic_search.context_doc_ids, ['doc1', 'doc2']);
      sst.equal(result.result.metadata.semantic_search.executor, 'traditional-rag');
      sst.equal(result.result.metadata.confidence?.score, 8);
      sst.equal(result.result.metadata.confidence?.reason, 'High confidence');
      sst.equal(result.result.metadata.performance.execution_time_ms, 1500);
      sst.equal(result.result.metadata.performance.tokens_used, 1000);
      sst.equal(result.result.metadata.performance.embedding_calls, 3);
      sst.equal(result.result.metadata.performance.conversation_entries, 5);
      sst.end();
    });

    st.test('should handle missing optional fields gracefully', async sst => {
      const mockResult = {
        output: {
          response: 'Simple response',
        },
      };

      const result = formatNonStreamingResponse(mockResult, 500, 'echo', 'req-456');

      sst.equal(result.success, true);
      sst.equal(result.result.content, 'Simple response');
      sst.equal(result.result.metadata.session_id, null);
      sst.equal(result.result.metadata.confidence, null);
      sst.equal(result.result.metadata.semantic_search.results_count, 0);
      sst.equal(result.result.metadata.performance.total_chunks, 0);
      sst.end();
    });

    st.test('formatNonStreamingResponse preserves content formatting', sst => {
      const mockResult = {
        output: {
          response: {
            text: '# Markdown Header\n\nThis is **bold** text with `code` and JSON:\n```json\n{"key": "value"}\n```',
          },
        },
        metadata: {
          session_id: 'ses-123',
          context_doc_ids: ['doc1', 'doc2'],
          conversation_entries: 2,
          semantic_search_average_score: 0.85,
        },
        metrics: {
          tokens_used: 150,
          embedding_calls: 3,
          rows: 5,
          tool_used: 'langgraph_workflow',
        },
      };

      const result = formatNonStreamingResponse(mockResult, 1500, 'ask_ai', 'req-123');

      sst.equal(result.success, true);
      sst.equal(result.result.type, 'complete');
      sst.equal(
        result.result.content,
        '# Markdown Header\n\nThis is **bold** text with `code` and JSON:\n```json\n{"key": "value"}\n```'
      );
      sst.equal(result.result.metadata.session_id, 'ses-123');
      sst.equal(result.result.metadata.agent_name, 'ask_ai');
      sst.equal(result.result.metadata.request_id, 'req-123');
      sst.end();
    });

    st.test('formatNonStreamingResponse handles complex nested content objects', sst => {
      const mockResult = {
        output: {
          response: {
            content: {
              message:
                '## Complex Response\n\nWith **formatting** and structure:\n- Item 1\n- Item 2',
            },
          },
          metadata: {},
          metrics: {},
        },
      };

      const result = formatNonStreamingResponse(mockResult, 1000, 'test_agent', 'req-456');

      sst.equal(result.success, true);
      sst.equal(
        result.result.content,
        '## Complex Response\n\nWith **formatting** and structure:\n- Item 1\n- Item 2'
      );
      sst.end();
    });

    st.test('formatNonStreamingResponse handles array content', sst => {
      const mockResult = {
        output: {
          response: [{ text: 'First part of response' }, { text: 'Second part with **markdown**' }],
          metadata: {},
          metrics: {},
        },
      };

      const result = formatNonStreamingResponse(mockResult, 800, 'array_agent', 'req-789');

      sst.equal(result.success, true);
      sst.equal(result.result.content, 'First part of response');
      sst.end();
    });

    st.end();
  });

  t.test('formatStreamingCompletionResponse', async st => {
    st.test('should format streaming completion data correctly', async sst => {
      const mockData = {
        fullResponse: 'Streaming response',
        searchResults: {
          resultsCount: 3,
          averageScore: 0.92,
          relevanceQuality: 'Excellent',
        },
        confidenceData: {
          confidenceScore: 9,
          confidenceReason: 'High relevance documents found',
        },
        finalMetadata: {
          metadata: {
            session_id: 'stream-session',
            context_doc_ids: ['doc3', 'doc4', 'doc5'],
            conversation_entries: 2,
          },
          metrics: {
            tokens_used: 2000,
            embedding_calls: 5,
          },
        },
        executionTimeMs: 2500,
        totalChunks: 15,
        averageChunkTimeMs: 166.67,
        agentName: 'ask_ai',
        requestId: 'req-stream',
        hasReceivedChunks: true,
      };

      const result = formatStreamingCompletionResponse(mockData);

      sst.equal(result.type, 'complete');
      sst.equal(result.content, 'Streaming response');
      sst.equal(result.metadata.session_id, 'stream-session');
      sst.equal(result.metadata.agent_name, 'ask_ai');
      sst.equal(result.metadata.request_id, 'req-stream');
      sst.equal(result.metadata.semantic_search.results_count, 3);
      sst.equal(result.metadata.semantic_search.average_score, 0.92);
      sst.equal(result.metadata.semantic_search.relevance_quality, 'Excellent');
      sst.same(result.metadata.semantic_search.context_doc_ids, ['doc3', 'doc4', 'doc5']);
      sst.equal(result.metadata.confidence?.score, 9);
      sst.equal(result.metadata.confidence?.reason, 'High relevance documents found');
      sst.equal(result.metadata.performance.execution_time_ms, 2500);
      sst.equal(result.metadata.performance.total_chunks, 15);
      sst.equal(result.metadata.performance.average_chunk_time_ms, 166.67);
      sst.equal(result.metadata.performance.tokens_used, 2000);
      sst.equal(result.metadata.performance.embedding_calls, 5);
      sst.equal(result.metadata.performance.conversation_entries, 2);
      sst.end();
    });

    st.test('formatStreamingCompletionResponse preserves content formatting', sst => {
      const mockData = {
        fullResponse:
          '## Streaming Response\n\nThis is a **formatted** response with:\n- Bullet points\n- `Code snippets`\n- JSON data:\n```json\n{"status": "success"}\n```',
        searchResults: {
          resultsCount: 3,
          averageScore: 0.85,
          relevanceQuality: 'Excellent',
        },
        confidenceData: {
          confidenceScore: 9,
          confidenceReason: 'High confidence in response quality',
        },
        finalMetadata: {
          metadata: {
            session_id: 'ses-stream-123',
            context_doc_ids: ['doc1', 'doc2', 'doc3'],
            conversation_entries: 1,
          },
          metrics: {
            tokens_used: 200,
            embedding_calls: 2,
          },
        },
        executionTimeMs: 2500,
        totalChunks: 15,
        averageChunkTimeMs: 166.67,
        agentName: 'streaming_agent',
        requestId: 'req-stream-456',
        hasReceivedChunks: true,
      };

      const result = formatStreamingCompletionResponse(mockData);

      sst.equal(result.type, 'complete');
      sst.equal(
        result.content,
        '## Streaming Response\n\nThis is a **formatted** response with:\n- Bullet points\n- `Code snippets`\n- JSON data:\n```json\n{"status": "success"}\n```'
      );
      sst.equal(result.metadata.session_id, 'ses-stream-123');
      sst.equal(result.metadata.agent_name, 'streaming_agent');
      sst.equal(result.metadata.request_id, 'req-stream-456');
      sst.equal(result.metadata.semantic_search.results_count, 3);
      sst.equal(result.metadata.confidence?.score, 9);
      sst.equal(result.metadata.performance.total_chunks, 15);
      sst.end();
    });

    st.end();
  });

  t.test('formatErrorResponse', async st => {
    st.test('should format error response correctly', async sst => {
      const mockError = new Error('Test error message');
      const result = formatErrorResponse(mockError, 1000, 'ask_ai', 'req-error', 5);

      sst.equal(result.type, 'complete');
      sst.equal(result.content, '');
      sst.equal(result.metadata.session_id, null);
      sst.equal(result.metadata.agent_name, 'ask_ai');
      sst.equal(result.metadata.request_id, 'req-error');
      sst.equal(result.metadata.error, true);
      sst.equal(result.metadata.error_message, 'Test error message');
      sst.equal(result.metadata.semantic_search.results_count, 0);
      sst.equal(result.metadata.confidence, null);
      sst.equal(result.metadata.performance.execution_time_ms, 1000);
      sst.equal(result.metadata.performance.total_chunks, 5);
      sst.end();
    });

    st.end();
  });

  t.end();
});
