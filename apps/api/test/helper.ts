// This file contains code that we reuse between our tests.
const helper = require('fastify-cli/helper.js');
import * as path from 'path';
import * as tap from 'tap';
import { FastifyInstance } from 'fastify';

export type Test = (typeof tap)['Test']['prototype'];

const AppPath = path.join(__dirname, '..', 'src', 'app.ts');

// Fill in this config with all the configurations
// needed for testing the application
async function config() {
  return {};
}

// Define options for app building
interface BuildOptions {
  onBuild?: (fastify: FastifyInstance) => void;
}

// Automatically build and tear down our instance
async function build(t: Test, options: BuildOptions = {}) {
  // you can set all the options supported by the fastify CLI command
  const argv = [AppPath];

  // fastify-plugin ensures that all decorators
  // are exposed for testing purposes, this is
  // different from the production setup
  const app = await helper.build(argv, await config());

  // Allow for custom configuration of the app
  if (options.onBuild) {
    options.onBuild(app);
  }

  // Tear down our app after we are done
  t.teardown(() => void app.close());

  return app;
}

export { config, build };
