// API Response Handling Examples
// TypeScript implementation for handling AI Agent API responses

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface AgentInvokeRequest {
  agent_name: string;
  input: any;
  context?: {
    sessionId?: string;
    organizationId?: string;
    userId?: string;
    metadata?: Record<string, any>;
  };
}

interface AgentInvokeResponse {
  success: boolean;
  result: {
    response: string;
    metadata: ResponseMetadata;
  };
  metadata: {
    agent_name: string;
    original_agent_name: string;
    execution_time: number;
    timestamp: string;
  };
}

interface ResponseMetadata {
  session_id: string;
  context_doc_ids: string[];
  conversation_entries: number;
  semantic_search_average_score: number;
  confidence_score?: number;
  confidence_reason?: string;
  tokens_used: number;
  embedding_calls: number;
  execution_time_ms: number;
  search_results?: SearchResult[];
  final_metadata?: Record<string, any>;
}

interface SearchResult {
  documentId: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
}

interface StreamChunk {
  type: 'connection_established' | 'content' | 'metadata' | 'complete' | 'error';
  content: string;
  metadata?: {
    chunkIndex?: number;
    elapsedMs?: number;
    searchResults?: SearchResult[];
    confidence?: ConfidenceData;
    finalResult?: Record<string, any>;
    error?: boolean;
    executionTimeMs?: number;
    totalChunks?: number;
  };
  timestamp: string;
}

interface ConfidenceData {
  score: number;
  reason: string;
  isHighConfidence: boolean;
}

// ============================================================================
// SINGLE-SHOT RESPONSE HANDLING
// ============================================================================

class SingleShotResponseHandler {
  /**
   * Handle a successful single-shot response
   */
  handleSuccess(response: AgentInvokeResponse): ProcessedResponse {
    const { result, metadata } = response;

    // Extract and validate confidence
    const confidence = this.processConfidence(result.metadata);

    // Process search results if available
    const searchResults = this.processSearchResults(result.metadata);

    // Build processed response
    const processed: ProcessedResponse = {
      content: result.response,
      confidence,
      searchResults,
      performance: {
        executionTime: metadata.execution_time,
        tokensUsed: result.metadata.tokens_used,
        embeddingCalls: result.metadata.embedding_calls,
      },
      context: {
        sessionId: result.metadata.session_id,
        documentIds: result.metadata.context_doc_ids,
        conversationEntries: result.metadata.conversation_entries,
      },
      metadata: result.metadata.final_metadata || {},
    };

    return processed;
  }

  /**
   * Process confidence data from response
   */
  private processConfidence(metadata: ResponseMetadata): ConfidenceInfo {
    const score = metadata.confidence_score || 0;
    const reason = metadata.confidence_reason || 'No confidence data available';

    return {
      score,
      reason,
      isHighConfidence: score >= 0.7,
      isLowConfidence: score < 0.5,
      needsReview: score < 0.3,
    };
  }

  /**
   * Process search results from response
   */
  private processSearchResults(metadata: ResponseMetadata): ProcessedSearchResults {
    const results = metadata.search_results || [];
    const averageScore = metadata.semantic_search_average_score;

    return {
      documents: results.map(result => ({
        id: result.documentId,
        content: result.content,
        relevance: result.score,
        metadata: result.metadata || {},
      })),
      averageRelevance: averageScore,
      quality: this.assessSearchQuality(averageScore),
      count: results.length,
    };
  }

  /**
   * Assess search quality based on average score
   */
  private assessSearchQuality(averageScore: number): 'high' | 'medium' | 'low' {
    if (averageScore >= 0.8) return 'high';
    if (averageScore >= 0.6) return 'medium';
    return 'low';
  }
}

// ============================================================================
// STREAMING RESPONSE HANDLING
// ============================================================================

class StreamingResponseHandler {
  private fullResponse = '';
  private searchResults: SearchResult[] = [];
  private confidenceData: ConfidenceData | null = null;
  private metadata: Record<string, any> = {};
  private chunkCount = 0;
  private startTime = Date.now();

  /**
   * Process streaming chunks and build complete response
   */
  async processStream(stream: AsyncIterable<StreamChunk>): Promise<ProcessedStreamingResponse> {
    for await (const chunk of stream) {
      this.processChunk(chunk);
    }

    return this.buildFinalResponse();
  }

  /**
   * Process individual streaming chunk
   */
  private processChunk(chunk: StreamChunk): void {
    this.chunkCount++;

    switch (chunk.type) {
      case 'connection_established':
        this.handleConnectionEstablished(chunk);
        break;

      case 'content':
        this.handleContentChunk(chunk);
        break;

      case 'metadata':
        this.handleMetadataChunk(chunk);
        break;

      case 'complete':
        this.handleCompletionChunk(chunk);
        break;

      case 'error':
        this.handleErrorChunk(chunk);
        break;

      default:
        console.warn(`Unknown chunk type: ${chunk.type}`);
    }
  }

  /**
   * Handle connection established event
   */
  private handleConnectionEstablished(chunk: StreamChunk): void {
    console.log('Streaming connection established:', {
      timestamp: chunk.timestamp,
      metadata: chunk.metadata,
    });
  }

  /**
   * Handle content chunk
   */
  private handleContentChunk(chunk: StreamChunk): void {
    this.fullResponse += chunk.content;

    // Emit progress event if needed
    this.emitProgress({
      type: 'content',
      content: chunk.content,
      fullResponse: this.fullResponse,
      chunkIndex: this.chunkCount,
      elapsedMs: Date.now() - this.startTime,
    });
  }

  /**
   * Handle metadata chunk
   */
  private handleMetadataChunk(chunk: StreamChunk): void {
    if (chunk.metadata) {
      if (chunk.metadata.searchResults) {
        this.searchResults = chunk.metadata.searchResults;
      }

      if (chunk.metadata.confidence) {
        this.confidenceData = chunk.metadata.confidence;
      }

      if (chunk.metadata.finalResult) {
        this.metadata = { ...this.metadata, ...chunk.metadata.finalResult };
      }
    }

    // Emit metadata event
    this.emitProgress({
      type: 'metadata',
      searchResults: this.searchResults,
      confidence: this.confidenceData,
      metadata: this.metadata,
      chunkIndex: this.chunkCount,
      elapsedMs: Date.now() - this.startTime,
    });
  }

  /**
   * Handle completion chunk
   */
  private handleCompletionChunk(chunk: StreamChunk): void {
    const finalMetadata = chunk.metadata || {};
    const executionTime = finalMetadata.executionTimeMs || Date.now() - this.startTime;

    console.log('Streaming completed:', {
      totalChunks: this.chunkCount,
      executionTime,
      finalMetadata,
    });
  }

  /**
   * Handle error chunk
   */
  private handleErrorChunk(chunk: StreamChunk): void {
    const error = {
      message: chunk.content,
      code: chunk.metadata?.errorCode || 'UNKNOWN_ERROR',
      timestamp: chunk.timestamp,
      chunkIndex: this.chunkCount,
    };

    console.error('Streaming error:', error);
    throw new StreamingError(error.message, error.code);
  }

  /**
   * Build final streaming response
   */
  private buildFinalResponse(): ProcessedStreamingResponse {
    const executionTime = Date.now() - this.startTime;

    return {
      content: this.fullResponse,
      confidence: this.processConfidence(),
      searchResults: this.processSearchResults(),
      performance: {
        executionTime,
        totalChunks: this.chunkCount,
        averageChunkTime: this.chunkCount > 0 ? executionTime / this.chunkCount : 0,
      },
      metadata: this.metadata,
    };
  }

  /**
   * Process confidence from streaming data
   */
  private processConfidence(): ConfidenceInfo {
    if (!this.confidenceData) {
      return {
        score: 0,
        reason: 'No confidence data available',
        isHighConfidence: false,
        isLowConfidence: true,
        needsReview: true,
      };
    }

    return {
      score: this.confidenceData.score,
      reason: this.confidenceData.reason,
      isHighConfidence: this.confidenceData.isHighConfidence,
      isLowConfidence: !this.confidenceData.isHighConfidence,
      needsReview: this.confidenceData.score < 0.3,
    };
  }

  /**
   * Process search results from streaming data
   */
  private processSearchResults(): ProcessedSearchResults {
    return {
      documents: this.searchResults.map(result => ({
        id: result.documentId,
        content: result.content,
        relevance: result.score,
        metadata: result.metadata || {},
      })),
      averageRelevance: this.calculateAverageRelevance(),
      quality: this.assessSearchQuality(),
      count: this.searchResults.length,
    };
  }

  /**
   * Calculate average relevance from search results
   */
  private calculateAverageRelevance(): number {
    if (this.searchResults.length === 0) return 0;

    const total = this.searchResults.reduce((sum, result) => sum + result.score, 0);
    return total / this.searchResults.length;
  }

  /**
   * Assess search quality based on results
   */
  private assessSearchQuality(): 'high' | 'medium' | 'low' {
    const averageRelevance = this.calculateAverageRelevance();

    if (averageRelevance >= 0.8) return 'high';
    if (averageRelevance >= 0.6) return 'medium';
    return 'low';
  }

  /**
   * Emit progress event (implement based on your event system)
   */
  private emitProgress(progress: StreamingProgress): void {
    // Implement based on your event system (EventEmitter, callback, etc.)
    console.log('Streaming progress:', progress);
  }
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

class APIErrorHandler {
  /**
   * Handle API errors with proper error types
   */
  handleError(error: any): never {
    if (error instanceof APIError) {
      switch (error.code) {
        case 'API_KEY_INVALID':
          throw new AuthenticationError('Invalid API key provided');
        case 'RATE_LIMIT_EXCEEDED':
          throw new RateLimitError('Rate limit exceeded, please try again later');
        case 'AGENT_NOT_FOUND':
          throw new NotFoundError(`Agent not found: ${error.message}`);
        case 'STREAMING_NOT_SUPPORTED':
          throw new NotSupportedError('This agent does not support streaming');
        default:
          throw new APIError(error.message, error.code, error.statusCode);
      }
    }

    if (error instanceof StreamingError) {
      throw new StreamingError(error.message, error.code);
    }

    if (error.name === 'AbortError') {
      throw new TimeoutError('Request timed out');
    }

    throw new NetworkError('Network error occurred', error);
  }

  /**
   * Handle low confidence responses
   */
  handleLowConfidence(response: ProcessedResponse): LowConfidenceResponse {
    return {
      originalResponse: response,
      warning: 'This response has low confidence and may not be accurate',
      confidenceScore: response.confidence.score,
      confidenceReason: response.confidence.reason,
      recommendations: this.generateRecommendations(response),
    };
  }

  /**
   * Generate recommendations for low confidence responses
   */
  private generateRecommendations(response: ProcessedResponse): string[] {
    const recommendations: string[] = [];

    if (response.confidence.score < 0.3) {
      recommendations.push('Consider rephrasing your question');
      recommendations.push('Provide more specific context');
    }

    if (response.searchResults.quality === 'low') {
      recommendations.push('No relevant documents found - consider uploading more content');
    }

    if (response.searchResults.count === 0) {
      recommendations.push('No supporting documents available for this query');
    }

    return recommendations;
  }
}

// ============================================================================
// USAGE EXAMPLES
// ============================================================================

class AIAgentClient {
  private singleShotHandler = new SingleShotResponseHandler();
  private streamingHandler = new StreamingResponseHandler();
  private errorHandler = new APIErrorHandler();

  /**
   * Example: Handle single-shot response
   */
  async handleSingleShotResponse(response: AgentInvokeResponse): Promise<void> {
    try {
      const processed = this.singleShotHandler.handleSuccess(response);

      // Check confidence
      if (processed.confidence.isLowConfidence) {
        const lowConfidence = this.errorHandler.handleLowConfidence(processed);
        console.warn('Low confidence response:', lowConfidence);
      }

      // Display response
      console.log('Response:', processed.content);
      console.log('Confidence:', processed.confidence.score);
      console.log('Search quality:', processed.searchResults.quality);
    } catch (error) {
      this.errorHandler.handleError(error);
    }
  }

  /**
   * Example: Handle streaming response
   */
  async handleStreamingResponse(stream: AsyncIterable<StreamChunk>): Promise<void> {
    try {
      const processed = await this.streamingHandler.processStream(stream);

      // Check confidence
      if (processed.confidence.isLowConfidence) {
        const lowConfidence = this.errorHandler.handleLowConfidence(processed);
        console.warn('Low confidence streaming response:', lowConfidence);
      }

      // Display final response
      console.log('Final response:', processed.content);
      console.log('Total chunks:', processed.performance.totalChunks);
      console.log('Execution time:', processed.performance.executionTime);
    } catch (error) {
      this.errorHandler.handleError(error);
    }
  }

  /**
   * Example: Real-time streaming with progress updates
   */
  async handleRealTimeStreaming(stream: AsyncIterable<StreamChunk>): Promise<void> {
    let currentResponse = '';
    let searchResults: SearchResult[] = [];
    let confidence: ConfidenceData | null = null;

    try {
      for await (const chunk of stream) {
        switch (chunk.type) {
          case 'content':
            currentResponse += chunk.content;
            // Update UI with current response
            this.updateUI(currentResponse);
            break;

          case 'metadata':
            if (chunk.metadata?.searchResults) {
              searchResults = chunk.metadata.searchResults;
            }
            if (chunk.metadata?.confidence) {
              confidence = chunk.metadata.confidence;
            }
            // Update UI with metadata
            this.updateMetadata(searchResults, confidence);
            break;

          case 'complete':
            // Finalize UI
            this.finalizeUI(currentResponse, searchResults, confidence);
            break;

          case 'error':
            this.handleStreamingError(chunk);
            break;
        }
      }
    } catch (error) {
      this.errorHandler.handleError(error);
    }
  }

  // UI update methods (implement based on your UI framework)
  private updateUI(content: string): void {
    // Update your UI with current content
    console.log('UI Update:', content);
  }

  private updateMetadata(searchResults: SearchResult[], confidence: ConfidenceData | null): void {
    // Update your UI with metadata
    console.log('Metadata Update:', { searchResults, confidence });
  }

  private finalizeUI(
    content: string,
    searchResults: SearchResult[],
    confidence: ConfidenceData | null
  ): void {
    // Finalize your UI
    console.log('Finalize UI:', { content, searchResults, confidence });
  }

  private handleStreamingError(chunk: StreamChunk): void {
    console.error('Streaming error:', chunk.content);
    // Update UI to show error
  }
}

// ============================================================================
// TYPE DEFINITIONS FOR PROCESSED RESPONSES
// ============================================================================

interface ProcessedResponse {
  content: string;
  confidence: ConfidenceInfo;
  searchResults: ProcessedSearchResults;
  performance: PerformanceMetrics;
  context: ResponseContext;
  metadata: Record<string, any>;
}

interface ProcessedStreamingResponse {
  content: string;
  confidence: ConfidenceInfo;
  searchResults: ProcessedSearchResults;
  performance: StreamingPerformanceMetrics;
  metadata: Record<string, any>;
}

interface ConfidenceInfo {
  score: number;
  reason: string;
  isHighConfidence: boolean;
  isLowConfidence: boolean;
  needsReview: boolean;
}

interface ProcessedSearchResults {
  documents: ProcessedDocument[];
  averageRelevance: number;
  quality: 'high' | 'medium' | 'low';
  count: number;
}

interface ProcessedDocument {
  id: string;
  content: string;
  relevance: number;
  metadata: Record<string, any>;
}

interface PerformanceMetrics {
  executionTime: number;
  tokensUsed: number;
  embeddingCalls: number;
}

interface StreamingPerformanceMetrics {
  executionTime: number;
  totalChunks: number;
  averageChunkTime: number;
}

interface ResponseContext {
  sessionId: string;
  documentIds: string[];
  conversationEntries: number;
}

interface StreamingProgress {
  type: 'content' | 'metadata';
  content?: string;
  fullResponse?: string;
  searchResults?: SearchResult[];
  confidence?: ConfidenceData;
  metadata?: Record<string, any>;
  chunkIndex: number;
  elapsedMs: number;
}

interface LowConfidenceResponse {
  originalResponse: ProcessedResponse;
  warning: string;
  confidenceScore: number;
  confidenceReason: string;
  recommendations: string[];
}

// ============================================================================
// ERROR CLASSES
// ============================================================================

class APIError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number
  ) {
    super(message);
    this.name = 'APIError';
  }
}

class StreamingError extends Error {
  constructor(
    message: string,
    public code: string
  ) {
    super(message);
    this.name = 'StreamingError';
  }
}

class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

class RateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RateLimitError';
  }
}

class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

class NotSupportedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotSupportedError';
  }
}

class TimeoutError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TimeoutError';
  }
}

class NetworkError extends Error {
  constructor(
    message: string,
    public originalError: Error
  ) {
    super(message);
    this.name = 'NetworkError';
  }
}

// ============================================================================
// EXPORT STATEMENTS
// ============================================================================

export {
  // Types
  AgentInvokeRequest,
  AgentInvokeResponse,
  ResponseMetadata,
  SearchResult,
  StreamChunk,
  ConfidenceData,
  ProcessedResponse,
  ProcessedStreamingResponse,
  ConfidenceInfo,
  ProcessedSearchResults,
  ProcessedDocument,
  PerformanceMetrics,
  StreamingPerformanceMetrics,
  ResponseContext,
  StreamingProgress,
  LowConfidenceResponse,

  // Classes
  SingleShotResponseHandler,
  StreamingResponseHandler,
  APIErrorHandler,
  AIAgentClient,

  // Error Classes
  APIError,
  StreamingError,
  AuthenticationError,
  RateLimitError,
  NotFoundError,
  NotSupportedError,
  TimeoutError,
  NetworkError,
};
