BASE_URL="http://localhost:8000"

# DATABASE PREVIEW/STAGING
DATABASE_URL_ROOT_USER="your_database_root_user_connection_string"
DATABASE_URL_RLS_USER="your_database_rls_user_connection_string"
DATABASE_URL_INTERNAL="your_database_internal_connection_string"
INTERNAL_DB_HAS_RLS=false

# AUTH
JWT_SECRET="your_jwt_secret_key"
REFRESH_TOKEN_SECRET="your_refresh_token_secret_key"
TOKEN_EXPIRY="15m"
REFRESH_TOKEN_EXPIRY="7d"
INTERNAL_API_SECRET="your_internal_api_secret"

# CORS
CORS_ORIGINS="*"

# MCP Configuration
MCP_ENABLED=true
MCP_SESSION_TTL=1800
MCP_CONTEXT_TTL=3600
MCP_MAX_CONTEXT_SIZE=10485760
MCP_MAX_SESSIONS_PER_ORG=5
MCP_CLEANUP_INTERVAL=300000
MCP_MAX_RETRY_ATTEMPTS=3
MCP_HEALTH_CHECK_INTERVAL=120000

# LangChain Configuration
LANGCHAIN_ENABLED=true
LANGCHAIN_MODEL=gpt-4o-mini
LANGCHAIN_EMBEDDINGS=text-embedding-3-large
LANGCHAIN_TEMPERATURE=0.7
LANGCHAIN_MAX_TOKENS=1000
LANGCHAIN_MAX_RETRIES=3
LANGCHAIN_REQUEST_TIMEOUT=30000
LANGCHAIN_RATE_LIMIT_RPM=60
LANGCHAIN_TRACING=false

# OpenAI
OPENAI_API_KEY="your_openai_api_key"
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_CHAT_INSTRUCTIONS="You are a helpful AI assistant specialized in information security and cybersecurity..."
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=1000

# Shared Infrastructure
REDIS_URL=redis://localhost:6379
SKIP_REDIS=false
PUBLIC_WS_BASE=ws://localhost:8000

# LANGGRAPH
ENABLE_LANGGRAPH=false

# LANGFUSE
LANGFUSE_PUBLIC_KEY="your_langfuse_public_key"
LANGFUSE_SECRET_KEY="your_langfuse_secret_key"
LANGFUSE_BASEURL="https://us.cloud.langfuse.com"
LANGFUSE_SAMPLE_RATE="1"
LANGFUSE_TRACING_ENABLED="true"
LANGFUSE_DEFAULT_PROMPT="system-default"

# LOGGING
LOG_LEVEL="debug"
