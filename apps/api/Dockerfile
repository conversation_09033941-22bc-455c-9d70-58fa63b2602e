FROM node:22-alpine AS builder

WORKDIR /app

# Enable pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy package files and source code
# Ignore files using .dockerignore
COPY . .

# Install dependencies
RUN pnpm install --no-frozen-lockfile --shamefully-hoist

# Build the application
RUN pnpm --filter @anter/shared-services build \
    && pnpm --filter @anter/domain-model build \
    && pnpm --filter @anter/mcp-tools build \
    && pnpm --filter @anter/agents build \
    && pnpm --filter @anter/api build





FROM node:22-alpine AS runtime

WORKDIR /app

# Enable pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# # Copy package files
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
COPY --from=builder /app/apps/api/package.json ./apps/api/
COPY --from=builder /app/apps/agents/package.json ./apps/agents/
COPY --from=builder /app/packages/shared-services/package.json ./packages/shared-services/
COPY --from=builder /app/packages/domain-model/package.json ./packages/domain-model/
COPY --from=builder /app/packages/mcp-tools/package.json ./packages/mcp-tools/

# Copy built files
COPY --from=builder /app/apps/api/dist ./apps/api/dist
COPY --from=builder /app/apps/agents/dist ./apps/agents/dist
COPY --from=builder /app/packages/shared-services/dist ./packages/shared-services/dist
COPY --from=builder /app/packages/domain-model/dist ./packages/domain-model/dist
COPY --from=builder /app/packages/mcp-tools/dist ./packages/mcp-tools/dist

# Install dependencies
RUN pnpm install --no-frozen-lockfile --shamefully-hoist

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S anter -u 1001

# Change ownership of the app directory
RUN chown -R anter:nodejs /app
USER anter

# Set environment variables
ENV NODE_ENV=production
ENV USE_HTTPS=false
ENV HOST=0.0.0.0
ENV PORT=8000

# Expose port
EXPOSE 8000

# Start the application
CMD ["node", "apps/api/dist/api/src/server.js"]
# CMD ["/bin/sh"]
