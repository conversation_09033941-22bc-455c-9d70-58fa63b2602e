{"info": {"_postman_id": "6fbe3439-660a-49ce-99a4-fa989fb8ffdf", "name": "askinfosec-api", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12187750"}, "item": [{"name": "Create Docs Embeddings", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"org_id\": \"cls01l2w50007flpihm5gag62\",\n    \"file_id\": \"cls01n3nb000nflpip81rk8sd\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/jobs/embeddings/docs", "host": ["{{url}}"], "path": ["jobs", "embeddings", "docs"]}}, "response": []}, {"name": "Create KB Embeddings", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"org_id\": \"cls01l2w50007flpihm5gag62\",\n    \"id\": \"cls0339z0000814cxv6emzy9p\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/jobs/embeddings/kb", "host": ["{{url}}"], "path": ["jobs", "embeddings", "kb"]}}, "response": []}, {"name": "Cha<PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"question\": \"Give me a list of risk factors based on the documents\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/chat", "host": ["{{url}}"], "path": ["chat"]}}, "response": []}], "variable": [{"key": "url", "value": "http://localhost:8000", "type": "string"}]}