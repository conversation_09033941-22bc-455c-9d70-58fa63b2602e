import fp from 'fastify-plugin';
import { FastifyInstance } from 'fastify';
import { AgentFactory } from '@anter/agents';
import { BaseAgent } from '@anter/agents';

declare module 'fastify' {
  interface FastifyInstance {
    agents: {
      factory: typeof AgentFactory;
      invoke: (agentName: string, input: any, context?: any) => Promise<any>;
      create: (agentName: string, capabilities?: string[]) => Promise<any>;
      metrics: {
        getAllMetrics: () => any;
        getMetrics: (agentName: string) => any;
      };
    };
  }
}

/**
 * Persistent agent pool to maintain memory between requests
 */
class AgentPool {
  private agents = new Map<string, BaseAgent>();
  private lastUsed = new Map<string, number>();
  private readonly maxIdleTime = 12 * 60 * 60 * 1000; // 12 hours
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify;
    // Cleanup idle agents every 12 hours
    setInterval(() => this.cleanupIdleAgents(fastify), 12 * 60 * 60 * 1000);
  }

  async getAgent(agentName: string): Promise<BaseAgent> {
    const existingAgent = this.agents.get(agentName);

    if (existingAgent) {
      this.lastUsed.set(agentName, Date.now());
      return existingAgent;
    }

    // Create new agent instance
    try {
      this.fastify.log.info(`[AgentPool] Creating new agent: ${agentName}`);

      // Create proper dependencies object with real database providers and Redis
      const dependencies = {
        logger: this.fastify.log,
        database: {
          log: this.fastify.log,
          dbWithTenant: async (orgId: string) => {
            return this.fastify.dbWithTenant(orgId);
          },
          dbInternalWithTenant: async (orgId: string) => {
            return this.fastify.dbInternalWithTenant(orgId);
          },
          redis: this.fastify.redis,
        },
      };

      // Create agent with Redis-backed memory if available
      const agent = await AgentFactory.create(
        agentName,
        {
          name: agentName,
          version: '0.0.1',
          archetype: 'reactive' as any,
          capabilities: ['chat', 'memory'] as any,
        },
        dependencies
      );

      this.agents.set(agentName, agent);
      this.lastUsed.set(agentName, Date.now());
      this.fastify.log.info(`[AgentPool] Successfully created agent: ${agentName}`);

      return agent;
    } catch (error) {
      console.error(`[AgentPool] Failed to create agent ${agentName}:`, error);
      throw error;
    }
  }

  private async cleanupIdleAgents(fastify: FastifyInstance): Promise<void> {
    const now = Date.now();
    const agentsToRemove: string[] = [];

    for (const [agentName, lastUsedTime] of this.lastUsed.entries()) {
      if (now - lastUsedTime > this.maxIdleTime) {
        agentsToRemove.push(agentName);
      }
    }

    for (const agentName of agentsToRemove) {
      const agent = this.agents.get(agentName);
      if (agent) {
        await agent.destroy();
        this.agents.delete(agentName);
        this.lastUsed.delete(agentName);
      }
    }

    if (agentsToRemove.length > 0) {
      fastify.log.info(`[AgentPool] Cleaned up ${agentsToRemove.length} idle agents`);
    }
  }

  async shutdown(): Promise<void> {
    for (const [, agent] of this.agents.entries()) {
      await agent.destroy();
    }
    this.agents.clear();
    this.lastUsed.clear();
  }
}

/**
 * Plugin to integrate AI agents with the main API
 */
async function agents(fastify: FastifyInstance, opts: any) {
  try {
    // Validate required environment variables
    const requiredEnvVars = ['OPENAI_API_KEY', 'DATABASE_URL_RLS_USER'];
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingEnvVars.length > 0) {
      fastify.log.warn(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
      fastify.log.warn(
        'Agent functionality may be limited. Please set the missing environment variables.'
      );
    }

    // Initialize agent system
    const { initializeAgentSystem } = await import('@anter/agents');
    initializeAgentSystem(fastify);
  } catch (error) {
    fastify.log.error('Failed to initialize agent system:', error);
    throw error;
  }

  // Create persistent agent pool
  const agentPool = new AgentPool(fastify);

  // Cleanup agents on server shutdown
  fastify.addHook('onClose', async () => {
    await agentPool.shutdown();
  });

  // Helper function to invoke an agent
  const invokeAgent = async (agentName: string, input: any, context?: any): Promise<any> => {
    try {
      fastify.log.info(`[invokeAgent] Starting invocation for agent: ${agentName}`);

      // Get persistent agent from pool (or create new one)
      const agent = await agentPool.getAgent(agentName);
      fastify.log.info(`[invokeAgent] Got agent from pool: ${agentName}`);

      // Transform API input format to agent input format
      const agentInput = {
        input,
        sessionId: context?.sessionId,
        organizationId: context?.organizationId,
        userId: context?.userId,
        metadata: context?.metadata,
      };

      fastify.log.info(
        `[AgentPool] Dispatching agent "${agentName}" | sessionId=${agentInput.sessionId}`
      );

      fastify.log.info(`[invokeAgent] About to call agent.invoke for: ${agentName}`);
      const result = await agent.invoke(agentInput);
      fastify.log.info(`[invokeAgent] Successfully invoked agent: ${agentName}`);
      return result;
    } catch (error) {
      // Check for common environment variable issues
      if (error instanceof Error) {
        if (error.message.includes('OPENAI_API_KEY') || error.message.includes('api key')) {
          fastify.log.error(
            `Agent ${agentName} invocation failed: Missing OPENAI_API_KEY environment variable`,
            {
              agentName,
              error: error.message,
              solution: 'Please set the OPENAI_API_KEY environment variable',
            }
          );
          throw new Error(
            'Agent initialization failed: Missing OPENAI_API_KEY environment variable. Please check your environment configuration.'
          );
        }

        if (error.message.includes('DATABASE_URL') || error.message.includes('database')) {
          fastify.log.error(`Agent ${agentName} invocation failed: Database connection issue`, {
            agentName,
            error: error.message,
            solution: 'Please check your DATABASE_URL environment variables',
          });
          throw new Error(
            'Agent initialization failed: Database connection issue. Please check your database configuration.'
          );
        }
      }

      // Log error details in a more visible format
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      fastify.log.error(`Agent ${agentName} invocation failed: ${errorMessage}`);
      if (errorStack) {
        fastify.log.error(`Error stack: ${errorStack}`);
      }

      // Also log structured data for debugging
      fastify.log.error(`Agent ${agentName} invocation failed:`, {
        agentName,
        error: errorMessage,
        stack: errorStack,
        input:
          typeof input === 'string'
            ? input.substring(0, 200) + '...'
            : JSON.stringify(input).substring(0, 200) + '...',
        context: context ? JSON.stringify(context).substring(0, 200) + '...' : undefined,
      });
      throw error;
    }
    // Note: We DO NOT destroy the agent anymore - it stays in the pool
  };

  // Helper function to create an agent for streaming
  const createAgent = async (agentName: string, capabilities: string[] = ['chat']) => {
    // Create proper dependencies object with real database providers and Redis
    const dependencies = {
      logger: fastify.log,
      database: {
        log: fastify.log,
        dbWithTenant: async (orgId: string) => {
          return fastify.dbWithTenant(orgId);
        },
        dbInternalWithTenant: async (orgId: string) => {
          return fastify.dbInternalWithTenant(orgId);
        },
        redis: fastify.redis,
      },
    };

    return await AgentFactory.create(
      agentName,
      {
        name: agentName,
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: capabilities as any,
      },
      dependencies
    );
  };

  // Helper function to get agent metrics
  const getMetrics = () => {
    const types = AgentFactory.getRegisteredTypes();
    const metrics: any = {};

    for (const type of types) {
      // This would need to be implemented in the AgentFactory
      // For now, return empty metrics
      metrics[type] = {
        invocations: 0,
        errors: 0,
        avgLatency: 0,
        lastInvocation: null,
      };
    }

    return metrics;
  };

  // Register agent system with Fastify
  fastify.decorate('agents', {
    factory: AgentFactory,
    invoke: invokeAgent,
    create: createAgent,
    metrics: {
      getAllMetrics: getMetrics,
      getMetrics: (agentName: string) => getMetrics()[agentName] || {},
    },
  });

  // Note: Routes are handled by the internal API routes at /api/v1/internal/agents/
  // This plugin only provides the agent system functionality
}

export default fp(agents, {
  name: 'agents',
  dependencies: ['sensible'],
});
