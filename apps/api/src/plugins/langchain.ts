import fp from 'fastify-plugin';
import { createLangChainConfig } from '../core/langchain/config.js';

export default fp(async (fastify, opts) => {
  const config = createLangChainConfig(fastify.env);

  if (!config.enabled) {
    fastify.log.info('<PERSON><PERSON><PERSON><PERSON> is disabled');
    return;
  }

  // Register LangChain services when enabled
  fastify.decorate('langchain', {
    config,
    // Factories will be added here when we implement them
  });

  fastify.log.info('LangChain plugin registered successfully');
});

declare module 'fastify' {
  interface FastifyInstance {
    langchain?: {
      config: any; // Will be properly typed when implemented
    };
  }
}
