import fp from 'fastify-plugin';
import fastifyRedis from '@fastify/redis';

declare module 'fastify' {
  interface FastifyInstance {
    isRedisMocked?: boolean;
  }
}

// Mock Redis client for development when Redis is not available
const createMockRedis = (): any => ({
  get: async (key: string) => {
    console.warn(`[MOCK REDIS] GET ${key} - Redis not available, returning null`);
    return null;
  },
  set: async (key: string, value: string) => {
    console.warn(`[MOCK REDIS] SET ${key} - Redis not available, operation ignored`);
    return 'OK';
  },
  setex: async (key: string, ttl: number, value: string) => {
    console.warn(`[MOCK REDIS] SETEX ${key} ${ttl} - Redis not available, operation ignored`);
    return 'OK';
  },
  del: async (...keys: string[]) => {
    console.warn(`[MOCK REDIS] DEL ${keys.join(', ')} - Redis not available, operation ignored`);
    return 0;
  },
  exists: async (key: string) => {
    console.warn(`[MOCK REDIS] EXISTS ${key} - Redis not available, returning 0`);
    return 0;
  },
  keys: async (pattern: string) => {
    console.warn(`[MOCK REDIS] KEYS ${pattern} - Redis not available, returning empty array`);
    return [];
  },
  lpush: async (key: string, ...values: string[]) => {
    console.warn(`[MOCK REDIS] LPUSH ${key} - Redis not available, operation ignored`);
    return 0;
  },
  lrange: async (key: string, start: number, stop: number) => {
    console.warn(
      `[MOCK REDIS] LRANGE ${key} ${start} ${stop} - Redis not available, returning empty array`
    );
    return [];
  },
  ltrim: async (key: string, start: number, stop: number) => {
    console.warn(
      `[MOCK REDIS] LTRIM ${key} ${start} ${stop} - Redis not available, operation ignored`
    );
    return 'OK';
  },
  expire: async (key: string, seconds: number) => {
    console.warn(`[MOCK REDIS] EXPIRE ${key} ${seconds} - Redis not available, operation ignored`);
    return 1;
  },
  info: async () => {
    console.warn(`[MOCK REDIS] INFO - Redis not available, returning mock info`);
    return 'redis_version:mock\r\nconnected_clients:0\r\nused_memory:0\r\n';
  },
  ping: async () => {
    console.warn(`[MOCK REDIS] PING - Redis not available, returning PONG`);
    return 'PONG';
  },
  flushall: async () => {
    console.warn(`[MOCK REDIS] FLUSHALL - Redis not available, operation ignored`);
    return 'OK';
  },
  quit: async () => {
    console.warn(`[MOCK REDIS] QUIT - Redis not available, operation ignored`);
    return 'OK';
  },
  // Add missing methods for RedisEmbeddingIndex
  hset: async (key: string, field: string, value: string) => {
    console.warn(`[MOCK REDIS] HSET ${key} ${field} - Redis not available, operation ignored`);
    return 1;
  },
  hget: async (key: string, field: string) => {
    console.warn(`[MOCK REDIS] HGET ${key} ${field} - Redis not available, returning null`);
    return null;
  },
  hdel: async (key: string, field: string) => {
    console.warn(`[MOCK REDIS] HDEL ${key} ${field} - Redis not available, operation ignored`);
    return 0;
  },
  hgetall: async (key: string) => {
    console.warn(`[MOCK REDIS] HGETALL ${key} - Redis not available, returning empty object`);
    return {};
  },
  pipeline: () => ({
    hset: (key: string, field: string, value: string) => {
      console.warn(
        `[MOCK REDIS] PIPELINE HSET ${key} ${field} - Redis not available, operation ignored`
      );
      return { hset: () => {} };
    },
    expire: (key: string, seconds: number) => {
      console.warn(
        `[MOCK REDIS] PIPELINE EXPIRE ${key} ${seconds} - Redis not available, operation ignored`
      );
      return { expire: () => {} };
    },
    exec: async () => {
      console.warn(`[MOCK REDIS] PIPELINE EXEC - Redis not available, returning empty results`);
      return [];
    },
  }),
});

export default fp(async fastify => {
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
  const isDevMode = process.env.NODE_ENV !== 'production';

  // Skip Redis connection attempt in development if SKIP_REDIS is set
  if (isDevMode && process.env.SKIP_REDIS === 'true') {
    fastify.log.info('SKIP_REDIS is set, using mock Redis client');
    const mockRedis = createMockRedis();
    fastify.decorate('redis', mockRedis);
    fastify.decorate('isRedisMocked', true);
    return;
  }

  try {
    // Try to register the real Redis plugin
    await fastify.register(fastifyRedis, {
      url: redisUrl,
      connectTimeout: 3000,
      lazyConnect: true,
    });

    // Test the connection with a timeout in ready hook
    fastify.addHook('onReady', async () => {
      try {
        const pingTimeout = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Redis ping timeout')), 3000);
        });

        await Promise.race([fastify.redis.ping(), pingTimeout]);

        fastify.log.info(`Redis connected successfully at ${redisUrl}`);
        fastify.decorate('isRedisMocked', false);
      } catch (error: any) {
        fastify.log.warn(
          `Redis ping failed after registration: ${error?.message || 'Unknown error'}`
        );

        if (isDevMode) {
          fastify.log.warn(
            'Development mode: Redis ping failed but continuing with registered client'
          );
          fastify.decorate('isRedisMocked', true);
        }
      }
    });
  } catch (error: any) {
    fastify.log.warn(`Redis registration failed: ${error?.message || 'Unknown error'}`);

    if (isDevMode) {
      fastify.log.warn('Development mode: Falling back to mock Redis client');

      // Register a mock Redis client
      const mockRedis = createMockRedis();
      fastify.decorate('redis', mockRedis);
      fastify.decorate('isRedisMocked', true);
    } else {
      // In production, we want to fail if Redis is not available
      throw new Error(`Redis connection required in production but failed: ${error?.message}`);
    }
  }

  // Add a cleanup hook to ensure Redis connection is closed gracefully
  fastify.addHook('onClose', (instance, done) => {
    if (instance.redis && !instance.isRedisMocked) {
      instance.redis
        .quit()
        .then(() => {
          instance.log.info('Redis connection closed gracefully.');
          done();
        })
        .catch(err => {
          instance.log.error(err, 'Error closing Redis connection');
          done();
        });
    } else {
      done();
    }
  });
});
