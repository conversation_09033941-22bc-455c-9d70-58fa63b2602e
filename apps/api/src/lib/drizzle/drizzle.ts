import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import type { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';

declare module 'fastify' {
  interface FastifyInstance {
    db: ReturnType<typeof drizzle>;
    dbWithTenant(orgId: string): Promise<ReturnType<typeof drizzle>>;
    dbBypassRLS(): Promise<ReturnType<typeof drizzle>>;
  }
}

const drizzlePlugin: FastifyPluginAsync = async fastify => {
  if (fastify.hasDecorator('db')) {
    throw new Error('A `db` decorator has already been registered.');
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL_ROOT_USER,
    ssl:
      process.env.DB_DISABLE_SSL_MODE === 'true'
        ? false
        : process.env.NODE_ENV === 'production'
          ? {
              rejectUnauthorized: false,
            }
          : false,
  });

  const db = drizzle(pool);

  //base drizzle client
  fastify.decorate('db', db);

  // drizzle client with tenant isolation
  fastify.decorate('dbWithTenant', async function (orgId: string) {
    await pool.query(`SELECT set_config('app.current_organization_id', $1, TRUE)`, [orgId]);
    await pool.query(`SELECT set_config('app.bypass_rls', 'off', TRUE)`);
    return db;
  });

  //drizzle client with RLS bypassed
  fastify.decorate('dbBypassRLS', async function () {
    await pool.query(`SELECT set_config('app.bypass_rls', 'on', TRUE)`);
    return db;
  });

  fastify.addHook('onClose', async () => {
    await pool.end();
  });
};

export default fp(drizzlePlugin);
