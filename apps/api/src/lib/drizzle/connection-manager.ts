import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import type { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';

declare module 'fastify' {
  interface FastifyInstance {
    db: ReturnType<typeof drizzle>;
    dbWithTenant(orgId: string): Promise<ReturnType<typeof drizzle>>;
    dbBypassRLS(): Promise<ReturnType<typeof drizzle>>;
    // New multi-database support
    dbInternal: ReturnType<typeof drizzle>;
    dbInternalWithTenant(orgId: string): Promise<ReturnType<typeof drizzle>>;
    dbInternalBypassRLS(): Promise<ReturnType<typeof drizzle>>;
    // Database configuration access
    getDatabaseConfig(schema: 'internal' | 'external'): DatabaseConfig;
  }
}

export interface DatabaseConfig {
  connectionString: string;
  hasRLS: boolean;
  schema: 'internal' | 'external';
}

interface EnvironmentConfig {
  DATABASE_URL_INTERNAL?: string;
  DATABASE_URL_ROOT_USER?: string;
  INTERNAL_DB_HAS_RLS?: string;
}

export class DatabaseConnectionManager {
  private internalPool: Pool;
  private externalPool: Pool;
  public internalDb: ReturnType<typeof drizzle>;
  public externalDb: ReturnType<typeof drizzle>;
  private internalHasRLS: boolean;
  private externalHasRLS: boolean;

  constructor(env?: EnvironmentConfig) {
    // Use provided env or fall back to process.env
    const envConfig = env || (process.env as EnvironmentConfig);

    // External database (current main database)
    this.externalPool = new Pool({
      connectionString: envConfig.DATABASE_URL_ROOT_USER,
      ssl:
        process.env.DB_DISABLE_SSL_MODE === 'true'
          ? false
          : process.env.NODE_ENV === 'production'
            ? {
                rejectUnauthorized: false,
              }
            : false,
    });
    this.externalDb = drizzle(this.externalPool);
    this.externalHasRLS = true; // Current database has RLS

    // Internal database (new database with different schema)
    if (!envConfig.DATABASE_URL_INTERNAL) {
      throw new Error('DATABASE_URL_INTERNAL environment variable is required but not set');
    }

    const internalConnectionString = envConfig.DATABASE_URL_INTERNAL;
    console.log(`[DatabaseConnectionManager] Internal database connection:`, {
      hasInternalUrl: !!envConfig.DATABASE_URL_INTERNAL,
      internalUrl: envConfig.DATABASE_URL_INTERNAL ? 'SET' : 'NOT SET',
      fallbackUrl: envConfig.DATABASE_URL_ROOT_USER ? 'SET' : 'NOT SET',
      finalConnection: 'INTERNAL',
    });

    this.internalPool = new Pool({
      connectionString: internalConnectionString,
      ssl:
        process.env.DB_DISABLE_SSL_MODE === 'true'
          ? false
          : process.env.NODE_ENV === 'production'
            ? {
                rejectUnauthorized: false,
              }
            : false,
    });
    this.internalDb = drizzle(this.internalPool);
    this.internalHasRLS = envConfig.INTERNAL_DB_HAS_RLS === 'true';

    console.log(`Database Connection Manager initialized:
      - External DB: ${this.externalHasRLS ? 'RLS Enabled' : 'RLS Disabled'}
      - Internal DB: ${this.internalHasRLS ? 'RLS Enabled' : 'RLS Disabled'}
    `);
  }

  // External database methods (current implementation)
  async getExternalDbWithTenant(orgId: string): Promise<ReturnType<typeof drizzle>> {
    if (this.externalHasRLS) {
      await this.externalPool.query(`SELECT set_config('app.current_organization_id', $1, TRUE)`, [
        orgId,
      ]);
      await this.externalPool.query(`SELECT set_config('app.bypass_rls', 'off', TRUE)`);
    }
    return this.externalDb;
  }

  async getExternalDbBypassRLS(): Promise<ReturnType<typeof drizzle>> {
    if (this.externalHasRLS) {
      await this.externalPool.query(`SELECT set_config('app.bypass_rls', 'on', TRUE)`);
    }
    return this.externalDb;
  }

  // Internal database methods (new implementation)
  async getInternalDbWithTenant(orgId: string): Promise<ReturnType<typeof drizzle>> {
    if (this.internalHasRLS) {
      await this.internalPool.query(`SELECT set_config('app.current_organization_id', $1, TRUE)`, [
        orgId,
      ]);
      await this.internalPool.query(`SELECT set_config('app.bypass_rls', 'off', TRUE)`);
    }
    return this.internalDb;
  }

  async getInternalDbBypassRLS(): Promise<ReturnType<typeof drizzle>> {
    if (this.internalHasRLS) {
      await this.internalPool.query(`SELECT set_config('app.bypass_rls', 'on', TRUE)`);
    }
    return this.internalDb;
  }

  // Get database configuration
  getDatabaseConfig(schema: 'internal' | 'external'): DatabaseConfig {
    if (schema === 'internal') {
      return {
        connectionString:
          process.env.DATABASE_URL_INTERNAL || process.env.DATABASE_URL_ROOT_USER || '',
        hasRLS: this.internalHasRLS,
        schema: 'internal',
      };
    }
    return {
      connectionString: process.env.DATABASE_URL_ROOT_USER || '',
      hasRLS: this.externalHasRLS,
      schema: 'external',
    };
  }

  // Cleanup method
  async close(): Promise<void> {
    await this.internalPool.end();
    await this.externalPool.end();
  }
}

const enhancedDrizzlePlugin: FastifyPluginAsync = async fastify => {
  if (fastify.hasDecorator('db')) {
    throw new Error('A `db` decorator has already been registered.');
  }

  const connectionManager = new DatabaseConnectionManager(fastify.env);

  // Base drizzle clients
  fastify.decorate('db', connectionManager.externalDb);
  fastify.decorate('dbInternal', connectionManager.internalDb);

  // External database with tenant isolation (current implementation)
  fastify.decorate('dbWithTenant', async function (orgId: string) {
    return connectionManager.getExternalDbWithTenant(orgId);
  });

  // External database with RLS bypassed
  fastify.decorate('dbBypassRLS', async function () {
    return connectionManager.getExternalDbBypassRLS();
  });

  // Internal database with tenant isolation (new implementation)
  fastify.decorate('dbInternalWithTenant', async function (orgId: string) {
    return connectionManager.getInternalDbWithTenant(orgId);
  });

  // Internal database with RLS bypassed
  fastify.decorate('dbInternalBypassRLS', async function () {
    return connectionManager.getInternalDbBypassRLS();
  });

  fastify.decorate('getDatabaseConfig', function (schema: 'internal' | 'external') {
    return connectionManager.getDatabaseConfig(schema);
  });

  fastify.addHook('onClose', async () => {
    await connectionManager.close();
  });
};

export default fp(enhancedDrizzlePlugin);
