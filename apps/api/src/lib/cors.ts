import fp from 'fastify-plugin';
import cors from '@fastify/cors';

export default fp(async fastify => {
  const corsOrigins = fastify.env.CORS_ORIGINS.split(',').map(origin => origin.trim());

  fastify.register(cors, {
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Internal-Secret'],
  });
});
