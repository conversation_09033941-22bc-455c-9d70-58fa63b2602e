/**
 * Shared response formatting utilities for consistent API responses
 */

/**
 * Helper function to properly extract and preserve content formatting from AI responses
 * This ensures that Markdown, JSON, and other formatted content is preserved exactly as provided by the AI
 */
function extractPreservedContent(content: any): string {
  if (typeof content === 'string') {
    // If it's already a string, return it as-is to preserve formatting
    return content;
  }

  if (content === null || content === undefined) {
    return '';
  }

  // If it's an object with a 'text' property (common in LangChain responses)
  if (typeof content === 'object' && content.text) {
    return content.text;
  }

  // If it's an array, try to extract text from the first item
  if (Array.isArray(content) && content.length > 0) {
    const firstItem = content[0];
    if (typeof firstItem === 'string') {
      return firstItem;
    }
    if (typeof firstItem === 'object' && firstItem.text) {
      return firstItem.text;
    }
  }

  // For other object types, try to preserve the original structure
  // by converting to a readable format that maintains formatting
  try {
    // If it looks like it might be <PERSON><PERSON><PERSON>, preserve the structure
    if (typeof content === 'object') {
      // Check if it has common response properties
      if (content.response || content.content || content.message) {
        const response = content.response || content.content || content.message;
        return extractPreservedContent(response);
      }

      // For other objects, use JSON.stringify with proper formatting
      return JSON.stringify(content, null, 2);
    }
  } catch (error) {
    // If JSON.stringify fails, fall back to String conversion
    console.warn(
      'Failed to preserve content formatting, falling back to string conversion:',
      error
    );
  }

  // Last resort: convert to string
  return String(content);
}

/**
 * Format non-streaming response with consistent structure
 */
export function formatNonStreamingResponse(
  result: any,
  executionTime: number,
  agentName: string,
  requestId: string
) {
  // Extract the response content with formatting preservation
  const rawResponse = result?.output?.response || result?.response || result?.output || '';
  const response = extractPreservedContent(rawResponse);

  // Extract metadata from the result
  const metadata = result?.output?.metadata || result?.metadata || {};
  const metrics = result?.output?.metrics || result?.metrics || {};

  // Extract session_id
  const sessionId = metadata?.session_id || null;

  // Extract context_doc_ids
  const contextDocIds = metadata?.context_doc_ids || [];

  // Extract conversation_entries
  const conversationEntries = metadata?.conversation_entries || 0;

  // Extract semantic search data
  const semanticSearchAverageScore = metadata?.semantic_search_average_score || 0;

  // Extract confidence data if available
  const confidenceScore = metadata?.confidence_score || null;
  const confidenceReason = metadata?.confidence_reason || null;

  // Extract performance metrics
  const tokensUsed = metrics?.tokens_used || result?.metadata?.tokensUsed || 0;
  const embeddingCalls = metrics?.embedding_calls || 0;
  const rows = metrics?.rows || 0;
  const toolUsed = metrics?.tool_used || 'unknown';

  return {
    success: true,
    result: {
      type: 'complete',
      content: response,
      metadata: {
        session_id: sessionId,
        agent_name: agentName,
        request_id: requestId,
        semantic_search: {
          results_count: rows,
          average_score: semanticSearchAverageScore,
          relevance_quality: 'Unknown',
          context_doc_ids: contextDocIds,
          executor: toolUsed,
        },
        confidence: confidenceScore
          ? {
              score: confidenceScore,
              reason: confidenceReason || 'Confidence assessment available',
            }
          : null,
        performance: {
          execution_time_ms: executionTime,
          total_chunks: 0, // Non-streaming doesn't have chunks
          average_chunk_time_ms: 0,
          embedding_calls: embeddingCalls,
          tokens_used: tokensUsed,
          conversation_entries: conversationEntries,
        },
      },
      timestamp: new Date().toISOString(),
    },
    metadata: {
      agent_name: agentName,
      execution_time: executionTime,
      timestamp: new Date().toISOString(),
    },
  };
}

/**
 * Format streaming completion response with consistent structure
 */
export function formatStreamingCompletionResponse(data: {
  fullResponse: string;
  searchResults: any;
  confidenceData: any;
  finalMetadata: any;
  executionTimeMs: number;
  totalChunks: number;
  averageChunkTimeMs: number;
  agentName: string;
  requestId: string;
  hasReceivedChunks: boolean;
}) {
  const {
    fullResponse,
    searchResults,
    confidenceData,
    finalMetadata,
    executionTimeMs,
    totalChunks,
    averageChunkTimeMs,
    agentName,
    requestId,
  } = data;

  // Extract session_id from finalMetadata if available
  const sessionId = finalMetadata?.metadata?.session_id || finalMetadata?.session_id || null;

  // Extract context_doc_ids from finalMetadata if available
  const contextDocIds =
    finalMetadata?.metadata?.context_doc_ids || searchResults?.context_doc_ids || [];

  // Extract conversation_entries from finalMetadata if available
  const conversationEntries =
    finalMetadata?.metadata?.conversation_entries || finalMetadata?.conversation_entries || 0;

  // Extract tokens_used from finalMetadata if available
  const tokensUsed = finalMetadata?.metrics?.tokens_used || finalMetadata?.tokens_used || 0;

  // Extract embedding_calls from finalMetadata if available
  const embeddingCalls =
    finalMetadata?.metrics?.embedding_calls || finalMetadata?.embedding_calls || 0;

  return {
    type: 'complete',
    content: fullResponse, // fullResponse is already a string from chunk accumulation
    metadata: {
      session_id: sessionId,
      agent_name: agentName,
      request_id: requestId,
      semantic_search: {
        results_count: searchResults?.resultsCount || searchResults?.documentCount || 0,
        average_score:
          searchResults?.averageScore || searchResults?.semantic_search_average_score || 0,
        relevance_quality: searchResults?.relevanceQuality || 'Unknown',
        context_doc_ids: contextDocIds,
        executor: finalMetadata?.metadata?.executor || 'streaming-rag-streaming',
      },
      confidence: confidenceData
        ? {
            score: confidenceData.confidenceScore || confidenceData.score || 0,
            reason:
              confidenceData.confidenceReason ||
              confidenceData.reason ||
              'No confidence assessment available',
          }
        : null,
      performance: {
        execution_time_ms: executionTimeMs,
        total_chunks: totalChunks,
        average_chunk_time_ms: averageChunkTimeMs,
        embedding_calls: embeddingCalls,
        tokens_used: tokensUsed,
        conversation_entries: conversationEntries,
      },
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Format error response with consistent structure
 */
export function formatErrorResponse(
  error: any,
  executionTime: number,
  agentName: string,
  requestId: string,
  totalChunks: number = 0
) {
  return {
    type: 'complete',
    content: '',
    metadata: {
      session_id: null,
      agent_name: agentName,
      request_id: requestId,
      error: true,
      error_message: error.message || 'Unknown error',
      semantic_search: {
        results_count: 0,
        average_score: 0,
        relevance_quality: 'Unknown',
        context_doc_ids: [],
        executor: 'streaming-rag-streaming',
      },
      confidence: null,
      performance: {
        execution_time_ms: executionTime,
        total_chunks: totalChunks,
        average_chunk_time_ms: 0,
        embedding_calls: 0,
        tokens_used: 0,
        conversation_entries: 0,
      },
    },
    timestamp: new Date().toISOString(),
  };
}
