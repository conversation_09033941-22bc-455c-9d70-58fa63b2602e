import { parse } from 'path';

export function convertToFile({
  data,
  path,
  date,
}: {
  data: Buffer;
  path: string;
  date: Date;
}): File {
  const { ext } = parse(path);
  const uint8Array = new Uint8Array(data);
  const lastModified = new Date(date).getTime();

  let mimeType: string;
  switch (ext) {
    case '.md':
      mimeType = 'text/markdown';
      break;
    case '.pdf':
      mimeType = 'application/pdf';
      break;
    default:
      throw new Error(`Unsupported file extension: ${ext}`);
  }

  const fileBlob = new Blob([uint8Array], { type: mimeType });
  const file = new File([fileBlob], path, {
    type: mimeType,
    lastModified,
  });

  return file;
}
