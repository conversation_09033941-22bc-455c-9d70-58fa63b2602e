import fp from 'fastify-plugin';
import fastifyEnv from '@fastify/env';

export default fp((fastify, opts, done) => {
  fastify.register(fastifyEnv, {
    confKey: 'env',
    dotenv: true,
    schema: {
      type: 'object',
      required: [
        'DATABASE_URL_ROOT_USER',
        'DATABASE_URL_INTERNAL',
        'OPENAI_API_KEY',
        'BASE_URL',
        'JWT_SECRET',
        'REFRESH_TOKEN_SECRET',
        'INTERNAL_API_SECRET',
        'TOKEN_EXPIRY',
        'REFRESH_TOKEN_EXPIRY',
        'CORS_ORIGINS',
      ],
      properties: {
        DATABASE_URL_RLS_USER: {
          type: 'string',
          default: 'postgres://postgres:postgres@localhost:5432/postgres',
        },
        DATABASE_URL_ROOT_USER: {
          type: 'string',
          default: 'postgres://postgres:postgres@localhost:5432/postgres',
        },
        OPENAI_API_KEY: { type: 'string', default: 'dev-openai-key' },
        BASE_URL: { type: 'string', default: 'http://localhost:8000' },
        JWT_SECRET: { type: 'string', default: 'dev-jwt-secret' },
        REFRESH_TOKEN_SECRET: { type: 'string', default: 'dev-refresh-secret' },
        INTERNAL_API_SECRET: { type: 'string', default: 'dev-internal-secret' },
        TOKEN_EXPIRY: { type: 'string', default: '15m' },
        REFRESH_TOKEN_EXPIRY: { type: 'string', default: '7d' },
        CORS_ORIGINS: { type: 'string', default: '*' },
        // Multi-Database Configuration
        DATABASE_URL_INTERNAL: {
          type: 'string',
          default: 'postgres://postgres:postgres@localhost:5432/postgres',
        },
        INTERNAL_DB_HAS_RLS: { type: 'string', default: 'false' },
        // MCP Configuration
        MCP_ENABLED: { type: 'string', default: 'true' },
        MCP_SESSION_TTL: { type: 'string', default: '1800000' }, // 30 minutes in milliseconds
        MCP_CONTEXT_TTL: { type: 'string', default: '3600' },
        MCP_MAX_CONTEXT_SIZE: { type: 'string', default: '10485760' },
        MCP_MAX_SESSIONS_PER_ORG: { type: 'string', default: '5' },
        MCP_CLEANUP_INTERVAL: { type: 'string', default: '300000' }, // 5 minutes in milliseconds
        MCP_MAX_RETRY_ATTEMPTS: { type: 'string', default: '3' },
        MCP_HEALTH_CHECK_INTERVAL: { type: 'string', default: '120000' }, // 2 minutes in milliseconds
        REDIS_URL: { type: 'string', default: 'redis://localhost:6379' },
        PUBLIC_WS_BASE: { type: 'string' },
        // LangChain Configuration
        LANGCHAIN_ENABLED: { type: 'string', default: 'true' },
        LANGCHAIN_MODEL: { type: 'string', default: 'gpt-4o-mini' },
        LANGCHAIN_EMBEDDINGS: { type: 'string', default: 'text-embedding-3-small' },
        LANGCHAIN_TEMPERATURE: { type: 'string', default: '0.7' },
        FORCE_HARDCODED_PROMPTS: { type: 'string', default: 'false' },
        // Langfuse Configuration Keys
        LANGFUSE_PUBLIC_KEY: { type: 'string', default: '' },
        LANGFUSE_SECRET_KEY: { type: 'string', default: '' },
        LANGFUSE_BASEURL: { type: 'string', default: '' },
        LANGFUSE_SAMPLE_RATE: { type: 'string', default: '1' },
        LANGFUSE_TRACING_ENABLED: { type: 'string', default: 'true' },
        // Existing key
        LANGFUSE_DEFAULT_PROMPT: { type: 'string', default: 'system-default' },
      },
    },
  });

  done();
});

declare module 'fastify' {
  interface FastifyInstance {
    env: {
      DATABASE_URL_RLS_USER: string;
      DATABASE_URL_ROOT_USER: string;
      OPENAI_API_KEY: string;
      BASE_URL: string;
      JWT_SECRET: string;
      REFRESH_TOKEN_SECRET: string;
      INTERNAL_API_SECRET: string;
      TOKEN_EXPIRY: string;
      REFRESH_TOKEN_EXPIRY: string;
      CORS_ORIGINS: string;
      // Multi-Database Configuration
      DATABASE_URL_INTERNAL: string;
      INTERNAL_DB_HAS_RLS: string;
      // MCP Configuration
      MCP_ENABLED: string;
      MCP_SESSION_TTL: string;
      MCP_CONTEXT_TTL: string;
      MCP_MAX_CONTEXT_SIZE: string;
      MCP_MAX_SESSIONS_PER_ORG: string;
      MCP_CLEANUP_INTERVAL: string;
      MCP_MAX_RETRY_ATTEMPTS: string;
      MCP_HEALTH_CHECK_INTERVAL: string;
      REDIS_URL: string;
      PUBLIC_WS_BASE?: string;
      // LangChain Configuration
      LANGCHAIN_ENABLED: string;
      LANGCHAIN_MODEL: string;
      LANGCHAIN_EMBEDDINGS: string;
      LANGCHAIN_TEMPERATURE: string;
      FORCE_HARDCODED_PROMPTS: string;
      // Langfuse keys
      LANGFUSE_PUBLIC_KEY: string;
      LANGFUSE_SECRET_KEY: string;
      LANGFUSE_BASEURL: string;
      LANGFUSE_SAMPLE_RATE: string;
      LANGFUSE_TRACING_ENABLED: string;
      // Langfuse Configuration
      LANGFUSE_DEFAULT_PROMPT: string;
    };
  }
}
