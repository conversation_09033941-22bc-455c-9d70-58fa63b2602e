/**
 * Configuration for agent routing and feature flags
 *
 * Note: This is now simplified since we have a single ask_ai agent
 * that uses the refactored implementation by default.
 */
export interface AgentRoutingConfig {
  // Legacy configuration - kept for backward compatibility
  // but no longer used since we have a single ask_ai agent
}

/**
 * Get the current agent routing configuration
 */
export function getAgentRoutingConfig(): AgentRoutingConfig {
  return {};
}

/**
 * Determine which agent name to use based on routing configuration
 *
 * Note: This now simply returns the requested agent name since
 * we have a single ask_ai agent that uses the refactored implementation.
 */
export function getActualAgentName(requestedAgentName: string): string {
  // For backward compatibility, map old agent names to the new ask_ai
  if (requestedAgentName === 'ask_ai_v2' || requestedAgentName === 'ask_ai_v2_refactored') {
    return 'ask_ai';
  }

  return requestedAgentName;
}
