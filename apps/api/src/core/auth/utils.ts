/**
 * Shared authentication utilities
 */

/**
 * Extract header value safely
 */
export function getHeaderValue(headers: Record<string, unknown>, key: string): string | undefined {
  const value = headers[key];
  return typeof value === 'string' ? value : undefined;
}

/**
 * Validate organization ID format
 */
export function isValidOrganizationId(orgId: string): boolean {
  // Basic validation - adjust based on your organization ID format
  return Boolean(orgId && orgId.length > 0 && /^[a-zA-Z0-9_-]+$/.test(orgId));
}

/**
 * Parse token expiry string to seconds
 */
export function parseTokenExpiry(expiry: string): number {
  // Parse time string to seconds
  const match = expiry.match(/^(\d+)([mhd])$/);
  if (!match) {
    return 15 * 60; // Default to 15 minutes
  }

  const value = parseInt(match[1], 10);
  const unit = match[2];

  switch (unit) {
    case 'm':
      return value * 60;
    case 'h':
      return value * 60 * 60;
    case 'd':
      return value * 24 * 60 * 60;
    default:
      return 15 * 60;
  }
}

/**
 * Check if a route should be skipped based on URL patterns
 */
export function shouldSkipRoute(url: string, skipRoutes: string[]): boolean {
  if (!skipRoutes || skipRoutes.length === 0) {
    return false;
  }

  return skipRoutes.some(skipRoute => {
    if (skipRoute.endsWith('/')) {
      return url.startsWith(skipRoute);
    }
    return url === skipRoute || url.startsWith(skipRoute);
  });
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Common error response patterns
 */
export const AuthErrorResponses = {
  unauthorized: (message: string = 'Unauthorized', code: string = 'UNAUTHORIZED') => ({
    error: 'Unauthorized',
    message,
    code,
  }),

  badRequest: (message: string, code: string = 'BAD_REQUEST') => ({
    error: 'Bad Request',
    message,
    code,
  }),

  missingBearerToken: () => ({
    error: 'Unauthorized',
    message: 'Bearer token required',
    code: 'MISSING_BEARER_TOKEN',
  }),

  invalidAuthorizationHeader: () => ({
    error: 'Unauthorized',
    message: 'Invalid Authorization header format',
    code: 'INVALID_AUTHORIZATION_HEADER',
  }),

  invalidJWTToken: () => ({
    error: 'Unauthorized',
    message: 'Invalid JWT token',
    code: 'INVALID_JWT_TOKEN',
  }),

  missingOrganizationId: () => ({
    error: 'Bad Request',
    message: 'x-organization-id header is required',
    code: 'MISSING_ORGANIZATION_ID',
  }),

  invalidOrganizationId: () => ({
    error: 'Bad Request',
    message: 'Invalid organization ID format',
    code: 'INVALID_ORGANIZATION_ID',
  }),

  userNotFound: () => ({
    error: 'Not Found',
    message: 'User not found',
    code: 'USER_NOT_FOUND',
  }),

  invalidOrganization: () => ({
    error: 'Forbidden',
    message: 'User is not a member of the specified organization',
    code: 'INVALID_ORGANIZATION',
  }),

  tokenGenerationFailed: () => ({
    error: 'Internal Server Error',
    message: 'Failed to generate access token',
    code: 'TOKEN_GENERATION_FAILED',
  }),

  internalError: () => ({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    code: 'INTERNAL_ERROR',
  }),
} as const;
