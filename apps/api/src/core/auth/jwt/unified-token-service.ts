import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { eq, and } from 'drizzle-orm';
import {
  JWTTokenResponse,
  JWTPayload,
  RequestTokenBody,
  TokenGenerationOptions,
} from '../../../types/api/auth';
import { checkInternalSecret } from '../middleware/internal-secret';
import { userTable } from '../../../api/v1/internal/schemas/drizzle/user';
import { memberTable } from '../../../api/v1/internal/schemas/drizzle/member';
import {
  getHeaderValue,
  isValidOrganizationId,
  parseTokenExpiry,
  AuthErrorResponses,
} from '../utils';

export class UnifiedTokenService {
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify;
  }

  /**
   * Unified token generation that handles both internal and external flows
   */
  async generateToken(
    request: FastifyRequest,
    reply: FastifyReply,
    options: TokenGenerationOptions
  ): Promise<JWTTokenResponse | void> {
    try {
      // Validate organization ID
      if (!isValidOrganizationId(options.organization_id)) {
        reply.code(400).send(AuthErrorResponses.invalidOrganizationId());
        return;
      }

      let payload: JWTPayload;

      if (options.authMethod === 'email') {
        // Internal flow: validate user exists and belongs to organization
        payload = await this.generateInternalToken(options.email!, options.organization_id);
      } else {
        // External flow: create synthetic token
        payload = await this.generateExternalToken(options.organization_id);
      }

      // Set the scope
      payload.scope = options.scope;

      // Generate JWT token
      const token = await this.fastify.jwt.sign(payload);

      // Calculate expiration time
      const expiresIn = this.getTokenExpiryInSeconds();

      this.fastify.log.info(
        {
          organizationId: options.organization_id,
          scope: options.scope,
          authMethod: options.authMethod,
          expiresIn,
        },
        'Generated unified JWT token'
      );

      return {
        access_token: token,
        token_type: 'Bearer',
        expires_in: expiresIn,
        organization_id: options.organization_id,
      };
    } catch (error) {
      this.fastify.log.error(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          organizationId: options.organization_id,
          scope: options.scope,
        },
        'Failed to generate unified JWT token'
      );

      if (error instanceof Error) {
        if (error.message === 'User not found') {
          reply.code(404).send(AuthErrorResponses.userNotFound());
        } else if (error.message === 'User is not a member of the specified organization') {
          reply.code(403).send(AuthErrorResponses.invalidOrganization());
        } else {
          reply.code(500).send(AuthErrorResponses.tokenGenerationFailed());
        }
      } else {
        reply.code(500).send(AuthErrorResponses.internalError());
      }
      return;
    }
  }

  /**
   * Generate token for internal API (email-based authentication)
   */
  private async generateInternalToken(email: string, organization_id: string): Promise<JWTPayload> {
    const db = this.fastify.db;

    // Fetch the user record
    const userResult = await db
      .select({ id: userTable.id, email: userTable.email, name: userTable.name })
      .from(userTable)
      .where(eq(userTable.email, email))
      .limit(1);

    const user = userResult[0];

    if (!user) {
      throw new Error('User not found');
    }

    // Ensure the user is part of the target organisation
    const membership = await db
      .select({ id: memberTable.id })
      .from(memberTable)
      .where(
        and(eq(memberTable.user_id, user.id), eq(memberTable.organization_id, organization_id))
      )
      .limit(1);

    if (membership.length === 0) {
      throw new Error('User is not a member of the specified organization');
    }

    return {
      id: user.id,
      email: user.email,
      organization_id,
      type: 'access',
    };
  }

  /**
   * Generate token for external API (internal secret-based authentication)
   */
  private async generateExternalToken(organization_id: string): Promise<JWTPayload> {
    return {
      id: `org_${organization_id}`, // Use organization ID as user ID for external tokens
      email: `external@${organization_id}.anter.tech`, // Placeholder email
      organization_id,
      type: 'access',
    };
  }

  /**
   * Validate JWT token with scope checking
   */
  async validateToken(
    token: string,
    expectedScope?: 'internal' | 'external-api'
  ): Promise<JWTPayload> {
    try {
      const decoded = await this.fastify.jwt.verify(token);

      if (!this.isValidJWTPayload(decoded)) {
        throw new Error('Invalid token payload structure');
      }

      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }

      // If scope is specified, validate it
      if (expectedScope && decoded.scope !== expectedScope) {
        throw new Error(`Invalid token scope. Expected: ${expectedScope}, Got: ${decoded.scope}`);
      }

      return decoded;
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Handle internal token request (email + organization_id)
   */
  async handleInternalTokenRequest(
    request: FastifyRequest<{ Body: RequestTokenBody }>,
    reply: FastifyReply
  ): Promise<void> {
    const { email, organization_id } = request.body;

    const result = await this.generateToken(request, reply, {
      email,
      organization_id,
      scope: 'internal',
      authMethod: 'email',
    });

    if (result) {
      reply.code(200).send(result);
    }
  }

  /**
   * Handle external token request (internal secret + organization_id)
   */
  async handleExternalTokenRequest(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    // First verify internal secret
    await checkInternalSecret(request, reply);

    const organizationId = getHeaderValue(request.headers, 'x-organization-id');

    if (!organizationId) {
      reply.code(400).send(AuthErrorResponses.missingOrganizationId());
      return;
    }

    const result = await this.generateToken(request, reply, {
      organization_id: organizationId,
      scope: 'external-api',
      authMethod: 'internal-secret',
    });

    if (result) {
      reply.code(200).send(result);
    }
  }

  private getTokenExpiryInSeconds(): number {
    const expiry = this.fastify.env?.TOKEN_EXPIRY || '15m';
    return parseTokenExpiry(expiry);
  }

  private isValidJWTPayload(obj: unknown): obj is JWTPayload {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    // Validate the object structure without unsafe type assertion
    const payload = obj as Record<string, unknown>;

    // Check if all required properties exist and have correct types
    return (
      typeof payload.id === 'string' &&
      typeof payload.email === 'string' &&
      typeof payload.organization_id === 'string' &&
      payload.type === 'access' &&
      (payload.scope === 'internal' ||
        payload.scope === 'external-api' ||
        payload.scope === undefined)
    );
  }
}
