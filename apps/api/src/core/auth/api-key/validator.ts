import { FastifyInstance } from 'fastify';
import { ApiKeyValidationResult, API_KEY_ERRORS } from '../../../types/api-key';
import { apiKeyTable } from '../../../api/v1/external/schemas/drizzle/api-key';
import { eq } from 'drizzle-orm';

export class ApiKeyValidator {
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify;
  }

  async validateApiKey(apiKey: string): Promise<ApiKeyValidationResult> {
    if (!apiKey) {
      return {
        isValid: false,
        error: API_KEY_ERRORS.MISSING_KEY.message,
      };
    }

    try {
      // Use Drizzle to validate the API key
      const keyData = await this.fastify.db
        .select({
          id: apiKeyTable.id,
          expires_at: apiKeyTable.expires_at,
        })
        .from(apiKeyTable)
        .where(eq(apiKeyTable.key, apiKey))
        .limit(1);

      if (!keyData.length || !keyData[0]) {
        return {
          isValid: false,
          error: API_KEY_ERRORS.INVALID_KEY.message,
        };
      }

      const key = keyData[0];

      // Check if key has expired
      if (key.expires_at && new Date(key.expires_at) < new Date()) {
        return {
          isValid: false,
          error: API_KEY_ERRORS.EXPIRED_KEY.message,
        };
      }

      return {
        isValid: true,
        keyId: key.id,
      };
    } catch (error) {
      this.fastify.log.error('Error validating API key:', error);
      return {
        isValid: false,
        error: API_KEY_ERRORS.INVALID_KEY.message,
      };
    }
  }
}
