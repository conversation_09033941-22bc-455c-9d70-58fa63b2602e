import { randomBytes } from 'crypto';
import { hashApiKey } from './hasher';

export interface GeneratedApiKey {
  key: string;
  hashedKey: string;
}

export async function generateApiKey(): Promise<GeneratedApiKey> {
  // Generate a random 32-byte key
  const key = randomBytes(32).toString('hex');

  // Hash the key for storage
  const hashedKey = await hashApiKey(key);

  return {
    key,
    hashedKey,
  };
}
