import { generateApi<PERSON><PERSON> } from './generator';
import { apiKeyTable, type New<PERSON><PERSON><PERSON><PERSON> } from '../../../api/v1/external/schemas/drizzle/api-key';
import crypto from 'crypto';
import { drizzle } from 'drizzle-orm/node-postgres';

interface CreateApiKeyParams {
  organizationId: string;
  userId?: string;
  name?: string;
  expiresAt?: Date;
  module?: string;
}

export async function createApiKey(params: CreateApiKeyParams, db: ReturnType<typeof drizzle>) {
  const { organizationId, userId = 'system', name = 'API Key', expiresAt, module } = params;

  // Generate the API key
  const { key, hashedKey } = await generateApiKey();

  // Create a prefix for the key (first 8 characters)
  const prefix = key.substring(0, 8);

  // Prepare the new API key data
  const newApiKey: NewApiKey = {
    id: crypto.randomUUID(),
    name,
    key: hashedKey,
    prefix,
    expires_at: expiresAt,
    user_id: userId,
    organization_id: organizationId,
    created_at: new Date(),
    updated_at: new Date(),
    module,
  };

  // Create the API key in the database
  const [apiKey] = await db.insert(apiKeyTable).values(newApiKey).returning();

  // Return the API key with the plaintext key (only time it's available)
  return {
    id: apiKey.id,
    key,
    organizationId: apiKey.organization_id,
    createdAt: apiKey.created_at,
    updatedAt: apiKey.updated_at,
    expiresAt: apiKey.expires_at,
  };
}
