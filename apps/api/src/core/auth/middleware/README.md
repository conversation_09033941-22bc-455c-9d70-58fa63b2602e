# Authentication Middleware Architecture

This directory contains two complementary authentication middleware files that serve different purposes in the API authentication system.

## File Purposes

### `internal-secret.ts` - Single Purpose Authentication Function

- **Purpose**: Provides a focused, reusable authentication function for internal secret validation
- **Functionality**: Validates the `x-internal-secret` header against environment variable `INTERNAL_API_SECRET`
- **Usage**: Can be imported and used as a `preHandler` in individual routes that need internal secret authentication
- **Scope**: Simple, stateless utility function
- **Dependencies**: None - self-contained

### `dual-auth.ts` - Multi-Method Authentication Orchestrator

- **Purpose**: Orchestrates multiple authentication methods based on route patterns
- **Functionality**:
  - Maintains a registry of route patterns and their required authentication methods
  - Routes requests to appropriate authentication handlers (internal-secret, JWT, API key)
  - **Uses `checkInternalSecret` as one of its authentication methods**
- **Usage**: Applied globally to all `/v1/external` routes via the dual auth plugin
- **Scope**: Complex class that manages multiple authentication strategies
- **Dependencies**: Imports and uses `checkInternalSecret` from `internal-secret.ts`

## Relationship

These files are **NOT duplicates** - they serve **complementary purposes**:

1. **`internal-secret.ts`** is a **utility function** that can be used independently
2. **`dual-auth.ts`** is an **orchestrator** that uses `internal-secret.ts` as one of its authentication methods

## Usage Pattern

### Before (Redundant Authentication)

Routes were using both:

- `preHandler: checkInternalSecret` directly in route definitions
- Dual auth middleware checking the same routes

This caused authentication to happen **twice** for the same routes.

### After (Clean Architecture)

- Routes registered under `/v1/external` prefix are handled by the dual auth middleware
- No redundant `preHandler` calls in route definitions
- Single authentication flow per request

## Route Authentication Methods

The dual auth middleware supports three authentication methods:

1. **`internal-secret`**: Uses `checkInternalSecret` function for server-to-server authentication
2. **`jwt`**: JWT token validation for user authentication
3. **`api-key`**: API key validation for external API consumers

## Route Registry

The dual auth middleware maintains a registry of route patterns and their authentication methods:

```typescript
{
  '/v1/external/auth/token': { method: 'internal-secret', description: 'JWT token generation endpoint' },
  '/v1/external/auth/verify/echo': { method: 'internal-secret', description: 'Echo verification endpoint' },
  '/v1/external/auth/verify': { method: 'jwt', description: 'JWT token verification endpoint' },
  '/v1/external/agent/invoke': { method: 'internal-secret', description: 'Agent invocation' },
  '/v1/external/agent/stream': { method: 'internal-secret', description: 'Agent streaming' },
  '/v1/external/key/create': { method: 'internal-secret', description: 'API key management' },
  '/v1/external/health': { method: 'internal-secret', description: 'Health check' },
  '/v1/external/embeddings': { method: 'internal-secret', description: 'Embeddings endpoint' },
  // ... more routes
}
```

## Benefits

1. **Separation of Concerns**: Each file has a single, clear responsibility
2. **Reusability**: `checkInternalSecret` can be used independently when needed
3. **Maintainability**: Authentication logic is centralized in the dual auth middleware
4. **Performance**: No redundant authentication checks
5. **Flexibility**: Easy to add new authentication methods or modify existing ones
