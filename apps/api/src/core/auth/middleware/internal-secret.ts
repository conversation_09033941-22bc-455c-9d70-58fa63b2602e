import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthErrorResponses } from '../utils';

/**
 * Middleware to check the internal secret header
 * Used for internal API endpoints that require server-to-server authentication
 */
export async function checkInternalSecret(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const internalSecret = request.headers['x-internal-secret'];
  const envSecret = process.env.INTERNAL_API_SECRET;

  // Security-conscious logging - only log in development and never expose secrets
  if (process.env.NODE_ENV === 'development') {
    request.log.debug(
      {
        hasHeader: !!internalSecret,
        headerType: typeof internalSecret,
        envType: typeof envSecret,
        isValid: internalSecret === envSecret,
      },
      'Internal secret authentication check'
    );
  }

  if (!internalSecret || internalSecret !== envSecret) {
    request.log.warn(
      {
        ip: request.ip,
        userAgent: request.headers['user-agent'],
        url: request.url,
      },
      'Internal secret authentication failed'
    );

    reply.code(401).send(AuthErrorResponses.unauthorized());
    return;
  }

  request.log.info('[Auth][InternalSecret] Authentication succeeded');
}
