import { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
import { RouteAuthConfig } from '../../../types/api/auth';
import { apiKeyMiddleware } from './service';
import { checkInternalSecret } from './internal-secret';
import { UnifiedTokenService } from '../jwt/unified-token-service';
import { extractTokenFromHeader, AuthErrorResponses } from '../utils';

export interface RouteAuthRegistry {
  [routePattern: string]: RouteAuthConfig;
}

export class DualAuthMiddleware {
  private routeAuthRegistry: RouteAuthRegistry;
  private fastify: FastifyInstance;
  private tokenService: UnifiedTokenService;

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify;
    this.routeAuthRegistry = this.initializeRouteAuthRegistry();
    this.tokenService = new UnifiedTokenService(fastify);
  }

  private initializeRouteAuthRegistry(): RouteAuthRegistry {
    return {
      // Internal secret routes (whitelisted)
      '/v1/external/auth/token': {
        method: 'internal-secret',
        description: 'JWT token generation endpoint',
      },
      '/v1/external/auth/verify/echo': {
        method: 'internal-secret',
        description: 'Echo verification endpoint',
      },
      '/v1/external/auth/verify': { method: 'jwt', description: 'JWT token verification endpoint' },
      '/v1/external/agent/invoke': { method: 'internal-secret', description: 'Agent invocation' },
      '/v1/external/agent/stream': { method: 'internal-secret', description: 'Agent streaming' },
      '/v1/external/key/create': { method: 'internal-secret', description: 'API key management' },
      '/v1/external/health': {
        method: 'none',
        description: 'Public health check - no authentication required',
      },
      '/v1/external/embeddings': { method: 'internal-secret', description: 'Embeddings endpoint' },
      '/v1/external/embeddings/update': {
        method: 'internal-secret',
        description: 'Embeddings update endpoint',
      },
      '/v1/external/embeddings/submit': {
        method: 'internal-secret',
        description: 'Embeddings submit endpoint',
      },
      '/v1/external/prompts': { method: 'internal-secret', description: 'Prompts endpoint' },
      // JWT protected routes (example - add more as needed)
      '/v1/external/agent/': { method: 'jwt', description: 'Agent endpoints requiring JWT' },
      '/v1/external/embeddings/': {
        method: 'jwt',
        description: 'Embedding endpoints requiring JWT',
      },

      // API key protected routes (default for external API)
      '/v1/external/': {
        method: 'api-key',
        description: 'External API endpoints using API keys',
      },
    };
  }

  private getRouteAuthConfig(request: FastifyRequest): RouteAuthConfig {
    const url = request.routeOptions?.url || request.url;

    // Find the most specific matching route pattern
    const matchingPatterns = Object.keys(this.routeAuthRegistry)
      .filter(pattern => {
        if (pattern.endsWith('/')) {
          return url.startsWith(pattern);
        }
        return url === pattern || url.startsWith(pattern);
      })
      .sort((a, b) => b.length - a.length); // Sort by length descending (most specific first)

    if (matchingPatterns.length > 0) {
      return this.routeAuthRegistry[matchingPatterns[0]];
    }

    // Default to API key authentication for external routes
    return { method: 'api-key', description: 'Default external API authentication' };
  }

  async authenticate(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const authConfig = this.getRouteAuthConfig(request);
    (request as any).authMethod = authConfig.method;

    this.fastify.log.debug(
      {
        url: request.url,
        authMethod: authConfig.method,
        description: authConfig.description,
      },
      'Route authentication configuration'
    );

    try {
      switch (authConfig.method) {
        case 'none':
          // No authentication required - allow the request to proceed
          return;

        case 'internal-secret':
          await checkInternalSecret(request, reply);
          break;

        case 'jwt':
          await this.authenticateJWT(request, reply);
          break;

        case 'api-key':
          await apiKeyMiddleware(request, reply);
          break;

        default:
          throw new Error(`Unknown authentication method: ${authConfig.method}`);
      }
    } catch (error) {
      this.fastify.log.error(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          url: request.url,
          authMethod: authConfig.method,
        },
        'Authentication failed'
      );
      throw error;
    }
  }

  private async authenticateJWT(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      reply.code(401).send(AuthErrorResponses.missingBearerToken());
      return;
    }

    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      reply.code(401).send(AuthErrorResponses.invalidAuthorizationHeader());
      return;
    }

    try {
      // Verify the JWT token using unified token service with external-api scope
      const payload = await this.tokenService.validateToken(token, 'external-api');

      // Set user information on request
      (request as any).user = payload;
      (request as any).organizationId = payload.organization_id;

      this.fastify.log.debug(
        {
          userId: payload.id,
          organizationId: payload.organization_id,
          scope: payload.scope,
        },
        'JWT authentication successful'
      );
    } catch (error) {
      reply.code(401).send(AuthErrorResponses.invalidJWTToken());
      return;
    }
  }

  // Method to register custom route authentication
  registerRouteAuth(routePattern: string, config: RouteAuthConfig): void {
    this.routeAuthRegistry[routePattern] = config;
    this.fastify.log.info(
      {
        routePattern,
        authMethod: config.method,
        description: config.description,
      },
      'Registered custom route authentication'
    );
  }
}
