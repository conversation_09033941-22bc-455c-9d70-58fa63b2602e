import { FastifyRequest, FastifyReply } from 'fastify';
import { Api<PERSON>eyValidator } from '../api-key/validator';
import { API_KEY_HEADER } from '../../../types/api-key';

export async function apiKeyMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const apiKey = request.headers[API_KEY_HEADER] as string;
  const validator = new ApiKeyValidator(request.server);

  const result = await validator.validateApiKey(apiKey);

  if (!result.isValid) {
    reply.code(401).send({
      error: result.error,
    });
    return;
  }

  // Add the validated API key ID to the request for later use
  request.apiKeyId = result.keyId;
}

// Extend FastifyRequest type to include apiKeyId
declare module 'fastify' {
  interface FastifyRequest {
    apiKeyId?: string;
  }
}
