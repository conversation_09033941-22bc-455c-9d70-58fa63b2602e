import { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
import { UnifiedTokenService } from '../jwt/unified-token-service';
import { shouldSkipRoute, extractTokenFromHeader, AuthErrorResponses } from '../utils';

export interface UnifiedJWTOptions {
  scope?: 'internal' | 'external-api';
  skipRoutes?: string[];
}

export class UnifiedJWTMiddleware {
  private fastify: FastifyInstance;
  private options: UnifiedJWTOptions;
  private tokenService: UnifiedTokenService;

  constructor(fastify: FastifyInstance, options: UnifiedJWTOptions = {}) {
    this.fastify = fastify;
    this.options = {
      scope: 'internal',
      skipRoutes: ['/auth/', '/authenticate/', '/'],
      ...options,
    };
    this.tokenService = new UnifiedTokenService(fastify);
  }

  /**
   * Main authentication method that handles JWT verification
   */
  async authenticate(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    // Check if route should be skipped
    if (this.shouldSkipRoute(request)) {
      return;
    }

    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      reply.code(401).send(AuthErrorResponses.missingBearerToken());
      return;
    }

    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      reply.code(401).send(AuthErrorResponses.invalidAuthorizationHeader());
      return;
    }

    try {
      // Verify the JWT token with scope validation
      const payload = await this.tokenService.validateToken(token, this.options.scope);

      // Set user information on request
      (request as any).user = payload;
      (request as any).organizationId = payload.organization_id;

      this.fastify.log.debug(
        {
          userId: payload.id,
          organizationId: payload.organization_id,
          scope: payload.scope,
          email: payload.email,
        },
        'Unified JWT authentication successful'
      );
    } catch (error) {
      this.fastify.log.error(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          url: request.url,
          scope: this.options.scope,
        },
        'Unified JWT authentication failed'
      );

      reply.code(401).send(AuthErrorResponses.invalidJWTToken());
    }
  }

  /**
   * Check if the current route should skip authentication
   */
  private shouldSkipRoute(request: FastifyRequest): boolean {
    const url = request.routeOptions?.url || request.url;
    return shouldSkipRoute(url, this.options.skipRoutes || []);
  }

  /**
   * Get the token service instance for token generation
   */
  getTokenService(): UnifiedTokenService {
    return this.tokenService;
  }

  /**
   * Update middleware options
   */
  updateOptions(options: Partial<UnifiedJWTOptions>): void {
    this.options = { ...this.options, ...options };
  }
}

/**
 * Fastify plugin for unified JWT authentication
 */
export const createUnifiedJWTPlugin = (options: UnifiedJWTOptions = {}) => {
  return async (fastify: FastifyInstance) => {
    const middleware = new UnifiedJWTMiddleware(fastify, options);

    // Add authentication hook
    fastify.addHook('preHandler', async (request, reply) => {
      await middleware.authenticate(request, reply);
    });

    // Decorate fastify with the middleware and token service
    fastify.decorate('unifiedJWT', middleware);
    fastify.decorate('tokenService', middleware.getTokenService());
  };
};

// Type declarations for Fastify
declare module 'fastify' {
  interface FastifyInstance {
    unifiedJWT: UnifiedJWTMiddleware;
    tokenService: UnifiedTokenService;
  }
}
