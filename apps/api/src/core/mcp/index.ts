// Export compatibility adapters with the same names as original classes
// This allows existing code to work without any changes
export {
  MCPServerAdapter as MCPServer,
  MCPSessionManagerAdapter as MCPSessionManager,
  MCPHealthCheckerAdapter as <PERSON>P<PERSON>ealthC<PERSON><PERSON>,
  MCPMetricsCollectorAdapter as MCPMetricsCollector,
} from './compatibility-adapters';

// Export the bridge for advanced usage if needed
export { APIAgentsMCPBridge } from './agents-bridge';

// Re-export types that are still used
export type { HealthCheckResult, SystemHealth, MCPMetrics, ToolCallMetric } from './types';
