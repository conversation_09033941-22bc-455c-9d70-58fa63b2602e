import { FastifyInstance } from 'fastify';
import { APIAgentsMCPBridge } from './agents-bridge';
import { MCPSession } from '../../types/mcp-session';
import { JWTPayload } from '../../types/api/auth';
import { HealthCheckResult, SystemHealth, MCPMetrics, ToolCallMetric } from './types';

/**
 * Compatibility adapter for MCPServer
 * Maintains the same interface but delegates to agents implementation
 */
export class MCPServerAdapter {
  private bridge: APIAgentsMCPBridge;

  constructor(fastify: FastifyInstance) {
    this.bridge = new APIAgentsMCPBridge(fastify);
  }

  async handleJsonRpcMessage(sessionId: string, message: any): Promise<any> {
    return await this.bridge.handleJsonRpcMessage(sessionId, message);
  }
}

/**
 * Compatibility adapter for MCPSessionManager
 * Maintains the same interface but delegates to agents implementation
 */
export class MCPSessionManagerAdapter {
  private bridge: APIAgentsMCPBridge;

  constructor(fastify: FastifyInstance) {
    this.bridge = new APIAgentsMCPBridge(fastify);
  }

  async createJWTSession(user: JWTPayload): Promise<MCPSession> {
    return await this.bridge.createJWTSession(user);
  }

  async createAPIKeySession(apiKeyId: string, organizationId: string): Promise<MCPSession> {
    return await this.bridge.createAPIKeySession(apiKeyId, organizationId);
  }

  async validateSession(sessionId: string): Promise<MCPSession | null> {
    return await this.bridge.validateSession(sessionId);
  }
}

/**
 * Compatibility adapter for MCPHealthChecker
 * Enhances original health checks with agents metrics
 */
export class MCPHealthCheckerAdapter {
  private bridge: APIAgentsMCPBridge;

  constructor(private fastify: FastifyInstance) {
    this.bridge = new APIAgentsMCPBridge(fastify);
  }

  async checkRedisHealth(): Promise<HealthCheckResult> {
    const start = Date.now();
    const timestamp = new Date().toISOString();

    try {
      if (this.fastify.isRedisMocked) {
        const responseTime = Date.now() - start;
        return {
          status: 'degraded',
          component: 'redis',
          timestamp,
          responseTime,
          details: {
            mocked: true,
            message: 'Using mock Redis client - data will not persist',
          },
        };
      }

      const pingResult = await this.fastify.redis.ping();
      const responseTime = Date.now() - start;

      if (pingResult === 'PONG') {
        const info = await this.fastify.redis.info('memory');
        const keyCount = await this.fastify.redis.dbsize();

        return {
          status: 'healthy',
          component: 'redis',
          timestamp,
          responseTime,
          details: {
            ping: pingResult,
            keyCount,
            memoryInfo: this.parseRedisInfo(info),
          },
        };
      } else {
        return {
          status: 'unhealthy',
          component: 'redis',
          timestamp,
          responseTime,
          error: 'Unexpected ping response',
        };
      }
    } catch (error) {
      const responseTime = Date.now() - start;
      return {
        status: 'unhealthy',
        component: 'redis',
        timestamp,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown Redis error',
      };
    }
  }

  async checkSessionStoreHealth(): Promise<HealthCheckResult> {
    const start = Date.now();
    const timestamp = new Date().toISOString();

    try {
      // Use agents metrics for enhanced session information
      const sessionInfo = this.bridge.getSessionInfo();
      const metrics = this.bridge.getMetrics();

      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        component: 'session_store',
        timestamp,
        responseTime,
        details: {
          activeSessionCount: metrics.activeSessions,
          totalSessions: metrics.totalSessions,
          sessionsByOrg: sessionInfo.byOrganization,
          successRate: metrics.successRate,
          agentsPowered: true,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - start;
      return {
        status: 'unhealthy',
        component: 'session_store',
        timestamp,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown session store error',
      };
    }
  }

  async checkDatabaseHealth(): Promise<HealthCheckResult> {
    const start = Date.now();
    const timestamp = new Date().toISOString();

    try {
      const { sql } = await import('drizzle-orm');
      const result = await this.fastify.db.execute(sql.raw('SELECT 1 as health_check'));

      const responseTime = Date.now() - start;

      if (result.rows.length > 0) {
        return {
          status: 'healthy',
          component: 'database',
          timestamp,
          responseTime,
          details: {
            queryTest: 'passed',
            rowCount: result.rows.length,
            agentsPowered: true,
          },
        };
      } else {
        return {
          status: 'unhealthy',
          component: 'database',
          timestamp,
          responseTime,
          error: 'Database query returned no results',
        };
      }
    } catch (error) {
      const responseTime = Date.now() - start;
      return {
        status: 'unhealthy',
        component: 'database',
        timestamp,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown database error',
      };
    }
  }

  async checkOverallHealth(): Promise<SystemHealth> {
    const timestamp = new Date().toISOString();
    const uptime = process.uptime();

    const [redisHealth, sessionHealth, dbHealth] = await Promise.all([
      this.checkRedisHealth(),
      this.checkSessionStoreHealth(),
      this.checkDatabaseHealth(),
    ]);

    const checks = [redisHealth, sessionHealth, dbHealth];
    const summary = {
      total: checks.length,
      healthy: checks.filter(c => c.status === 'healthy').length,
      unhealthy: checks.filter(c => c.status === 'unhealthy').length,
      degraded: checks.filter(c => c.status === 'degraded').length,
    };

    let overallStatus: 'healthy' | 'unhealthy' | 'degraded';
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }

    return {
      status: overallStatus,
      timestamp,
      uptime,
      checks,
      summary,
    };
  }

  private parseRedisInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = info.split('\r\n');
    for (const line of lines) {
      if (line && !line.startsWith('#') && line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }
    return result;
  }
}

/**
 * Compatibility adapter for MCPMetricsCollector
 * Enhances original metrics with agents data
 */
export class MCPMetricsCollectorAdapter {
  private bridge: APIAgentsMCPBridge;
  private responseTimes: number[] = [];
  private readonly MAX_STORED_METRICS = 1000;

  constructor(private fastify: FastifyInstance) {
    this.bridge = new APIAgentsMCPBridge(fastify);
  }

  async recordToolCall(metric: ToolCallMetric): Promise<void> {
    // Store in Redis for persistence (maintaining original behavior)
    try {
      const redisKey = `mcp:metrics:tool_calls:${metric.timestamp}`;
      await this.fastify.redis.setex(redisKey, 86400, JSON.stringify(metric));

      const toolCounterKey = `mcp:metrics:tools:${metric.tool_name}:count`;
      const errorCounterKey = `mcp:metrics:tools:${metric.tool_name}:errors`;

      await this.fastify.redis.incr(toolCounterKey);
      if (!metric.success) {
        await this.fastify.redis.incr(errorCounterKey);
      }
    } catch (error) {
      this.fastify.log.warn(error, 'Failed to store tool call metrics in Redis');
    }
  }

  async recordResponseTime(duration: number): Promise<void> {
    this.responseTimes.push(duration);
    if (this.responseTimes.length > this.MAX_STORED_METRICS) {
      this.responseTimes = this.responseTimes.slice(-this.MAX_STORED_METRICS);
    }
  }

  async getAllMetrics(): Promise<MCPMetrics> {
    // Combine original metrics with agents metrics
    const agentsMetrics = this.bridge.getMetrics();
    // const sessionInfo = this.bridge.getSessionInfo(); // Available if needed

    // Get Redis metrics
    const redisMetrics = await this.getRedisMetrics();

    // Get tool metrics from Redis (maintaining original behavior)
    const toolMetrics = await this.getToolMetrics();

    // Get performance metrics
    const performanceMetrics = this.getPerformanceMetrics();

    // Get WebSocket metrics
    const websocketMetrics = await this.getWebSocketMetrics();

    return {
      sessions: {
        active: agentsMetrics.activeSessions,
        total_created: agentsMetrics.totalSessions,
        expired: agentsMetrics.expiredSessions,
        average_duration: agentsMetrics.averageSessionDuration,
      },
      tools: {
        total_calls: toolMetrics.total_calls,
        calls_by_tool: toolMetrics.calls_by_tool,
        success_rate: agentsMetrics.successRate,
        average_response_time: toolMetrics.average_response_time,
        errors_by_tool: toolMetrics.errors_by_tool,
      },
      performance: performanceMetrics,
      websocket: websocketMetrics,
      redis: redisMetrics,
    };
  }

  // Include all the original helper methods...
  private async getToolMetrics() {
    // Implementation from original MCPMetricsCollector
    try {
      const toolKeys = await this.fastify.redis.keys('mcp:metrics:tools:*:count');
      const callsByTool: Record<string, number> = {};
      const errorsByTool: Record<string, number> = {};
      let totalCalls = 0;

      for (const key of toolKeys) {
        const toolName = key.split(':')[3];
        const count = await this.fastify.redis.get(key);
        const errorCount =
          (await this.fastify.redis.get(`mcp:metrics:tools:${toolName}:errors`)) || '0';

        if (count) {
          const callCount = parseInt(count);
          callsByTool[toolName] = callCount;
          errorsByTool[toolName] = parseInt(errorCount);
          totalCalls += callCount;
        }
      }

      return {
        total_calls: totalCalls,
        calls_by_tool: callsByTool,
        success_rate: 100,
        average_response_time: 0,
        errors_by_tool: errorsByTool,
      };
    } catch (error) {
      return {
        total_calls: 0,
        calls_by_tool: {},
        success_rate: 100,
        average_response_time: 0,
        errors_by_tool: {},
      };
    }
  }

  private getPerformanceMetrics() {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    const sortedTimes = [...this.responseTimes].sort((a, b) => a - b);

    return {
      uptime,
      memory_usage: memoryUsage,
      response_times: {
        p50: this.calculatePercentile(sortedTimes, 50),
        p95: this.calculatePercentile(sortedTimes, 95),
        p99: this.calculatePercentile(sortedTimes, 99),
      },
    };
  }

  private async getWebSocketMetrics() {
    try {
      const activeConnections = (await this.fastify.redis.get('mcp:metrics:ws:active')) || '0';
      const totalConnections = (await this.fastify.redis.get('mcp:metrics:ws:total')) || '0';
      const messagesSent = (await this.fastify.redis.get('mcp:metrics:ws:sent')) || '0';
      const messagesReceived = (await this.fastify.redis.get('mcp:metrics:ws:received')) || '0';

      return {
        active_connections: parseInt(activeConnections),
        total_connections: parseInt(totalConnections),
        messages_sent: parseInt(messagesSent),
        messages_received: parseInt(messagesReceived),
      };
    } catch (error) {
      return {
        active_connections: 0,
        total_connections: 0,
        messages_sent: 0,
        messages_received: 0,
      };
    }
  }

  private async getRedisMetrics() {
    try {
      if (this.fastify.isRedisMocked) {
        return {
          connected: false,
          key_count: 0,
          memory_usage: 'mocked',
        };
      }

      const keyCount = await this.fastify.redis.dbsize();
      const info = await this.fastify.redis.info('memory');
      const memoryUsage = this.parseRedisInfo(info).used_memory_human;

      return {
        connected: true,
        key_count: keyCount,
        memory_usage: memoryUsage,
      };
    } catch (error) {
      return {
        connected: false,
        key_count: 0,
      };
    }
  }

  private parseRedisInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = info.split('\r\n');
    for (const line of lines) {
      if (line && !line.startsWith('#') && line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }
    return result;
  }

  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[index] || 0;
  }

  // Additional helper methods for compatibility
  async incrementSessionCounter(type: 'total' | 'expired'): Promise<void> {
    try {
      await this.fastify.redis.incr(`mcp:metrics:sessions:${type}`);
    } catch (error) {
      this.fastify.log.warn(error, `Failed to increment session counter: ${type}`);
    }
  }

  async incrementWebSocketCounter(
    type: 'active' | 'total' | 'sent' | 'received',
    increment = 1
  ): Promise<void> {
    try {
      await this.fastify.redis.incrby(`mcp:metrics:ws:${type}`, increment);
    } catch (error) {
      this.fastify.log.warn(error, `Failed to increment WebSocket counter: ${type}`);
    }
  }

  async decrementWebSocketActive(): Promise<void> {
    try {
      await this.fastify.redis.decr('mcp:metrics:ws:active');
    } catch (error) {
      this.fastify.log.warn(error, 'Failed to decrement WebSocket active counter');
    }
  }
}
