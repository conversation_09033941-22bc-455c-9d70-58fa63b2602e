/**
 * MCP Types
 * Extracted from legacy implementation for compatibility
 */

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  component: string;
  timestamp: string;
  responseTime: number;
  details?: Record<string, any>;
  error?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  checks: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

export interface MCPMetrics {
  sessions: {
    active: number;
    total_created: number;
    expired: number;
    average_duration: number;
  };
  tools: {
    total_calls: number;
    calls_by_tool: Record<string, number>;
    success_rate: number;
    average_response_time: number;
    errors_by_tool: Record<string, number>;
  };
  performance: {
    uptime: number;
    memory_usage: NodeJS.MemoryUsage;
    response_times: {
      p50: number;
      p95: number;
      p99: number;
    };
  };
  websocket: {
    active_connections: number;
    total_connections: number;
    messages_sent: number;
    messages_received: number;
  };
  redis: {
    connected: boolean;
    key_count: number;
    memory_usage?: string;
  };
}

export interface ToolCallMetric {
  tool_name: string;
  session_id: string;
  timestamp: number;
  duration: number;
  success: boolean;
  error?: string;
}
