import { FastifyInstance } from 'fastify';
import { MCPConnectionManager, ConnectionPoolConfig, IConnectionManager } from '@anter/agents';
import { MCPSession } from '../../types/mcp-session';
import { JWTPayload } from '../../types/api/auth';

/**
 * Bridge between API MCP infrastructure and Agents MCP implementation
 * Provides the same interface as API MCP classes but delegates to agents
 */
export class APIAgentsMCPBridge {
  private connectionManager: MCPConnectionManager;

  constructor(private fastify: FastifyInstance) {
    // Create connection manager adapter for agents using Fastify's database decorators
    const connectionManagerAdapter: IConnectionManager = {
      dbWithTenant: (orgId: string) => this.fastify.dbWithTenant(orgId),
      dbInternalWithTenant: (orgId: string) => this.fastify.dbInternalWithTenant(orgId),
      dbBypassRLS: () => this.fastify.dbBypassRLS(),
      dbInternalBypassRLS: () => this.fastify.dbInternalBypassRLS(),
      getDatabaseConfig: (schema: 'internal' | 'external') => {
        return this.fastify.getDatabaseConfig(schema);
      },
      log: {
        debug: (message: string) => this.fastify.log.debug(message),
        info: (message: string) => this.fastify.log.info(message),
        warn: (message: string) => this.fastify.log.warn(message),
        error: (error: any, message: string) => this.fastify.log.error(error, message),
      },
      redis: this.fastify.redis, // Add the Redis client from Fastify instance
    };

    // Configuration for connection manager - use validated environment variables
    const config: Partial<ConnectionPoolConfig> = {
      maxSessionsPerOrg: parseInt(this.fastify.env.MCP_MAX_SESSIONS_PER_ORG || '5'),
      sessionTTL: parseInt(this.fastify.env.MCP_SESSION_TTL || '1800000'), // 30 minutes
      cleanupInterval: parseInt(this.fastify.env.MCP_CLEANUP_INTERVAL || '300000'), // 5 minutes
      maxRetryAttempts: parseInt(this.fastify.env.MCP_MAX_RETRY_ATTEMPTS || '3'),
      healthCheckInterval: parseInt(this.fastify.env.MCP_HEALTH_CHECK_INTERVAL || '120000'), // 2 minutes
    };

    this.connectionManager = new MCPConnectionManager(connectionManagerAdapter, config);
  }

  /**
   * Handle JSON-RPC message using agents implementation
   */
  async handleJsonRpcMessage(sessionId: string, message: any): Promise<any> {
    try {
      // Validate session and extract organization ID
      const session = await this.validateSession(sessionId);
      if (!session) {
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32001,
            message: 'Invalid session',
          },
        };
      }

      // Add session context to the message
      const messageWithContext = {
        ...message,
        sessionId,
        organizationId: session.organization_id,
      };

      // Delegate to agents connection manager
      const result = await (this.connectionManager as any).handleJsonRpcMessage(
        sessionId,
        messageWithContext
      );

      return {
        jsonrpc: '2.0',
        id: message.id,
        result,
      };
    } catch (error) {
      this.fastify.log.error(error, 'Error handling JSON-RPC message');
      return {
        jsonrpc: '2.0',
        id: message.id,
        error: {
          code: -32603,
          message: 'Internal error',
          data: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Create JWT session
   */
  async createJWTSession(user: JWTPayload): Promise<MCPSession> {
    return await this.createSession(user.organization_id);
  }

  /**
   * Create API key session
   */
  async createAPIKeySession(apiKeyId: string, organizationId: string): Promise<MCPSession> {
    return await this.createSession(organizationId);
  }

  /**
   * Validate session and extract organization ID
   */
  async validateSession(sessionId: string): Promise<MCPSession | null> {
    try {
      // For now, we'll use a simple session validation
      // In a real implementation, you'd validate against your session store
      if (!sessionId || !sessionId.startsWith('session_')) {
        return null;
      }

      // Extract organization ID from session ID (temporary implementation)
      const parts = sessionId.split('_');
      if (parts.length < 3) {
        return null;
      }

      const organizationId = parts[2]; // Assuming format: session_timestamp_orgId

      return {
        id: sessionId,
        organization_id: organizationId,
        created_at: new Date(),
        last_activity: new Date(),
        status: 'active',
        auth_type: 'jwt',
      };
    } catch (error) {
      this.fastify.log.error(error, 'Error validating session');
      return null;
    }
  }

  /**
   * Create a new session for an organization
   */
  async createSession(organizationId: string): Promise<MCPSession> {
    const sessionId = `session_${Date.now()}_${organizationId}`;
    const session: MCPSession = {
      id: sessionId,
      organization_id: organizationId,
      created_at: new Date(),
      last_activity: new Date(),
      status: 'active',
      auth_type: 'jwt',
    };

    return session;
  }

  /**
   * Get session information for health checks
   */
  getSessionInfo(): {
    activeSessions: number;
    totalSessions: number;
    byOrganization: Record<string, number>;
    successRate: number;
  } {
    // This is a placeholder implementation
    // In a real implementation, you'd get this from the connection manager
    return {
      activeSessions: 0,
      totalSessions: 0,
      byOrganization: {},
      successRate: 1.0,
    };
  }

  /**
   * Get metrics for health checks
   */
  getMetrics(): {
    activeSessions: number;
    totalSessions: number;
    expiredSessions: number;
    averageSessionDuration: number;
    successRate: number;
  } {
    return {
      activeSessions: 0,
      totalSessions: 0,
      expiredSessions: 0,
      averageSessionDuration: 0,
      successRate: 1.0,
    };
  }

  /**
   * Get connection manager for direct access
   */
  getConnectionManager(): MCPConnectionManager {
    return this.connectionManager;
  }

  /**
   * Health check for the bridge
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
