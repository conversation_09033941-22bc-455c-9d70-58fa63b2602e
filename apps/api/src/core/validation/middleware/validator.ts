import { FastifyRequest, FastifyReply, HookHandlerDoneFunction } from 'fastify';
import { FastifySchema } from 'fastify';

export interface ValidationOptions {
  schema: FastifySchema;
  validateParams?: boolean;
  validateQuery?: boolean;
  validateBody?: boolean;
}

export const createValidator = (options: ValidationOptions) => {
  return async (request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) => {
    try {
      // Fastify automatically validates against the schema
      // We just need to ensure the schema is properly defined
      done();
    } catch (error) {
      reply.code(400).send({
        error: 'Validation Error',
        code: 'VALIDATION_ERROR',
        details: error,
      });
    }
  };
};
