import { FastifySchema } from 'fastify';
import { baseSchema } from './base';

export const createItemSchema: FastifySchema = {
  ...baseSchema,
  body: {
    type: 'object',
    required: ['name', 'description'],
    properties: {
      name: {
        type: 'string',
        minLength: 1,
        maxLength: 100,
      },
      description: {
        type: 'string',
        minLength: 1,
        maxLength: 1000,
      },
      tags: {
        type: 'array',
        items: { type: 'string' },
        maxItems: 10,
      },
    },
  },
};

export const updateItemSchema: FastifySchema = {
  ...baseSchema,
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
  },
  body: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 1,
        maxLength: 100,
      },
      description: {
        type: 'string',
        minLength: 1,
        maxLength: 1000,
      },
      tags: {
        type: 'array',
        items: { type: 'string' },
        maxItems: 10,
      },
    },
    minProperties: 1,
  },
};
