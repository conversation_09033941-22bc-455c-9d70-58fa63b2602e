import { FastifySchema } from 'fastify';

export const baseSchema: FastifySchema = {
  response: {
    400: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
        details: { type: 'object' },
      },
    },
    401: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
      },
    },
    403: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
      },
    },
    404: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
      },
    },
    500: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
      },
    },
  },
};

export const paginationSchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 10 },
  },
};

export const idParamSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
  },
  required: ['id'],
};
