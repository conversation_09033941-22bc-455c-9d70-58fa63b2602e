import { FastifySchema } from 'fastify';
import { baseSchema } from './base';

const ALLOWED_MIME_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/markdown',
  'application/json',
];

export const fileUploadSchema: FastifySchema = {
  ...baseSchema,
  body: {
    type: 'object',
    required: ['file'],
    properties: {
      file: {
        type: 'object',
        required: ['filename', 'mimetype', 'data'],
        properties: {
          filename: {
            type: 'string',
            minLength: 1,
            maxLength: 255,
            pattern: '^[a-zA-Z0-9-_]+\\.[a-zA-Z0-9]+$',
          },
          mimetype: {
            type: 'string',
            enum: ALLOWED_MIME_TYPES,
          },
          data: {
            type: 'string',
            format: 'binary',
          },
        },
      },
      metadata: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            maxLength: 1000,
          },
          tags: {
            type: 'array',
            items: { type: 'string' },
            maxItems: 10,
          },
        },
      },
    },
  },
};

// Schema for file download/retrieval
export const fileDownloadSchema: FastifySchema = {
  ...baseSchema,
  params: {
    type: 'object',
    required: ['fileId'],
    properties: {
      fileId: { type: 'string', format: 'uuid' },
    },
  },
  querystring: {
    type: 'object',
    properties: {
      download: { type: 'boolean', default: false },
    },
  },
};

// Schema for file metadata update
export const fileMetadataUpdateSchema: FastifySchema = {
  ...baseSchema,
  params: {
    type: 'object',
    required: ['fileId'],
    properties: {
      fileId: { type: 'string', format: 'uuid' },
    },
  },
  body: {
    type: 'object',
    required: ['metadata'],
    properties: {
      metadata: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            maxLength: 1000,
          },
          tags: {
            type: 'array',
            items: { type: 'string' },
            maxItems: 10,
          },
        },
        minProperties: 1,
      },
    },
  },
};
