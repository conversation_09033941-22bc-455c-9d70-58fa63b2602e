import Redis from 'ioredis';

export interface KeyMetadata {
  key: string;
  ttl: number | string;
  type: string;
  size: number;
  sizeFormatted: string;
  error?: string;
}

export interface KeysData {
  contentHashes: string[];
  docCache: string[];
  embeddings: string[];
}

export class OrganizationDataViewer {
  private organizationId: string;
  private redis: Redis;

  constructor(organizationId: string, redis: Redis) {
    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    // Validate organization ID format
    // if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
    //   throw new Error('Invalid organization ID format. Expected format: org-{uuid}');
    // }

    this.organizationId = organizationId;
    this.redis = redis;
  }

  /**
   * Get all keys for the organization
   */
  async getAllKeys(): Promise<KeysData> {
    const keys: KeysData = {
      contentHashes: [],
      docCache: [],
      embeddings: [],
    };

    try {
      // Get content_hashes keys
      const contentHashesPattern = `content_hashes:${this.organizationId}`;
      const contentHashesKeys = await this.redis.keys(contentHashesPattern);
      keys.contentHashes = contentHashesKeys;

      // Get doc_cache keys
      const docCachePattern = `doc_cache:${this.organizationId}`;
      const docCacheKeys = await this.redis.keys(docCachePattern);
      keys.docCache = docCacheKeys;

      // Get embeddings keys
      const embeddingsPattern = `embeddings:${this.organizationId}:*`;
      const embeddingsKeys = await this.redis.keys(embeddingsPattern);
      keys.embeddings = embeddingsKeys;

      return keys;
    } catch (error) {
      throw new Error(
        `Failed to get keys: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get key metadata (TTL, type, size) without loading content
   */
  async getKeyMetadata(key: string): Promise<KeyMetadata> {
    try {
      const [ttl, type, size] = await Promise.all([
        this.redis.ttl(key),
        this.redis.type(key),
        this.redis.memory('USAGE', key).catch(() => 0), // Memory usage in bytes
      ]);

      return {
        key,
        ttl: ttl === -1 ? 'no-expiry' : ttl,
        type,
        size: (size as number) || 0,
        sizeFormatted: this.formatBytes((size as number) || 0),
      };
    } catch (error) {
      return {
        key,
        ttl: 'error',
        type: 'error',
        size: 0,
        sizeFormatted: '0 B',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Clean up Redis connection
   */
  async cleanup(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
    }
  }
}
