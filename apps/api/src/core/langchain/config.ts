/**
 * Lang<PERSON>hain configuration derived from environment variables
 */
export const createLangChainConfig = (env: any) => ({
  enabled: env.LANGCHAIN_ENABLED === 'true',
  model: {
    name: env.LANGCHAIN_MODEL,
    temperature: parseFloat(env.LANGCHAIN_TEMPERATURE),
    maxTokens: parseInt(env.LANGCHAIN_MAX_TOKENS),
    maxRetries: parseInt(env.LANGCHAIN_MAX_RETRIES),
    timeout: parseInt(env.LANGCHAIN_REQUEST_TIMEOUT),
  },
  embeddings: {
    model: env.LANGCHAIN_EMBEDDINGS,
  },
  rateLimiting: {
    requestsPerMinute: parseInt(env.LANGCHAIN_RATE_LIMIT_RPM),
  },
  tracing: {
    enabled: env.LANGCHAIN_TRACING === 'true',
    langsmithApiKey: env.LANGSMITH_API_KEY,
  },
  // Integration with existing services
  redis: {
    url: env.REDIS_URL, // Reuse existing Redis for caching
  },
});

export type LangChainConfig = ReturnType<typeof createLangChainConfig>;

export const createIntegratedConfig = (env: any) => ({
  langchain: createLangChainConfig(env),
  mcp: {
    enabled: env.MCP_ENABLED === 'true',
    sessionTtl: parseInt(env.MCP_SESSION_TTL),
    contextTtl: parseInt(env.MCP_CONTEXT_TTL),
    maxContextSize: parseInt(env.MCP_MAX_CONTEXT_SIZE),
  },
  // Shared resources
  redis: env.REDIS_URL,
  websocket: env.PUBLIC_WS_BASE,
});
