import { drizzle } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import { organizationTable } from '@anter/domain-model';

export class OrganizationService {
  private db: ReturnType<typeof drizzle>;
  constructor(db: ReturnType<typeof drizzle>) {
    this.db = db;
  }

  async getOrganizationById(id: string) {
    const res = await this.db
      .select()
      .from(organizationTable)
      .where(eq(organizationTable.id, id))
      .limit(1);

    return res[0] ?? null;
  }

  async createOrganization(data: any) {
    const res = await this.db.insert(organizationTable).values(data).returning();
    return res[0];
  }

  async updateOrganization(id: string, data: any) {
    const res = await this.db
      .update(organizationTable)
      .set(data)
      .where(eq(organizationTable.id, id))
      .returning();

    return res[0];
  }

  async deleteOrganization(id: string) {
    return await this.db.delete(organizationTable).where(eq(organizationTable.id, id)).returning();
  }
}
