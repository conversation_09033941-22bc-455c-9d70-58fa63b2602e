import fp from 'fastify-plugin';
import jwt from '@fastify/jwt';
import { FastifyPluginAsync } from 'fastify';
import { JWTPayload } from '../../../../types/api/auth';
import { UnifiedTokenService } from '../../../../core/auth/jwt/unified-token-service';
import { extractTokenFromHeader, AuthErrorResponses } from '../../../../core/auth/utils';

declare module 'fastify' {
  interface FastifyInstance {
    verifyRefreshToken(token: string): Promise<JWTPayload>;
    signRefreshToken(payload: Omit<JWTPayload, 'type'>): string;
    tokenService: UnifiedTokenService;
  }
}

const jwtPlugin: FastifyPluginAsync = async (fastify, opts) => {
  // Ensure JWT_SECRET is set - fail fast if not configured
  const jwtSecret = fastify.env?.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error(
      'JWT_SECRET environment variable is required but not set. Please configure a secure JWT secret.'
    );
  }

  // Register JWT plugin with access token configuration
  await fastify.register(jwt, {
    secret: jwtSecret,
    sign: {
      expiresIn: fastify.env?.TOKEN_EXPIRY || '15m',
    },
    verify: {
      maxAge: fastify.env?.TOKEN_EXPIRY || '15m',
    },
  });

  // Initialize unified token service
  const tokenService = new UnifiedTokenService(fastify);
  fastify.decorate('tokenService', tokenService);

  // Add refresh token verification method
  fastify.decorate('verifyRefreshToken', async (token: string) => {
    try {
      const decoded = (await fastify.jwt.verify(token)) as JWTPayload;
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }
      return decoded;
    } catch (err) {
      throw new Error('Invalid refresh token');
    }
  });

  // Add refresh token signing method
  fastify.decorate('signRefreshToken', (payload: Omit<JWTPayload, 'type'>) => {
    return fastify.jwt.sign({ ...payload, type: 'refresh' } as JWTPayload);
  });

  // Add JWT authentication hook using unified token service
  fastify.addHook('onRequest', async (request, reply) => {
    try {
      // Skip authentication for auth routes and root route
      if (
        request.routeOptions?.url?.includes('/auth/') ||
        request.routeOptions?.url?.includes('/authenticate/') ||
        request.routeOptions?.url === '/'
      ) {
        return;
      }

      const authHeader = request.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        reply.code(401).send(AuthErrorResponses.missingBearerToken());
        return;
      }

      const token = extractTokenFromHeader(authHeader);

      if (!token) {
        reply.code(401).send(AuthErrorResponses.invalidAuthorizationHeader());
        return;
      }

      // Verify the JWT token using unified token service with internal scope
      const payload = await tokenService.validateToken(token, 'internal');

      // Set user information on request
      (request as any).user = payload;
      (request as any).organizationId = payload.organization_id;

      fastify.log.debug(
        {
          userId: payload.id,
          organizationId: payload.organization_id,
          scope: payload.scope,
        },
        'Internal JWT authentication successful'
      );
    } catch (error) {
      fastify.log.error(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          url: request.url,
        },
        'Internal JWT authentication failed'
      );

      reply.code(401).send(AuthErrorResponses.invalidJWTToken());
    }
  });
};

export default fp(jwtPlugin);
