import { FastifySchema } from 'fastify';

// Schema for GET /:id endpoint
export const getOrganizationSchema: FastifySchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', description: 'Organization ID' },
    },
    required: ['id'],
  },
  headers: {
    type: 'object',
    properties: {
      organization_id: { type: 'string', description: 'The tenant ID for data isolation' },
    },
    required: ['organization_id'],
  },
  response: {
    200: {
      description: 'Successful response',
      type: 'object',
      properties: {
        id: { type: 'string' },
        company_name: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
      },
      required: ['id', 'company_name', 'created_at'],
    },
    400: {
      description: 'Invalid request',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    404: {
      description: 'Organization not found',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    500: {
      description: 'Internal server error',
      type: 'object',
      properties: {
        error: { type: 'string' },
        message: { type: 'string' },
      },
    },
  },
};

// Schema for creating an organization
export const createOrganizationSchema: FastifySchema = {
  body: {
    type: 'object',
    properties: {
      company_name: { type: 'string', description: 'Organization name' },
      organization_id: { type: 'string', description: 'Organization ID for tenant isolation' },
    },
    required: ['company_name', 'organization_id'],
  },
  response: {
    201: {
      description: 'Organization created successfully',
      type: 'object',
      properties: {
        id: { type: 'string' },
        company_name: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
      },
      required: ['id', 'company_name', 'created_at'],
    },
    400: {
      description: 'Invalid request',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    500: {
      description: 'Internal server error',
      type: 'object',
      properties: {
        error: { type: 'string' },
        message: { type: 'string' },
      },
    },
  },
};

// Schema for updating an organization
export const updateOrganizationSchema: FastifySchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', description: 'Organization ID' },
    },
    required: ['id'],
  },
  headers: {
    type: 'object',
    properties: {
      organization_id: { type: 'string', description: 'The tenant ID for data isolation' },
    },
    required: ['organization_id'],
  },
  body: {
    type: 'object',
    properties: {
      company_name: { type: 'string', description: 'Organization name' },
    },
    required: ['company_name'],
  },
  response: {
    200: {
      description: 'Organization updated successfully',
      type: 'object',
      properties: {
        id: { type: 'string' },
        company_name: { type: 'string' },
        updated_at: { type: 'string', format: 'date-time' },
      },
      required: ['id', 'company_name'],
    },
    400: {
      description: 'Invalid request',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    404: {
      description: 'Organization not found',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    500: {
      description: 'Internal server error',
      type: 'object',
      properties: {
        error: { type: 'string' },
        message: { type: 'string' },
      },
    },
  },
};

// Schema for deleting an organization
export const deleteOrganizationSchema: FastifySchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', description: 'Organization ID' },
    },
    required: ['id'],
  },
  headers: {
    type: 'object',
    properties: {
      organization_id: { type: 'string', description: 'The tenant ID for data isolation' },
    },
    required: ['organization_id'],
  },
  response: {
    204: {
      description: 'Organization deleted successfully',
      type: 'null',
    },
    400: {
      description: 'Invalid request',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    404: {
      description: 'Organization not found',
      type: 'object',
      properties: { error: { type: 'string' } },
    },
    500: {
      description: 'Internal server error',
      type: 'object',
      properties: {
        error: { type: 'string' },
        message: { type: 'string' },
      },
    },
  },
};
