import { FastifyInstance } from 'fastify';
import {
  getOrganizationSchema,
  createOrganizationSchema,
  updateOrganizationSchema,
  deleteOrganizationSchema,
} from '../../schemas/organization';
import { OrganizationController } from '../../controllers/organization';
import { OrganizationService } from '../../services/organization';

export default async function organizationRoutes(fastify: FastifyInstance): Promise<void> {
  // GET organization by ID
  fastify.get('/organization/:id', {
    schema: getOrganizationSchema,
    handler: async (request, reply) => {
      const organization_id_header = request.headers['organization_id'];

      if (!organization_id_header || typeof organization_id_header !== 'string') {
        return reply.code(400).send({ error: 'Organization ID is missing or invalid in headers' });
      }

      const db = await fastify.dbWithTenant(organization_id_header);
      const organizationService = new OrganizationService(db);
      const organizationController = new OrganizationController(organizationService);

      return organizationController.getOrganizationById(request, reply);
    },
  });

  // POST create new organization
  fastify.post('/organization/', {
    schema: createOrganizationSchema,
    handler: async (request, reply) => {
      const organization_id_header = request.headers['organization_id'];

      if (!organization_id_header || typeof organization_id_header !== 'string') {
        return reply.code(400).send({ error: 'Organization ID is missing or invalid in headers' });
      }
      const db = await fastify.dbWithTenant(organization_id_header);
      const organizationService = new OrganizationService(db);
      const organizationController = new OrganizationController(organizationService);

      return organizationController.createOrganization(request, reply);
    },
  });

  // PUT update organization by ID
  fastify.put('/organization/:id', {
    schema: updateOrganizationSchema,
    handler: async (request, reply) => {
      const organization_id_header = request.headers['organization_id'];

      if (!organization_id_header || typeof organization_id_header !== 'string') {
        return reply.code(400).send({ error: 'Organization ID is missing or invalid in headers' });
      }

      const db = await fastify.dbWithTenant(organization_id_header);
      const organizationService = new OrganizationService(db);
      const organizationController = new OrganizationController(organizationService);

      return organizationController.updateOrganization(request, reply);
    },
  });

  // DELETE organization
  fastify.delete('/organization/:id', {
    schema: deleteOrganizationSchema,
    handler: async (request, reply) => {
      const organization_id_header = request.headers['organization_id'];

      if (!organization_id_header || typeof organization_id_header !== 'string') {
        return reply.code(400).send({ error: 'Organization ID is missing or invalid in headers' });
      }

      const db = await fastify.dbWithTenant(organization_id_header);
      const organizationService = new OrganizationService(db);
      const organizationController = new OrganizationController(organizationService);

      return organizationController.deleteOrganization(request, reply);
    },
  });
}
