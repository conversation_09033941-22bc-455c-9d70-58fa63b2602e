import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { formatNonStreamingResponse } from '../../../../../lib/utils/response-formatter';

/**
 * Internal agent routes
 * Protected by JWT authentication
 */
export default async function agentRoutes(fastify: FastifyInstance): Promise<void> {
  // POST /agent/invoke - Invoke an agent with input
  fastify.post('/agent/invoke', {
    schema: {
      tags: ['Agents'],
      summary: 'Invoke an agent',
      description: 'Invoke an AI agent with provided input and context',
      body: {
        type: 'object',
        required: ['agent_name', 'input'],
        properties: {
          agent_name: {
            type: 'string',
            description: 'Name of the agent to invoke',
            examples: ['echo', 'chat', 'security-analyst'],
          },
          input: {
            description: 'Input data for the agent (can be string, object, or any type)',
            // Allow any type - let the agent handle validation
          },
          context: {
            type: 'object',
            description: 'Optional context for the agent',
            additionalProperties: true,
          },
        },
        additionalProperties: false,
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            result: {
              type: 'object',
              additionalProperties: true,
            },
            metadata: {
              type: 'object',
              properties: {
                agent_name: { type: 'string' },
                execution_time: { type: 'number' },
                timestamp: { type: 'string' },
              },
            },
          },
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            code: { type: 'string' },
          },
        },
        500: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { agent_name, input, context } = request.body as {
          agent_name: string;
          input: any;
          context?: any;
        };

        const startTime = Date.now();

        // Enhance context with request ID
        const enhancedContext = {
          ...context,
          metadata: {
            ...context?.metadata,
            requestId: request.id,
          },
        };

        // Use the agents system from the plugin
        const result = await fastify.agents.invoke(agent_name, input, enhancedContext);

        const executionTime = Date.now() - startTime;

        return formatNonStreamingResponse(result, executionTime, agent_name, request.id);
      } catch (error: any) {
        fastify.log.error('Agent invocation failed:', error);

        if (error.message.includes('not found') || error.message.includes('Unknown agent')) {
          return reply.code(400).send({
            error: `Agent '${(request.body as any)?.agent_name}' not found`,
            code: 'AGENT_NOT_FOUND',
          });
        }

        return reply.code(500).send({
          error: 'Agent invocation failed',
          code: 'AGENT_INVOCATION_ERROR',
        });
      }
    },
  });

  // GET /agent/list - List available agents
  fastify.get('/agent/list', {
    schema: {
      tags: ['Agents'],
      summary: 'List available agents',
      description: 'Get a list of all available AI agents',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            agents: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  description: { type: 'string' },
                  capabilities: {
                    type: 'array',
                    items: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        fastify.log.info('Attempting to list agents...');
        fastify.log.info('fastify.agents exists:', !!fastify.agents);

        if (!fastify.agents) {
          fastify.log.error('fastify.agents is undefined - agents plugin not loaded properly');
          return reply.code(500).send({
            error: 'Agents system not initialized',
            code: 'AGENTS_NOT_INITIALIZED',
          });
        }

        fastify.log.info('fastify.agents.factory exists:', !!fastify.agents.factory);

        if (!fastify.agents.factory) {
          fastify.log.error('fastify.agents.factory is undefined');
          return reply.code(500).send({
            error: 'Agent factory not available',
            code: 'AGENT_FACTORY_NOT_AVAILABLE',
          });
        }

        const types = fastify.agents.factory.getRegisteredTypes();
        fastify.log.info('Retrieved agent types:', types);

        const agents = types.map((type: string) => ({
          name: type,
          description: `${type} agent`,
          capabilities: ['chat', 'invoke'],
        }));

        return {
          success: true,
          agents,
        };
      } catch (error: any) {
        fastify.log.error('Failed to list agents:', error.message);
        fastify.log.error('Error stack:', error.stack);
        return reply.code(500).send({
          error: 'Failed to list agents',
          code: 'AGENT_LIST_ERROR',
        });
      }
    },
  });

  // GET /agent/metrics - Get agent metrics
  fastify.get('/agent/metrics', {
    schema: {
      tags: ['Agents'],
      summary: 'Get agent metrics',
      description: 'Get performance metrics for all agents',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            metrics: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  invocations: { type: 'number' },
                  errors: { type: 'number' },
                  avgLatency: { type: 'number' },
                  lastInvocation: { type: ['string', 'null'] },
                },
              },
            },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const metrics = fastify.agents.metrics.getAllMetrics();

        return {
          success: true,
          metrics,
        };
      } catch (error: any) {
        fastify.log.error('Failed to get agent metrics:', error);
        return reply.code(500).send({
          error: 'Failed to get agent metrics',
          code: 'AGENT_METRICS_ERROR',
        });
      }
    },
  });

  // GET /agent/health - Agent system health check
  fastify.get('/agent/health', {
    schema: {
      tags: ['Agents'],
      summary: 'Agent system health check',
      description: 'Check the health status of the agent system',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            status: { type: 'string' },
            timestamp: { type: 'string' },
            agents: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                healthy: { type: 'number' },
              },
            },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const types = fastify.agents.factory.getRegisteredTypes();

        return {
          success: true,
          status: 'healthy',
          timestamp: new Date().toISOString(),
          agents: {
            total: types.length,
            healthy: types.length, // Assume all registered agents are healthy
          },
        };
      } catch (error: any) {
        fastify.log.error('Agent health check failed:', error);
        return reply.code(500).send({
          error: 'Agent health check failed',
          code: 'AGENT_HEALTH_ERROR',
        });
      }
    },
  });

  // GET /agent/:agentName - Get specific agent info
  fastify.get('/agent/:agentName', {
    schema: {
      tags: ['Agents'],
      summary: 'Get agent information',
      description: 'Get detailed information about a specific agent',
      params: {
        type: 'object',
        properties: {
          agentName: {
            type: 'string',
            description: 'Name of the agent',
          },
        },
        required: ['agentName'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            agent: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                description: { type: 'string' },
                capabilities: {
                  type: 'array',
                  items: { type: 'string' },
                },
                metrics: {
                  type: 'object',
                  properties: {
                    invocations: { type: 'number' },
                    errors: { type: 'number' },
                    avgLatency: { type: 'number' },
                    lastInvocation: { type: ['string', 'null'] },
                  },
                },
              },
            },
          },
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { agentName } = request.params as { agentName: string };
        const types = fastify.agents.factory.getRegisteredTypes();

        if (!types.includes(agentName)) {
          return reply.code(404).send({
            error: `Agent '${agentName}' not found`,
            code: 'AGENT_NOT_FOUND',
          });
        }

        const metrics = fastify.agents.metrics.getMetrics(agentName);

        return {
          success: true,
          agent: {
            name: agentName,
            description: `${agentName} agent`,
            capabilities: ['chat', 'invoke'],
            metrics,
          },
        };
      } catch (error: any) {
        fastify.log.error('Failed to get agent info:', error);
        return reply.code(500).send({
          error: 'Failed to get agent information',
          code: 'AGENT_INFO_ERROR',
        });
      }
    },
  });
}
