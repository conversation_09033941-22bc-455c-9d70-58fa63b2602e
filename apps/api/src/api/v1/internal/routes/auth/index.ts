import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { RequestTokenBody } from '../../../../../types/api/auth';
import { UnifiedTokenService } from '../../../../../core/auth/jwt/unified-token-service';
import { requestTokenSchema } from '../../schemas/auth/token';

/**
 * Auth routes for internal API
 */
export default async function authRoutes(fastify: FastifyInstance): Promise<void> {
  // Endpoint to request access token with email and organization ID
  // Protected with internal secret
  fastify.post('/authenticate/token', {
    schema: requestTokenSchema,
    handler: async (request: FastifyRequest<{ Body: RequestTokenBody }>, reply: FastifyReply) => {
      try {
        const tokenService = new UnifiedTokenService(fastify);
        await tokenService.handleInternalTokenRequest(request, reply);
      } catch (error) {
        if (error instanceof Error) {
          if (error.message === 'User not found') {
            reply.code(404).send({
              error: 'Not Found',
              message: 'User not found',
              code: 'USER_NOT_FOUND',
            });
          } else if (error.message === 'User is not a member of the specified organization') {
            reply.code(403).send({
              error: 'Forbidden',
              message: 'User is not a member of the specified organization',
              code: 'INVALID_ORGANIZATION',
            });
          } else {
            fastify.log.error(error);
            reply.code(500).send({
              error: 'Internal Server Error',
              message: 'Failed to generate access token',
              code: 'INTERNAL_ERROR',
            });
          }
        } else {
          fastify.log.error(error);
          reply.code(500).send({
            error: 'Internal Server Error',
            message: 'An unexpected error occurred',
            code: 'INTERNAL_ERROR',
          });
        }
      }
    },
  });
}
