import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { MCPSessionManager, MCPServer, MCPMetricsCollector } from '../../../../../core/mcp';
import { JWTPayload } from '../../../../../types/api/auth';
import mcpHealthRoutes from './health';

export default async function mcpRoutes(fastify: FastifyInstance): Promise<void> {
  // Register health and metrics routes
  await fastify.register(mcpHealthRoutes);

  // Initialize metrics collector
  const metricsCollector = new MCPMetricsCollector(fastify);

  // Initialize MCP session for internal routes
  fastify.post('/mcp/session/init', {
    // JWT authentication is handled by the internal middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const sessionManager = new MCPSessionManager(fastify);

        const session = await sessionManager.createJWTSession(user);

        // Track session creation
        await metricsCollector.incrementSessionCounter('total');

        // Generate session token
        const sessionToken = fastify.jwt.sign({
          session_id: session.id,
          organization_id: session.organization_id,
          auth_type: 'jwt',
          expires_at: new Date(Date.now() + 30 * 60 * 1000),
        });

        // Build the public WebSocket base so dev/stage/prod all work
        const protocol = request.protocol === 'https' ? 'wss' : 'ws';
        const baseWs = process.env.PUBLIC_WS_BASE || `${protocol}://${request.hostname}`;

        reply.send({
          session_id: session.id,
          websocket_url: `${baseWs}/api/v1/internal/mcp/ws/${session.id}`,
          session_token: sessionToken,
          expires_at: new Date(Date.now() + 30 * 60 * 1000),
        });
      } catch (error) {
        fastify.log.error(error, 'Failed to create MCP session');
        reply.code(500).send({
          error: 'Failed to create MCP session',
          code: 'MCP_SESSION_ERROR',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  });

  // Get session status
  fastify.get('/mcp/session/:sessionId/status', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { sessionId } = request.params as { sessionId: string };
        const sessionManager = new MCPSessionManager(fastify);

        const session = await sessionManager.validateSession(sessionId);
        if (!session) {
          return reply.code(404).send({
            error: 'Session not found or expired',
            code: 'SESSION_NOT_FOUND',
          });
        }

        reply.send({
          session_id: session.id,
          status: session.status,
          created_at: session.created_at,
          last_activity: session.last_activity,
          auth_type: session.auth_type,
        });
      } catch (error) {
        fastify.log.error(error, 'Failed to get session status');
        reply.code(500).send({
          error: 'Failed to get session status',
          code: 'SESSION_STATUS_ERROR',
        });
      }
    },
  });

  // Get WebSocket connection information
  fastify.get('/mcp/session/:sessionId/websocket', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { sessionId } = request.params as { sessionId: string };
        const sessionManager = new MCPSessionManager(fastify);

        const session = await sessionManager.validateSession(sessionId);
        if (!session) {
          return reply.code(404).send({
            error: 'Session not found or expired',
            code: 'SESSION_NOT_FOUND',
          });
        }

        // Build the WebSocket URL
        const protocol = request.protocol === 'https' ? 'wss' : 'ws';
        const baseWs = process.env.PUBLIC_WS_BASE || `${protocol}://${request.hostname}`;
        const websocketUrl = `${baseWs}/api/v1/internal/mcp/ws/${sessionId}`;

        reply.send({
          session_id: sessionId,
          websocket_url: websocketUrl,
          websocket_ready: session.status === 'active',
          connection_instructions: {
            url: websocketUrl,
            headers: {
              Authorization: 'Bearer <session_token>',
            },
            protocol: 'JSON-RPC 2.0',
            note: 'Use the session_token from session initialization for WebSocket authentication',
          },
        });
      } catch (error) {
        fastify.log.error(error, 'Failed to get WebSocket connection info');
        reply.code(500).send({
          error: 'Failed to get WebSocket connection info',
          code: 'WEBSOCKET_INFO_ERROR',
        });
      }
    },
  });

  // WebSocket upgrade for persistent MCP connection
  fastify.register(async function (fastify) {
    fastify.get('/mcp/ws/:sessionId', { websocket: true }, (connection, request) => {
      const sessionId = (request.params as any).sessionId;

      fastify.log.info(`MCP WebSocket connection established for session: ${sessionId}`);

      // Track WebSocket connection
      metricsCollector.incrementWebSocketCounter('active');
      metricsCollector.incrementWebSocketCounter('total');

      connection.on('message', async (message: any) => {
        // Track incoming message
        await metricsCollector.incrementWebSocketCounter('received');
        try {
          const jsonRpcMessage = JSON.parse(message.toString());
          const mcpServer = new MCPServer(fastify);
          const response = await mcpServer.handleJsonRpcMessage(sessionId, jsonRpcMessage);
          connection.send(JSON.stringify(response));

          // Track outgoing message
          await metricsCollector.incrementWebSocketCounter('sent');
        } catch (error) {
          fastify.log.error(error, 'MCP message handling error');

          let errorResponse;
          try {
            const jsonRpcMessage = JSON.parse(message.toString());
            errorResponse = {
              jsonrpc: '2.0',
              error: { code: -32700, message: 'Parse error' },
              id: jsonRpcMessage?.id || null,
            };
          } catch {
            errorResponse = {
              jsonrpc: '2.0',
              error: { code: -32700, message: 'Parse error' },
              id: null,
            };
          }

          connection.send(JSON.stringify(errorResponse));
        }
      });

      connection.on('close', () => {
        fastify.log.info(`MCP WebSocket connection closed for session: ${sessionId}`);
        // Track WebSocket disconnection
        metricsCollector.decrementWebSocketActive();
      });

      connection.on('error', (error: any) => {
        fastify.log.error(error, `MCP WebSocket error for session: ${sessionId}`);
      });
    });
  });
}
