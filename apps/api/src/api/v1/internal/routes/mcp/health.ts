import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { MC<PERSON><PERSON>ealth<PERSON><PERSON><PERSON>, MCPMetricsCollector } from '../../../../../core/mcp';

export default async function mcpHealthRoutes(fastify: FastifyInstance): Promise<void> {
  // Health check endpoint
  fastify.get('/mcp/health', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const healthChecker = new MCPHealthChecker(fastify);
      const health = await healthChecker.checkOverallHealth();

      const statusCode = health.status === 'healthy' ? 200 : 503;
      reply.code(statusCode).send(health);
    } catch (error) {
      fastify.log.error(error, 'Health check failed');
      reply.code(503).send({
        status: 'unhealthy',
        message: 'Health check failed',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Live check endpoint
  fastify.get('/mcp/health/live', async (request: FastifyRequest, reply: FastifyReply) => {
    reply.send({
      status: 'alive',
      timestamp: new Date().toISOString(),
    });
  });

  // Readiness check endpoint
  fastify.get('/mcp/health/ready', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const healthChecker = new MCPHealthChecker(fastify);
      const [redisHealth, dbHealth, sessionHealth] = await Promise.all([
        healthChecker.checkRedisHealth(),
        healthChecker.checkDatabaseHealth(),
        healthChecker.checkSessionStoreHealth(),
      ]);

      const allHealthy = [redisHealth, dbHealth, sessionHealth].every(h => h.status === 'healthy');
      const statusCode = allHealthy ? 200 : 503;

      reply.code(statusCode).send({
        status: allHealthy ? 'ready' : 'not_ready',
        timestamp: new Date().toISOString(),
        checks: {
          redis: redisHealth,
          database: dbHealth,
          sessions: sessionHealth,
        },
      });
    } catch (error) {
      fastify.log.error(error, 'Readiness check failed');
      reply.code(503).send({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: 'Readiness check failed',
      });
    }
  });

  // Individual component health checks
  fastify.get('/mcp/health/redis', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const healthChecker = new MCPHealthChecker(fastify);
      const health = await healthChecker.checkRedisHealth();
      const statusCode = health.status === 'healthy' ? 200 : 503;
      reply.code(statusCode).send(health);
    } catch (error) {
      fastify.log.error(error, 'Redis health check failed');
      reply.code(503).send({
        status: 'unhealthy',
        component: 'redis',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      });
    }
  });

  fastify.get('/mcp/health/database', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const healthChecker = new MCPHealthChecker(fastify);
      const health = await healthChecker.checkDatabaseHealth();
      const statusCode = health.status === 'healthy' ? 200 : 503;
      reply.code(statusCode).send(health);
    } catch (error) {
      fastify.log.error(error, 'Database health check failed');
      reply.code(503).send({
        status: 'unhealthy',
        component: 'database',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      });
    }
  });

  fastify.get('/mcp/health/sessions', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const healthChecker = new MCPHealthChecker(fastify);
      const health = await healthChecker.checkSessionStoreHealth();
      const statusCode = health.status === 'healthy' ? 200 : 503;
      reply.code(statusCode).send(health);
    } catch (error) {
      fastify.log.error(error, 'Session store health check failed');
      reply.code(503).send({
        status: 'unhealthy',
        component: 'session_store',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      });
    }
  });

  // Metrics endpoint (moved to separate endpoint for clarity)
  fastify.get('/mcp/metrics', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metricsCollector = new MCPMetricsCollector(fastify);
      const metrics = await metricsCollector.getAllMetrics();
      reply.send({
        timestamp: new Date().toISOString(),
        ...metrics,
      });
    } catch (error) {
      fastify.log.error(error, 'Failed to collect metrics');
      reply.code(500).send({
        error: 'Failed to collect metrics',
        timestamp: new Date().toISOString(),
      });
    }
  });
}
