import { FastifyReply, FastifyRequest } from 'fastify';
import { OrganizationService } from '../services/organization';

export class OrganizationController {
  private organizationService: OrganizationService;

  constructor(organizationService: OrganizationService) {
    this.organizationService = organizationService;
  }

  async getOrganizationById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    try {
      const organization = await this.organizationService.getOrganizationById(id);

      if (!organization) {
        return reply.code(404).send({ error: 'Organization not found' });
      }

      return reply.code(200).send(organization);
    } catch (error) {
      console.error('Error fetching organization:', error);
      return reply.code(500).send({ error: 'Internal server error' });
    }
  }

  async createOrganization(request: FastifyRequest, reply: FastifyReply) {
    const data = request.body;
    try {
      const newOrganization = await this.organizationService.createOrganization(data);
      return reply.code(201).send(newOrganization);
    } catch (error) {
      console.error('Error creating organization:', error);
      return reply.code(500).send({ error: 'Internal server error' });
    }
  }

  async updateOrganization(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };
    const data = request.body;

    try {
      const updatedOrganization = await this.organizationService.updateOrganization(id, data);

      if (!updatedOrganization) {
        return reply.code(404).send({ error: 'Organization not found' });
      }

      return reply.code(200).send(updatedOrganization);
    } catch (error) {
      console.error('Error updating organization:', error);
      return reply.code(500).send({ error: 'Internal server error' });
    }
  }

  async deleteOrganization(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    try {
      // Before deleting, it's good practice to check if the organization exists and is accessible by the tenant
      const existingOrganization = await this.organizationService.getOrganizationById(id);

      if (!existingOrganization) {
        return reply.code(404).send({ error: 'Organization not found' });
      }

      await this.organizationService.deleteOrganization(id);
      return reply.code(204).send();
    } catch (error) {
      console.error('Error deleting organization:', error);
      return reply.code(500).send({ error: 'Internal server error' });
    }
  }
}
