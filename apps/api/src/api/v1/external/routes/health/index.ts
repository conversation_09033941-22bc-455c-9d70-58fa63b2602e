import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';

/**
 * Health routes for external API
 * These routes provide health status information for external API consumers
 */
export default async function healthRoutes(fastify: FastifyInstance): Promise<void> {
  // Basic health check endpoint
  fastify.get('/health', {
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Basic health check - just return API status
        const health = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          service: 'anter-external-api',
          version: process.env.npm_package_version || '1.0.0',
          uptime: process.uptime(),
        };

        reply.code(200).send(health);
      } catch (error) {
        fastify.log.error(error);
        reply.code(503).send({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          service: 'anter-external-api',
          error: 'Health check failed',
        });
      }
    },
  });

  // Detailed health check with component status
  fastify.get('/health/detailed', {
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Check database connectivity
        let dbStatus = 'healthy';
        try {
          await fastify.db.execute('SELECT 1');
        } catch (error) {
          dbStatus = 'unhealthy';
          fastify.log.error('Database health check failed:', error);
        }

        // Check Redis connectivity if available
        let redisStatus = 'healthy';
        try {
          if (fastify.redis) {
            await fastify.redis.ping();
          }
        } catch (error) {
          redisStatus = 'unhealthy';
          fastify.log.error('Redis health check failed:', error);
        }

        const components = {
          database: { status: dbStatus },
          redis: { status: redisStatus },
        };

        const allHealthy = Object.values(components).every(c => c.status === 'healthy');
        const status = allHealthy ? 'healthy' : 'degraded';

        const health = {
          status,
          timestamp: new Date().toISOString(),
          service: 'anter-external-api',
          version: process.env.npm_package_version || '1.0.0',
          uptime: process.uptime(),
          summary: {
            total: Object.keys(components).length,
            healthy: Object.values(components).filter(c => c.status === 'healthy').length,
          },
          components,
        };

        const statusCode = allHealthy ? 200 : 503;
        reply.code(statusCode).send(health);
      } catch (error) {
        fastify.log.error(error);
        reply.code(503).send({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          service: 'anter-external-api',
          error: 'Health check failed',
        });
      }
    },
  });
}
