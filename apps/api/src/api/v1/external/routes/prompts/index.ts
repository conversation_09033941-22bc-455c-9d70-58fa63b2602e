/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access */
// Routes for Langfuse prompt management

import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { Langfuse } from 'langfuse';
import { PromptMinimalResponse, CreatePromptBody } from '@anter/shared-services';

/**
 * Returns a singleton Langfuse client instance initialised from environment variables.
 */
function getLangfuseClient(fastify: FastifyInstance): Langfuse | null {
  const globalKey = '__anter_langfuse_client';
  const g: any = global;
  if (g[globalKey]) {
    return g[globalKey] as Langfuse;
  }

  const {
    LANGFUSE_PUBLIC_KEY: publicKey,
    LANGFUSE_SECRET_KEY: secretKey,
    LANGFUSE_BASEURL: baseUrl,
    LANGFUSE_SAMPLE_RATE,
    LANGFUSE_TRACING_ENABLED,
  } = fastify.env;

  const sampleRate = parseFloat(LANGFUSE_SAMPLE_RATE || '1');
  const tracingEnabled = LANGFUSE_TRACING_ENABLED !== 'false';
  if (!publicKey || !secretKey || !tracingEnabled) {
    return null;
  }

  const client = new Langfuse({
    publicKey,
    secretKey,
    baseUrl,
    sampleRate: Math.max(0, Math.min(1, sampleRate)),
    enabled: tracingEnabled,
    requestTimeout: 10000,
    flushAt: 10,
    flushInterval: 5000,
  });
  g[globalKey] = client;
  return client;
}

function isCreatePromptBody(obj: unknown): obj is CreatePromptBody {
  if (typeof obj !== 'object' || obj === null) return false;

  const name = (obj as any).name;
  const prompt = (obj as any).prompt;
  const type = (obj as any).type;
  const config = (obj as any).config;
  const tags = (obj as any).tags;

  // Quick validation of required fields
  const hasValidName = typeof name === 'string' && name.trim() !== '';
  const hasValidPrompt = prompt && (typeof prompt === 'string' || Array.isArray(prompt));

  if (!hasValidName || !hasValidPrompt) return false;

  // Quick validation of optional fields
  const hasValidType = type === undefined || type === 'text' || type === 'chat';
  const hasValidConfig = config === undefined || typeof config === 'object';
  const hasValidTags =
    tags === undefined || (Array.isArray(tags) && tags.every(tag => typeof tag === 'string'));

  return hasValidType && hasValidConfig && hasValidTags;
}

export default async function promptsRoutes(fastify: FastifyInstance): Promise<void> {
  /**
   * GET /prompts
   * Returns minimal prompt information from Langfuse
   * Query params:
   *   - name (required)
   *   - version (optional number)
   *   - label (optional string)
   */
  fastify.get('/prompts', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { name, version, label } = request.query as {
          name?: string;
          version?: string | number;
          label?: string;
        };

        if (!name) {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_NAME',
            message: 'Query parameter "name" is required',
          });
          return;
        }

        const langfuseClient = getLangfuseClient(fastify);

        fastify.log.info('Langfuse client', { langfuseClient });

        if (!langfuseClient) {
          reply.code(503).send({
            error: 'Service Unavailable',
            code: 'LANGFUSE_DISABLED',
            message: 'Langfuse is not configured or disabled',
          });
          return;
        }

        const prompt = await langfuseClient.getPrompt(name, version ? Number(version) : undefined, {
          label,
        });

        if (!prompt) {
          reply.code(404).send({
            error: 'Not Found',
            code: 'PROMPT_NOT_FOUND',
            message: `Prompt with name "${name}" not found`,
          });
          return;
        }

        const response: PromptMinimalResponse = {
          name: prompt.name,
          version: prompt.version,
          tags: (prompt as any).tags || (prompt as any).labels || [],
          prompt: prompt.prompt as any,
        };

        reply.code(200).send(response);
      } catch (error) {
        fastify.log.error('Failed to retrieve prompt', { error });
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'PROMPT_RETRIEVAL_FAILED',
          message: 'Failed to retrieve prompt',
        });
      }
    },
  });

  /**
   * POST /prompts
   * Create a new prompt in Langfuse.
   * Body should conform to CreatePromptBody interface.
   */
  fastify.post('/prompts', {
    config: {
      bodyLimit: 1048576, // 1MB limit for large prompts
    },
    schema: {
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          type: { type: 'string', enum: ['text', 'chat'] },
          prompt: { type: 'string' },
          config: { type: 'object' },
          tags: { type: 'array', items: { type: 'string' } },
        },
        required: ['name', 'prompt'],
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const bodyRaw = request.body;
        if (!isCreatePromptBody(bodyRaw)) {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'INVALID_BODY',
            message: 'Request body must be a valid JSON object with required fields',
          });
          return;
        }

        const body = bodyRaw;

        // Validate required fields
        if (!body.name || typeof body.name !== 'string') {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_NAME',
            message: 'Field "name" is required and must be a string',
          });
          return;
        }

        if (!body.prompt) {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_PROMPT',
            message: 'Field "prompt" is required',
          });
          return;
        }

        const langfuseClient = getLangfuseClient(fastify);
        if (!langfuseClient) {
          reply.code(503).send({
            error: 'Service Unavailable',
            code: 'LANGFUSE_DISABLED',
            message: 'Langfuse is not configured or disabled',
          });
          return;
        }

        // Prepare the create body with only required fields for Langfuse
        const createBody: any = {
          // Changed from CreateTextPromptBody to any
          name: body.name,
          prompt: body.prompt,
          type: body.type || 'text', // Default to 'text' if not provided
          labels: body.tags || ['production', 'latest'],
        };

        // Only add optional fields if they have meaningful values
        if (body.config && Object.keys(body.config).length > 0) {
          createBody.config = body.config;
        }

        if (body.tags && Array.isArray(body.tags) && body.tags.length > 0) {
          createBody.tags = body.tags;
        }

        const createdPrompt = await langfuseClient.createPrompt(createBody);

        const response: PromptMinimalResponse = {
          name: createdPrompt.name,
          version: createdPrompt.version,
          tags: (createdPrompt as any).tags || (createdPrompt as any).labels || [],
          prompt: createdPrompt.prompt as any,
        };
        reply.code(201).send(response);
      } catch (error) {
        fastify.log.error('Failed to create prompt', { error });
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'PROMPT_CREATION_FAILED',
          message: 'Failed to create prompt',
        });
      }
    },
  });
}
