import { FastifyInstance } from 'fastify';
import { FastifyRequest, FastifyReply } from 'fastify';
import { UnifiedTokenService } from '../../../../../core/auth/jwt/unified-token-service';

/**
 * Auth routes for external API
 * These routes bypass API key authentication and require internal secret
 */
export default async function authRoutes(fastify: FastifyInstance): Promise<void> {
  // Echo route - simply returns the request body
  fastify.post('/verify/echo', {
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      reply.code(200).send({
        message: 'Echo route',
        requestBody: request.body,
        headers: request.headers,
      });
    },
  });

  // JWT token generation endpoint
  fastify.post('/token', {
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const tokenService = new UnifiedTokenService(fastify);
      await tokenService.handleExternalTokenRequest(request, reply);
    },
  });

  // JWT token verification endpoint
  fastify.get('/verify', {
    schema: {
      tags: ['Authentication'],
      summary: 'Verify JWT token',
      description: 'Verify the validity of a JWT token and return token information',
      security: [
        {
          bearerAuth: [],
        },
      ],
      response: {
        200: {
          type: 'object',
          properties: {
            valid: { type: 'boolean' },
            token_info: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                organization_id: { type: 'string' },
                type: { type: 'string' },
                scope: { type: 'string' },
                iat: { type: 'number' },
                exp: { type: 'number' },
              },
            },
            expires_in: { type: 'number' },
            message: { type: 'string' },
          },
        },
        401: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Get the Authorization header
        const authHeader = request.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return reply.code(401).send({
            error: 'Unauthorized',
            message: 'Missing or invalid Authorization header. Use format: Bearer <token>',
            code: 'MISSING_AUTHORIZATION_HEADER',
          });
        }

        // Extract the token
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        // Verify the token using the unified token service
        const tokenService = new UnifiedTokenService(fastify);
        const decoded = await tokenService.validateToken(token, 'external-api');

        // Calculate time until expiration
        const now = Math.floor(Date.now() / 1000);
        const expiresIn = (decoded as any).exp - now;

        // Check if token is expired
        if (expiresIn <= 0) {
          return reply.code(401).send({
            error: 'Unauthorized',
            message: 'Token has expired',
            code: 'TOKEN_EXPIRED',
          });
        }

        // Return token information
        reply.code(200).send({
          valid: true,
          token_info: {
            id: (decoded as any).id,
            email: (decoded as any).email,
            organization_id: (decoded as any).organization_id,
            type: (decoded as any).type,
            scope: (decoded as any).scope,
            iat: (decoded as any).iat,
            exp: (decoded as any).exp,
          },
          expires_in: expiresIn,
          message: 'Token is valid',
        });
      } catch (error) {
        fastify.log.error(
          {
            error: error instanceof Error ? error.message : 'Unknown error',
            url: request.url,
          },
          'Token verification failed'
        );

        reply.code(401).send({
          error: 'Unauthorized',
          message: 'Invalid or malformed token',
          code: 'INVALID_TOKEN',
        });
      }
    },
  });
}
