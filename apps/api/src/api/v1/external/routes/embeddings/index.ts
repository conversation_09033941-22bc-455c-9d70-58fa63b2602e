import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import {
  OrganizationDataViewer,
  KeyMetadata,
} from '../../../../../core/embeddings/organization-data-viewer';
import { MCPConnectionManager } from '@anter/agents';
import {
  GetEmbeddingsSchema,
  UpdateEmbeddingsSchema,
  SubmitEmbeddingsSchema,
} from '../../schemas/embeddings';

/**
 * Embeddings routes for external API
 * These routes provide access to organization embeddings data
 */
export default async function embeddingsRoutes(fastify: FastifyInstance): Promise<void> {
  // Get embeddings data for a specific organization
  fastify.get('/embeddings', {
    schema: GetEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const organizationIdHeader = request.headers['x-organization-id'];

        if (!organizationIdHeader || typeof organizationIdHeader !== 'string') {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_ORGANIZATION_ID',
            message: 'X-Organization-Id header is required and must be a string',
          });
          return;
        }

        const organizationId = organizationIdHeader;

        // Validate organization ID format
        // NOTE: This format is not applicable for all organizations, so we're skipping it for now
        // if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
        //   reply.code(400).send({
        //     error: 'Bad Request',
        //     code: 'INVALID_ORGANIZATION_ID',
        //     message: 'Invalid organization ID format. Expected format: org-{uuid}',
        //   });
        //   return;
        // }

        // Create viewer instance
        const viewer = new OrganizationDataViewer(organizationId, fastify.redis);

        // Get all keys for the organization
        const keys = await viewer.getAllKeys();
        const totalKeys = keys.contentHashes.length + keys.docCache.length + keys.embeddings.length;

        // Get detailed metadata for each key
        const contentHashes = await Promise.all(
          keys.contentHashes.map(key => viewer.getKeyMetadata(key))
        );

        const docCache = await Promise.all(keys.docCache.map(key => viewer.getKeyMetadata(key)));

        const embeddings = await Promise.all(
          keys.embeddings.map(key => viewer.getKeyMetadata(key))
        );

        // Calculate totals
        const totalSize = [...contentHashes, ...docCache, ...embeddings].reduce(
          (sum, key) => sum + key.size,
          0
        );

        // Group embeddings by document for better organization
        const documentGroups: Record<string, KeyMetadata[]> = {};
        embeddings.forEach((metadata: KeyMetadata) => {
          const docMatch = metadata.key.match(/embeddings:[^:]+:([^:]+)::\d+$/);
          const docId = docMatch ? docMatch[1] : 'unknown';

          if (!documentGroups[docId]) {
            documentGroups[docId] = [];
          }
          documentGroups[docId].push(metadata);
        });

        // Format document groups
        const formattedDocumentGroups = Object.entries(documentGroups).map(([docId, metadatas]) => {
          const totalDocSize = metadatas.reduce((sum, m) => sum + m.size, 0);
          const chunkCount = metadatas.length;

          return {
            documentId: docId,
            chunkCount,
            totalSize: totalDocSize,
            sizeFormatted: viewer.formatBytes(totalDocSize),
            chunks: metadatas,
          };
        });

        const response = {
          organizationId,
          summary: {
            contentHashes: keys.contentHashes.length,
            docCache: keys.docCache.length,
            embeddings: keys.embeddings.length,
            totalKeys,
            totalSize,
            totalSizeFormatted: viewer.formatBytes(totalSize),
          },
          data: {
            contentHashes,
            docCache,
            embeddings: formattedDocumentGroups,
          },
        };

        reply.code(200).send(response);
      } catch (error) {
        fastify.log.error('Error retrieving embeddings data', { error });
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_RETRIEVAL_FAILED',
          message: 'Failed to retrieve embeddings data',
        });
      }
    },
  });

  // Update embeddings for a specific organization
  fastify.post('/embeddings/update', {
    schema: UpdateEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const organizationIdHeader = request.headers['x-organization-id'];

        if (!organizationIdHeader || typeof organizationIdHeader !== 'string') {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_ORGANIZATION_ID',
            message: 'X-Organization-Id header is required and must be a string',
          });
          return;
        }

        const organizationId = organizationIdHeader;

        // Validate organization ID format
        if (!organizationId.match(/^org-[a-f0-9-]+$/)) {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'INVALID_ORGANIZATION_ID',
            message: 'Invalid organization ID format. Expected format: org-{uuid}',
          });
          return;
        }

        fastify.log.info('Starting embeddings update process', { organizationId });

        // Create MCP connection manager adapter for agents using Fastify's database decorators
        const connectionManagerAdapter = {
          dbWithTenant: (orgId: string) => fastify.dbWithTenant(orgId),
          dbInternalWithTenant: (orgId: string) => fastify.dbInternalWithTenant(orgId),
          dbBypassRLS: () => fastify.dbBypassRLS(),
          dbInternalBypassRLS: () => fastify.dbInternalBypassRLS(),
          getDatabaseConfig: (schema: 'internal' | 'external') => {
            return fastify.getDatabaseConfig(schema);
          },
          log: {
            debug: (message: string) => fastify.log.debug(message),
            info: (message: string) => fastify.log.info(message),
            warn: (message: string) => fastify.log.warn(message),
            error: (error: any, message: string) => fastify.log.error(error, message),
          },
          redis: fastify.redis,
        };

        // Create MCP connection manager
        const mcpConnectionManager = new MCPConnectionManager(connectionManagerAdapter);

        // Step 1: Retrieve all documents using MCP tool
        fastify.log.info('Retrieving documents from database', { organizationId });
        const documentsResult = await mcpConnectionManager.executeToolCall(
          organizationId,
          'get_all_internal_documents',
          {
            limit: 1000,
            offset: 0,
            filter: {
              status: 'active', // Only active documents
            },
            includeIdsOnly: false, // We need full document data
          }
        );

        if (!documentsResult.success || !documentsResult.data) {
          fastify.log.error('Failed to retrieve documents', {
            organizationId,
            error: documentsResult.error,
          });
          reply.code(500).send({
            error: 'Internal Server Error',
            code: 'DOCUMENT_RETRIEVAL_FAILED',
            message: 'Failed to retrieve documents from database',
            details: documentsResult.error,
          });
          return;
        }

        // Parse the documents from the MCP tool result
        let documents: any[] = [];
        try {
          // Debug: Log the raw response structure
          fastify.log.info('MCP tool response structure', {
            organizationId,
            hasData: !!documentsResult.data,
            dataType: typeof documentsResult.data,
            dataKeys: documentsResult.data ? Object.keys(documentsResult.data) : [],
            rawData: JSON.stringify(documentsResult.data, null, 2),
          });

          // The MCP bridge already parses the JSON response, so we can use it directly
          const parsedResult = documentsResult.data;
          if (!parsedResult || typeof parsedResult !== 'object') {
            throw new Error('MCP tool returned empty or invalid response');
          }

          // Comprehensive validation of parsed result
          if (!parsedResult || typeof parsedResult !== 'object') {
            throw new Error('Invalid JSON structure returned from MCP tool');
          }

          // Validate success field
          if (typeof parsedResult.success !== 'boolean') {
            throw new Error('Invalid success field in MCP tool response');
          }

          if (parsedResult.success) {
            // Validate documents array
            if (!Array.isArray(parsedResult.documents)) {
              throw new Error('Invalid documents field in MCP tool response');
            }

            // Validate each document has required fields
            for (const doc of parsedResult.documents) {
              if (!doc || typeof doc !== 'object') {
                throw new Error('Invalid document structure in MCP tool response');
              }
              if (!doc.id || typeof doc.id !== 'string') {
                throw new Error('Invalid document ID in MCP tool response');
              }
            }

            documents = parsedResult.documents;
          } else {
            throw new Error(`MCP tool returned error: ${parsedResult.error || 'Unknown error'}`);
          }
        } catch (parseError) {
          fastify.log.error('Failed to parse documents from MCP tool', {
            organizationId,
            error: parseError,
          });
          reply.code(500).send({
            error: 'Internal Server Error',
            code: 'DOCUMENT_PARSING_FAILED',
            message: 'Failed to parse documents from MCP tool',
            details: parseError instanceof Error ? parseError.message : 'Unknown parsing error',
          });
          return;
        }

        fastify.log.info('Retrieved documents from database', {
          organizationId,
          documentCount: documents.length,
        });

        // Step 2: Clear existing embeddings for this organization
        fastify.log.info('Clearing existing embeddings', { organizationId });
        const redisClient = (mcpConnectionManager as any).getRedisClient?.();
        if (redisClient) {
          try {
            // Clear all embeddings for this organization
            const pattern = `embeddings:${organizationId}:*`;
            const keys = await redisClient.keys(pattern);
            if (keys.length > 0) {
              await redisClient.del(...keys);
              fastify.log.info('Cleared existing embeddings', {
                organizationId,
                clearedKeys: keys.length,
              });
            }

            // Clear document cache for this organization
            const cachePattern = `doc_cache:${organizationId}`;
            await redisClient.del(cachePattern);
            fastify.log.info('Cleared document cache', { organizationId });

            // Clear content hashes for this organization
            const hashPattern = `content_hashes:${organizationId}`;
            await redisClient.del(hashPattern);
            fastify.log.info('Cleared content hashes', { organizationId });
          } catch (redisError) {
            fastify.log.warn('Failed to clear existing embeddings', {
              organizationId,
              error: redisError,
            });
            // Continue with the process even if clearing fails
          }
        }

        // Step 3: Process and index documents
        fastify.log.info('Processing and indexing documents', {
          organizationId,
          documentCount: documents.length,
        });

        // Import required services for document processing
        const {
          DocumentProcessor,
          DocumentSanitizerService,
          SemanticSearchServiceEnhanced,
          EmbeddingService,
          ContentHashService,
        } = require('@anter/mcp-tools');

        // Create services
        const sanitizer = new DocumentSanitizerService();
        const processor = new DocumentProcessor(sanitizer);
        const embeddingService = new EmbeddingService({ organizationId });
        const contentHashService = new ContentHashService();
        const semanticSearchService = new SemanticSearchServiceEnhanced(
          embeddingService,
          contentHashService,
          fastify.log,
          redisClient
        );

        // Process documents
        const processedDocuments = [];
        for (const doc of documents) {
          try {
            fastify.log.info('Processing document', {
              organizationId,
              documentId: doc.id,
              hasContent: !!doc.content,
              hasBufferFile: !!doc.buffer_file,
              fileType: doc.fileType || doc.file_type,
            });

            // Use the correct method for processing documents with buffer_file
            const processedChunks = await processor.processDocumentContent({
              id: doc.id,
              name: doc.name,
              content: doc.content,
              buffer_file: doc.buffer_file,
              fileType: doc.fileType || doc.file_type,
              file_type: doc.file_type,
              organization_id: doc.organization_id || organizationId,
              status: doc.status,
              created_at: doc.createdAt || doc.created_at,
              updated_at: doc.updatedAt || doc.updated_at,
            });

            // Filter out null chunks and add to processed documents
            const validChunks = processedChunks.filter((chunk: any) => chunk !== null);
            processedDocuments.push(...validChunks);

            fastify.log.info('Document processed successfully', {
              organizationId,
              documentId: doc.id,
              chunksProcessed: validChunks.length,
              totalChunks: processedChunks.length,
            });
          } catch (processError) {
            fastify.log.warn('Failed to process document', {
              organizationId,
              documentId: doc.id,
              error: processError,
            });
            // Continue with other documents
          }
        }

        fastify.log.info('Processed documents', {
          organizationId,
          processedCount: processedDocuments.length,
        });

        // Step 4: Index documents for semantic search
        if (processedDocuments.length > 0) {
          fastify.log.info('Indexing documents for semantic search', {
            organizationId,
            documentCount: processedDocuments.length,
          });

          try {
            await semanticSearchService.indexDocumentsIncremental(
              processedDocuments,
              organizationId,
              {
                redisClient,
                logger: fastify.log,
                maxTokens: 1500,
                overlapTokens: 150,
              }
            );

            fastify.log.info('Successfully indexed documents for semantic search', {
              organizationId,
              indexedCount: processedDocuments.length,
            });
          } catch (indexError) {
            fastify.log.error('Failed to index documents for semantic search', {
              organizationId,
              error: indexError,
            });
            reply.code(500).send({
              error: 'Internal Server Error',
              code: 'INDEXING_FAILED',
              message: 'Failed to index documents for semantic search',
              details: indexError instanceof Error ? indexError.message : 'Unknown indexing error',
            });
            return;
          }
        }

        // Step 5: Return success response
        const response = {
          success: true,
          organizationId,
          summary: {
            documentsRetrieved: documents.length,
            documentsProcessed: processedDocuments.length,
            documentsIndexed: processedDocuments.length,
            timestamp: new Date().toISOString(),
          },
          message: 'Embeddings updated successfully',
        };

        fastify.log.info('Embeddings update completed successfully', {
          organizationId,
          summary: response.summary,
        });

        reply.code(200).send(response);
      } catch (error) {
        fastify.log.error('Error updating embeddings', { error });
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_UPDATE_FAILED',
          message: 'Failed to update embeddings',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  });

  // Submit text documents for embedding generation
  fastify.post('/embeddings/submit', {
    schema: SubmitEmbeddingsSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const organizationIdHeader = request.headers['x-organization-id'];

        if (!organizationIdHeader || typeof organizationIdHeader !== 'string') {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'MISSING_ORGANIZATION_ID',
            message: 'X-Organization-Id header is required and must be a string',
          });
          return;
        }

        const organizationId = organizationIdHeader;

        // Parse and validate request body
        const requestBody = request.body as any;
        if (!requestBody || !requestBody.documents || !Array.isArray(requestBody.documents)) {
          reply.code(400).send({
            error: 'Bad Request',
            code: 'INVALID_REQUEST_BODY',
            message: 'Request body must contain a documents array',
          });
          return;
        }

        const { documents, options = {} } = requestBody;
        const { maxTokens = 1500, overlapTokens = 150, clearExisting = false } = options;

        fastify.log.info('Starting embeddings submit process', {
          organizationId,
          documentCount: documents.length,
          options: { maxTokens, overlapTokens, clearExisting },
        });

        // Validate documents
        for (const doc of documents) {
          if (!doc.id || !doc.name || !doc.content) {
            reply.code(400).send({
              error: 'Bad Request',
              code: 'INVALID_DOCUMENT',
              message: 'Each document must have id, name, and content fields',
            });
            return;
          }
          if (doc.content.trim().length < 20) {
            reply.code(400).send({
              error: 'Bad Request',
              code: 'INSUFFICIENT_CONTENT',
              message: `Document ${doc.id} has insufficient content (minimum 20 characters)`,
            });
            return;
          }
        }

        // Create MCP connection manager adapter for agents using Fastify's database decorators
        const connectionManagerAdapter = {
          dbWithTenant: (orgId: string) => fastify.dbWithTenant(orgId),
          dbInternalWithTenant: (orgId: string) => fastify.dbInternalWithTenant(orgId),
          dbBypassRLS: () => fastify.dbBypassRLS(),
          dbInternalBypassRLS: () => fastify.dbInternalBypassRLS(),
          getDatabaseConfig: (schema: 'internal' | 'external') => {
            return fastify.getDatabaseConfig(schema);
          },
          log: {
            debug: (message: string) => fastify.log.debug(message),
            info: (message: string) => fastify.log.info(message),
            warn: (message: string) => fastify.log.warn(message),
            error: (error: any, message: string) => fastify.log.error(error, message),
          },
          redis: fastify.redis,
        };

        // Create MCP connection manager
        const mcpConnectionManager = new MCPConnectionManager(connectionManagerAdapter);

        // Clear existing embeddings if requested
        if (clearExisting) {
          fastify.log.info('Clearing existing embeddings', { organizationId });
          const redisClient = (mcpConnectionManager as any).getRedisClient?.();
          if (redisClient) {
            try {
              // Clear all embeddings for this organization
              const pattern = `embeddings:${organizationId}:*`;
              const keys = await redisClient.keys(pattern);
              if (keys.length > 0) {
                await redisClient.del(...keys);
                fastify.log.info('Cleared existing embeddings', {
                  organizationId,
                  clearedKeys: keys.length,
                });
              }

              // Clear document cache for this organization
              const cachePattern = `doc_cache:${organizationId}`;
              await redisClient.del(cachePattern);

              // Clear content hashes for this organization
              const hashPattern = `content_hashes:${organizationId}`;
              await redisClient.del(hashPattern);
            } catch (redisError) {
              fastify.log.warn('Failed to clear existing embeddings', {
                organizationId,
                error: redisError,
              });
              // Continue with the process even if clearing fails
            }
          }
        }

        fastify.log.info('Processing and indexing submitted documents', {
          organizationId,
          documentCount: documents.length,
        });

        // Import required services for document processing
        const {
          SemanticSearchServiceEnhanced,
          EmbeddingService,
          ContentHashService,
        } = require('@anter/mcp-tools');

        // Create services
        const embeddingService = new EmbeddingService({ organizationId });
        const contentHashService = new ContentHashService();
        const redisClient = (mcpConnectionManager as any).getRedisClient?.();
        const semanticSearchService = new SemanticSearchServiceEnhanced(
          embeddingService,
          contentHashService,
          fastify.log,
          redisClient
        );

        // Convert submitted documents to ProcessedDocument format
        const processedDocuments = [];
        for (const doc of documents) {
          try {
            fastify.log.info('Processing submitted document', {
              organizationId,
              documentId: doc.id,
              contentLength: doc.content.length,
            });

            // Create a ProcessedDocument directly from the submitted content
            const processedDoc = {
              id: doc.id,
              name: doc.name,
              content: doc.content.trim(),
              metadata: {
                ...doc.metadata,
                organization_id: organizationId,
                source: 'api_submit',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              },
            };

            processedDocuments.push(processedDoc);

            fastify.log.info('Document processed successfully', {
              organizationId,
              documentId: doc.id,
              contentLength: processedDoc.content.length,
            });
          } catch (processError) {
            fastify.log.warn('Failed to process document', {
              organizationId,
              documentId: doc.id,
              error: processError,
            });
            // Continue with other documents
          }
        }

        fastify.log.info('Processed submitted documents', {
          organizationId,
          processedCount: processedDocuments.length,
        });

        // Index documents for semantic search
        let totalChunks = 0;
        let chunkedDocuments: string[] = [];

        if (processedDocuments.length > 0) {
          fastify.log.info('Indexing documents for semantic search', {
            organizationId,
            documentCount: processedDocuments.length,
          });

          try {
            const indexingResult = await semanticSearchService.indexDocumentsIncremental(
              processedDocuments,
              organizationId,
              {
                redisClient,
                logger: fastify.log,
                maxTokens,
                overlapTokens,
              }
            );

            totalChunks = indexingResult.totalChunks;
            chunkedDocuments = indexingResult.chunkedDocuments;

            fastify.log.info('Successfully indexed documents for semantic search', {
              organizationId,
              indexedCount: indexingResult.indexed,
              skippedCount: indexingResult.skipped,
              totalChunks,
              chunkedDocuments,
            });
          } catch (indexError) {
            fastify.log.error('Failed to index documents for semantic search', {
              organizationId,
              error: indexError,
            });
            reply.code(500).send({
              error: 'Internal Server Error',
              code: 'INDEXING_FAILED',
              message: 'Failed to index documents for semantic search',
              details: indexError instanceof Error ? indexError.message : 'Unknown indexing error',
            });
            return;
          }
        }

        // Return success response
        const response = {
          success: true,
          organizationId,
          summary: {
            documentsReceived: documents.length,
            documentsProcessed: processedDocuments.length,
            documentsIndexed: processedDocuments.length,
            totalChunks,
            chunkedDocuments,
            timestamp: new Date().toISOString(),
          },
          message: 'Documents submitted and embeddings generated successfully',
        };

        fastify.log.info('Embeddings submit completed successfully', {
          organizationId,
          summary: response.summary,
        });

        reply.code(200).send(response);
      } catch (error) {
        fastify.log.error('Error submitting embeddings', { error });
        reply.code(500).send({
          error: 'Internal Server Error',
          code: 'EMBEDDINGS_SUBMIT_FAILED',
          message: 'Failed to submit embeddings',
          details: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
  });
}
