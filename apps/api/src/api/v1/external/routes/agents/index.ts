import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { getActualAgentName } from '../../../../../config/agent-routing';
import {
  formatNonStreamingResponse,
  formatStreamingCompletionResponse,
  formatErrorResponse,
} from '../../../../../lib/utils/response-formatter';

export default async function externalAgentRoutes(fastify: FastifyInstance): Promise<void> {
  // POST /invoke - Invoke an agent with input
  fastify.post('/invoke', {
    schema: {
      tags: ['Agents'],
      summary: 'Invoke an agent',
      description: 'Invoke an AI agent with provided input and context',
      body: {
        type: 'object',
        required: ['agent_name', 'input'],
        properties: {
          agent_name: {
            type: 'string',
            description: 'Name of the agent to invoke',
            examples: ['echo', 'chat', 'security-analyst', 'ask_ai'],
          },
          input: {
            description: 'Input data for the agent (can be string, object, or any type)',
            // Allow any type - let the agent handle validation
          },
          context: {
            type: 'object',
            description: 'Optional context for the agent',
            additionalProperties: true,
          },
        },
        additionalProperties: false,
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            result: {
              type: 'object',
              additionalProperties: true,
            },
            metadata: {
              type: 'object',
              properties: {
                agent_name: { type: 'string' },
                original_agent_name: { type: 'string' },
                execution_time: { type: 'number' },
                timestamp: { type: 'string' },
              },
            },
          },
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            code: { type: 'string' },
          },
        },
        500: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const { agent_name, input, context } = request.body as {
        agent_name: string;
        input: any;
        context?: any;
      };

      const startTime = Date.now();
      console.log('[/agent/invoke] context:', context);

      // Validate session ID for conversation memory
      if (!context?.sessionId) {
        fastify.log.warn(
          'No sessionId provided in request context - conversation memory will not be available',
          {
            requestId: request.id,
            agentName: agent_name,
            hasContext: !!context,
            contextKeys: context ? Object.keys(context) : [],
          }
        );
      } else {
        fastify.log.info('Session ID provided for conversation memory', {
          requestId: request.id,
          sessionId: context.sessionId,
          agentName: agent_name,
        });
      }

      // Use routing configuration to determine which agent to use
      const actualAgentName = getActualAgentName(agent_name);

      if (actualAgentName !== agent_name) {
        fastify.log.info(`Agent routing: ${agent_name} -> ${actualAgentName}`);
      }

      try {
        // Use the agents system from the plugin
        const result = await fastify.agents.invoke(actualAgentName, input, {
          sessionId: context?.sessionId,
          organizationId: context?.organizationId,
          userId: context?.userId,
          metadata: {
            ...context?.metadata,
            requestId: request.id,
            originalAgentName: agent_name,
            actualAgentName: actualAgentName,
          },
        });

        const executionTime = Date.now() - startTime;

        return formatNonStreamingResponse(result, executionTime, actualAgentName, request.id);
      } catch (error: any) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorStack = error instanceof Error ? error.stack : undefined;

        // Log error details in a more visible format
        fastify.log.error(`Agent invocation failed: ${errorMessage}`);
        if (errorStack) {
          fastify.log.error(`Error stack: ${errorStack}`);
        }

        // Also log structured data for debugging
        fastify.log.error('Agent invocation failed:', {
          error: errorMessage,
          stack: errorStack,
          agentName: agent_name,
          actualAgentName,
          requestId: request.id,
        });

        if (error.message.includes('not found') || error.message.includes('Unknown agent')) {
          return reply.code(400).send({
            error: `Agent '${(request.body as any)?.agent_name}' not found`,
            code: 'AGENT_NOT_FOUND',
          });
        }

        return reply.code(500).send({
          error: 'Agent invocation failed',
          code: 'AGENT_INVOCATION_ERROR',
        });
      }
    },
  });

  // POST /stream - Stream agent responses using Server-Sent Events
  fastify.post('/stream', {
    schema: {
      tags: ['Agents'],
      summary: 'Stream agent responses',
      description: 'Stream AI agent responses using Server-Sent Events (SSE)',
      body: {
        type: 'object',
        required: ['agent_name', 'input'],
        properties: {
          agent_name: {
            type: 'string',
            description: 'Name of the agent to invoke',
            examples: ['ask_ai', 'echo', 'chat'],
          },
          input: {
            description: 'Input data for the agent (can be string, object, or any type)',
          },
          context: {
            type: 'object',
            description: 'Optional context for the agent',
            additionalProperties: true,
          },
        },
        additionalProperties: false,
      },
    },
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const { agent_name, input, context } = request.body as {
        agent_name: string;
        input: any;
        context?: any;
      };

      // Validate session ID for conversation memory
      if (!context?.sessionId) {
        fastify.log.warn(
          'No sessionId provided in request context - conversation memory will not be available',
          {
            requestId: request.id,
            agentName: agent_name,
            hasContext: !!context,
            contextKeys: context ? Object.keys(context) : [],
          }
        );
      } else {
        fastify.log.info('Session ID provided for conversation memory', {
          requestId: request.id,
          sessionId: context.sessionId,
          agentName: agent_name,
        });
      }

      // Resolve to the streaming-capable agent variant
      const actualAgentName = agent_name === 'ask_ai' ? 'ask_ai_streaming' : agent_name;

      // Check if agent supports streaming
      const agent = await fastify.agents.create(actualAgentName, ['streaming']);
      if (!agent) {
        return reply.code(400).send({
          error: `Agent '${actualAgentName}' not found`,
          code: 'AGENT_NOT_FOUND',
        });
      }

      if (!agent.capabilities.includes('streaming')) {
        return reply.code(400).send({
          error: `Agent '${agent_name}' does not support streaming`,
          code: 'STREAMING_NOT_SUPPORTED',
        });
      }

      // Set enhanced SSE headers with security considerations
      reply.raw.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
        Connection: 'keep-alive',
        'X-Request-ID': request.id,
        'X-Trace-ID': request.id,
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Max-Age': '86400',
      });

      // Send initial connection established event
      reply.raw.write(
        `data: ${JSON.stringify({
          type: 'connection_established',
          timestamp: new Date().toISOString(),
          requestId: request.id,
          agentName: actualAgentName,
          capabilities: agent.capabilities,
        })}\n\n`
      );

      // Declare variables outside try block for use in catch block
      const startTime = Date.now();
      let fullResponse = '';
      let searchResults: any = null;
      let confidenceData: any = null;
      let finalMetadata: any = null;
      let chunkCount = 0;
      let lastChunkTime = Date.now();

      try {
        // Construct proper AgentInput structure
        const agentInput = {
          input: input,
          sessionId: context?.sessionId,
          organizationId: context?.organizationId,
          userId: context?.userId,
          traceId: request.id,
          metadata: {
            ...context?.metadata,
            requestId: request.id,
            timestamp: new Date().toISOString(),
            userAgent: request.headers['user-agent'],
            ipAddress: request.ip,
          },
        };

        // Stream the agent response with timeout protection
        fastify.log.info(`Starting streaming for request ${request.id}`, {
          requestId: request.id,
          agentName: actualAgentName,
          startTime: new Date().toISOString(),
        });

        let hasReceivedChunks = false;
        for await (const chunk of agent.stream(agentInput)) {
          const currentTime = Date.now();
          hasReceivedChunks = true;

          // Check for connection timeout (60 seconds without data)
          if (currentTime - lastChunkTime > 60000) {
            fastify.log.warn(`Streaming timeout for request ${request.id}`);
            reply.raw.write(
              `data: ${JSON.stringify({
                type: 'error',
                content: 'Connection timeout - please try again',
                timestamp: new Date().toISOString(),
              })}\n\n`
            );
            break;
          }

          lastChunkTime = currentTime;
          chunkCount++;

          const eventData = {
            type: chunk.type,
            content: chunk.content,
            metadata: {
              ...chunk.metadata,
              chunkIndex: chunkCount,
              elapsedMs: currentTime - startTime,
            },
            timestamp: new Date().toISOString(),
          };

          fastify.log.info(`Sending chunk ${chunkCount} for request ${request.id}`, {
            requestId: request.id,
            chunkType: chunk.type,
            chunkIndex: chunkCount,
            elapsedMs: currentTime - startTime,
            hasContent: !!chunk.content,
          });

          // Accumulate data for final response
          switch (chunk.type) {
            case 'content':
              fullResponse += chunk.content;
              break;
            case 'metadata':
              if (chunk.content?.searchResults) {
                searchResults = chunk.content.searchResults;
              }
              if (chunk.content?.confidence) {
                confidenceData = chunk.content.confidence;
              }
              if (chunk.content?.finalResult) {
                finalMetadata = chunk.content.finalResult;
              }
              break;
            case 'complete':
              // Agent has signaled completion - this is the final chunk
              fastify.log.info(`Agent completion chunk received for request ${request.id}`, {
                requestId: request.id,
                chunkIndex: chunkCount,
                metadata: JSON.stringify(chunk.metadata, null, 2),
              });
              console.log('DEBUG - Completion chunk metadata:', {
                requestId: request.id,
                chunkIndex: chunkCount,
                metadata: chunk.metadata,
              });

              // Send the completion chunk as-is (agent already formatted it)
              reply.raw.write(`data: ${JSON.stringify(eventData)}\n\n`);
              reply.raw.end();
              return;
            case 'error':
              fastify.log.error(`Error chunk received for request ${request.id}`, {
                requestId: request.id,
                errorContent: chunk.content,
                chunkIndex: chunkCount,
              });

              // Send error event
              reply.raw.write(
                `data: ${JSON.stringify({
                  type: 'error',
                  content: chunk.content,
                  metadata: chunk.metadata,
                  timestamp: new Date().toISOString(),
                })}\n\n`
              );

              // Send completion event
              reply.raw.write(
                `data: ${JSON.stringify({
                  type: 'complete',
                  content: '',
                  metadata: {
                    error: true,
                    executionTimeMs: Date.now() - startTime,
                    totalChunks: chunkCount,
                  },
                  timestamp: new Date().toISOString(),
                  sources: chunk.metadata?.sources,
                })}\n\n`
              );

              reply.raw.end();
              return;

            default:
              fastify.log.warn(`Unknown chunk type for request ${request.id}`, {
                requestId: request.id,
                chunkType: chunk.type,
                chunkIndex: chunkCount,
              });
              break;
          }

          // Send the chunk with rate limiting (max 100 chunks per second)
          if (chunkCount % 100 === 0) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }

          reply.raw.write(`data: ${JSON.stringify(eventData)}\n\n`);
        }

        fastify.log.info(`Streaming loop completed for request ${request.id}`, {
          requestId: request.id,
          totalChunks: chunkCount,
          hasReceivedChunks,
          elapsedMs: Date.now() - startTime,
        });

        // Ensure we always send a completion signal, even if no chunks were received
        if (!hasReceivedChunks) {
          fastify.log.warn(
            `No chunks received for request ${request.id}, sending empty completion`,
            {
              requestId: request.id,
              elapsedMs: Date.now() - startTime,
            }
          );

          const emptyCompletionData = formatStreamingCompletionResponse({
            fullResponse: '',
            searchResults: null,
            confidenceData: null,
            finalMetadata: null,
            executionTimeMs: Date.now() - startTime,
            totalChunks: 0,
            averageChunkTimeMs: 0,
            agentName: actualAgentName,
            requestId: request.id,
            hasReceivedChunks: false,
          });

          fastify.log.info(`Sending empty completion event for request ${request.id}`, {
            requestId: request.id,
            totalChunks: 0,
            hasReceivedChunks: false,
          });

          reply.raw.write(`data: ${JSON.stringify(emptyCompletionData)}\n\n`);
          reply.raw.end();

          fastify.log.info(`Empty streaming completed for request ${request.id}`, {
            requestId: request.id,
            finalChunkCount: 0,
            finalExecutionTime: Date.now() - startTime,
          });
          return;
        }

        // Send completion event with consistent format
        const completionData = formatStreamingCompletionResponse({
          fullResponse,
          searchResults,
          confidenceData,
          finalMetadata,
          executionTimeMs: Date.now() - startTime,
          totalChunks: chunkCount,
          averageChunkTimeMs: chunkCount > 0 ? (Date.now() - startTime) / chunkCount : 0,
          agentName: actualAgentName,
          requestId: request.id,
          hasReceivedChunks,
        });

        fastify.log.info(`Sending completion event for request ${request.id}`, {
          requestId: request.id,
          totalChunks: chunkCount,
          hasReceivedChunks,
          executionTimeMs: Date.now() - startTime,
          hasFullResponse: !!fullResponse,
          hasSearchResults: !!searchResults,
        });

        reply.raw.write(`data: ${JSON.stringify(completionData)}\n\n`);
        reply.raw.end();

        fastify.log.info(`Streaming completed successfully for request ${request.id}`, {
          requestId: request.id,
          finalChunkCount: chunkCount,
          finalExecutionTime: Date.now() - startTime,
        });
      } catch (error: any) {
        fastify.log.error('Agent streaming failed:', error, {
          requestId: request.id,
          agentName: actualAgentName,
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack,
          elapsedMs: Date.now() - startTime,
          totalChunks: chunkCount,
        });

        // Send error event with detailed error information
        const errorData = {
          type: 'error',
          content: error.message || 'An error occurred during streaming',
          metadata: {
            errorType: error.name || 'UNKNOWN_ERROR',
            errorCode: error.code || 'STREAMING_ERROR',
            elapsedMs: Date.now() - startTime,
            requestId: request.id,
            totalChunks: chunkCount,
          },
          timestamp: new Date().toISOString(),
        };

        fastify.log.info(`Sending error event for request ${request.id}`, {
          requestId: request.id,
          errorType: errorData.metadata.errorType,
          errorCode: errorData.metadata.errorCode,
        });

        reply.raw.write(`data: ${JSON.stringify(errorData)}\n\n`);

        // Send completion event with error information
        const errorCompletionData = formatErrorResponse(
          error,
          Date.now() - startTime,
          agent_name,
          request.id,
          chunkCount
        );

        fastify.log.info(`Sending error completion event for request ${request.id}`, {
          requestId: request.id,
          errorMessage: errorCompletionData.metadata.error_message,
        });

        reply.raw.write(`data: ${JSON.stringify(errorCompletionData)}\n\n`);
        reply.raw.end();

        fastify.log.info(`Error streaming completed for request ${request.id}`, {
          requestId: request.id,
          finalChunkCount: chunkCount,
          finalExecutionTime: Date.now() - startTime,
        });
      }
    },
  });
}
