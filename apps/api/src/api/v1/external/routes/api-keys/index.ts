import { FastifyInstance } from 'fastify';
import { FastifyRequest, FastifyReply } from 'fastify';
import { CreateApiKeySchema } from '../../schemas/api-keys';
import { createOrganizationApiKey } from '../../controllers/api-keys';

/**
 * Auth routes for external API
 * These routes bypass API key authentication
 */
export default async function apiKeyRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.post(
    '/key/create',
    {
      schema: CreateApiKeySchema,
      attachValidation: true,
      // Authentication handled by dual auth middleware
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      if (request.validationError) {
        return reply.code(400).send({
          error: 'Invalid API key request',
          code: 'API_KEY_VALIDATION_ERROR',
          details: request.validationError.validation,
        });
      }

      const { organizationId } = request.body as { organizationId: string };

      try {
        const apiKey = await createOrganizationApiKey(fastify, {
          organizationId,
        });

        return reply.code(201).send(apiKey);
      } catch (error) {
        fastify.log.error(error, 'Failed to create API key');
        return reply.code(500).send({
          error: 'Failed to create API key',
          code: 'API_KEY_CREATION_ERROR',
        });
      }
    }
  );
}
