import { FastifySchema } from 'fastify';

// Schema for API key response
export const apiKeyResponseSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
    key: { type: 'string' },
    organizationId: { type: 'string', format: 'uuid' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    expiresAt: { type: 'string', format: 'date-time', nullable: true },
  },
};

// Error response schema
export const errorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
    details: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          message: { type: 'string' },
          path: { type: 'string' },
        },
      },
      nullable: true,
    },
  },
};

// Schema for creating API keys
export const CreateApiKeySchema: FastifySchema = {
  body: {
    type: 'object',
    required: ['organizationId'],
    properties: {
      organizationId: { type: 'string', format: 'uuid' },
    },
  },
  response: {
    201: apiKeyResponseSchema,
    400: errorResponseSchema,
    500: errorResponseSchema,
  },
};

// TypeScript interface for API key
export interface ApiKey {
  id: string;
  key: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string | null;
}
