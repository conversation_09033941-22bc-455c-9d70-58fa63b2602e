import { FastifySchema } from 'fastify';

// Update summary schema
export const updateSummarySchema = {
  type: 'object',
  properties: {
    documentsRetrieved: { type: 'number' },
    documentsProcessed: { type: 'number' },
    documentsIndexed: { type: 'number' },
    timestamp: { type: 'string', format: 'date-time' },
  },
  required: ['documentsRetrieved', 'documentsProcessed', 'documentsIndexed', 'timestamp'],
};

// Success response schema for update
export const updateEmbeddingsResponseSchema = {
  type: 'object',
  properties: {
    success: { type: 'boolean' },
    organizationId: { type: 'string' },
    summary: updateSummarySchema,
    message: { type: 'string' },
  },
  required: ['success', 'organizationId', 'summary', 'message'],
};

// Error response schema
export const updateEmbeddingsErrorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
    message: { type: 'string' },
    details: { type: 'string', nullable: true },
  },
  required: ['error', 'code', 'message'],
};

// Complete schema for POST /embeddings/update
export const UpdateEmbeddingsSchema: FastifySchema = {
  headers: {
    type: 'object',
    required: ['x-organization-id'],
    properties: {
      'x-organization-id': { type: 'string' },
    },
  },
  response: {
    200: updateEmbeddingsResponseSchema,
    400: updateEmbeddingsErrorResponseSchema,
    500: updateEmbeddingsErrorResponseSchema,
  },
};

// TypeScript interfaces
export interface UpdateSummary {
  documentsRetrieved: number;
  documentsProcessed: number;
  documentsIndexed: number;
  timestamp: string;
}

export interface UpdateEmbeddingsResponse {
  success: boolean;
  organizationId: string;
  summary: UpdateSummary;
  message: string;
}
