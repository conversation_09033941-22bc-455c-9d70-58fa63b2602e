import { FastifySchema } from 'fastify';

// Document input schema for text submission
export const documentInputSchema = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      description: 'Unique identifier for the document',
    },
    name: {
      type: 'string',
      description: 'Human-readable name for the document',
    },
    content: {
      type: 'string',
      minLength: 20,
      description: 'The text content to generate embeddings for',
    },
    metadata: {
      type: 'object',
      additionalProperties: true,
      description: 'Optional metadata to associate with the document',
    },
  },
  required: ['id', 'name', 'content'],
  additionalProperties: false,
};

// Request body schema for submit endpoint
export const submitEmbeddingsRequestSchema = {
  type: 'object',
  properties: {
    documents: {
      type: 'array',
      items: documentInputSchema,
      minItems: 1,
      maxItems: 100, // Reasonable limit for batch processing
      description: 'Array of documents to process and generate embeddings for',
    },
    options: {
      type: 'object',
      properties: {
        maxTokens: {
          type: 'number',
          minimum: 100,
          maximum: 8000,
          default: 1500,
          description: 'Maximum tokens per chunk for large documents',
        },
        overlapTokens: {
          type: 'number',
          minimum: 0,
          maximum: 500,
          default: 150,
          description: 'Number of overlapping tokens between chunks',
        },
        clearExisting: {
          type: 'boolean',
          default: false,
          description:
            'Whether to clear existing embeddings for the organization before processing',
        },
      },
      additionalProperties: false,
    },
  },
  required: ['documents'],
  additionalProperties: false,
};

// Processing summary schema
export const submitSummarySchema = {
  type: 'object',
  properties: {
    documentsReceived: { type: 'number' },
    documentsProcessed: { type: 'number' },
    documentsIndexed: { type: 'number' },
    totalChunks: { type: 'number' },
    chunkedDocuments: {
      type: 'array',
      items: { type: 'string' },
    },
    timestamp: { type: 'string', format: 'date-time' },
  },
  required: [
    'documentsReceived',
    'documentsProcessed',
    'documentsIndexed',
    'totalChunks',
    'chunkedDocuments',
    'timestamp',
  ],
};

// Success response schema for submit
export const submitEmbeddingsResponseSchema = {
  type: 'object',
  properties: {
    success: { type: 'boolean' },
    organizationId: { type: 'string' },
    summary: submitSummarySchema,
    message: { type: 'string' },
  },
  required: ['success', 'organizationId', 'summary', 'message'],
};

// Error response schema
export const submitEmbeddingsErrorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
    message: { type: 'string' },
    details: { type: 'string', nullable: true },
  },
  required: ['error', 'code', 'message'],
};

// Complete schema for POST /embeddings/submit
export const SubmitEmbeddingsSchema: FastifySchema = {
  headers: {
    type: 'object',
    required: ['x-organization-id'],
    properties: {
      'x-organization-id': { type: 'string' },
    },
  },
  body: submitEmbeddingsRequestSchema,
  response: {
    200: submitEmbeddingsResponseSchema,
    400: submitEmbeddingsErrorResponseSchema,
    500: submitEmbeddingsErrorResponseSchema,
  },
};

// TypeScript interfaces
export interface DocumentInput {
  id: string;
  name: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface SubmitOptions {
  maxTokens?: number;
  overlapTokens?: number;
  clearExisting?: boolean;
}

export interface SubmitEmbeddingsRequest {
  documents: DocumentInput[];
  options?: SubmitOptions;
}

export interface SubmitSummary {
  documentsReceived: number;
  documentsProcessed: number;
  documentsIndexed: number;
  totalChunks: number;
  chunkedDocuments: string[];
  timestamp: string;
}

export interface SubmitEmbeddingsResponse {
  success: boolean;
  organizationId: string;
  summary: SubmitSummary;
  message: string;
}

export interface SubmitEmbeddingsError {
  error: string;
  code: string;
  message: string;
  details?: string;
}
