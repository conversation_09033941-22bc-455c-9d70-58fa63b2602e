import { FastifySchema } from 'fastify';

// Key metadata schema
export const keyMetadataSchema = {
  type: 'object',
  properties: {
    key: { type: 'string' },
    size: { type: 'number' },
    ttl: { type: 'number', nullable: true },
    type: { type: 'string' },
  },
  required: ['key', 'size', 'type'],
};

// Document chunk schema
export const documentChunkSchema = {
  type: 'object',
  properties: {
    documentId: { type: 'string' },
    chunkCount: { type: 'number' },
    totalSize: { type: 'number' },
    sizeFormatted: { type: 'string' },
    chunks: {
      type: 'array',
      items: keyMetadataSchema,
    },
  },
  required: ['documentId', 'chunkCount', 'totalSize', 'sizeFormatted', 'chunks'],
};

// Summary schema
export const embeddingsSummarySchema = {
  type: 'object',
  properties: {
    contentHashes: { type: 'number' },
    docCache: { type: 'number' },
    embeddings: { type: 'number' },
    totalKeys: { type: 'number' },
    totalSize: { type: 'number' },
    totalSizeFormatted: { type: 'string' },
  },
  required: [
    'contentHashes',
    'docCache',
    'embeddings',
    'totalKeys',
    'totalSize',
    'totalSizeFormatted',
  ],
};

// Data schema
export const embeddingsDataSchema = {
  type: 'object',
  properties: {
    contentHashes: {
      type: 'array',
      items: keyMetadataSchema,
    },
    docCache: {
      type: 'array',
      items: keyMetadataSchema,
    },
    embeddings: {
      type: 'array',
      items: documentChunkSchema,
    },
  },
  required: ['contentHashes', 'docCache', 'embeddings'],
};

// Success response schema
export const getEmbeddingsResponseSchema = {
  type: 'object',
  properties: {
    organizationId: { type: 'string' },
    summary: embeddingsSummarySchema,
    data: embeddingsDataSchema,
  },
  required: ['organizationId', 'summary', 'data'],
};

// Error response schema
export const getEmbeddingsErrorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
    message: { type: 'string' },
    details: { type: 'string', nullable: true },
  },
  required: ['error', 'code', 'message'],
};

// Complete schema for GET /embeddings
export const GetEmbeddingsSchema: FastifySchema = {
  headers: {
    type: 'object',
    required: ['x-organization-id'],
    properties: {
      'x-organization-id': { type: 'string' },
    },
  },
  response: {
    200: getEmbeddingsResponseSchema,
    400: getEmbeddingsErrorResponseSchema,
    500: getEmbeddingsErrorResponseSchema,
  },
};

// TypeScript interfaces
export interface KeyMetadata {
  key: string;
  size: number;
  ttl?: number | null;
  type: string;
}

export interface DocumentChunk {
  documentId: string;
  chunkCount: number;
  totalSize: number;
  sizeFormatted: string;
  chunks: KeyMetadata[];
}

export interface EmbeddingsSummary {
  contentHashes: number;
  docCache: number;
  embeddings: number;
  totalKeys: number;
  totalSize: number;
  totalSizeFormatted: string;
}

export interface EmbeddingsData {
  contentHashes: KeyMetadata[];
  docCache: KeyMetadata[];
  embeddings: DocumentChunk[];
}

export interface GetEmbeddingsResponse {
  organizationId: string;
  summary: EmbeddingsSummary;
  data: EmbeddingsData;
}
