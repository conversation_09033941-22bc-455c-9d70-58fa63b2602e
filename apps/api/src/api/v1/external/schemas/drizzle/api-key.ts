import { pgTable, varchar, timestamp } from 'drizzle-orm/pg-core';

export const apiKeyTable = pgTable('api_key', {
  id: varchar('id', { length: 32 }).primaryKey(),
  name: varchar('name', { length: 64 }).notNull(),
  key: varchar('key', { length: 64 }).notNull(),
  prefix: varchar('prefix', { length: 64 }).notNull(),
  expires_at: timestamp('expires_at', { withTimezone: true }),
  user_id: varchar('user_id', { length: 32 }).notNull(),
  organization_id: varchar('organization_id', { length: 32 }).notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull(),
  last_used_at: timestamp('last_used_at', { withTimezone: true }),
  module: varchar('module', { length: 128 }),
});

export type ApiKey = typeof apiKeyTable.$inferSelect;
export type NewApiKey = typeof apiKeyTable.$inferInsert;
