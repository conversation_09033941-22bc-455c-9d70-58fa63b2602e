import { FastifyInstance } from 'fastify';
import { createApiKey } from '../../../../../core/auth/api-key/service';

export const createOrganizationApiKey = async (
  fastify: FastifyInstance,
  { organizationId }: { organizationId: string }
) => {
  // Use dbWithTenant to enforce Row Level Security
  const db = await fastify.dbWithTenant(organizationId);

  try {
    const apiKey = await createApiKey(
      {
        organizationId,
        name: 'External API Key',
        module: 'external-api',
      },
      db
    );

    return apiKey;
  } catch (error) {
    throw error;
  }
};
