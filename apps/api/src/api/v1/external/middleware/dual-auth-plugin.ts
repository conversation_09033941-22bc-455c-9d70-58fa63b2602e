import fp from 'fastify-plugin';
import { FastifyPluginAsync } from 'fastify';
import { DualAuthMiddleware } from '../../../../core/auth/middleware/dual-auth';

const dualAuthPlugin: FastifyPluginAsync = async (fastify, opts) => {
  // Initialize the dual authentication middleware
  const dualAuthMiddleware = new DualAuthMiddleware(fastify);

  // Add the dual authentication middleware to routes
  fastify.addHook('preHandler', async (request, reply) => {
    await dualAuthMiddleware.authenticate(request, reply);
  });

  // Decorate fastify with the dual auth middleware for custom route registration
  fastify.decorate('dualAuth', dualAuthMiddleware);
};

export default fp(dualAuthPlugin);
