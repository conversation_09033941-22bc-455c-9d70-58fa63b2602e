import fp from 'fastify-plugin';
import { FastifyPluginAsync } from 'fastify';
import { apiKeyMiddleware } from '../../../../core/auth/middleware/service';

const apiKeyPlugin: FastifyPluginAsync = async (fastify, opts) => {
  // Add the API key middleware to routes that need it
  fastify.addHook('preHandler', async (request, reply) => {
    // Skip API key validation for auth routes and root route
    if (
      request.routeOptions?.url === '/' ||
      request.routeOptions?.url?.includes('/echo') ||
      request.routeOptions?.url?.startsWith('/auth/') ||
      request.routeOptions?.url?.startsWith('/api-keys/') ||
      request.routeOptions?.url?.includes('/agent/invoke') ||
      request.routeOptions?.url?.includes('/agent/stream') ||
      request.routeOptions?.url?.includes('/health') ||
      request.routeOptions?.url?.includes('/embeddings')
    ) {
      return;
    }

    await apiKeyMiddleware(request, reply);
  });
};

export default fp(apiKeyPlugin);
