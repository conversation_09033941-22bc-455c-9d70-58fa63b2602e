import 'dotenv/config';
import Fastify from 'fastify';
import { readFileSync } from 'fs';
import { join, resolve } from 'path';
import app from './app';
// Temporary console-backed logger (will be replaced with Fastify logger at runtime)
// eslint-disable-next-line import/no-extraneous-dependencies

// Global logger bridging
import { setGlobalLogger } from '../../agents/src/observability/global-logger';
import { ILogger } from '../../agents/src/integration/mcp-server/mcp-bridge-enhanced';

function adaptFastifyLogger(fLog: any): ILogger {
  return {
    debug: (message: string, meta?: Record<string, any>) => {
      if (meta && Object.keys(meta).length > 0) {
        fLog.debug(meta, message);
      } else {
        fLog.debug(message);
      }
    },
    info: (message: string, meta?: Record<string, any>) => {
      if (meta && Object.keys(meta).length > 0) {
        fLog.info(meta, message);
      } else {
        fLog.info(message);
      }
    },
    warn: (message: string, meta?: Record<string, any>) => {
      if (meta && Object.keys(meta).length > 0) {
        fLog.warn(meta, message);
      } else {
        fLog.warn(message);
      }
    },
    error: (err: any, message: string, meta?: Record<string, any>) => {
      if (meta && Object.keys(meta).length > 0) {
        fLog.error({ ...meta, err }, message);
      } else {
        fLog.error(err, message);
      }
    },
  };
}

// Initialise logger with console; it will be swapped for Fastify's logger once available
let logger: ILogger = {
  debug: console.debug.bind(console),
  info: console.info.bind(console),
  warn: console.warn.bind(console),
  error: (error: any, message: string) => console.error(message, error),
};

// Environment variables
const NODE_ENV = process.env.NODE_ENV || 'development';
const PORT = parseInt(process.env.PORT || '8000', 10);
const HOST = process.env.HOST || 'localhost';
const USE_HTTPS = process.env.USE_HTTPS !== 'false'; // Default to HTTPS unless explicitly disabled

// Navigate to workspace root from current file location
// When compiled, this file is at dist/apps/api/src/server.js
// Certificates are copied to dist/certs during build
const DIST_ROOT = resolve(__dirname, '../../../'); // Go to dist/
const CERT_DIR = join(DIST_ROOT, 'certs');
const CERT_PATH = join(CERT_DIR, 'cert.pem');
const KEY_PATH = join(CERT_DIR, 'key.pem');

async function createServer() {
  let serverOptions: any = {
    logger: {
      level: process.env.LOG_LEVEL || 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
        },
      },
    },
  };

  let isHttpsEnabled = false;

  // Try to use HTTPS if enabled and certificates exist
  if (USE_HTTPS) {
    try {
      const httpsOptions = {
        key: readFileSync(KEY_PATH),
        cert: readFileSync(CERT_PATH),
      };

      serverOptions.https = httpsOptions;
      isHttpsEnabled = true;
      logger.info(`🔒 NODE environment is in "${NODE_ENV}" mode`);
      logger.info(`🔒 HTTPS enabled using certificates from ${CERT_DIR}`);
    } catch (error) {
      logger.warn(`⚠️  HTTPS certificates not found at ${CERT_DIR}`);
      logger.info(`💡 Falling back to HTTP. To enable HTTPS:`);
      logger.info(`   1. Generate certificates: cd apps/teams && pnpm run generate-certs`);
      logger.info(`   2. Or set USE_HTTPS=false to disable this warning`);
      logger.info('');

      // Remove https options to fall back to HTTP
      serverOptions = {
        logger: serverOptions.logger,
      };
    }
  } else {
    logger.info(`🔓 HTTP mode (HTTPS disabled by USE_HTTPS=false)`);
  }

  const server = Fastify(serverOptions);
  // Attach the HTTPS status to the server instance for later detection
  (server as any).isHttpsEnabled = isHttpsEnabled;

  return server;
}

async function start() {
  try {
    const server = await createServer();

    // Register global logger before other modules initialise
    const fastifyLogger: ILogger = adaptFastifyLogger(server.log as any);
    setGlobalLogger(fastifyLogger);
    logger = fastifyLogger;

    // Register the app
    await server.register(app);

    // Start the server with port conflict resolution
    let currentPort = PORT;

    try {
      await server.listen({
        port: currentPort,
        host: HOST,
      });
    } catch (err: any) {
      if (err.code === 'EADDRINUSE') {
        logger.warn(`⚠️  Port ${currentPort} is in use. Attempting to kill existing process...`);

        // Try to kill the process using the port, but be more careful
        const { exec } = require('child_process');
        const util = require('util');
        const execAsync = util.promisify(exec);

        try {
          // Get the PID of the process using the port
          const { stdout: pidOutput } = await execAsync(`lsof -ti:${currentPort}`);
          const pids = pidOutput
            .trim()
            .split('\n')
            .filter((pid: string) => pid);

          if (pids.length > 0) {
            // Only kill if it's not our own process
            const currentPid = process.pid.toString();
            const pidsToKill = pids.filter((pid: string) => pid !== currentPid);

            if (pidsToKill.length > 0) {
              await execAsync(`kill -9 ${pidsToKill.join(' ')}`);
              logger.info(
                `✅ Killed existing process(es) on port ${currentPort}: ${pidsToKill.join(', ')}`
              );

              // Wait a moment for the port to be released
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Try to start the server again
              await server.listen({
                port: currentPort,
                host: HOST,
              });
            } else {
              logger.warn(
                `⚠️  Port ${currentPort} is used by our own process, trying alternative port...`
              );
              // Try the next port
              currentPort++;
              await server.listen({
                port: currentPort,
                host: HOST,
              });
            }
          } else {
            logger.warn(`⚠️  No process found on port ${currentPort}, trying alternative port...`);
            currentPort++;
            await server.listen({
              port: currentPort,
              host: HOST,
            });
          }
        } catch (killErr) {
          logger.error(killErr, `❌ Failed to resolve port conflict on port ${currentPort}`);
          process.exit(1);
        }
      } else {
        throw err; // Re-throw non-port-related errors
      }
    }

    // Print all registered routes using Fastify's built-in method
    logger.info('📋 Available API Endpoints:');
    const routes = server.printRoutes();
    logger.info(routes);

    // Check if the server is actually running HTTPS by looking at the server instance
    const isHttps = (server as any).isHttpsEnabled;
    const protocol = isHttps ? 'https' : 'http';

    logger.info(`🚀 Server listening on ${protocol}://${HOST}:${currentPort}`);
    logger.info(`📋 API Base URL: ${protocol}://${HOST}:${currentPort}`);

    // Add curl command for testing the health endpoint
    const curlCommand = isHttps
      ? `curl -k ${protocol}://${HOST}:${currentPort}/v1/external/health`
      : `curl ${protocol}://${HOST}:${currentPort}/v1/external/health`;
    logger.info(`🧪 Test health endpoint: ${curlCommand}`);
  } catch (err) {
    logger.error(err, '❌ Error starting server');
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logger.info('\n👋 Shutting down server gracefully...');
  try {
    // Give the server time to finish current requests
    await new Promise(resolve => setTimeout(resolve, 1000));
    process.exit(0);
  } catch (err) {
    logger.error(err, 'Error during graceful shutdown');
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('\n👋 Shutting down server gracefully...');
  try {
    // Give the server time to finish current requests
    await new Promise(resolve => setTimeout(resolve, 1000));
    process.exit(0);
  } catch (err) {
    logger.error(err, 'Error during graceful shutdown');
    process.exit(1);
  }
});

start();
