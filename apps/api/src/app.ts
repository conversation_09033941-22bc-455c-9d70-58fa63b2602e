/* eslint-disable */
// Fastify application entry point

import { join } from 'path';
import AutoLoad, { AutoloadPluginOptions } from '@fastify/autoload';
import { FastifyPluginAsync, FastifyServerOptions } from 'fastify';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import sensiblePlugin from './core/security/sensible';
import envPlugin from './config/env/env';
import agentsPlugin from './plugins/agents';
import redisPlugin from './plugins/redis';
import dualAuthPlugin from './api/v1/external/middleware/dual-auth-plugin';
import jwtPlugin from './api/v1/internal/middleware/jwt';
import enhancedDrizzlePlugin from './lib/drizzle/connection-manager';
import websocketPlugin from './plugins/websocket';

// Import route handlers directly
import agentRoutes from './api/v1/internal/routes/agents';
import authRoutes from './api/v1/internal/routes/auth';
import organizationRoutes from './api/v1/internal/routes/organization';
import mcpRoutes from './api/v1/internal/routes/mcp';
import externalApiKeyRoutes from './api/v1/external/routes/api-keys';
import externalAuthRoutes from './api/v1/external/routes/auth';
import externalAgentRoutes from './api/v1/external/routes/agents';
import healthRoutes from './api/v1/external/routes/health';
import embeddingsRoutes from './api/v1/external/routes/embeddings';
import promptsRoutes from './api/v1/external/routes/prompts';

// eslint-disable-next-line
export interface AppOptions extends FastifyServerOptions, Partial<AutoloadPluginOptions> {}

// Renamed to appPluginOptions to avoid confusion with plugin opts if any are passed by CLI
const appPluginOptions: AppOptions = {
  logger: { level: 'debug' }, // Enable debug logging for Fastify
};

const app: FastifyPluginAsync<AppOptions> = async (fastify, opts): Promise<void> => {
  // Place here your custom code!
  await fastify.register(envPlugin);
  await fastify.register(sensiblePlugin);
  await fastify.register(helmet);

  // Root route for test compatibility
  fastify.get('/', async (request, reply) => {
    return { root: true };
  });

  await fastify.register(enhancedDrizzlePlugin);
  await fastify.register(redisPlugin);

  // Add a test route to verify database connectivity (in app plugin scope)
  fastify.get('/test-db-connection', async (request, reply) => {
    try {
      fastify.log.info('🔍 Testing database connection via test route...');

      // Test external database
      const externalDb = await fastify.dbWithTenant('test-org');
      const externalResult = await externalDb.execute(
        "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'public'"
      );
      fastify.log.info('✅ External database test passed', {
        tableCount: (externalResult as any).rows?.[0]?.count,
      });

      // Test internal database
      const internalDb = await fastify.dbInternalWithTenant('test-org');
      const internalResult = await internalDb.execute(
        "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'public'"
      );
      fastify.log.info('✅ Internal database test passed', {
        tableCount: (internalResult as any).rows?.[0]?.count,
      });

      // Test if documents table exists in internal database
      const documentsTableResult = await internalDb.execute(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'documents'
      `);
      fastify.log.info('✅ Documents table check', {
        tableExists: (documentsTableResult as any).rows?.[0]?.count > 0,
      });

      // Test if there are any documents at all (without any filters)
      const allDocumentsResult = await internalDb.execute(`
        SELECT COUNT(*) as count FROM documents
      `);
      fastify.log.info('✅ All documents count check', {
        totalDocuments: (allDocumentsResult as any).rows?.[0]?.count,
      });

      // Test if there are documents for any organization
      const orgDocumentsResult = await internalDb.execute(`
        SELECT COUNT(*) as count 
        FROM documents 
        WHERE organization_id IS NOT NULL
      `);
      fastify.log.info('✅ Organization documents count check', {
        orgDocuments: (orgDocumentsResult as any).rows?.[0]?.count,
      });

      // Test if there are any documents with status = 'active'
      const activeDocumentsResult = await internalDb.execute(`
        SELECT COUNT(*) as count 
        FROM documents 
        WHERE status = 'active'
      `);
      fastify.log.info('✅ Active documents count check', {
        activeDocuments: (activeDocumentsResult as any).rows?.[0]?.count,
      });

      const response = {
        success: true,
        external: { tableCount: (externalResult as any).rows?.[0]?.count },
        internal: { tableCount: (internalResult as any).rows?.[0]?.count },
        documentsTable: { exists: (documentsTableResult as any).rows?.[0]?.count > 0 },
        allDocuments: { count: (allDocumentsResult as any).rows?.[0]?.count },
        orgDocuments: { count: (orgDocumentsResult as any).rows?.[0]?.count },
        activeDocuments: { count: (activeDocumentsResult as any).rows?.[0]?.count },
      };

      fastify.log.info('✅ Test route completed successfully', response);
      return response;
    } catch (error) {
      fastify.log.error(error, '❌ Database test failed');
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // Do not touch the following lines

  // This loads all plugins defined in plugins
  // those should be support plugins that are reused
  // through your application
  await fastify.register(AutoLoad, {
    dir: join(__dirname, 'plugins'),
    options: opts,
  });

  // Register a dedicated plugin for internal API routes, with its own prefix
  fastify.register(
    async internalApiInstance => {
      // Register plugins specific to this internal API group
      await internalApiInstance.register(jwtPlugin);
      await internalApiInstance.register(agentsPlugin);
      await internalApiInstance.register(websocketPlugin);

      // Register each route handler directly WITHOUT prefixes
      // Each route file will define its own full paths
      await internalApiInstance.register(agentRoutes);
      await internalApiInstance.register(authRoutes);
      await internalApiInstance.register(organizationRoutes);
      await internalApiInstance.register(mcpRoutes);
    },
    { prefix: '/v1/internal' }
  ); // Prefix applied to the encapsulating plugin instance

  // Register external API routes with specific prefixes to avoid conflicts
  // Register the dual authentication plugin that handles both JWT and API key auth
  await fastify.register(dualAuthPlugin, { prefix: '/v1/external' });
  await fastify.register(agentsPlugin, { prefix: '/v1/external' });

  // Register JWT plugin globally for external routes
  await fastify.register(jwt, {
    secret: fastify.env?.JWT_SECRET || 'fallback-secret',
    sign: {
      expiresIn: fastify.env?.TOKEN_EXPIRY || '15m',
    },
    verify: {
      maxAge: fastify.env?.TOKEN_EXPIRY || '15m',
    },
  });

  // Register each external route handler with specific prefixes
  await fastify.register(externalApiKeyRoutes, { prefix: '/v1/external' });
  await fastify.register(externalAgentRoutes, { prefix: '/v1/external/agent' });
  await fastify.register(externalAuthRoutes, { prefix: '/v1/external/auth' });
  await fastify.register(healthRoutes, { prefix: '/v1/external' });
  await fastify.register(embeddingsRoutes, { prefix: '/v1/external' });
  await fastify.register(promptsRoutes, { prefix: '/v1/external' });
};

export default app;
export { appPluginOptions };
