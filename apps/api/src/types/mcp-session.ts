export interface MCPSession {
  id: string;
  user_id?: string; // Optional for external routes
  organization_id: string;
  api_key_id?: string; // For external routes
  created_at: Date;
  last_activity: Date;
  status: 'active' | 'inactive' | 'expired';
  context_data?: Record<string, any>;
  auth_type: 'jwt' | 'api_key';
}

export interface MCPSessionToken {
  session_id: string;
  organization_id: string;
  auth_type: 'jwt' | 'api_key';
  expires_at: Date;
}
