export interface ApiKeyValidationResult {
  isValid: boolean;
  error?: string;
  keyId?: string;
}

export interface ApiKeyValidationError {
  code: string;
  message: string;
  statusCode: number;
}

export const API_KEY_HEADER = 'x-api-key';

export const API_KEY_ERRORS = {
  MISSING_KEY: {
    code: 'API_KEY_MISSING',
    message: 'API key is required',
    statusCode: 401,
  },
  INVALID_KEY: {
    code: 'API_KEY_INVALID',
    message: 'Invalid API key',
    statusCode: 401,
  },
  EXPIRED_KEY: {
    code: 'API_KEY_EXPIRED',
    message: 'API key has expired',
    statusCode: 401,
  },
} as const;
