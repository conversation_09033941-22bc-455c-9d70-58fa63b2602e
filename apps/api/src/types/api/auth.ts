export interface EmailLoginBody {
  email: string;
  code: string;
}

export interface RequestEmailCodeBody {
  email: string;
}

export interface RequestTokenBody {
  email: string;
  organization_id: string;
}

export interface PasswordLoginBody {
  email: string;
  password: string;
}

export interface RegisterBody {
  email: string;
  password: string;
  name: string;
  organization_id: string;
}

export interface JWTPayload {
  id: string;
  email: string;
  organization_id: string;
  type: 'access' | 'refresh';
  scope?: 'internal' | 'external-api'; // Optional scope to distinguish token types
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: string;
    email: string;
    name: string | null;
    organization_id: string;
  };
}

export interface RefreshTokenBody {
  refresh_token: string;
}

// New types for dual authentication system
export interface JWTTokenRequest {
  organization_id: string;
}

export interface JWTTokenResponse {
  access_token: string;
  token_type: 'Bearer';
  expires_in: number;
  organization_id: string;
}

export type AuthMethod = 'jwt' | 'api-key' | 'internal-secret' | 'none';

export interface RouteAuthConfig {
  method: AuthMethod;
  description?: string;
}

// Unified token service types
export interface TokenGenerationOptions {
  email?: string;
  organization_id: string;
  scope: 'internal' | 'external-api';
  authMethod: 'email' | 'internal-secret';
}
