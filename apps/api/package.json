{"name": "@anter/api", "version": "0.0.1", "description": "This project was bootstrapped with Fastify-CLI.", "main": "app.ts", "directories": {"test": "test"}, "scripts": {"test": "pnpm run build:ts && tsc -p test/tsconfig.json && tap test/**/*.test.ts || exit 0", "test:run": "tap test/**/*.test.ts", "pretest": "pnpm run build:ts && tsc -p test/tsconfig.json", "start": "node dist/api/src/server.js", "build": "pnpm build:ts", "build:ts": "tsc --skipLibCheck && pnpm copy:certs", "copy:certs": "mkdir -p dist/certs && if [ -f ../../certs/cert.pem ]; then cp ../../certs/*.pem dist/certs/; echo 'Certificates copied to dist/certs/'; else echo 'No certificates found in ../../certs/'; fi", "dev": "pnpm run build:ts && concurrently -k -p \"[{name}]\" -n \"TypeScript,Server\" -c \"yellow.bold,cyan.bold\" \"tsc -w\" \"sleep 3 && nodemon\"", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf dist", "clean:all": "rm -rf dist node_modules"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anter/agents": "workspace:^", "@anter/domain-model": "workspace:^", "@anter/mcp-tools": "workspace:^", "@anter/shared-services": "workspace:^", "@fastify/autoload": "^6.3.1", "@fastify/cors": "^11.1.0", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^10.0.0", "@fastify/redis": "^7.0.2", "@fastify/sensible": "^6.0.3", "@fastify/websocket": "^11.2.0", "@langchain/community": "^0.3.55", "@langchain/core": "^0.3.75", "@langchain/openai": "^0.6.11", "@modelcontextprotocol/sdk": "^1.18.0", "@types/redis": "^4.0.11", "ajv": "^8.17.1", "bcrypt": "^6.0.0", "drizzle-orm": "^0.44.5", "fastify": "^5.6.0", "fastify-cli": "^7.4.0", "fastify-plugin": "^5.0.1", "langchain": "0.3.33", "lodash": "^4.17.21", "mammoth": "^1.10.0", "pdf-parse": "^1.1.1", "pg": "^8.16.3", "redis": "^5.8.2", "langfuse": "^3.38.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^24.0.1", "@types/pg": "^8.15.4", "@types/tap": "^18.0.0", "concurrently": "^9.1.2", "fastify-tsconfig": "^3.0.0", "nodemon": "^3.0.2", "pino-pretty": "^11.0.0", "prettier": "^3.5.3", "tap": "^21.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}