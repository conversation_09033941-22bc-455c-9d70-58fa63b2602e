# Build Stage
FROM node:21-alpine3.19 AS builder

WORKDIR /app

ENV DATABASE_URL_ROOT_USER="postgresql://postgres.cqwoxgrygbiyfcrzgfix:<EMAIL>:5432/askinfosec?pgbouncer=true"

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy package files
COPY package.json pnpm-lock.yaml ./
COPY prisma ./prisma

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source files
COPY . .

# Generate Prisma client
RUN pnpm prisma generate

# Build the app
RUN pnpm build


# Production Stage
FROM node:21-alpine3.19 AS production

WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy package files and prisma schema
COPY package.json pnpm-lock.yaml ./

# Install production dependencies and prisma
RUN pnpm install --prod --frozen-lockfile

    # Copy built files
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder /app/node_modules/.pnpm ./node_modules/.pnpm
COPY --from=builder /app/node_modules/prisma ./node_modules/prisma
COPY --from=builder /app/node_modules/.bin/prisma ./node_modules/.bin/prisma

# Set environment variables
ENV NODE_ENV=production
ENV FASTIFY_ADDRESS=0.0.0.0
ENV FASTIFY_PORT=8000

EXPOSE 8000

# CMD ["pnpm", "start"]

CMD ["/bin/sh", "-c", "npx fastify start dist/src/app.js"]