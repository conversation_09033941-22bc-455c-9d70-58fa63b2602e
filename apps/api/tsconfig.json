{"extends": "fastify-tsconfig", "compilerOptions": {"target": "ESNext", "module": "CommonJS", "moduleResolution": "node", "outDir": "dist", "sourceMap": true, "allowJs": true, "typeRoots": ["node_modules/@types"], "types": ["bcrypt", "jest", "lodash", "pg", "node"], "paths": {"@anter/domain-model": ["../../packages/domain-model/dist/index"], "@anter/domain-model/*": ["../../packages/domain-model/dist/*"], "@anter/agents": ["../../apps/agents/dist/index"], "@anter/agents/*": ["../../apps/agents/dist/*"], "@anter/shared-services": ["../../packages/shared-services/dist/index"], "@anter/shared-services/*": ["../../packages/shared-services/dist/*"]}}, "include": ["src/**/*.ts", "test/auth/auth.test.ts"]}