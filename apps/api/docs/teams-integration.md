# Microsoft Teams Integration Guide

## Overview

The AskInfoSec Teams bot provides a conversational AI interface directly within Microsoft Teams, using the same backend API and agent system as the CLI tool. This guide covers the architecture, setup, and deployment of the Teams integration.

## Architecture

```mermaid
graph TB
    subgraph "Microsoft Teams"
        Teams[Teams Client]
        TeamsApp[Teams App]
    end

    subgraph "Bot Service"
        BotFramework[Bot Framework SDK]
        TokenManager[JWT Token Manager]
        AIService[AI Service]
    end

    subgraph "AskInfoSec API"
        AuthEndpoint["/api/v1/internal/authenticate/token"]
        AgentEndpoint["/api/v1/internal/agent/invoke"]
        HealthEndpoint["/api/v1/internal/health"]
    end

    subgraph "Agent System"
        AskAIAgent[Ask AI Agent]
        MCPTools[MCP Tools]
        Database[(Database)]
    end

    Teams --> TeamsApp
    TeamsApp --> BotFramework
    BotFramework --> AIService
    AIService --> TokenManager
    TokenManager --> AuthEndpoint
    AIService --> AgentEndpoint
    AIService --> HealthEndpoint
    AgentEndpoint --> AskAIAgent
    AskAIAgent --> MCPTools
    MCPTools --> Database
```

## Key Components

### 1. Teams Bot Service

Located in `apps/teams/bot/`, the bot service handles:

- **Bot Framework Integration**: Manages Teams conversations and events
- **JWT Authentication**: Automatic token management with caching and refresh
- **API Communication**: Proper integration with the AskInfoSec API
- **Error Handling**: Comprehensive error handling and logging

### 2. Token Management

The `TokenManager` class handles JWT authentication:

```javascript
// Automatic token fetching and caching
const tokenManager = new TokenManager();
const validToken = await tokenManager.getValidToken();
```

Features:

- Automatic token refresh before expiry
- Configurable refresh buffer (default: 5 minutes)
- Error handling and retry logic
- Token validation and caching

### 3. Agent Integration

The bot uses the same agent invocation pattern as the CLI:

```javascript
const payload = {
  agent_name: 'ask_ai',
  input: {
    input: userMessage,
    sessionId: `teams-session-${conversationId}`,
    organizationId: config.organizationId,
    userId: teamsUserId,
    metadata: {
      source: 'teams',
      timestamp: new Date().toISOString(),
      query_type: 'general_question',
      teams_context: {
        conversationId,
        channelId,
        teamId,
        userName,
        teamsId,
      },
    },
  },
  context: {
    sessionId,
    organizationId,
    userId,
    requestId: `req-${uuid()}`,
    timestamp: new Date().toISOString(),
    platform: 'teams',
  },
};
```

## API Endpoints Used

### Authentication Endpoint

- **URL**: `/api/v1/internal/authenticate/token`
- **Method**: POST
- **Authentication**: `X-Internal-Secret` header
- **Purpose**: Generate JWT tokens for API access

### Agent Invocation Endpoint

- **URL**: `/api/v1/internal/agent/invoke`
- **Method**: POST
- **Authentication**: `Authorization: Bearer <jwt-token>`
- **Purpose**: Process user messages through the Ask AI agent

### Health Check Endpoint

- **URL**: `/api/v1/internal/health`
- **Method**: GET
- **Authentication**: `Authorization: Bearer <jwt-token>`
- **Purpose**: Monitor API health and connectivity

## Configuration

### Environment Variables

```bash
# Bot Framework Configuration
BOT_ID=your-bot-app-id
BOT_PASSWORD=your-bot-app-password

# AskInfoSec API Configuration
AISEC_API_URL=https://your-api-domain.com
AISEC_EMAIL=<EMAIL>
AISEC_ORG_ID=your-organization-id
INTERNAL_API_SECRET=your-internal-secret

# Teams App Configuration
TEAMS_APP_ID=your-teams-app-id
TEAMS_APP_BASE_URL=https://your-teams-app-domain.com
```

### Multi-Tenant Considerations

The Teams bot respects the same multi-tenant architecture:

- Each conversation is isolated by organization ID
- JWT tokens are organization-specific
- Database queries use Row Level Security (RLS)
- User context includes organization information

## Deployment Options

### Option 1: Azure App Service (Recommended)

```bash
# Deploy using Azure CLI
az webapp create \
  --resource-group myResourceGroup \
  --plan myAppServicePlan \
  --name myTeamsBot \
  --runtime "NODE|18-lts"

# Configure environment variables
az webapp config appsettings set \
  --resource-group myResourceGroup \
  --name myTeamsBot \
  --settings \
    BOT_ID="your-bot-id" \
    BOT_PASSWORD="your-bot-password" \
    AISEC_API_URL="https://your-api.com"
```

### Option 2: Docker Container

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3978
CMD ["npm", "run", "bot:start"]
```

### Option 3: Local Development

```bash
# Start the bot service
cd apps/teams
npm install
npm run bot:dev

# Use ngrok for local testing
ngrok http 3978
# Update Bot Framework endpoint to https://your-ngrok-url.ngrok.io/api/messages
```

## Security Considerations

### Authentication Security

- JWT tokens have limited lifetime and automatic refresh
- Internal API secret is required for token generation
- All API communication uses HTTPS in production
- Bot Framework handles Teams authentication

### Data Privacy

- User messages are processed through your controlled API
- Organization isolation ensures data separation
- Conversation context is maintained per Teams session
- No sensitive data is logged

### Network Security

- Bot service validates all incoming requests
- Rate limiting can be implemented at the API level
- CORS is configured for Teams domains only
- Security headers are applied via Helmet middleware

## Monitoring and Observability

### Application Insights Integration

```javascript
// Automatic telemetry collection
import appInsights from 'applicationinsights';

if (process.env.APPINSIGHTS_INSTRUMENTATIONKEY) {
  appInsights.setup().start();
}
```

### Custom Metrics

The bot tracks:

- Message processing time
- API response times
- Token refresh frequency
- Error rates and types
- User engagement metrics

### Logging

Structured logging with Winston:

```javascript
logger.info('Processing Teams message', {
  userId: userInfo.id,
  conversationId: context.activity.conversation.id,
  messageLength: message.length,
  organizationId: config.organizationId,
});
```

## Troubleshooting

### Common Issues

1. **Bot not responding**

   - Check Bot Framework App ID and Password
   - Verify endpoint URL in Bot Registration
   - Check application logs for errors

2. **Authentication failures**

   - Verify `INTERNAL_API_SECRET` configuration
   - Check `AISEC_EMAIL` and `AISEC_ORG_ID`
   - Ensure API is accessible from bot service

3. **API connection issues**
   - Verify `AISEC_API_URL` is correct
   - Check network connectivity and firewall rules
   - Ensure JWT token is being generated successfully

### Debug Commands

```bash
# Test token generation
node -e "
const { TokenManager } = require('./bot/utils/tokenManager.js');
const tm = new TokenManager();
tm.getValidToken()
  .then(token => console.log('Success:', token.substring(0, 50) + '...'))
  .catch(err => console.error('Error:', err.message));
"

# Test API connectivity
curl -X POST https://your-api.com/api/v1/internal/authenticate/token \
  -H "Content-Type: application/json" \
  -H "X-Internal-Secret: your-secret" \
  -d '{"email":"your-email","organization_id":"your-org-id"}'

# Test agent invocation
curl -X POST https://your-api.com/api/v1/internal/agent/invoke \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "agent_name": "ask_ai",
    "input": {
      "input": "Hello from Teams",
      "sessionId": "test-session",
      "organizationId": "your-org-id",
      "userId": "test-user"
    },
    "context": {
      "sessionId": "test-session",
      "organizationId": "your-org-id",
      "userId": "test-user",
      "requestId": "test-request",
      "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
      "platform": "teams"
    }
  }'
```

## Best Practices

### Performance

- Token caching reduces API calls
- Connection pooling for HTTP requests
- Async/await for non-blocking operations
- Proper error handling prevents cascading failures

### User Experience

- Show typing indicators for longer operations
- Use Adaptive Cards for rich interactions
- Provide helpful error messages
- Support common commands (help, about, status)

### Maintenance

- Regular dependency updates
- Monitor token refresh rates
- Log analysis for usage patterns
- Health check automation

## Integration Testing

### Unit Tests

```javascript
describe('TokenManager', () => {
  it('should fetch and cache JWT tokens', async () => {
    const tokenManager = new TokenManager();
    const token = await tokenManager.getValidToken();
    expect(token).toMatch(/^eyJ/); // JWT format
  });
});
```

### Integration Tests

```javascript
describe('AIService', () => {
  it('should process messages through agent API', async () => {
    const aiService = new AIService();
    const response = await aiService.processMessage('Test message', mockUserInfo, mockContext);
    expect(response.text).toBeDefined();
  });
});
```

### End-to-End Tests

Use Bot Framework Emulator or automated testing tools to verify:

- Complete conversation flows
- Error handling scenarios
- Authentication edge cases
- Multi-user scenarios

This integration provides a seamless bridge between Microsoft Teams and your AskInfoSec AI system, maintaining the same security, performance, and functionality standards as other components.
