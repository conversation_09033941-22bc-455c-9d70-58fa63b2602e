# Request Validation

## Overview

This project uses Fastify's built-in schema validation to validate incoming requests. The validation is based on JSON Schema and provides automatic validation of request body, parameters, and query strings.

## Basic Usage

```typescript
import { FastifySchema } from 'fastify';

const schema: FastifySchema = {
  body: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string', minLength: 1 },
    },
  },
};

fastify.post('/items', { schema }, handler);
```

## Customizing Error Responses

Fastify provides several ways to customize validation error responses:

### 1. Global Error Handler

You can customize validation errors globally by setting the `setErrorHandler` option:

```typescript
// In your app.ts or plugin
fastify.setErrorHandler((error, request, reply) => {
  if (error.validation) {
    reply.code(400).send({
      error: 'Validation Error',
      code: 'VALIDATION_ERROR',
      details: error.validation,
    });
    return;
  }
  // Handle other errors...
});
```

### 2. Route-Specific Error Handler

You can also handle validation errors for specific routes:

```typescript
fastify.post(
  '/items',
  {
    schema,
    errorHandler: (error, request, reply) => {
      if (error.validation) {
        reply.code(400).send({
          error: 'Invalid Item Data',
          code: 'ITEM_VALIDATION_ERROR',
          details: error.validation,
        });
        return;
      }
      // Handle other errors...
    },
  },
  handler
);
```

### 3. Using @fastify/sensible

If you're using `@fastify/sensible`, you can use its built-in error handling:

```typescript
import sensible from '@fastify/sensible';

fastify.register(sensible);

// The error handler will automatically format validation errors
fastify.setErrorHandler((error, request, reply) => {
  if (error.validation) {
    reply.badRequest(error.message);
    return;
  }
  // Handle other errors...
});
```

## Validation Schema Structure

### Basic Schema

```typescript
const schema: FastifySchema = {
  body: {
    type: 'object',
    required: ['field1', 'field2'],
    properties: {
      field1: { type: 'string' },
      field2: { type: 'number' },
    },
  },
};
```

### Response Schema

```typescript
const schema: FastifySchema = {
  response: {
    200: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
      },
    },
    400: {
      type: 'object',
      properties: {
        error: { type: 'string' },
        code: { type: 'string' },
      },
    },
  },
};
```

### Query Parameters

```typescript
const schema: FastifySchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', minimum: 1 },
      limit: { type: 'number', minimum: 1, maximum: 100 },
    },
  },
};
```

### URL Parameters

```typescript
const schema: FastifySchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
  },
};
```

## Common Validation Patterns

### Pagination

```typescript
const paginationSchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 10 },
  },
};
```

### UUID Parameters

```typescript
const idParamSchema = {
  type: 'object',
  required: ['id'],
  properties: {
    id: { type: 'string', format: 'uuid' },
  },
};
```

### Date Fields

```typescript
const dateFieldSchema = {
  type: 'object',
  properties: {
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
  },
};
```

## Best Practices

1. **Reuse Common Schemas**: Create reusable schema components for common patterns
2. **Validate Early**: Apply validation as early as possible in the request lifecycle
3. **Clear Error Messages**: Provide clear and helpful error messages
4. **Consistent Error Format**: Use a consistent error response format across all endpoints
5. **Type Safety**: Use TypeScript interfaces to ensure type safety with your schemas

## Testing Validation

Use Fastify's `inject` method to test validation:

```typescript
const response = await app.inject({
  method: 'POST',
  url: '/items',
  payload: {
    // Invalid payload
  },
});

expect(response.statusCode).toBe(400);
expect(JSON.parse(response.payload)).toMatchObject({
  error: 'Validation Error',
  code: 'VALIDATION_ERROR',
});
```
