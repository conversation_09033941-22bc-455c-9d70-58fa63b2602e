# JWT Authentication Consolidation

## Overview

This document describes the consolidated JWT authentication system that unifies both internal and external API authentication flows while maintaining backward compatibility and improving maintainability.

## Architecture

### Before Consolidation

The system had two separate JWT implementations:

1. **Internal JWT** (`apps/api/src/core/auth/jwt/service.ts`)

   - Email + organization_id authentication
   - Database user validation
   - Internal scope tokens
   - Simple JWT plugin middleware

2. **External JWT** (`apps/api/src/core/auth/jwt/external-token-service.ts`)
   - Internal secret + organization_id authentication
   - Synthetic user creation
   - External-api scope tokens
   - Complex dual auth middleware

### After Consolidation

A unified system with:

1. **UnifiedTokenService** (`apps/api/src/core/auth/jwt/unified-token-service.ts`)

   - Single service handling both authentication flows
   - Shared token generation and validation logic
   - Scope-based access control
   - Consistent error handling

2. **UnifiedJWTMiddleware** (`apps/api/src/core/auth/middleware/unified-jwt.ts`)
   - Flexible middleware supporting both scopes
   - Configurable route skipping
   - Integrated token service

## Key Components

### UnifiedTokenService

The core service that handles both internal and external token generation:

```typescript
class UnifiedTokenService {
  // Generate tokens for both flows
  async generateToken(request, reply, options: TokenGenerationOptions);

  // Validate tokens with scope checking
  async validateToken(token: string, expectedScope?: 'internal' | 'external-api');

  // Handle internal token requests (email + organization_id)
  async handleInternalTokenRequest(request, reply);

  // Handle external token requests (internal secret + organization_id)
  async handleExternalTokenRequest(request, reply);
}
```

### Token Generation Options

```typescript
interface TokenGenerationOptions {
  email?: string; // Required for internal flow
  organization_id: string; // Required for both flows
  scope: 'internal' | 'external-api'; // Token scope
  authMethod: 'email' | 'internal-secret'; // Authentication method
}
```

## Authentication Flows

### Internal API Flow

1. **Request**: `POST /v1/internal/authenticate/token`

   ```json
   {
     "email": "<EMAIL>",
     "organization_id": "org-123"
   }
   ```

2. **Validation**:

   - User exists in database
   - User is member of specified organization
   - Organization ID format validation

3. **Token Generation**:
   ```json
   {
     "id": "user-uuid",
     "email": "<EMAIL>",
     "organization_id": "org-123",
     "type": "access",
     "scope": "internal"
   }
   ```

### External API Flow

1. **Request**: `POST /v1/external/auth/token`

   ```
   Headers:
   x-internal-secret: <secret>
   x-organization-id: org-123
   ```

2. **Validation**:

   - Internal secret verification
   - Organization ID format validation

3. **Token Generation**:
   ```json
   {
     "id": "org_org-123",
     "email": "<EMAIL>",
     "organization_id": "org-123",
     "type": "access",
     "scope": "external-api"
   }
   ```

## Token Validation

### Scope-Based Validation

Tokens are validated based on their scope:

- **Internal tokens** (`scope: 'internal'`) can only access internal API routes
- **External tokens** (`scope: 'external-api'`) can only access external API routes

### Validation Process

1. **Token Structure**: Verify JWT payload structure
2. **Token Type**: Ensure token type is 'access'
3. **Scope Validation**: Check token scope matches expected scope
4. **Expiration**: Verify token hasn't expired

## Middleware Integration

### Internal API Middleware

```typescript
// apps/api/src/api/v1/internal/middleware/jwt.ts
const jwtPlugin: FastifyPluginAsync = async (fastify, opts) => {
  const tokenService = new UnifiedTokenService(fastify);

  fastify.addHook('onRequest', async (request, reply) => {
    // Skip auth routes
    if (shouldSkipRoute(request)) return;

    // Validate token with internal scope
    const payload = await tokenService.validateToken(token, 'internal');
    request.user = payload;
  });
};
```

### External API Middleware

```typescript
// apps/api/src/core/auth/middleware/dual-auth.ts
class DualAuthMiddleware {
  private tokenService = new UnifiedTokenService(fastify);

  private async authenticateJWT(request, reply) {
    // Validate token with external-api scope
    const payload = await this.tokenService.validateToken(token, 'external-api');
    request.user = payload;
  }
}
```

## Migration Guide

### For Internal Routes

**Before**:

```typescript
import { AuthService } from '../../../../../core/auth/jwt/service';

const authService = new AuthService(fastify);
const result = await authService.requestAccessToken(email, organization_id);
```

**After**:

```typescript
import { UnifiedTokenService } from '../../../../../core/auth/jwt/unified-token-service';

const tokenService = new UnifiedTokenService(fastify);
await tokenService.handleInternalTokenRequest(request, reply);
```

### For External Routes

**Before**:

```typescript
import { ExternalTokenService } from '../../../../../core/auth/jwt/external-token-service';

const tokenService = new ExternalTokenService(fastify);
const result = await tokenService.generateToken(request, reply);
```

**After**:

```typescript
import { UnifiedTokenService } from '../../../../../core/auth/jwt/unified-token-service';

const tokenService = new UnifiedTokenService(fastify);
await tokenService.handleExternalTokenRequest(request, reply);
```

## Benefits

### 1. **Maintainability**

- Single source of truth for JWT logic
- Consistent error handling and validation
- Shared utility functions

### 2. **Security**

- Scope-based access control
- Consistent token validation
- Unified security practices

### 3. **Flexibility**

- Easy to add new authentication methods
- Configurable middleware options
- Extensible token generation

### 4. **Code Reuse**

- Shared token validation logic
- Common utility functions
- Unified type definitions

## Configuration

### Environment Variables

```bash
JWT_SECRET=your-secret-key
TOKEN_EXPIRY=15m  # 15 minutes, 1h, 1d, etc.
INTERNAL_API_SECRET=your-internal-secret
```

### Token Expiry

Tokens support flexible expiry formats:

- `15m` - 15 minutes
- `1h` - 1 hour
- `1d` - 1 day

## Error Handling

### Common Error Codes

- `MISSING_BEARER_TOKEN` - No Authorization header
- `INVALID_JWT_TOKEN` - Malformed or invalid token
- `INVALID_TOKEN_SCOPE` - Token scope doesn't match route
- `USER_NOT_FOUND` - User doesn't exist (internal flow)
- `INVALID_ORGANIZATION` - User not in organization (internal flow)
- `MISSING_ORGANIZATION_ID` - Missing organization header (external flow)

### Error Response Format

```json
{
  "error": "Unauthorized",
  "message": "Invalid JWT token",
  "code": "INVALID_JWT_TOKEN"
}
```

## Testing

### Token Generation Tests

```typescript
// Test internal token generation
const internalToken = await tokenService.generateToken(request, reply, {
  email: '<EMAIL>',
  organization_id: 'test-org',
  scope: 'internal',
  authMethod: 'email',
});

// Test external token generation
const externalToken = await tokenService.generateToken(request, reply, {
  organization_id: 'test-org',
  scope: 'external-api',
  authMethod: 'internal-secret',
});
```

### Token Validation Tests

```typescript
// Test internal token validation
const payload = await tokenService.validateToken(token, 'internal');

// Test external token validation
const payload = await tokenService.validateToken(token, 'external-api');
```

## Future Enhancements

### 1. **Refresh Token Support**

- Add refresh token generation
- Implement token refresh endpoints
- Automatic token renewal

### 2. **Advanced Scopes**

- Role-based scopes
- Permission-based scopes
- Custom scope definitions

### 3. **Token Revocation**

- Token blacklisting
- User logout endpoints
- Session management

### 4. **Monitoring & Analytics**

- Token usage metrics
- Authentication success/failure rates
- Performance monitoring

## Conclusion

The consolidated JWT implementation provides a unified, maintainable, and secure authentication system that supports both internal and external API flows while maintaining backward compatibility and improving code quality.
