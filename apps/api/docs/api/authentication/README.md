# Authentication Documentation

This directory contains documentation for authentication-related features in the API.

## Authentication Methods

### 1. JWT (JSON Web Token) Authentication

This is the primary authentication method for most API endpoints, used for user sessions and access control.

**Token Types:**

1.  **Access Token**

    - Short-lived token (15 minutes by default)
    - Used for API requests
    - Contains user information and `type: 'access'`
    - Configured via `TOKEN_EXPIRY` environment variable

2.  **Refresh Token**
    - Long-lived token (7 days by default)
    - Used to obtain new access tokens
    - Contains user information and `type: 'refresh'`
    - Configured via `REFRESH_TOKEN_EXPIRY` environment variable

### 2. API Key Authentication (X-Internal-Secret)

This method is used for specific internal endpoints, such as generating API keys, where JWT authentication is bypassed. It relies on a shared secret for authentication.

**Usage:**

- Include the `X-Internal-Secret` header in your request with the pre-shared secret value.
- Routes using this method typically start with `/auth/` and are configured to skip JWT validation.

## Authentication Flow

### JWT Authentication Flow

1.  **Login/Registration**

    - User authenticates via email code or registration
    - Server generates both access and refresh tokens
    - Response includes:
      ```json
      {
        "access_token": "jwt.access.token",
        "refresh_token": "jwt.refresh.token",
        "user": {
          "id": "user_id",
          "email": "<EMAIL>",
          "name": "User Name",
          "organization_id": "org_id"
        }
      }
      ```

## API Key Authentication

The API uses API keys for authentication. Each API key is a unique identifier that grants access to protected endpoints.

### API Key Format

- Generated as a 32-byte random hex string
- Stored in the database as a SHA-256 hash
- Can be configured with an optional expiration date

### Headers

API keys must be included in the `X-API-Key` header:

```
X-API-Key: your-api-key-here
```

The header name is case-insensitive.

### Components

#### Generator

The `generateApiKey` function creates new API keys:

```typescript
import { generateApiKey } from '@core/auth/api-key/generator';

const { key, hashedKey } = await generateApiKey();
```

Returns:

- `key`: The plain text API key to be shared with the client
- `hashedKey`: The hashed version to be stored in the database

#### Validator

The `ApiKeyValidator` class validates API keys:

```typescript
import { ApiKeyValidator } from '@core/auth/api-key/validator';

const validator = new ApiKeyValidator(app);
const result = await validator.validateApiKey(apiKey);
```

Returns:

- `isValid`: Boolean indicating if the key is valid
- `keyId`: The ID of the API key if valid
- `error`: Error message if invalid

#### Middleware

The `apiKeyMiddleware` protects routes that require API key authentication. See the [Middleware Documentation](../middleware/README.md) for details.

### Error Codes

| Error Code    | Description                        |
| ------------- | ---------------------------------- |
| `MISSING_KEY` | No API key provided in the request |
| `INVALID_KEY` | The provided API key is invalid    |
| `EXPIRED_KEY` | The provided API key has expired   |

### Security Considerations

1. API keys are never stored in plain text
2. Keys are hashed using SHA-256 before storage
3. Expired keys are automatically rejected
4. The original key is only shown once during generation
5. Keys should be transmitted over HTTPS only

### Best Practices

1. Generate API keys with appropriate expiration dates
2. Rotate API keys periodically
3. Use different API keys for different environments
4. Monitor API key usage for suspicious activity
5. Revoke compromised API keys immediately
