# Middleware Documentation

This directory contains documentation for all middleware used in the API.

## API Key Middleware

The API Key middleware (`apiKeyMiddleware`) is used to protect routes that require API key authentication.

### Usage

```typescript
import { apiKeyMiddleware } from '../../v1/external/middleware/api-key';

// Apply to a specific route
app.get('/protected-route', {
  preHandler: apiKeyMiddleware,
  handler: async (request, reply) => {
    // Your route handler
  },
});

// Apply to all routes in a plugin
app.register(async function (fastify) {
  fastify.addHook('preHandler', apiKeyMiddleware);
});
```

### Behavior

1. Checks for the presence of an API key in the `X-API-Key` header
2. Validates the API key against the database
3. Checks if the API key has expired
4. Attaches the API key ID to the request object for use in route handlers

### Error Responses

- `401 Unauthorized` with message "API key is required" if no API key is provided
- `401 Unauthorized` with message "Invalid API key" if the API key is invalid
- `401 Unauthorized` with message "API key has expired" if the API key has expired

### Request Object

The middleware attaches the following to the request object:

- `request.apiKeyId`: The ID of the validated API key

## Other Middleware

Additional middleware documentation will be added here as they are implemented.
