# Embeddings Submit API Route

## Overview

The `/embeddings/submit` endpoint allows external clients to submit raw text documents directly for embedding generation and storage. Unlike the `/embeddings/update` endpoint which retrieves documents from the database, this endpoint accepts text content directly in the request body.

## Endpoint

### POST /v1/external/embeddings/submit

Accepts raw text documents and generates embeddings for them using the same infrastructure as the existing `/embeddings/update` endpoint.

## Authentication

This endpoint requires authentication using either:

- `X-API-Key` header with a valid API key, OR
- `X-Internal-Secret` header with the internal secret

**Organization Scoping**: All requests must include the `X-Organization-Id` header to specify which organization the embeddings belong to.

## Headers

| Header              | Required | Type   | Description                                           |
| ------------------- | -------- | ------ | ----------------------------------------------------- |
| `X-API-Key`         | Yes\*    | string | API key for external authentication                   |
| `X-Internal-Secret` | Yes\*    | string | Internal secret for service-to-service authentication |
| `X-Organization-Id` | Yes      | string | Organization ID in format `org-{uuid}`                |
| `Content-Type`      | Yes      | string | Must be `application/json`                            |

\*Either `X-API-Key` or `X-Internal-Secret` is required.

## Request Body

```json
{
  "documents": [
    {
      "id": "doc-123",
      "name": "Sample Document",
      "content": "This is the text content to generate embeddings for...",
      "metadata": {
        "category": "technical",
        "author": "John Doe",
        "tags": ["api", "documentation"]
      }
    }
  ],
  "options": {
    "maxTokens": 1500,
    "overlapTokens": 150,
    "clearExisting": false
  }
}
```

### Request Schema

#### Documents Array

- **Type**: Array of document objects
- **Required**: Yes
- **Min Items**: 1
- **Max Items**: 100

#### Document Object

| Field      | Required | Type   | Description                                      |
| ---------- | -------- | ------ | ------------------------------------------------ |
| `id`       | Yes      | string | Unique identifier for the document               |
| `name`     | Yes      | string | Human-readable name for the document             |
| `content`  | Yes      | string | Text content (minimum 20 characters)             |
| `metadata` | No       | object | Optional metadata to associate with the document |

#### Options Object (Optional)

| Field           | Required | Type    | Default | Description                                               |
| --------------- | -------- | ------- | ------- | --------------------------------------------------------- |
| `maxTokens`     | No       | number  | 1500    | Maximum tokens per chunk for large documents (100-8000)   |
| `overlapTokens` | No       | number  | 150     | Number of overlapping tokens between chunks (0-500)       |
| `clearExisting` | No       | boolean | false   | Whether to clear existing embeddings for the organization |

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "organizationId": "org-123",
  "summary": {
    "documentsReceived": 5,
    "documentsProcessed": 5,
    "documentsIndexed": 5,
    "totalChunks": 12,
    "chunkedDocuments": ["doc-123", "doc-456"],
    "timestamp": "2025-01-16T10:30:00.000Z"
  },
  "message": "Documents submitted and embeddings generated successfully"
}
```

### Error Responses

#### Bad Request (400)

```json
{
  "error": "Bad Request",
  "code": "MISSING_ORGANIZATION_ID",
  "message": "X-Organization-Id header is required and must be a string"
}
```

#### Internal Server Error (500)

```json
{
  "error": "Internal Server Error",
  "code": "INDEXING_FAILED",
  "message": "Failed to index documents for semantic search",
  "details": "Specific error details..."
}
```

## Integration with Existing Infrastructure

### Redis Storage

- Uses the same Redis key patterns as `/embeddings/update`: `embeddings:org-{orgId}:doc-{docId}::{chunkNumber}`
- Stores embeddings with identical metadata structure
- Supports the same organization scoping and TTL policies

### Document Processing

- **Text Chunking**: Large documents are automatically chunked using `TextChunkingService`
- **Embedding Generation**: Uses `EmbeddingService` with OpenAI's `text-embedding-3-large` model
- **Content Hashing**: Implements incremental indexing to avoid re-processing unchanged content

### Semantic Search Integration

- Embeddings are immediately available for semantic search via existing search endpoints
- Uses `SemanticSearchServiceEnhanced` for optimized batch processing
- Supports the same filtering and similarity search capabilities

## Key Differences from `/embeddings/update`

| Aspect                 | `/embeddings/update`                    | `/embeddings/submit`                 |
| ---------------------- | --------------------------------------- | ------------------------------------ |
| **Data Source**        | Database documents via MCP tools        | Direct text content in request       |
| **Document Retrieval** | Queries database for active documents   | Accepts documents in request body    |
| **Content Processing** | Extracts text from various file formats | Processes plain text directly        |
| **Batch Size**         | Unlimited (all org documents)           | Limited to 100 documents per request |
| **Use Case**           | Organization-wide embedding refresh     | External client text submission      |

## Example Usage

### Submit Single Document

```bash
curl -X POST "http://localhost:3000/v1/external/embeddings/submit" \
  -H "X-API-Key: your-api-key" \
  -H "X-Organization-Id: org-123" \
  -H "Content-Type: application/json" \
  -d '{
    "documents": [{
      "id": "doc-001",
      "name": "API Documentation",
      "content": "This document describes the API endpoints...",
      "metadata": {
        "type": "documentation",
        "version": "1.0"
      }
    }]
  }'
```

### Submit Multiple Documents with Options

```bash
curl -k -X POST "https://localhost:8000/v1/external/embeddings/submit" \
  -H "X-Internal-Secret: dev-secret-key-123" \
  -H "X-Organization-ID: cm9of7eyo0006i1o45sqroxev" \
  -H "Content-Type: application/json" \
  -d '{
    "documents": [
      {
        "id": "grc-001",
        "name": "GRC Framework Overview",
        "content": "Q: What is GRC and why is it important for organizations? A: GRC (Governance, Risk, and Compliance) is a strategic framework that helps organizations align their IT activities with business objectives, manage risks effectively, and ensure compliance with regulatory requirements. It provides a structured approach to decision-making, risk assessment, and regulatory adherence, enabling organizations to operate more efficiently while maintaining security and meeting legal obligations."
      },
      {
        "id": "grc-002",
        "name": "Risk Management Process",
        "content": "Q: What are the key steps in the risk management process? A: The risk management process typically includes: 1) Risk identification - recognizing potential threats and vulnerabilities, 2) Risk assessment - evaluating the likelihood and impact of risks, 3) Risk mitigation - implementing controls to reduce risk exposure, 4) Risk monitoring - continuously tracking and reviewing risks, and 5) Risk reporting - communicating risk status to stakeholders and decision-makers."
      },
      {
        "id": "grc-003",
        "name": "Compliance Requirements",
        "content": "Q: How do organizations ensure compliance with industry regulations? A: Organizations ensure compliance through: 1) Regular compliance assessments and audits, 2) Implementation of compliance management systems, 3) Employee training and awareness programs, 4) Documentation of policies and procedures, 5) Continuous monitoring and reporting, 6) Regular updates to align with changing regulations, and 7) Establishing clear accountability and responsibility structures."
      },
      {
        "id": "grc-004",
        "name": "Governance Structure",
        "content": "Q: What are the essential components of effective corporate governance? A: Effective corporate governance includes: 1) Clear organizational structure with defined roles and responsibilities, 2) Transparent decision-making processes, 3) Regular board meetings and oversight, 4) Ethical standards and code of conduct, 5) Performance monitoring and evaluation, 6) Stakeholder communication and engagement, 7) Risk oversight and management, and 8) Compliance with legal and regulatory requirements."
      },
      {
        "id": "grc-005",
        "name": "GRC Technology Solutions",
        "content": "Q: What role does technology play in modern GRC programs? A: Technology is crucial for modern GRC programs by providing: 1) Automated risk assessment and monitoring tools, 2) Compliance management systems for tracking regulatory requirements, 3) Data analytics for identifying patterns and trends, 4) Workflow automation for policy management, 5) Real-time reporting and dashboards, 6) Integration capabilities across different business functions, 7) Audit trail and documentation management, and 8) Predictive analytics for proactive risk management."
      }
    ],
    "options": {
      "maxTokens": 2000,
      "overlapTokens": 200,
      "clearExisting": true
    }
  }'
```

## Error Codes

| Code                       | Description                                             |
| -------------------------- | ------------------------------------------------------- |
| `MISSING_ORGANIZATION_ID`  | X-Organization-Id header is missing or invalid          |
| `INVALID_ORGANIZATION_ID`  | Organization ID format is incorrect                     |
| `INVALID_REQUEST_BODY`     | Request body is malformed or missing documents array    |
| `INVALID_DOCUMENT`         | Document is missing required fields (id, name, content) |
| `INSUFFICIENT_CONTENT`     | Document content is too short (< 20 characters)         |
| `INDEXING_FAILED`          | Failed to generate embeddings or store in Redis         |
| `EMBEDDINGS_SUBMIT_FAILED` | General error during processing                         |

## Performance Considerations

- **Batch Processing**: Submit multiple documents in a single request for better performance
- **Chunking**: Large documents are automatically chunked to respect token limits
- **Incremental Indexing**: Only changed content is re-processed using content hashing
- **Redis Optimization**: Uses pipeline operations for efficient batch storage
