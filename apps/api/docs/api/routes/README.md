# API Route Folders

## Overview

Routes are organized by API version and by type (internal vs. external) to support clear separation of concerns, security, and maintainability. This structure enables easy API versioning and clear boundaries between endpoints for internal (frontend) and external (partner/client) use.

## Directory Structure

```
src/
└── api/
    └── v1/
        ├── internal/
        │   ├── routes/
        │   ├── controllers/
        │   ├── schemas/
        │   └── middleware/
        └── external/
            ├── routes/
            ├── controllers/
            ├── schemas/
            └── middleware/
```

- **internal/**: Endpoints for our frontend application (private, trusted).
- **external/**: Endpoints for partners/clients (public, stricter security).
- **routes/**: Route handlers (Fastify plugins).
- **controllers/**: Business logic for each route.
- **schemas/**: JSON schema validation for requests/responses.
- **middleware/**: Middleware specific to internal/external APIs.

## Adding a New Route

1. **Choose the correct version and type:**
   - Internal: `src/api/v1/internal/routes/`
   - External: `src/api/v1/external/routes/`
2. **Create a new file or folder** for your route (e.g., `users.ts` or `users/index.ts`).
3. **Implement the route as a Fastify plugin** and register controllers, schemas, and middleware as needed.
4. **Add corresponding controller, schema, and middleware files** in their respective folders.

## API Versioning

- Each API version is a separate directory under `src/api/` (e.g., `v1`, `v2`).
- To introduce breaking changes, create a new version directory and copy/adapt routes as needed.
- Maintain old versions for backward compatibility until officially deprecated.
- Document all changes and deprecations in the project changelog or API documentation.

## Example: Adding a v2 Route

1. Create `src/api/v2/internal/routes/yourRoute.ts` and implement the new logic.
2. Add/update controllers, schemas, and middleware in `src/api/v2/internal/` as needed.
3. Register the new version in your Fastify app if not autoloaded.

## References

- See `src/api/v1/internal/routes/` and `src/api/v1/external/routes/` for examples.
- For more on API versioning, see the [API Versioning Best Practices](../coding-rules.mdc).

# Routes Folder

Routes define endpoints within your application. Fastify provides an
easy path to a microservice architecture, in the future you might want
to independently deploy some of those.

In this folder you should define all the routes that define the endpoints
of your web application.
Each service is a [Fastify
plugin](https://www.fastify.io/docs/latest/Reference/Plugins/), it is
encapsulated (it can have its own independent plugins) and it is
typically stored in a file; be careful to group your routes logically,
e.g. all `/users` routes in a `users.js` file. We have added
a `root.js` file for you with a '/' root added.

If a single file become too large, create a folder and add a `index.js` file there:
this file must be a Fastify plugin, and it will be loaded automatically
by the application. You can now add as many files as you want inside that folder.
In this way you can create complex routes within a single monolith,
and eventually extract them.

If you need to share functionality between routes, place that
functionality into the `plugins` folder, and share it via
[decorators](https://www.fastify.io/docs/latest/Reference/Decorators/).

## Authentication Flow

The application uses JWT (JSON Web Tokens) for authentication with a dual-token system:

### Token Types

1. **Access Token**

   - Short-lived token (15 minutes by default)
   - Used for API requests
   - Contains user information and `type: 'access'`
   - Configured via `TOKEN_EXPIRY` environment variable

2. **Refresh Token**
   - Long-lived token (7 days by default)
   - Used to obtain new access tokens
   - Contains user information and `type: 'refresh'`
   - Configured via `REFRESH_TOKEN_EXPIRY` environment variable

### Authentication Flow

1. **Login/Registration**

   - User authenticates via email code or registration
   - Server generates both access and refresh tokens
   - Response includes:
     ```json
     {
       "access_token": "jwt.access.token",
       "refresh_token": "jwt.refresh.token",
       "user": {
         "id": "user_id",
         "email": "<EMAIL>",
         "name": "User Name",
         "organization_id": "org_id"
       }
     }
     ```

2. **API Requests**

   - Client includes access token in Authorization header
   - Server verifies token and type
   - If token is invalid or expired, request is rejected

3. **Token Refresh**
   - When access token expires, client uses refresh token
   - Server verifies refresh token and type
   - If valid, new access and refresh tokens are issued
   - If invalid, user must re-authenticate

### Security Features

- Token type checking prevents token misuse
- Short-lived access tokens minimize exposure
- Refresh tokens can be revoked if needed
- All tokens are signed with a secure secret
- Environment variables for configuration
- Type-safe implementation

### Environment Variables

```env
JWT_SECRET="your-secret-key"
TOKEN_EXPIRY="15m"
```

### Implementation Details

The JWT implementation is handled by the `@fastify/jwt` plugin with custom decorators for refresh token handling. The authentication flow is managed by the `AuthService` class, which provides methods for:

- Token generation
- Token verification
- Refresh token handling
- User authentication
- Session management

For more details, see the implementation in:

- `src/plugins/jwt.ts`
- `src/services/auth.ts`
- `src/types/auth.ts`
