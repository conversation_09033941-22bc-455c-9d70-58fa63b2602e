# Prompts API Route

## Overview

The prompts route exposes a minimal interface to retrieve and create prompts stored in Langfuse. Only the essential information is returned to reduce payload size.

## Endpoints

### GET /v1/external/prompts

Retrieves a prompt by name (and optionally version/label).

#### Authentication

This endpoint requires **internal secret** authentication using the `X-Internal-Secret` header. This is the same mechanism used for the embeddings route.

#### Query Parameters

| Parameter | Type   | Required | Description                             |
| --------- | ------ | -------- | --------------------------------------- |
| `name`    | string | Yes      | Unique prompt name                      |
| `version` | number | No       | Specific version to retrieve            |
| `label`   | string | No       | Label to filter (e.g. `latest`, `prod`) |

#### Request Example

```bash
curl -X GET "http://localhost:8000/v1/external/prompts?name=greeting&label=latest" \
  -H "X-Internal-Secret: your-internal-secret"
```

#### Success Response (200)

```json
{
  "name": "greeting",
  "version": 3,
  "tags": ["customer-support"],
  "prompt": "Hello {{user}}, how can I help you today?"
}
```

---

### POST /v1/external/prompts

Creates a new prompt in Langfuse.

#### Authentication

This endpoint requires **internal secret** authentication using the `X-Internal-Secret` header.

#### Request Body

`Content-Type: application/json`

The body must conform to the following schema (see `apps/api/src/types/api/prompts.ts`):

```jsonc
{
  "name": "system-default",
  "type": "text", // "chat" or "text" (defaults to "text")
  "prompt": "Hello {{user}}!", // string for text prompts or array for chat prompts
  "config": { "temperature": 0.7 },
}
```

#### Request Example

```bash
curl -X POST "http://localhost:3000/v1/external/prompts" \
  -H "X-Internal-Secret: your-internal-secret" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "system-default",
    "type": "text",
    "prompt": "Hello {{user}}, how can I help you today?"
  }'
```

#### Success Response (201)

```json
{
  "name": "system-default",
  "version": 1,
  "tags": ["customer-support"],
  "prompt": "Hello {{user}}, how can I help you today?"
}
```

## Error Responses

| Code                     | HTTP Status | Description                          |
| ------------------------ | ----------- | ------------------------------------ |
| `MISSING_NAME`           | 400         | Query parameter `name` missing       |
| `LANGFUSE_DISABLED`      | 503         | Langfuse not configured/enabled      |
| `PROMPT_NOT_FOUND`       | 404         | Prompt not found                     |
| `INVALID_BODY`           | 400         | Request body missing/invalid         |
| `MISSING_FIELDS`         | 400         | Required fields missing in body      |
| `PROMPT_CREATION_FAILED` | 500         | Langfuse failed to create the prompt |

## Integration Notes

- **Tags vs Labels**: The API returns the `tags` array. Langfuse internally maintains both tags and labels; tags will be returned if available, otherwise labels are used.
- **Prompt Types**: `text` prompts return the prompt as a string, while `chat` prompts return an array of chat messages.
- **Versioning**: Each creation increments the version automatically. Retrieve a specific version using the `version` query parameter.
