# Embeddings API Route

## Overview

The embeddings routes provide access to organization embeddings data stored in Redis. These routes allow external systems to:

- Retrieve metadata about content hashes, document cache, and embeddings for a specific organization
- Update embeddings by processing documents from the database
- Submit raw text documents directly for embedding generation

## Endpoints

**Note**: For detailed documentation on the new text submission endpoint, see [embeddings-submit.md](./embeddings-submit.md).

### GET /v1/external/embeddings

Retrieves embeddings data for a specific organization.

#### Authentication

This endpoint requires internal secret authentication using the `X-Internal-Secret` header.

#### Headers

| Header              | Type   | Required | Description                            |
| ------------------- | ------ | -------- | -------------------------------------- |
| `X-Internal-Secret` | string | Yes      | Internal API secret for authentication |
| `X-Organization-Id` | string | Yes      | Organization ID in format `org-{uuid}` |

#### Request Example

```bash
curl -X GET "http://localhost:3000/v1/external/embeddings" \
  -H "X-Internal-Secret: your-internal-secret" \
  -H "X-Organization-Id: your-organization-id"
```

#### Response Format

##### Success Response (200)

```json
{
  "organizationId": "your-organization-id",
  "summary": {
    "contentHashes": 10,
    "docCache": 5,
    "embeddings": 50,
    "totalKeys": 65,
    "totalSize": 1048576,
    "totalSizeFormatted": "1.0 MB"
  },
  "data": {
    "contentHashes": [...],
    "docCache": [...],
    "embeddings": [...]
  }
}
```

### POST /v1/external/embeddings/update

Updates embeddings for a specific organization by retrieving documents from the database and creating Redis indexes for cache and embeddings.

#### Authentication

This endpoint requires either API key authentication or internal secret authentication.

#### Headers

| Header              | Type   | Required | Description                            |
| ------------------- | ------ | -------- | -------------------------------------- |
| `X-API-Key`         | string | Yes\*    | API key for authentication             |
| `X-Internal-Secret` | string | Yes\*    | Internal API secret for authentication |
| `X-Organization-Id` | string | Yes      | Organization ID in format `org-{uuid}` |

\*Either `X-API-Key` or `X-Internal-Secret` is required.

#### Request Example

```bash
curl -X POST "http://localhost:3000/v1/external/embeddings/update" \
  -H "X-API-Key: your-api-key" \
  -H "X-Organization-Id: org-123"
```

#### Response Format

##### Success Response (200)

```json
{
  "success": true,
  "organizationId": "org-123",
  "summary": {
    "documentsRetrieved": 50,
    "documentsProcessed": 48,
    "documentsIndexed": 48,
    "timestamp": "2025-01-16T10:30:00.000Z"
  },
  "message": "Embeddings updated successfully"
}
```

##### Error Response (400)

```json
{
  "error": "Bad Request",
  "code": "MISSING_ORGANIZATION_ID",
  "message": "X-Organization-Id header is required and must be a string"
}
```

##### Error Response (500)

```json
{
  "error": "Internal Server Error",
  "code": "DOCUMENT_RETRIEVAL_FAILED",
  "message": "Failed to retrieve documents from database",
  "details": "Error details..."
}
```

## Process Flow

### Embeddings Update Process

The `POST /v1/external/embeddings/update` endpoint performs the following steps:

1. **Document Retrieval**: Calls the MCP tool `get_all_internal_documents` to retrieve all active documents for the organization
2. **Cache Clearing**: Clears existing embeddings, document cache, and content hashes for the organization
3. **Document Processing**: Processes each document using the document processor and sanitizer
4. **Indexing**: Indexes processed documents for semantic search using Redis
5. **Summary**: Returns a comprehensive summary of the operation

### Error Handling

The endpoint includes comprehensive error handling for:

- Missing or invalid organization ID
- MCP connection failures
- Document retrieval failures
- Document parsing errors
- Indexing failures
- Redis operation failures

## Usage Examples

### Update Embeddings for an Organization

```bash
# Using API key authentication
curl -X POST "http://localhost:3000/v1/external/embeddings/update" \
  -H "X-API-Key: your-api-key" \
  -H "X-Organization-Id: org-123"

# Using internal secret authentication
curl -X POST "http://localhost:3000/v1/external/embeddings/update" \
  -H "X-Internal-Secret: your-internal-secret" \
  -H "X-Organization-Id: org-123"
```

### Check Embeddings Status

```bash
curl -X GET "http://localhost:3000/v1/external/embeddings" \
  -H "X-Internal-Secret: your-internal-secret" \
  -H "X-Organization-Id: org-123"
```

## Integration Notes

- **Performance**: The update process can take several minutes for organizations with large document sets
- **Idempotency**: The endpoint is idempotent - calling it multiple times will produce the same result
- **Atomicity**: The process clears existing embeddings before creating new ones to ensure consistency
- **Error Recovery**: If the process fails, existing embeddings remain unchanged
- **Monitoring**: The endpoint provides detailed logging for monitoring and debugging

## Security Considerations

- **Authentication**: All endpoints require proper authentication
- **Organization Isolation**: Each organization's embeddings are isolated
- **Input Validation**: All inputs are validated and sanitized
- **Error Information**: Error responses do not expose sensitive internal details
