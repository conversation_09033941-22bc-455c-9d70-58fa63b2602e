# MCP Client Integration Guide

This guide provides comprehensive examples and instructions for integrating with the AskInfoSec MCP server.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Authentication](#authentication)
3. [Session Management](#session-management)
4. [WebSocket Communication](#websocket-communication)
5. [Tool Usage Examples](#tool-usage-examples)
6. [Client Libraries](#client-libraries)
7. [<PERSON><PERSON><PERSON>ling](#error-handling)
8. [Health Monitoring](#health-monitoring)
9. [Performance Best Practices](#performance-best-practices)

## Quick Start

### 1. Initialize MCP Session

First, authenticate and create an MCP session:

```javascript
// Using JWT authentication (internal)
const sessionResponse = await fetch('/api/v1/internal/mcp/session/init', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json',
  },
});

const sessionData = await sessionResponse.json();
console.log(sessionData);
// {
//   "session_id": "mcp_1704067200_abc123xyz",
//   "websocket_url": "ws://localhost:8000/api/v1/internal/mcp/ws/mcp_1704067200_abc123xyz",
//   "session_token": "session.jwt.token",
//   "expires_at": "2024-01-01T12:30:00.000Z"
// }
```

### 2. Establish WebSocket Connection

```javascript
const ws = new WebSocket(sessionData.websocket_url);

ws.onopen = () => {
  console.log('MCP WebSocket connected');

  // Initialize MCP protocol
  ws.send(
    JSON.stringify({
      jsonrpc: '2.0',
      method: 'initialize',
      params: {
        protocolVersion: '2025-03-26',
        capabilities: {
          roots: { listChanged: true },
          sampling: {},
        },
        clientInfo: {
          name: 'AskInfoSec Client',
          version: '0.0.1',
        },
      },
      id: 1,
    })
  );
};

ws.onmessage = event => {
  const response = JSON.parse(event.data);
  console.log('MCP Response:', response);
};
```

## Authentication

### JWT Token Authentication (Internal Routes)

For internal applications using JWT tokens:

```typescript
interface MCPClientConfig {
  apiBase: string;
  jwtToken: string;
}

class MCPClient {
  constructor(private config: MCPClientConfig) {}

  async createSession(): Promise<SessionInfo> {
    const response = await fetch(`${this.config.apiBase}/api/v1/internal/mcp/session/init`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.config.jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Session creation failed: ${response.statusText}`);
    }

    return await response.json();
  }
}
```

### API Key Authentication (External Routes)

For external applications using API keys:

```typescript
// Future implementation for external routes
class ExternalMCPClient {
  constructor(
    private apiKey: string,
    private apiBase: string
  ) {}

  async createSession(): Promise<SessionInfo> {
    const response = await fetch(`${this.apiBase}/api/v1/external/mcp/session/init`, {
      method: 'POST',
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
      },
    });

    return await response.json();
  }
}
```

## Session Management

### Session Lifecycle

```typescript
class SessionManager {
  private session: SessionInfo | null = null;
  private ws: WebSocket | null = null;

  async initializeSession(client: MCPClient): Promise<void> {
    this.session = await client.createSession();

    // Connect WebSocket
    this.ws = new WebSocket(this.session.websocket_url);

    // Set up connection handlers
    this.setupWebSocketHandlers();
  }

  async checkSessionHealth(): Promise<boolean> {
    if (!this.session) return false;

    try {
      const response = await fetch(
        `/api/v1/internal/mcp/session/${this.session.session_id}/status`,
        {
          headers: {
            Authorization: `Bearer ${this.session.session_token}`,
          },
        }
      );

      return response.ok;
    } catch (error) {
      return false;
    }
  }

  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => this.initializeProtocol();
    this.ws.onmessage = event => this.handleMessage(event);
    this.ws.onclose = () => this.handleDisconnect();
    this.ws.onerror = error => this.handleError(error);
  }
}
```

## WebSocket Communication

### JSON-RPC 2.0 Protocol

All MCP communication uses JSON-RPC 2.0 format:

```typescript
interface JSONRPCRequest {
  jsonrpc: '2.0';
  method: string;
  params?: any;
  id: number | string;
}

interface JSONRPCResponse {
  jsonrpc: '2.0';
  result?: any;
  error?: {
    code: number;
    message: string;
  };
  id: number | string | null;
}
```

### Protocol Initialization

```javascript
// Send initialization request
const initRequest = {
  jsonrpc: '2.0',
  method: 'initialize',
  params: {
    protocolVersion: '2025-03-26',
    capabilities: {
      roots: { listChanged: true },
      sampling: {},
    },
    clientInfo: {
      name: 'Your Client Name',
      version: '0.0.1',
    },
  },
  id: 1,
};

ws.send(JSON.stringify(initRequest));

// Expected response
// {
//   "jsonrpc": "2.0",
//   "id": 1,
//   "result": {
//     "protocolVersion": "2025-03-26",
//     "capabilities": {
//       "resources": { "subscribe": true, "listChanged": true },
//       "tools": { "listChanged": true },
//       "prompts": { "listChanged": true },
//       "logging": {}
//     },
//     "serverInfo": {
//       "name": "AskInfoSec MCP Server",
//       "version": "0.0.1"
//     }
//   }
// }
```

## Enhanced Tool Features

### Tool Categories and Organization

The MCP server organizes tools into logical categories for better discoverability:

- **Database**: Data querying and analysis tools (`query_database`)
- **Context**: Session context management (`get_context`, `set_context`)
- **System**: Health monitoring and status tools (`system_status`)
- **Analysis**: Advanced data analysis with async processing (`analyze_data`)
- **Async**: Task management for long-running operations (`get_task_status`, `cancel_task`, `list_tasks`)

### Enhanced Tools List Response

```javascript
// tools/list now returns categorized tools with metadata
const listToolsRequest = {
  jsonrpc: '2.0',
  method: 'tools/list',
  id: 2,
};

// Response includes categories and metadata
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "tools": [
      {
        "name": "analyze_data",
        "description": "Perform advanced data analysis...",
        "category": "analysis",
        "async": true,
        "estimatedDuration": 30000
      }
      // ... other tools
    ],
    "categories": {
      "database": [...],
      "context": [...],
      "system": [...],
      "analysis": [...],
      "async": [...]
    },
    "metadata": {
      "total_tools": 8,
      "async_tools": 1,
      "categories_available": ["database", "context", "system", "analysis", "async"]
    }
  }
}
```

### Async Operations Support

Long-running operations return a task ID immediately, allowing you to monitor progress:

```javascript
// Start async analysis (returns immediately)
const startAnalysis = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'analyze_data',
    arguments: {
      query: 'SELECT * FROM user_activities WHERE created_at > NOW() - INTERVAL 30 DAY',
      analysis_type: 'security', // or 'performance', 'trends'
      options: {
        time_range: '30d',
        filters: { severity: 'high' },
      },
    },
  },
  id: 5,
};

// Immediate response with task ID
{
  "jsonrpc": "2.0",
  "id": 5,
  "result": {
    "content": [{
      "type": "text",
      "text": "{\n  \"success\": true,\n  \"task_id\": \"task_1234567890_abc123\",\n  \"status\": \"processing\",\n  \"message\": \"Analysis started. Use get_task_status to check progress.\"\n}"
    }]
  }
}
```

### Task Management

```javascript
// Check task progress
const checkStatus = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'get_task_status',
    arguments: {
      task_id: 'task_1234567890_abc123',
    },
  },
  id: 6,
};

// List all tasks for session
const listTasks = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'list_tasks',
    arguments: {
      status: 'active', // 'all', 'active', 'completed', 'failed'
      limit: 20,
    },
  },
  id: 7,
};

// Cancel running task
const cancelTask = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'cancel_task',
    arguments: {
      task_id: 'task_1234567890_abc123',
    },
  },
  id: 8,
};
```

## Tool Usage Examples

### 1. Database Queries

```javascript
// List available tools
const listToolsRequest = {
  jsonrpc: '2.0',
  method: 'tools/list',
  id: 2,
};

ws.send(JSON.stringify(listToolsRequest));

// Execute a database query
const queryRequest = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'query_database',
    arguments: {
      query: 'SELECT id, name, created_at FROM organizations LIMIT 10',
    },
  },
  id: 3,
};

ws.send(JSON.stringify(queryRequest));

// Expected response
// {
//   "jsonrpc": "2.0",
//   "id": 3,
//   "result": {
//     "content": [{
//       "type": "text",
//       "text": "{\n  \"success\": true,\n  \"rowCount\": 3,\n  \"data\": [...]\n}"
//     }]
//   }
// }
```

### 2. Context Management

```javascript
// Set context data
const setContextRequest = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'set_context',
    arguments: {
      key: 'user_preferences',
      data: {
        theme: 'dark',
        language: 'en',
        notifications: true,
      },
    },
  },
  id: 4,
};

ws.send(JSON.stringify(setContextRequest));

// Get context data
const getContextRequest = {
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'get_context',
    arguments: {
      key: 'user_preferences',
    },
  },
  id: 5,
};

ws.send(JSON.stringify(getContextRequest));
```

### 3. Resource Access

```javascript
// List available resources
const listResourcesRequest = {
  jsonrpc: '2.0',
  method: 'resources/list',
  id: 6,
};

ws.send(JSON.stringify(listResourcesRequest));

// Read organization data
const readResourceRequest = {
  jsonrpc: '2.0',
  method: 'resources/read',
  params: {
    uri: 'database://organizations',
  },
  id: 7,
};

ws.send(JSON.stringify(readResourceRequest));
```

## Client Libraries

### TypeScript/JavaScript Client

```typescript
export class AskInfoSecMCPClient {
  private ws: WebSocket | null = null;
  private requestId = 0;
  private pendingRequests = new Map<
    number,
    {
      resolve: (value: any) => void;
      reject: (error: Error) => void;
    }
  >();

  constructor(
    private sessionInfo: SessionInfo,
    private options: ClientOptions = {}
  ) {}

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.sessionInfo.websocket_url);

      this.ws.onopen = () => {
        this.initialize().then(resolve).catch(reject);
      };

      this.ws.onmessage = event => {
        this.handleResponse(JSON.parse(event.data));
      };

      this.ws.onerror = reject;
    });
  }

  async callTool(name: string, args: any): Promise<any> {
    return this.sendRequest('tools/call', {
      name,
      arguments: args,
    });
  }

  async listTools(): Promise<any> {
    return this.sendRequest('tools/list');
  }

  async listResources(): Promise<any> {
    return this.sendRequest('resources/list');
  }

  async readResource(uri: string): Promise<any> {
    return this.sendRequest('resources/read', { uri });
  }

  private async sendRequest(method: string, params?: any): Promise<any> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected');
    }

    const id = ++this.requestId;
    const request = {
      jsonrpc: '2.0',
      method,
      params,
      id,
    };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      this.ws!.send(JSON.stringify(request));

      // Set timeout
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out`));
        }
      }, this.options.timeout || 30000);
    });
  }

  private handleResponse(response: JSONRPCResponse): void {
    const { id, result, error } = response;

    if (id && this.pendingRequests.has(id as number)) {
      const { resolve, reject } = this.pendingRequests.get(id as number)!;
      this.pendingRequests.delete(id as number);

      if (error) {
        reject(new Error(`${error.code}: ${error.message}`));
      } else {
        resolve(result);
      }
    }
  }
}
```

### Python Client

```python
import asyncio
import json
import websockets
from typing import Dict, Any, Optional

class AskInfoSecMCPClient:
    def __init__(self, session_info: Dict[str, Any]):
        self.session_info = session_info
        self.ws = None
        self.request_id = 0
        self.pending_requests = {}

    async def connect(self):
        self.ws = await websockets.connect(self.session_info['websocket_url'])
        await self.initialize()

        # Start message handler
        asyncio.create_task(self._handle_messages())

    async def initialize(self):
        response = await self.send_request('initialize', {
            'protocolVersion': '2025-03-26',
            'capabilities': {
                'roots': {'listChanged': True},
                'sampling': {}
            },
            'clientInfo': {
                'name': 'Python MCP Client',
                'version': '1.0.0'
            }
        })
        return response

    async def call_tool(self, name: str, args: Dict[str, Any]) -> Any:
        return await self.send_request('tools/call', {
            'name': name,
            'arguments': args
        })

    async def list_tools(self) -> Any:
        return await self.send_request('tools/list')

    async def send_request(self, method: str, params: Optional[Dict] = None) -> Any:
        self.request_id += 1
        request = {
            'jsonrpc': '2.0',
            'method': method,
            'id': self.request_id
        }

        if params:
            request['params'] = params

        future = asyncio.Future()
        self.pending_requests[self.request_id] = future

        await self.ws.send(json.dumps(request))
        return await future

    async def _handle_messages(self):
        async for message in self.ws:
            response = json.loads(message)
            request_id = response.get('id')

            if request_id and request_id in self.pending_requests:
                future = self.pending_requests.pop(request_id)

                if 'error' in response:
                    future.set_exception(Exception(response['error']['message']))
                else:
                    future.set_result(response.get('result'))
```

## Error Handling

### Common Error Codes

```typescript
enum MCPErrorCode {
  // JSON-RPC standard errors
  PARSE_ERROR = -32700,
  INVALID_REQUEST = -32600,
  METHOD_NOT_FOUND = -32601,
  INVALID_PARAMS = -32602,
  INTERNAL_ERROR = -32603,

  // MCP specific errors
  INVALID_SESSION = -32001,
  SESSION_EXPIRED = -32002,
  TOOL_ERROR = -32003,
  RESOURCE_NOT_FOUND = -32004,
}

class MCPError extends Error {
  constructor(
    public code: number,
    message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

// Error handling example
try {
  const result = await client.callTool('query_database', {
    query: 'INVALID SQL',
  });
} catch (error) {
  if (error instanceof MCPError) {
    switch (error.code) {
      case MCPErrorCode.INVALID_PARAMS:
        console.error('Invalid parameters:', error.message);
        break;
      case MCPErrorCode.TOOL_ERROR:
        console.error('Tool execution failed:', error.message);
        break;
      default:
        console.error('MCP error:', error.message);
    }
  }
}
```

## Health Monitoring

### Check Server Health

```javascript
// Check overall MCP server health
const healthResponse = await fetch('/api/v1/internal/mcp/health');
const health = await healthResponse.json();

console.log('Server Health:', health);
// {
//   "status": "healthy",
//   "timestamp": "2024-01-01T12:00:00.000Z",
//   "uptime": 86400,
//   "checks": [...],
//   "summary": {
//     "total": 3,
//     "healthy": 3,
//     "unhealthy": 0,
//     "degraded": 0
//   }
// }

// Check specific components
const redisHealth = await fetch('/api/v1/internal/mcp/health/redis');
const dbHealth = await fetch('/api/v1/internal/mcp/health/database');
const sessionsHealth = await fetch('/api/v1/internal/mcp/health/sessions');
```

### Monitor Metrics

```javascript
// Get server metrics
const metricsResponse = await fetch('/api/v1/internal/mcp/metrics');
const metrics = await metricsResponse.json();

console.log('Server Metrics:', metrics);
// {
//   "timestamp": "2024-01-01T12:00:00.000Z",
//   "sessions": {
//     "active": 15,
//     "total_created": 150,
//     "expired": 10,
//     "average_duration": 1800000
//   },
//   "tools": {
//     "total_calls": 500,
//     "calls_by_tool": {
//       "query_database": 300,
//       "get_context": 100,
//       "set_context": 100
//     },
//     "success_rate": 98.5,
//     "average_response_time": 250
//   },
//   ...
// }
```

## Performance Best Practices

### 1. Connection Management

```typescript
class ConnectionPool {
  private connections: Map<string, AskInfoSecMCPClient> = new Map();

  async getConnection(userId: string): Promise<AskInfoSecMCPClient> {
    let client = this.connections.get(userId);

    if (!client || !client.isConnected()) {
      // Create new session
      const sessionInfo = await this.createSession(userId);
      client = new AskInfoSecMCPClient(sessionInfo);
      await client.connect();

      this.connections.set(userId, client);
    }

    return client;
  }
}
```

### 2. Request Batching

```typescript
class BatchProcessor {
  private batchQueue: Array<{
    method: string;
    params: any;
    resolve: Function;
    reject: Function;
  }> = [];

  async batchCall(requests: Array<{ method: string; params: any }>): Promise<any[]> {
    // Implement batching logic for multiple tool calls
    return Promise.all(requests.map(req => this.client.sendRequest(req.method, req.params)));
  }
}
```

### 3. Caching Strategy

```typescript
class CacheManager {
  private cache = new Map<string, { data: any; expires: number }>();

  async cachedToolCall(name: string, args: any, ttl: number = 300000): Promise<any> {
    const key = `${name}:${JSON.stringify(args)}`;
    const cached = this.cache.get(key);

    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }

    const result = await this.client.callTool(name, args);
    this.cache.set(key, {
      data: result,
      expires: Date.now() + ttl,
    });

    return result;
  }
}
```

## Testing

### Unit Tests

```typescript
describe('MCP Client', () => {
  let client: AskInfoSecMCPClient;
  let mockWebSocket: jest.Mocked<WebSocket>;

  beforeEach(() => {
    // Setup mock WebSocket
    mockWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      readyState: WebSocket.OPEN,
    } as any;

    client = new AskInfoSecMCPClient(mockSessionInfo);
    (client as any).ws = mockWebSocket;
  });

  test('should call database tool', async () => {
    const promise = client.callTool('query_database', {
      query: 'SELECT * FROM users',
    });

    // Simulate server response
    const response = {
      jsonrpc: '2.0',
      id: 1,
      result: { content: [{ type: 'text', text: '{"success": true}' }] },
    };

    (client as any).handleResponse(response);

    const result = await promise;
    expect(result.content[0].text).toContain('success');
  });
});
```

### Integration Tests

```typescript
describe('MCP Integration', () => {
  test('should create session and execute tools', async () => {
    const mcpClient = new MCPClient({
      apiBase: 'http://localhost:8000',
      jwtToken: process.env.TEST_JWT_TOKEN!,
    });

    const sessionInfo = await mcpClient.createSession();
    expect(sessionInfo.session_id).toMatch(/^mcp_/);

    const client = new AskInfoSecMCPClient(sessionInfo);
    await client.connect();

    const tools = await client.listTools();
    expect(tools.tools).toHaveLength(3);

    const result = await client.callTool('get_context', {});
    expect(result).toBeDefined();
  });
});
```

This guide provides a comprehensive foundation for integrating with the AskInfoSec MCP server. For additional support, refer to the [MCP specification](https://modelcontextprotocol.io) or contact the development team.
