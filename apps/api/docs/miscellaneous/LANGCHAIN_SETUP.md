## LangChain Setup Guide

This document describes **where** and **how** the project wires <PERSON><PERSON><PERSON><PERSON> into the existing Fastify service. It is intentionally implementation-agnostic—use it as a reference when adding new LangChain chains, tools or models.

### 1. Directory Layout

```
src/
  core/
    langchain/          # ⬅ centralised LangChain configuration
      client.ts         # OpenAI / other LLM client factory
      embeddings.ts     # Embedding model factory
      vectorstore.ts    # Vector store factory (e.g. Redis, PGVector)
      runnables.ts      # Re-usable chains, prompts and utilities
      index.ts          # Re-exports for convenience
  plugins/
    langchain.ts        # Fastify plugin registering shared LangChain instances
```

All LangChain-specific code lives under `src/core/langchain` to keep concerns isolated and discoverable.

### 2. Environment Variables

Secrets and tuning parameters are **never** hard-coded. Extend `src/config/env/env.ts` with any of the following keys as needed:

```
OPENAI_API_KEY                # Existing – used by LangChain client
LANGCHAIN_MODEL               # e.g. "gpt-4o-mini"
LANGCHAIN_EMBEDDINGS          # e.g. "text-embedding-3-small"
LANGCHAIN_TEMPERATURE         # e.g. "0.7" for model creativity control
LANGCHAIN_MAX_TOKENS          # e.g. "1000" for response length limits
LANGCHAIN_MAX_RETRIES         # e.g. "3" for API retry attempts
LANGCHAIN_REQUEST_TIMEOUT     # e.g. "30000" (milliseconds)
LANGCHAIN_TRACING             # "true" to enable tracing via env var (optional)
LANGSMITH_API_KEY             # For LangSmith monitoring (optional)
LANGCHAIN_RATE_LIMIT_RPM      # e.g. "60" for requests per minute
```

Validation is handled automatically by the `@fastify/env` plugin so invalid or missing values fail fast at boot time.

### 3. Fastify Plugin

`src/plugins/langchain.ts` is the single entry point that:

1. Imports the factories from `src/core/langchain`.
2. Instantiates each once per process with proper error handling and configuration.
3. Decorates the Fastify instance, e.g. `fastify.langchain = { client, embeddings, vectorstore }`.
4. Implements rate limiting and request retry logic to handle production load.
5. Sets up monitoring and observability hooks for performance tracking.

That decoration makes LangChain resources available in controllers and services via `fastify.langchain` without repeated construction.

### 4. TypeScript Declarations

Extend Fastify's module declaration so that the IDE recognises the new decorator:

```ts
declare module 'fastify' {
  interface FastifyInstance {
    langchain: {
      client: ChatOpenAI;
      embeddings: Embeddings;
      vectorstore: VectorStore;
      // Additional utilities for production use
      rateLimiter?: RateLimiter;
      monitor?: LangChainMonitor;
      cache?: LangChainCache;
    };
  }
}
```

**Type Safety Benefits:**

- Compile-time validation of LLM inputs/outputs
- Prevention of runtime errors from mismatched data types
- Enhanced IDE autocomplete and refactoring support
- Clear contracts for prompt templates and variables

### 5. Testing Strategy

- **Unit tests:** Provide mocks in `test/fixtures/mocks/langchain/` to isolate business logic from external calls.
- **Integration tests:** Spin up the Fastify app normally; the real LangChain objects are already injected by the plugin.
- **Load testing:** Test rate limiting and concurrency controls under high load scenarios.
- **Error handling:** Verify retry logic, timeout handling, and graceful degradation.
- **Prompt testing:** Version control and regression testing for prompt templates.

### 6. Production Considerations

**Error Handling & Resilience:**

- Implement exponential backoff for API failures
- Set appropriate timeouts for all LLM calls
- Handle rate limiting gracefully with queuing
- Log errors with sufficient context for debugging

**Performance & Monitoring:**

- Track token usage and costs per request
- Monitor response latency and success rates
- Implement caching for repeated queries
- Set up alerts for unusual patterns or failures

**Security:**

- Validate all inputs before passing to LLMs
- Sanitize outputs to prevent injection attacks
- Implement proper authentication for API endpoints
- Use environment variables for all sensitive data

### 7. Further Reading

For hands-on examples and best practices, consult the official LangChain JS tutorials:

- Build a simple LLM application – <https://js.langchain.com/docs/tutorials/build_a_simple_llm_application_with_chat_models_and_prompt_templates>
- Build a semantic search engine – <https://js.langchain.com/docs/tutorials/build_a_semantic_search_engine>
- Rate limiting guide – <https://python.langchain.com/docs/how_to/chat_model_rate_limiting/>
- LangSmith for monitoring – <https://docs.smith.langchain.com/>

These guides illustrate concepts like prompt templates, embeddings, vector stores and RAG patterns that you will integrate using the structure defined above.

---

_Last updated: 2025-01-27_
