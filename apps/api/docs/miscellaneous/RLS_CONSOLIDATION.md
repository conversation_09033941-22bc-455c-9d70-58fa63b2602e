# Row Level Security (RLS) Consolidation Plan

## Current State

The project currently has two separate implementations of Row Level Security (RLS) for multi-tenant isolation:

1. **Fastify Plugin Implementation** (`/src/plugins/prisma.ts`):

   - Initializes and attaches the PrismaClient to the Fastify instance
   - Provides `prismaWithTenant(orgId)` and `prismaBypassRLS()` methods
   - Uses Prisma's `$extends` API with transaction-based session variable setting

2. **Standalone Implementation** (`/src/lib/prisma/prisma.ts`):
   - Provides a `tenantGuarded` function that extends PrismaClient
   - Used directly in some services without going through the Fastify instance

This duplication creates maintenance challenges and potential inconsistencies in how RLS is applied across the application.

## Consolidation Goals

1. **Single Source of Truth**: Maintain one implementation of RLS logic
2. **Consistent API**: Provide a consistent interface for accessing tenant-isolated data
3. **Backward Compatibility**: Minimize disruption to existing code
4. **Testability**: Ensure the consolidated implementation is easy to test

## Consolidation Approach

### 1. Create a Unified RLS Implementation

Create a unified implementation in `/src/lib/prisma/rls.ts` that will be used by both the Fastify plugin and any standalone usage:

```typescript
// /src/lib/prisma/rls.ts
import { PrismaClient } from '@prisma/client';

/**
 * Extends a PrismaClient instance with tenant isolation
 * @param prisma The PrismaClient instance to extend
 * @param orgId The organization ID to restrict queries to
 * @returns A PrismaClient instance with tenant isolation
 */
export function withTenantIsolation(prisma: PrismaClient, orgId: string): PrismaClient {
  // If prisma is a stub/mock without $extends, return it directly (e.g., during tests)
  if (typeof (prisma as any).$extends !== 'function') {
    return prisma;
  }

  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }) {
          const [, , result] = await prisma.$transaction([
            prisma.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
            prisma.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
}

/**
 * Extends a PrismaClient instance to bypass RLS
 * @param prisma The PrismaClient instance to extend
 * @returns A PrismaClient instance with RLS bypassed
 */
export function withBypassRLS(prisma: PrismaClient): PrismaClient {
  // If prisma is a stub/mock without $extends, return it directly (e.g., during tests)
  if (typeof (prisma as any).$extends !== 'function') {
    return prisma;
  }

  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }) {
          const [, result] = await prisma.$transaction([
            prisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
}
```

### 2. Update the Fastify Plugin

Update the Fastify plugin to use the unified implementation:

```typescript
// /src/plugins/prisma.ts
import { PrismaClient } from '@prisma/client';
import type { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';
import { withTenantIsolation, withBypassRLS } from '../lib/prisma/rls';

// ... existing code ...

const prismaPlugin: FastifyPluginAsync<PrismaPluginOptions> = async function (fastify, opts) {
  if (!fastify.hasDecorator('prisma')) {
    const client =
      'client' in opts
        ? opts.client
        : new PrismaClient({
            log: [{ emit: 'stdout', level: 'error' }],
            ...('clientConfig' in opts ? opts.clientConfig : {}),
          });

    await client.$connect();

    // Decorate the fastify instance with the Prisma client
    fastify.decorate('prisma', client);

    // Add the prismaWithTenant method for tenant isolation
    fastify.decorate('prismaWithTenant', function (orgId: string) {
      return withTenantIsolation(this.prisma, orgId);
    });

    // Add the prismaBypassRLS method
    fastify.decorate('prismaBypassRLS', function () {
      return withBypassRLS(this.prisma);
    });

    // Handle cleanup
    fastify.addHook('onClose', async server => {
      await server.prisma.$disconnect();
    });
  } else {
    throw new Error(
      'A `prisma` decorator has already been registered, please ensure you are not registering multiple instances of this plugin'
    );
  }
};
```

### 3. Update the Standalone Implementation

Update the standalone implementation to use the unified functions:

```typescript
// /src/lib/prisma/prisma.ts
import { PrismaClient } from '@prisma/client';
import { withTenantIsolation, withBypassRLS } from './rls';

// Singleton PrismaClient instance
const prisma = new PrismaClient({
  log: [{ emit: 'stdout', level: 'error' }],
});

/**
 * Get a PrismaClient instance with tenant isolation
 * @param orgId Organization ID to restrict queries to
 * @returns A PrismaClient instance with tenant isolation
 */
export function tenantGuarded(orgId: string): PrismaClient {
  return withTenantIsolation(prisma, orgId);
}

/**
 * Get a PrismaClient instance with RLS bypassed
 * @returns A PrismaClient instance with RLS bypassed
 */
export function bypassedPrisma(): PrismaClient {
  return withBypassRLS(prisma);
}

// Export the base PrismaClient instance
export default prisma;
```

### 4. Update Service Code

Update any service code that directly uses the `tenantGuarded` function:

```typescript
// Before
import { tenantGuarded } from './prisma';
const db = tenantGuarded(orgId);

// After - Option 1: Continue using the standalone implementation
import { tenantGuarded } from '../../lib/prisma/prisma';
const db = tenantGuarded(orgId);

// After - Option 2: Use the Fastify instance (preferred for consistency)
const db = fastify.prismaWithTenant(orgId);
```

## Implementation Plan

1. **Create Unified Implementation**:

   - Create `/src/lib/prisma/rls.ts` with the shared functions
   - Write tests for the shared functions

2. **Update Fastify Plugin**:

   - Modify `/src/plugins/prisma.ts` to use the shared functions
   - Update tests for the Fastify plugin

3. **Update Standalone Implementation**:

   - Modify `/src/lib/prisma/prisma.ts` to use the shared functions
   - Update tests for the standalone implementation

4. **Update Service Code**:

   - Identify all services using the standalone implementation
   - Update them to use the preferred approach (Fastify instance or updated standalone functions)

5. **Comprehensive Testing**:
   - Run all tests to ensure the consolidation doesn't break existing functionality
   - Add new tests for edge cases

## Migration Strategy

To minimize disruption, follow this migration strategy:

1. **Implement Changes in a Feature Branch**:

   - Create a new branch for the consolidation work
   - Implement and test all changes in isolation

2. **Gradual Service Updates**:

   - Update services one by one, starting with less critical ones
   - Test thoroughly after each update

3. **Documentation**:

   - Update documentation to reflect the consolidated approach
   - Provide migration examples for developers

4. **Code Review**:

   - Conduct a thorough code review with the team
   - Address any concerns or edge cases

5. **Deployment**:
   - Deploy the changes in a staging environment
   - Monitor for any issues before deploying to production

## Benefits of Consolidation

1. **Reduced Maintenance**: Single implementation to maintain and update
2. **Consistent Behavior**: Ensures RLS is applied consistently across the application
3. **Improved Testability**: Easier to test with a single implementation
4. **Better Developer Experience**: Clear, consistent API for working with tenant-isolated data

## Potential Challenges

1. **Breaking Changes**: Services directly using the standalone implementation may need updates
2. **Testing Complexity**: Ensuring all edge cases are covered
3. **Performance Considerations**: Ensuring the consolidated implementation maintains performance

## Conclusion

By consolidating the two RLS implementations, we can achieve a more maintainable, consistent, and robust approach to multi-tenant isolation. The proposed plan provides a clear path forward while minimizing disruption to existing code.
