# Testing Guide

This document provides guidance on testing the AskInfoSec API, with a focus on the Prisma Row Level Security (RLS) implementation.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Structure](#test-structure)
3. [Mock Implementation](#mock-implementation)
4. [Running Tests](#running-tests)
5. [Writing New Tests](#writing-new-tests)

## Testing Philosophy

Our testing approach follows these principles:

1. **Isolation**: Tests should be isolated and not depend on external services or databases.
2. **Predictability**: Tests should produce the same results consistently.
3. **Coverage**: Tests should cover both happy paths and edge cases.
4. **Readability**: Tests should be easy to understand and maintain.

## Test Structure

The test suite is organized into the following categories:

- **Unit Tests**: Test individual functions and components in isolation.
- **Integration Tests**: Test the interaction between components.
- **Route Tests**: Test API endpoints and their responses.
- **Plugin Tests**: Test Fastify plugins.

## Mock Implementation

### Prisma Mock

The Prisma client is mocked to simulate database operations without requiring a real database connection. The mock implementation is located at `/test/mocks/prisma.ts`.

#### Creating a Mock Prisma Client

```typescript
// Import the mock creator
import { createMockPrismaClient } from '../mocks/prisma';

// Create a mock Prisma client
const mockPrisma = createMockPrismaClient();

// Use the mock client in tests
const result = await mockPrisma.organization.findUnique({
  where: { id: 'test-org-id' },
});
```

#### Setting Up Mock Prisma on Fastify

For tests that need to use the Fastify instance with Prisma, use the `setupMockPrisma` helper:

```typescript
// Import the setup helper
import { setupMockPrisma } from '../mocks/prisma';

// Set up the mock on a Fastify instance
const app = await build(t, {
  onBuild: fastify => {
    setupMockPrisma(fastify);
  },
});
```

### Custom Mocks

For specific test cases, you can extend the default mock implementation:

```typescript
// Create a custom mock with specific behavior
const customMockPrisma = {
  ...createMockPrismaClient(),
  organization: {
    ...createMockPrismaClient().organization,
    findUnique: async (args: any) => {
      // Custom implementation for this specific test
      if (args.where.id === 'special-case') {
        return { id: 'special-case', company_name: 'Special Case Org' };
      }
      return null;
    },
  },
};
```

## Running Tests

### Running All Tests

```bash
$ pnpm test
```

### Running Specific Test Files

```bash
$ pnpm test test/integration/organization.int.test.ts
```

### Running Tests with Coverage

```bash
$ pnpm test:coverage
```

## Writing New Tests

### Testing Database Operations

When testing database operations, focus on the behavior rather than the actual database interaction:

```typescript
test('organization can be retrieved by ID', async t => {
  const mockPrisma = createMockPrismaClient();
  const orgId = 'test-org-id';

  const org = await mockPrisma.organization.findUnique({
    where: { id: orgId },
  });

  t.ok(org, 'organization was found');
  t.equal(org?.id, orgId, 'organization has correct ID');
});
```

### Testing Tenant Isolation

To test tenant isolation, verify that the `prismaWithTenant` method is called with the correct organization ID:

```typescript
test('tenant isolation is enforced', async t => {
  const app = await build(t, {
    onBuild: fastify => {
      // Replace the prismaWithTenant method with a spy
      const originalMethod = fastify.prismaWithTenant;
      let calledWithOrgId: string | null = null;

      fastify.prismaWithTenant = function (orgId: string) {
        calledWithOrgId = orgId;
        return originalMethod.call(this, orgId);
      };

      // Store the spy for later assertion
      fastify.testContext = { calledWithOrgId: () => calledWithOrgId };
    },
  });

  // Make a request that should use tenant isolation
  await app.inject({
    method: 'GET',
    url: '/organization/test-org-id',
  });

  // Assert that prismaWithTenant was called with the correct org ID
  t.equal(
    app.testContext.calledWithOrgId(),
    'test-org-id',
    'prismaWithTenant was called with correct org ID'
  );
});
```

### Testing Error Handling

Test how your application handles database errors:

```typescript
test('database errors are handled properly', async t => {
  const mockPrisma = createMockPrismaClient();

  // Override a method to simulate an error
  mockPrisma.organization.findUnique = async () => {
    throw new Error('Database connection failed');
  };

  // Set up the app with the error-throwing mock
  const app = await build(t, {
    onBuild: fastify => {
      fastify.decorate('prismaWithTenant', () => mockPrisma);
    },
  });

  // Make a request that should trigger the error
  const res = await app.inject({
    method: 'GET',
    url: '/organization/test-org-id',
  });

  // Assert that the error is handled properly
  t.equal(res.statusCode, 500, 'returns 500 status code on database error');
  t.same(
    JSON.parse(res.payload),
    {
      error: 'Internal Server Error',
      message: 'An error occurred',
      statusCode: 500,
    },
    'returns proper error response'
  );
});
```

## Best Practices

1. **Keep Tests Focused**: Each test should focus on a single aspect of functionality.
2. **Use Descriptive Test Names**: Test names should clearly describe what is being tested.
3. **Clean Up After Tests**: Use `t.teardown()` to clean up resources after tests.
4. **Avoid Test Interdependence**: Tests should not depend on the state from other tests.
5. **Mock External Dependencies**: Always mock external services and databases.
6. **Test Edge Cases**: Include tests for error conditions and edge cases.
7. **Keep Mocks Simple**: Mocks should only implement the behavior needed for the test.

## Troubleshooting

### Common Test Failures

1. **"Cannot add decorator after server has started"**:

   - This occurs when trying to add a Fastify decorator after the server has started.
   - Solution: Use `Object.defineProperty` instead of `fastify.decorate` in tests, or ensure decorators are added before the server starts.

2. **Type Errors with Mocks**:

   - TypeScript may complain about missing properties in mock objects.
   - Solution: Use type assertions (`as any` or `as PrismaClient`) when necessary, but be careful not to hide real type issues.

3. **Inconsistent Test Results**:
   - Tests that sometimes pass and sometimes fail may indicate a dependency on external state.
   - Solution: Ensure tests are properly isolated and don't depend on external resources or the order of execution.

### Debugging Tests

To debug tests, you can use the `--inspect` flag with Node.js:

```bash
$ node --inspect node_modules/.bin/tap test/integration/organization.int.test.ts
```

Then connect to the debugger using Chrome DevTools or your IDE's debugger.
