# API Testing Guide

This document provides instructions for testing the AskInfoSec API endpoints using curl commands.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Organization Endpoints](#organization-endpoints)
3. [Document Embedding Endpoints](#document-embedding-endpoints)
4. [Chat Endpoints](#chat-endpoints)
5. [Troubleshooting](#troubleshooting)

## Prerequisites

Before testing the API, ensure that:

1. The API server is running locally (default: `http://localhost:3000`) or you have the correct URL for the deployed instance
2. You have valid organization IDs for testing
3. curl is installed on your system

## Organization Endpoints

### Get Organization by ID

The organization endpoint implements Row Level Security (RLS) to ensure that only authorized users can access organization data. The organization ID is used both as a path parameter and as the tenant identifier for RLS.

**Endpoint:** `GET /api/v1/internal/organization/:id`

**Request:**

```bash
# Replace {org_id} with a valid organization ID
curl -X GET "http://localhost:3000/api/v1/internal/organization/{org_id}" \
  -H "Content-Type: application/json"
```

**Example:**

```bash
# Get organization with ID 'org-123'
curl -X GET "http://localhost:3000/api/v1/internal/organization/org-123" \
  -H "Content-Type: application/json"
```

**Expected Response (Success):**

```json
{
  "organization_id": "org-123",
  "organization_name": "Example Organization"
}
```

**Expected Response (Not Found):**

```json
{
  "error": "organization_not_found"
}
```

### Testing RLS with Organization Endpoints

To verify that Row Level Security is working correctly, you can try accessing an organization with a different organization ID in the URL:

```bash
# This should fail due to RLS, as the URL org ID doesn't match the tenant
curl -X GET "http://localhost:3000/api/v1/internal/organization/different-org-id" \
  -H "Content-Type: application/json"
```

This should return a 404 error, as the RLS policy prevents cross-tenant access.

## Document Embedding Endpoints

### Generate Document Embeddings

**Endpoint:** `POST /api/v1/internal/jobs/embeddings/docs`

**Request:**

```bash
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/docs" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "YOUR_ORGANIZATION_ID",
    "documentId": "YOUR_DOCUMENT_ID"
  }'
```

**Example:**

```bash
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/docs" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org-123",
    "documentId": "doc-456"
  }'
```

**Expected Response (Success):**

```json
{
  "message": "Document embedding job initiated successfully"
}
```

### Generate Knowledge Base Embeddings

**Endpoint:** `POST /api/v1/internal/jobs/embeddings/kb`

**Request:**

```bash
# Replace {org_id} and {id} with valid IDs
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/kb" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "{org_id}",
    "id": "{kb_id}"
  }'
```

**Example:**

```bash
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/kb" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org-123",
    "id": "kb-789"
  }'
```

**Expected Response (Success):**

```json
{
  "message": "Knowledge base embedding job initiated successfully"
}
```

## Chat Endpoints

### Generate Answer from Document

**Endpoint:** `POST /api/v1/internal/chat/completions`

**Request:**

```bash
curl -X POST "http://localhost:3000/api/v1/internal/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "YOUR_ORGANIZATION_ID",
    "messages": [
      { "role": "user", "content": "What is the capital of France?" }
    ]
  }'
```

**Example:**

```bash
curl -X POST "http://localhost:3000/api/v1/internal/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org-123",
    "messages": [
      { "role": "user", "content": "Tell me about the latest cybersecurity threats." }
    ]
  }'
```

**Expected Response:**

```json
{
  "answer": "The answer to your question."
}
```

## API Key Authentication Testing

### Generate API Key

**Endpoint:** `POST /api/v1/external/api-keys/create`

**Request:**

```bash
curl -X POST "http://localhost:3000/api/v1/external/api-keys/create" \
  -H "Content-Type: application/json" \
  -H "X-Internal-Secret: YOUR_INTERNAL_API_SECRET" \
  -d '{
    "organizationId": "YOUR_ORGANIZATION_ID"
  }'
```

**Expected Response (Success):**

```json
{
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

**Expected Response (Error):**

```json
{
  "error": "Unauthorized",
  "code": "UNAUTHORIZED"
}
```

## Multi-Tenant Testing Script

To test multi-tenant isolation, you can use the following shell script. Replace `ORG_A` and `ORG_B` with valid organization IDs from your database.

```bash
#!/bin/bash

ORG_A="YOUR_ORG_ID_A"
ORG_B="YOUR_ORG_ID_B"

# Test accessing Organization A with its own ID (should succeed)
echo "Testing accessing Organization A with its own ID:"
curl -X GET "http://localhost:3000/api/v1/internal/organization/$ORG_A" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test accessing Organization A with Organization B's ID (should fail)
echo "Testing accessing Organization A with Organization B's ID:"
curl -X GET "http://localhost:3000/api/v1/internal/organization/$ORG_B" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test document embeddings with correct organization ID (should succeed)
echo "Testing document embeddings with correct organization ID:"
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/docs" \
  -H "Content-Type: application/json" \
  -d "{\"organizationId\": \"$ORG_A\", \"documentId\": \"file-123\"}"
echo -e "\n"

# Test API key generation with correct organization ID (should succeed)
echo "Testing API key generation with correct organization ID:"
curl -X POST "http://localhost:3000/api/v1/external/api-keys/create" \
  -H "Content-Type: application/json" \
  -H "X-Internal-Secret: YOUR_INTERNAL_API_SECRET" \
  -d "{\"organizationId\": \"$ORG_A\"}"
echo -e "\n"
```

Save this script as `test_multi_tenant.sh`, make it executable with `chmod +x test_multi_tenant.sh`, and run it to test multi-tenant isolation.

## API Key Generation Endpoint

This endpoint is used to generate API keys for organizations and requires `X-Internal-Secret` authentication.

**Endpoint:** `POST /api/v1/external/api-keys/create`

**Request:**

```bash
curl -X POST "http://localhost:3000/api/v1/external/api-keys/create" \
  -H "Content-Type: application/json" \
  -H "X-Internal-Secret: YOUR_INTERNAL_API_SECRET" \
  -d '{
    "organizationId": "YOUR_ORGANIZATION_ID"
  }'
```

**Expected Response (Success):**

```json
{
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

**Expected Response (Error):**

```json
{
  "error": "Unauthorized",
  "code": "UNAUTHORIZED"
}
```

## Multi-Tenant Testing Script

To test multi-tenant isolation, you can use the following shell script. Replace `ORG_A` and `ORG_B` with valid organization IDs from your database.

```bash
#!/bin/bash

ORG_A="YOUR_ORG_ID_A"
ORG_B="YOUR_ORG_ID_B"

# Test accessing Organization A with its own ID (should succeed)
echo "Testing accessing Organization A with its own ID:"
curl -X GET "http://localhost:3000/api/v1/internal/organization/$ORG_A" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test accessing Organization A with Organization B's ID (should fail)
echo "Testing accessing Organization A with Organization B's ID:"
curl -X GET "http://localhost:3000/api/v1/internal/organization/$ORG_B" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test document embeddings with correct organization ID (should succeed)
echo "Testing document embeddings with correct organization ID:"
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/docs" \
  -H "Content-Type: application/json" \
  -d "{\"organizationId\": \"$ORG_A\", \"documentId\": \"file-123\"}"
echo -e "\n"

# Test API key generation with correct organization ID (should succeed)
echo "Testing API key generation with correct organization ID:"
curl -X POST "http://localhost:3000/api/v1/external/api-keys/create" \
  -H "Content-Type: application/json" \
  -H "X-Internal-Secret: YOUR_INTERNAL_API_SECRET" \
  -d "{\"organizationId\": \"$ORG_A\"}"
echo -e "\n"
```

## Troubleshooting

### Common Issues

1. **404 Not Found**:

   - Verify that the API server is running
   - Check that the endpoint path is correct (e.g., `http://localhost:3000/api/v1/internal/your-route`)
   - Ensure that the organization ID exists in the database

2. **500 Internal Server Error**:

   - Check the server logs for detailed error information
   - Verify that the database is accessible
   - Ensure that the request payload is correctly formatted

3. **401 Unauthorized**:
   - If authentication is enabled, ensure that valid credentials are provided (e.g., JWT token or `X-Internal-Secret` header)

### Debugging Tips

1. **Use Verbose Mode**: Add the `-v` flag to curl commands to see detailed request and response information:

```bash
curl -v -X GET "http://localhost:3000/api/v1/internal/organization/org-123" \
  -H "Content-Type: application/json"
```

2. **Check Request Format**: Ensure that the JSON payload is correctly formatted:

```bash
# Pretty-print the JSON payload for verification
echo '{
  "organizationId": "org-123",
  "documentId": "doc-456"
}' | jq
```

3. **Test with Different Content Types**: If you're having issues, try different content type headers:

```bash
curl -X POST "http://localhost:3000/api/v1/internal/jobs/embeddings/docs" \
  -H "Content-Type: application/json" \
  --data-binary '{
    "organizationId": "org-123",
    "documentId": "doc-456"
  }'
```

4. **Monitor Server Logs**: Keep the server logs open in another terminal window to see real-time error messages:

```bash
# If using pnpm
pnpm dev | grep ERROR
```
