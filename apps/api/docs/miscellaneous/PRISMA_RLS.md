# Prisma Row Level Security (RLS) Implementation

This document provides a detailed overview of the Row Level Security (RLS) implementation in the AskInfoSec API using Prisma ORM.

## Table of Contents

1. [Overview](#overview)
2. [Implementation](#implementation)
3. [Usage](#usage)
4. [Testing](#testing)
5. [Troubleshooting](#troubleshooting)

## Overview

Row Level Security (RLS) is implemented to enforce multi-tenant isolation at the database level. This ensures that each organization can only access its own data, providing a secure foundation for the multi-tenant architecture of the application.

The implementation consists of:

- A Fastify plugin that extends the Prisma client with RLS capabilities
- Methods to enforce tenant isolation or bypass RLS when needed
- PostgreSQL RLS policies configured in the database

## Implementation

### Prisma Plugin

The core of the RLS implementation is in the Fastify Prisma plugin located at `/src/plugins/prisma.ts`. This plugin:

1. Initializes a shared PrismaClient instance
2. Decorates the Fastify instance with methods to enforce or bypass RLS
3. Manages the database connection lifecycle

```typescript
// Key components of the Prisma plugin
const prismaPlugin: FastifyPluginAsync<PrismaPluginOptions> = async function (fastify, opts) {
  if (!fastify.hasDecorator('prisma')) {
    // Initialize the Prisma client
    const client =
      'client' in opts
        ? opts.client
        : new PrismaClient({
            log: [{ emit: 'stdout', level: 'error' }],
            ...('clientConfig' in opts ? opts.clientConfig : {}),
          });

    await client.$connect();

    // Decorate the fastify instance with the Prisma client
    fastify.decorate('prisma', client);

    // Add methods for tenant isolation and RLS bypass
    fastify.decorate('prismaWithTenant', function (orgId: string) {
      // Implementation details...
    });

    fastify.decorate('prismaBypassRLS', function () {
      // Implementation details...
    });
  }
};
```

### Tenant Isolation

The `prismaWithTenant` method enforces tenant isolation by setting PostgreSQL session variables before executing queries:

```typescript
fastify.decorate('prismaWithTenant', function (orgId: string) {
  // If prisma is a stub/mock without $extends, return it directly (e.g., during tests)
  if (typeof (this.prisma as any).$extends !== 'function') {
    return this.prisma as PrismaClient;
  }

  // Store a reference to the prisma client
  const prismaClient = this.prisma;

  return prismaClient.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }) {
          const [, , result] = await prismaClient.$transaction([
            prismaClient.$executeRaw`SELECT set_config('app.current_organization_id', ${orgId}, TRUE)`,
            prismaClient.$executeRaw`SELECT set_config('app.bypass_rls', 'off', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
});
```

### Bypassing RLS

The `prismaBypassRLS` method allows administrators or system processes to bypass RLS when necessary:

```typescript
fastify.decorate('prismaBypassRLS', function () {
  // Store a reference to the prisma client
  const prismaClient = this.prisma;

  return prismaClient.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }) {
          const [, result] = await prismaClient.$transaction([
            prismaClient.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
            query(args),
          ]);
          return result;
        },
      },
    },
  }) as PrismaClient;
});
```

## Usage

### Using Tenant Isolation

To enforce tenant isolation in your routes or services, use the `prismaWithTenant` method:

```typescript
// Example from src/routes/organization/index.ts
fastify.get<{ Params: { id: string } }>('/organization/:id', async (request, reply) => {
  const { id } = request.params;
  const prisma = fastify.prismaWithTenant(id);
  const org = await prisma.organization.findUnique({ where: { id } });
  if (!org) {
    return reply.code(404).send({ error: 'organization_not_found' });
  }
  return { organization_id: org.id, organization_name: org.company_name };
});
```

### Bypassing RLS

For administrative operations or system tasks that need to access data across tenants:

```typescript
// Example of bypassing RLS
const prisma = fastify.prismaBypassRLS();
const allOrganizations = await prisma.organization.findMany();
```

## Testing

### Mock Implementation

For testing, we've created a mock implementation of the Prisma client that simulates RLS behavior without requiring a real database connection. The mock is located at `/test/mocks/prisma.ts`.

Key components of the mock:

```typescript
export function createMockPrismaClient() {
  // Basic mock implementation that can be extended as needed
  return {
    $connect: async () => {},
    $disconnect: async () => {},
    $transaction: async (ops: any[]) => Promise.all(ops),
    $executeRaw: async (..._args: any[]) => null,
    organization: {
      // Mock implementations for organization methods
    },
    file: {
      // Mock implementations for file methods
    },
    // Other model implementations
  } as unknown as PrismaClient;
}
```

### Integration Tests

Integration tests use the mock Prisma client to test database operations without requiring a real database:

```typescript
// Example from test/integration/organization.int.test.ts
test('Prisma client can connect successfully', async t => {
  // Create a mock Prisma client directly
  const mockPrisma = createMockPrismaClient();

  // Test that we can connect to the mock database
  await t.resolves(mockPrisma.$connect(), 'can connect to database');

  // Test that we can find an organization
  const orgId = 'int-test-org';
  const org = await mockPrisma.organization.findUnique({ where: { id: orgId } });

  // Verify the organization data
  t.ok(org, 'organization was found');

  if (org) {
    t.equal(org.id, orgId, 'organization has correct ID');
    t.equal(org.company_name, 'IntegrationOrg', 'organization has correct name');
  }
});
```

### Route Tests

For testing routes that use the Prisma client, we set up the mock implementation on the Fastify instance:

```typescript
// Helper function to set up mock Prisma on a Fastify instance
export function setupMockPrisma(fastify: FastifyInstance) {
  // Create a shared mock client
  const mockPrismaClient = createMockPrismaClient();

  // Add the prisma property and methods to the Fastify instance
  if (!fastify.prisma) {
    Object.defineProperty(fastify, 'prisma', {
      value: mockPrismaClient,
      writable: true,
      configurable: true,
    });
  }

  // Add the prismaWithTenant method
  if (!fastify.prismaWithTenant) {
    Object.defineProperty(fastify, 'prismaWithTenant', {
      value: (orgId: string) => mockPrismaClient,
      writable: true,
      configurable: true,
    });
  }

  // Add the prismaBypassRLS method
  if (!fastify.prismaBypassRLS) {
    Object.defineProperty(fastify, 'prismaBypassRLS', {
      value: () => mockPrismaClient,
      writable: true,
      configurable: true,
    });
  }

  return mockPrismaClient;
}
```

## Troubleshooting

### Common Issues

1. **Scope Issues in Extension Methods**:

   - When using `$extends` with Prisma, be careful with the `this` context inside callback functions.
   - Always capture references to objects needed inside callbacks, as the context changes.

   ```typescript
   // Incorrect - this.prisma is undefined in the callback
   return this.prisma.$extends({
     query: {
       $allModels: {
         async $allOperations({ args, query }) {
           // Error: this.prisma is undefined here
           const result = await this.prisma.$transaction([...]);
         },
       },
     },
   });

   // Correct - capture a reference to prisma before $extends
   const prismaClient = this.prisma;
   return prismaClient.$extends({
     query: {
       $allModels: {
         async $allOperations({ args, query }) {
           // Works: prismaClient is in scope
           const result = await prismaClient.$transaction([...]);
         },
       },
     },
   });
   ```

2. **Testing with Fastify Decorators**:

   - When testing with Fastify, be aware that decorators cannot be added after the server has started.
   - Use `Object.defineProperty` to add properties to the Fastify instance in tests, or ensure decorators are added before the server starts.

3. **Type Safety with Mocks**:

   - When mocking the Prisma client, use type assertions carefully to ensure type safety.
   - For complex objects like file with document vectors, use type assertions to handle properties not in the type definition:

   ```typescript
   // Example of handling properties not in the type definition
   const fileWithVectors = file as any;
   t.ok(Array.isArray(fileWithVectors.document_vector), 'file has document vectors');
   ```

### Performance Considerations

- The RLS implementation adds a small overhead to each database query due to the transaction and session variable setting.
- For performance-critical operations that don't require tenant isolation, consider using `prismaBypassRLS` with appropriate access controls.
