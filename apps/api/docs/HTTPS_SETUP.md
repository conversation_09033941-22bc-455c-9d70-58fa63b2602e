# HTTPS Setup for AskInfoSec API

The AskInfoSec API now supports both HTTP and HTTPS with HTTPS as the default option.

## Quick Start

### Prerequisites

1. Install dependencies: `pnpm install`
2. Generate HTTPS certificates: `cd apps/teams && pnpm run generate-certs`

### Development Scripts

**HTTPS (Default)**

```bash
pnpm run dev          # HTTPS with auto-reload
pnpm run start:https  # HTTPS production mode
```

**HTTP (Fallback)**

```bash
pnpm run dev:http     # HTTP with auto-reload
pnpm run start:http   # HTTP production mode
```

## Environment Variables

| Variable    | Default     | Description          |
| ----------- | ----------- | -------------------- |
| `USE_HTTPS` | `true`      | Enable/disable HTTPS |
| `PORT`      | `8000`      | Server port          |
| `HOST`      | `localhost` | Server host          |
| `LOG_LEVEL` | `info`      | Logging level        |

## HTTPS Certificate Setup

The server automatically looks for certificates in `certs/` (workspace root):

- `cert.pem` (certificate)
- `key.pem` (private key)

### Generate Certificates

```bash
# From workspace root
pnpm run generate-certs

# Or from any app directory
cd apps/teams && pnpm run generate-certs
cd apps/api && pnpm run generate-certs  # (if you add the script)
```

This will create trusted certificates using `mkcert` if available, or self-signed certificates as fallback.

## Server Behavior

1. **HTTPS Enabled (default)**:

   - If certificates exist → HTTPS server
   - If certificates missing → Falls back to HTTP with warning

2. **HTTPS Disabled**:
   - Always uses HTTP
   - Set `USE_HTTPS=false` to disable

## Integration with Teams Bot

The Teams bot is automatically configured to use HTTPS:

- Default API URL: `https://localhost:8000`
- Accepts self-signed certificates
- Falls back to HTTP if needed

## Development Workflow

1. **First time setup**:

   ```bash
   pnpm run generate-certs  # Generate certificates (from workspace root)
   cd apps/api && pnpm run dev
   ```

2. **Daily development**:

   ```bash
   cd apps/api && pnpm run dev  # Starts HTTPS by default
   ```

3. **HTTP only (if needed)**:
   ```bash
   cd apps/api && pnpm run dev:http
   ```

## Troubleshooting

### Certificate Issues

- Run `pnpm run generate-certs` from workspace root to regenerate
- Check that certificates exist in `certs/` (workspace root)
- Use `USE_HTTPS=false` to disable HTTPS temporarily

### Port Conflicts

- Change `PORT=8001` in environment
- Update Teams bot `AISEC_API_URL` accordingly

### Browser Security Warnings

- Use `mkcert` for trusted certificates (recommended)
- Or accept the security warning for self-signed certificates

## Production Considerations

For production deployment:

1. Use proper SSL certificates from a CA
2. Set `USE_HTTPS=true`
3. Configure reverse proxy (nginx/cloudflare) if needed
4. Update environment variables accordingly
