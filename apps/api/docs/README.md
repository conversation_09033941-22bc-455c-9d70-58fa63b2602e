# AskInfosec API

A Fastify-based API project focused on information security, combining document processing capabilities with AI features.

## Technology Stack

- **Backend Framework**: Fastify (Node.js)
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Package Manager**: pnpm
- **AI Integration**: LangChain with OpenAI

## Project Structure

```
src/
├── api/             # API related files (controllers, middleware, routes, schemas)
│   ├── v1/
│   │   ├── external/    # External API (public facing)
│   │   └── internal/    # Internal API (for internal services)
├── core/            # Core business logic and services
├── plugins/         # Fastify plugins
├── lib/             # Utility functions and shared code
├── app.ts           # Main application entry point
└── types.ts         # TypeScript type definitions
```

## Key Features

- **Document Processing**: PDF and Word document support with intelligent text extraction
- **AI Integration**: LangChain and OpenAI integration with agent-based architecture
- **Multi-Client Support**: CLI tool, Teams bot, and programmatic agent interfaces
- **Security**: Implements security headers via `@fastify/helmet` with JWT authentication
- **Multi-Tenancy**: Row Level Security (RLS) for organization isolation
- **Environment Configuration**: Environment variable management
- **Database Management**: Prisma ORM for PostgreSQL with tenant isolation

## Development Setup

1. Clone the repository
2. Copy `.env.sample` to `.env` and update the values. Ensure you set `DATABASE_URL_RLS_USER` and `OPENAI_API_KEY`.
3. Install dependencies:
   ```bash
   pnpm i
   ```
4. Start development server:
   ```bash
   pnpm dev
   ```

## Available Scripts

- `pnpm dev`: Start development server with hot-reloading
- `pnpm test`: Run TypeScript tests
- `pnpm build:ts`: Compile TypeScript
- `pnpm start`: Start production server

## Development Workflow

The project uses `concurrently` to run multiple development processes simultaneously. Here's how the development workflow operates:

### Dev Script Breakdown

```bash
"dev": "npm run build:ts && concurrently -k -p \"[{name}]\" -n \"TypeScript,App\" -c \"yellow.bold,cyan.bold\" \"npm:watch:ts\" \"npm:dev:start\""
```

This script performs the following:

1. **Initial Build**: `npm run build:ts`

   - Compiles TypeScript code before starting the development environment
   - Only proceeds if compilation is successful

2. **Concurrent Processes**: The `concurrently` command runs two processes:
   - **TypeScript Watcher**: Automatically recompiles code on file changes
   - **Fastify Server**: Development server with hot-reloading

### Concurrently Configuration

- `-k`: Kills all processes if one exits
- `-p "[{name}]"`: Adds process name prefix to output
- `-n "TypeScript,App"`: Names for the processes
- `-c "yellow.bold,cyan.bold"`: Color coding for output
  - TypeScript process: Yellow
  - App process: Cyan

### Output Example

```
[TypeScript] Watching for file changes...
[App] Server listening on http://localhost:8000
```

This setup provides:

- Automatic TypeScript recompilation on file changes
- Server auto-restart when compiled files change
- Clear, color-coded process output in a single terminal
- Seamless development experience with hot-reloading

## Database Management

To update the Prisma schema from the existing database:

```bash
npx prisma db pull
npx prisma generate
```

Note: If there is a new table with default cuid id, you must update it manually in the schema.prisma file.

## Notable Dependencies

- `@fastify/autoload`: Automatic route loading
- `@fastify/sensible`: Common HTTP error handling
- `@langchain/community` & `@langchain/openai`: AI/ML capabilities
- `@prisma/client`: Database ORM
- `lodash`: Utility functions
- `mammoth`: Word document processing
- `pdf-parse`: PDF document processing

## Security Features

- Helmet integration for security headers
- Environment variable management
- Sensible error handling
- TypeScript for type safety

## Client Integrations

The AskInfoSec API supports multiple client interfaces:

### CLI Tool (`apps/cli`)

Command-line interface for agent interactions and system management using JWT authentication.

### Teams Bot (`apps/teams`)

Microsoft Teams bot providing conversational AI within Teams channels and direct messages. Features automatic JWT token management and rich UI components with Adaptive Cards.

### Agent System (`apps/agents`)

Programmatic agent framework with MCP (Model Context Protocol) tools integration for document processing and database operations.

For detailed setup and deployment instructions, see:

- [Teams Integration Guide](./teams-integration.md)
- [MCP Implementation](./mcp-implementation/)

## API Documentation

The project includes a Postman collection (`askinfosec-api.postman_collection.json`) for API testing and documentation.

## Contributing

1. Follow the existing code structure and patterns
2. Ensure all new code is properly typed with TypeScript
3. Add appropriate tests for new features
4. Update this documentation for any significant changes

## License

ISC
