#!/usr/bin/env node

/**
 * Example MCP Client for AskInfoSec API
 *
 * This example demonstrates how to:
 * 1. Create an MCP session
 * 2. Connect via WebSocket
 * 3. Initialize the MCP protocol
 * 4. Call tools
 * 5. Manage context
 * 6. Monitor health and metrics
 */

const WebSocket = require('ws');
const https = require('https');
const http = require('http');

class MCPClientExample {
  constructor(config) {
    this.config = config;
    this.requestId = 0;
    this.pendingRequests = new Map();
    this.ws = null;
    this.session = null;
  }

  async run() {
    try {
      console.log('🚀 Starting MCP Client Example\n');

      // Step 1: Check server health
      await this.checkServerHealth();

      // Step 2: Create MCP session
      await this.createSession();

      // Step 3: Connect WebSocket
      await this.connectWebSocket();

      // Step 4: Initialize MCP protocol
      await this.initializeProtocol();

      // Step 5: Demonstrate tool usage
      await this.demonstrateTools();

      // Step 6: Check metrics
      await this.checkMetrics();

      console.log('\n✅ MCP Client Example completed successfully!');
    } catch (error) {
      console.error('❌ Error:', error.message);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  async checkServerHealth() {
    console.log('📊 Checking server health...');

    try {
      const health = await this.makeHttpRequest('/api/v1/internal/mcp/health');
      console.log(`   Status: ${health.status}`);
      console.log(`   Uptime: ${Math.round(health.uptime / 60)} minutes`);
      console.log(`   Components: ${health.summary.healthy}/${health.summary.total} healthy\n`);
    } catch (error) {
      console.log('   ⚠️  Health check failed, continuing anyway...\n');
    }
  }

  async createSession() {
    console.log('🔑 Creating MCP session...');

    // Note: In a real implementation, you would use a valid JWT token
    const response = await this.makeHttpRequest('/api/v1/internal/mcp/session/init', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.config.jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    this.session = response;
    console.log(`   Session ID: ${this.session.session_id}`);
    console.log(`   WebSocket URL: ${this.session.websocket_url}\n`);
  }

  async connectWebSocket() {
    console.log('🔌 Connecting to WebSocket...');

    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.session.websocket_url);

      this.ws.on('open', () => {
        console.log('   ✅ WebSocket connected\n');
        resolve();
      });

      this.ws.on('message', data => {
        this.handleMessage(JSON.parse(data.toString()));
      });

      this.ws.on('error', error => {
        console.error('   ❌ WebSocket error:', error.message);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('   🔒 WebSocket disconnected');
      });
    });
  }

  async initializeProtocol() {
    console.log('🔧 Initializing MCP protocol...');

    const response = await this.sendRequest('initialize', {
      protocolVersion: '2025-03-26',
      capabilities: {
        roots: { listChanged: true },
        sampling: {},
      },
      clientInfo: {
        name: 'MCP Example Client',
        version: '0.0.1',
      },
    });

    console.log(`   Protocol Version: ${response.protocolVersion}`);
    console.log(`   Server: ${response.serverInfo.name} v${response.serverInfo.version}\n`);
  }

  async demonstrateTools() {
    console.log('🛠️  Demonstrating Enhanced MCP Tools...\n');

    // List available tools with categories
    console.log('   📋 Listing tools by category:');
    const tools = await this.sendRequest('tools/list');

    console.log(`      Total tools: ${tools.metadata.total_tools}`);
    console.log(`      Async tools: ${tools.metadata.async_tools}`);
    console.log(`      Categories: ${tools.metadata.categories_available.join(', ')}\n`);

    // Show tools by category
    Object.entries(tools.categories).forEach(([category, categoryTools]) => {
      console.log(`   📂 ${category.toUpperCase()} Category:`);
      categoryTools.forEach(tool => {
        const asyncFlag = tool.async ? '⏳' : '⚡';
        const duration = tool.estimatedDuration ? `(~${tool.estimatedDuration}ms)` : '';
        console.log(`      ${asyncFlag} ${tool.name}: ${tool.description} ${duration}`);
      });
      console.log();
    });

    // Check system status
    console.log('   🔍 Checking system status:');
    try {
      const statusResult = await this.sendRequest('tools/call', {
        name: 'system_status',
        arguments: { component: 'all' },
      });
      const statusData = JSON.parse(statusResult.content[0].text);
      console.log(
        `      ✅ Redis: ${statusData.system_status.redis?.connected ? 'Connected' : 'Disconnected'}`
      );
      console.log(
        `      ✅ Sessions: ${statusData.system_status.sessions?.active_sessions || 0} active`
      );
    } catch (error) {
      console.log(`      ⚠️  Status check failed: ${error.message}`);
    }
    console.log();

    // Demonstrate context management
    console.log('   💾 Setting context data:');
    await this.sendRequest('tools/call', {
      name: 'set_context',
      arguments: {
        key: 'demo_data',
        data: {
          timestamp: new Date().toISOString(),
          client: 'example-client',
          demo: true,
        },
      },
    });
    console.log('      ✅ Context data set successfully');

    // Get context data
    console.log('   📖 Getting context data:');
    const contextResult = await this.sendRequest('tools/call', {
      name: 'get_context',
      arguments: {
        key: 'demo_data',
      },
    });

    const contextData = JSON.parse(contextResult.content[0].text);
    console.log(`      📄 Retrieved: ${JSON.stringify(contextData.data, null, 2)}`);

    // Demonstrate database query (safe SELECT only)
    console.log('   🗄️  Executing database query:');
    try {
      const queryResult = await this.sendRequest('tools/call', {
        name: 'query_database',
        arguments: {
          query: "SELECT 1 as test_value, 'MCP Demo' as message",
        },
      });

      const dbData = JSON.parse(queryResult.content[0].text);
      console.log(`      📊 Query result: ${dbData.rowCount} rows returned`);
    } catch (error) {
      console.log(`      ⚠️  Query failed: ${error.message}`);
    }

    // Demonstrate async operations
    console.log('   ⏳ Starting async data analysis:');
    try {
      const analysisResult = await this.sendRequest('tools/call', {
        name: 'analyze_data',
        arguments: {
          query: 'SELECT 1 as sample_data',
          analysis_type: 'security',
          options: {
            time_range: '7d',
            filters: { demo: true },
          },
        },
      });

      const analysisData = JSON.parse(analysisResult.content[0].text);
      if (analysisData.success) {
        const taskId = analysisData.task_id;
        console.log(`      🎯 Analysis started with task ID: ${taskId}`);

        // Monitor progress
        console.log('   📊 Monitoring task progress:');
        let attempts = 0;
        const maxAttempts = 6;

        while (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 6000)); // Wait 6 seconds

          const statusResult = await this.sendRequest('tools/call', {
            name: 'get_task_status',
            arguments: { task_id: taskId },
          });

          const statusData = JSON.parse(statusResult.content[0].text);
          if (statusData.success) {
            const task = statusData.task;
            console.log(
              `      📈 Progress: ${task.progress}% - ${task.current_step || task.status}`
            );

            if (['completed', 'failed', 'cancelled'].includes(task.status)) {
              if (task.status === 'completed') {
                console.log('      ✅ Analysis completed successfully!');
                if (task.result) {
                  console.log(`      📊 Analysis type: ${task.result.analysis_type}`);
                  console.log(`      📊 Data points: ${task.result.data_points_analyzed}`);
                }
              } else {
                console.log(`      ⚠️  Analysis ${task.status}: ${task.error || 'No details'}`);
              }
              break;
            }
          }
          attempts++;
        }

        // List tasks to show management capabilities
        console.log('   📋 Listing session tasks:');
        const tasksResult = await this.sendRequest('tools/call', {
          name: 'list_tasks',
          arguments: {
            status: 'all',
            limit: 5,
          },
        });

        const tasksData = JSON.parse(tasksResult.content[0].text);
        console.log(`      📊 Session has ${tasksData.tasks.length} total tasks`);
        console.log(`      📊 Active: ${tasksData.summary.session_metrics.active_tasks}`);
        console.log(`      📊 Completed: ${tasksData.summary.session_metrics.completed_tasks}`);
      }
    } catch (error) {
      console.log(`      ⚠️  Async analysis failed: ${error.message}`);
    }

    console.log();
  }

  async checkMetrics() {
    console.log('📈 Checking server metrics...');

    try {
      const metrics = await this.makeHttpRequest('/api/v1/internal/mcp/metrics');

      console.log('   📊 Current metrics:');
      console.log(`      Active sessions: ${metrics.sessions.active}`);
      console.log(`      Total sessions created: ${metrics.sessions.total_created}`);
      console.log(`      Total tool calls: ${metrics.tools.total_calls}`);
      console.log(`      Success rate: ${metrics.tools.success_rate}%`);
      console.log(`      WebSocket connections: ${metrics.websocket.active_connections}`);
      console.log(`      Redis connected: ${metrics.redis.connected ? '✅' : '❌'}`);

      if (Object.keys(metrics.tools.calls_by_tool).length > 0) {
        console.log('   🔧 Tool usage breakdown:');
        Object.entries(metrics.tools.calls_by_tool).forEach(([tool, count]) => {
          console.log(`      ${tool}: ${count} calls`);
        });
      }
    } catch (error) {
      console.log('   ⚠️  Metrics check failed:', error.message);
    }
  }

  async sendRequest(method, params) {
    return new Promise((resolve, reject) => {
      const id = ++this.requestId;
      const request = {
        jsonrpc: '2.0',
        method,
        params,
        id,
      };

      this.pendingRequests.set(id, { resolve, reject });
      this.ws.send(JSON.stringify(request));

      // Set timeout
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out`));
        }
      }, 30000);
    });
  }

  handleMessage(message) {
    const { id, result, error } = message;

    if (id && this.pendingRequests.has(id)) {
      const { resolve, reject } = this.pendingRequests.get(id);
      this.pendingRequests.delete(id);

      if (error) {
        reject(new Error(`${error.code}: ${error.message}`));
      } else {
        resolve(result);
      }
    }
  }

  async makeHttpRequest(path, options = {}) {
    return new Promise((resolve, reject) => {
      const url = `${this.config.apiBase}${path}`;
      const isHttps = url.startsWith('https');
      const client = isHttps ? https : http;

      const requestOptions = {
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'MCP-Example-Client/1.0.0',
          ...options.headers,
        },
      };

      const req = client.request(url, requestOptions, res => {
        let data = '';

        res.on('data', chunk => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(JSON.parse(data));
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${data}`));
            }
          } catch (error) {
            reject(new Error(`Parse error: ${error.message}`));
          }
        });
      });

      req.on('error', reject);

      if (options.body) {
        req.write(JSON.stringify(options.body));
      }

      req.end();
    });
  }
}

// Configuration
const config = {
  apiBase: process.env.API_BASE || 'http://localhost:8000',
  jwtToken: process.env.JWT_TOKEN || 'your-jwt-token-here',
};

// Run example if called directly
if (require.main === module) {
  const client = new MCPClientExample(config);
  client.run().catch(console.error);
}

module.exports = MCPClientExample;
