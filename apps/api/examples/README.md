# MCP Client Examples

This directory contains example implementations for connecting to the AskInfoSec MCP server.

## Available Examples

### JavaScript/Node.js Client (`mcp-client.js`)

A comprehensive example demonstrating all MCP server capabilities:

- ✅ Health monitoring
- ✅ Session management
- ✅ WebSocket communication
- ✅ Enhanced tool categories (database, context, system, analysis, async)
- ✅ Synchronous tool execution (immediate response)
- ✅ Asynchronous operations (long-running tasks with progress monitoring)
- ✅ Task management (monitor, list, cancel async operations)
- ✅ Context management
- ✅ Comprehensive metrics monitoring

#### Prerequisites

```bash
npm install ws
```

#### Usage

```bash
# Set environment variables
export API_BASE="http://localhost:8000"
export JWT_TOKEN="your-jwt-token-here"

# Run the example
node examples/mcp-client.js
```

#### Expected Output

```
🚀 Starting MCP Client Example

📊 Checking server health...
   Status: healthy
   Uptime: 15 minutes
   Components: 3/3 healthy

🔑 Creating MCP session...
   Session ID: mcp_1704067200_abc123xyz
   WebSocket URL: ws://localhost:8000/api/v1/internal/mcp/ws/mcp_1704067200_abc123xyz

🔌 Connecting to WebSocket...
   ✅ WebSocket connected

🔧 Initializing MCP protocol...
   Protocol Version: 2025-03-26
   Server: AskInfoSec MCP Server v1.0.0

🛠️  Demonstrating Enhanced MCP Tools...

   📋 Listing tools by category:
      Total tools: 8
      Async tools: 1
      Categories: database, context, system, analysis, async

   📂 DATABASE Category:
      ⚡ query_database: Execute read-only SQL queries on organization data (~1000ms)

   📂 CONTEXT Category:
      ⚡ get_context: Retrieve session context data (~500ms)
      ⚡ set_context: Store data in session context (~500ms)

   📂 SYSTEM Category:
      ⚡ system_status: Get comprehensive system status and health information (~2000ms)

   📂 ANALYSIS Category:
      ⏳ analyze_data: Perform advanced data analysis with security, performance, or trend insights (~30000ms)

   📂 ASYNC Category:
      ⚡ get_task_status: Check the status and progress of an async task (~300ms)
      ⚡ cancel_task: Cancel a running async task (~500ms)
      ⚡ list_tasks: List async tasks for the current session (~800ms)

   🔍 Checking system status:
      ✅ Redis: Connected
      ✅ Sessions: 1 active

   💾 Setting context data:
      ✅ Context data set successfully
   📖 Getting context data:
      📄 Retrieved: {
        "timestamp": "2024-01-01T12:00:00.000Z",
        "client": "example-client",
        "demo": true
      }
   🗄️  Executing database query:
      📊 Query result: 1 rows returned
   ⏳ Starting async data analysis:
      🎯 Analysis started with task ID: task_1704067200_xyz789
   📊 Monitoring task progress:
      📈 Progress: 10% - Validating query...
      📈 Progress: 30% - Fetching data...
      📈 Progress: 60% - Performing analysis...
      📈 Progress: 90% - Generating insights...
      📈 Progress: 100% - Complete
      ✅ Analysis completed successfully!
      📊 Analysis type: security
      📊 Data points: 3457
   📋 Listing session tasks:
      📊 Session has 1 total tasks
      📊 Active: 0
      📊 Completed: 1

📈 Checking server metrics...
   📊 Current metrics:
      Active sessions: 1
      Total sessions created: 15
      Total tool calls: 45
      Success rate: 98.5%
      WebSocket connections: 1
      Redis connected: ✅
   🔧 Tool usage breakdown:
      query_database: 25 calls
      get_context: 10 calls
      set_context: 10 calls

✅ MCP Client Example completed successfully!
```

## Integration Patterns

### 1. Basic Connection

```javascript
const MCPClientExample = require('./mcp-client');

const client = new MCPClientExample({
  apiBase: 'http://localhost:8000',
  jwtToken: 'your-jwt-token',
});

await client.run();
```

### 2. Health Monitoring

```javascript
// Check server health before connecting
const health = await client.makeHttpRequest('/api/v1/internal/mcp/health');
if (health.status !== 'healthy') {
  console.warn('Server is not healthy:', health);
}
```

### 3. Tool Execution

```javascript
// Execute tools through WebSocket
const result = await client.sendRequest('tools/call', {
  name: 'query_database',
  arguments: {
    query: 'SELECT COUNT(*) as total FROM users',
  },
});
```

### 4. Context Management

```javascript
// Set persistent context
await client.sendRequest('tools/call', {
  name: 'set_context',
  arguments: {
    key: 'user_preferences',
    data: { theme: 'dark', lang: 'en' },
  },
});

// Retrieve context
const context = await client.sendRequest('tools/call', {
  name: 'get_context',
  arguments: { key: 'user_preferences' },
});
```

### 5. Asynchronous Operations

```javascript
// Start long-running analysis
const analysis = await client.sendRequest('tools/call', {
  name: 'analyze_data',
  arguments: {
    query: 'SELECT * FROM security_events WHERE created_at > NOW() - INTERVAL 30 DAY',
    analysis_type: 'security',
    options: {
      time_range: '30d',
      filters: { severity: 'high' },
    },
  },
});

const taskId = JSON.parse(analysis.content[0].text).task_id;
```

### 6. Task Management

```javascript
// Monitor task progress
const status = await client.sendRequest('tools/call', {
  name: 'get_task_status',
  arguments: { task_id: taskId },
});

// List all tasks
const tasks = await client.sendRequest('tools/call', {
  name: 'list_tasks',
  arguments: { status: 'active', limit: 10 },
});

// Cancel task if needed
const cancelled = await client.sendRequest('tools/call', {
  name: 'cancel_task',
  arguments: { task_id: taskId },
});
```

### 7. System Monitoring

```javascript
// Check system status
const systemStatus = await client.sendRequest('tools/call', {
  name: 'system_status',
  arguments: { component: 'all' }, // or 'redis', 'database', 'sessions'
});
```

## Error Handling

The example includes comprehensive error handling:

```javascript
try {
  const result = await client.sendRequest('tools/call', {
    name: 'invalid_tool',
    arguments: {},
  });
} catch (error) {
  if (error.message.includes('-32601')) {
    console.log('Tool not found');
  } else if (error.message.includes('-32602')) {
    console.log('Invalid parameters');
  }
}
```

## Environment Configuration

| Variable    | Default                 | Description                  |
| ----------- | ----------------------- | ---------------------------- |
| `API_BASE`  | `http://localhost:8000` | Base URL for the API server  |
| `JWT_TOKEN` | `your-jwt-token-here`   | JWT token for authentication |

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**

   - Ensure the server is running
   - Check the WebSocket URL format
   - Verify JWT token is valid

2. **Tool Execution Errors**

   - Check tool parameters match the schema
   - Ensure SQL queries are SELECT-only
   - Verify session permissions

3. **Health Check Failures**
   - Check Redis connectivity
   - Verify database connection
   - Review server logs

### Debug Mode

Enable verbose logging:

```bash
DEBUG=mcp:* node examples/mcp-client.js
```

## Next Steps

1. Review the [Client Integration Guide](../docs/mcp-implementation/client-integration.md)
2. Explore the [MCP Server Documentation](../docs/mcp-implementation/mcp-server.md)
3. Check the [API Documentation](../docs/api/)

## Support

For additional support:

- Check the [MCP Specification](https://modelcontextprotocol.io)
- Review server logs for error details
- Contact the development team
