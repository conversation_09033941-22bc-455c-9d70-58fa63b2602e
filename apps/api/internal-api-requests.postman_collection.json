{"info": {"name": "Internal API Requests", "description": "Collection for testing the internal API endpoints (Updated for agents-powered MCP architecture with enhanced session management, connection pooling, and advanced tool execution)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Get Access Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Internal-Secret", "value": "{{internal_api_secret}}", "description": "Secret key for internal API access"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{user_email}}\",\n    \"organization_id\": \"{{organization_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/authenticate/token", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "authenticate", "token"]}, "description": "Get an access token for a user with a specific organization ID"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var jsonData = JSON.parse(responseBody);", "if (jsonData.access_token) {", "    pm.collectionVariables.set(\"access_token\", jsonData.access_token);", "    console.log(\"Access token saved to collection variable\");", "}", "", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has access_token\", function () {", "    pm.expect(jsonData).to.have.property('access_token');", "});"]}}]}]}, {"name": "MCP", "item": [{"name": "Session Management", "item": [{"name": "Init MCP Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/session/init", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "session", "init"]}, "description": "Initialize an MCP session"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "pm.test('Status 200', () => pm.response.to.have.status(200));", "pm.test('Contains websocket_url', () => pm.expect(json).to.have.property('websocket_url'));", "pm.test('Contains session_id', () => pm.expect(json).to.have.property('session_id'));", "pm.test('Contains session_token', () => pm.expect(json).to.have.property('session_token'));", "pm.collectionVariables.set('mcp_session_id', json.session_id);", "pm.collectionVariables.set('mcp_session_token', json.session_token);"]}}]}, {"name": "Get Session Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/session/{{mcp_session_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "session", "{{mcp_session_id}}", "status"]}, "description": "Get status of an MCP session"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains session_id', () => pm.expect(json).to.have.property('session_id'));", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains auth_type', () => pm.expect(json).to.have.property('auth_type'));"]}}]}]}, {"name": "Health Checks", "item": [{"name": "Overall Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health"]}, "description": "Get MCP server health status (Powered by agents implementation with enhanced session management and connection pooling)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 503', () => {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains timestamp', () => pm.expect(json).to.have.property('timestamp'));", "pm.test('Status is valid', () => {", "    pm.expect(['healthy', 'unhealthy', 'degraded']).to.include(json.status);", "});"]}}]}, {"name": "Liveness Probe", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health/live", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health", "live"]}, "description": "Liveness probe for container orchestration"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Status is alive', () => pm.expect(json.status).to.equal('alive'));", "pm.test('Contains timestamp', () => pm.expect(json).to.have.property('timestamp'));"]}}]}, {"name": "Readiness Probe", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health/ready", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health", "ready"]}, "description": "Readiness probe for container orchestration"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 503', () => {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains checks', () => pm.expect(json).to.have.property('checks'));", "pm.test('Checks contain redis and database', () => {", "    pm.expect(json.checks).to.have.property('redis');", "    pm.expect(json.checks).to.have.property('database');", "});"]}}]}, {"name": "Redis Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health/redis", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health", "redis"]}, "description": "Check Redis component health"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 503', () => {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains component', () => pm.expect(json).to.have.property('component'));"]}}]}, {"name": "Database Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health/database", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health", "database"]}, "description": "Check Database component health"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 503', () => {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains component', () => pm.expect(json).to.have.property('component'));"]}}]}, {"name": "Session Store Health Check", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/health/sessions", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "health", "sessions"]}, "description": "Check Session Store component health (Enhanced with agents bridge - includes session pooling metrics and advanced connection management)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 503', () => {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains component', () => pm.expect(json).to.have.property('component'));", "pm.test('Enhanced with agents features', () => {", "    if (json.details) {", "        pm.expect(json.details).to.have.property('agentsPowered');", "        pm.expect(json.details.agentsPowered).to.be.true;", "        pm.expect(json.details).to.have.property('activeSessionCount');", "        pm.expect(json.details).to.have.property('successRate');", "    }", "});"]}}]}]}, {"name": "Metrics", "item": [{"name": "Get All Metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/metrics", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "metrics"]}, "description": "Get MCP server metrics and statistics (Enhanced with agents bridge - includes session pooling, connection management, and advanced tool execution metrics)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains timestamp', () => pm.expect(json).to.have.property('timestamp'));", "pm.test('Contains sessions metrics', () => pm.expect(json).to.have.property('sessions'));", "pm.test('Contains tools metrics', () => pm.expect(json).to.have.property('tools'));", "pm.test('Contains performance metrics', () => pm.expect(json).to.have.property('performance'));", "pm.test('Contains websocket metrics', () => pm.expect(json).to.have.property('websocket'));", "pm.test('Contains redis metrics', () => pm.expect(json).to.have.property('redis'));", "pm.test('Enhanced agents metrics structure', () => {", "    if (json.sessions) {", "        pm.expect(json.sessions).to.have.property('active');", "        pm.expect(json.sessions).to.have.property('total_created');", "        pm.expect(json.sessions).to.have.property('average_duration');", "    }", "    if (json.tools) {", "        pm.expect(json.tools).to.have.property('total_calls');", "        pm.expect(json.tools).to.have.property('success_rate');", "        pm.expect(json.tools).to.have.property('average_response_time');", "    }", "});"]}}]}]}, {"name": "Tool Execution", "item": [{"name": "Test MCP Tool via WebSocket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"jsonrpc\": \"2.0\",\n    \"id\": 1,\n    \"method\": \"tools/list\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/mcp/session/{{mcp_session_id}}/test-tool", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "session", "{{mcp_session_id}}", "test-tool"]}, "description": "Test MCP tool execution through agents bridge (Note: This would typically be done via WebSocket, but this demonstrates the enhanced agents-powered tool execution)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Note: This endpoint may not exist in the current implementation", "// This test demonstrates what enhanced MCP tool execution looks like", "pm.test('Status 200 or 404', () => {", "    pm.expect([200, 404, 501]).to.include(pm.response.code);", "});", "if (pm.response.code === 200) {", "    var json = pm.response.json();", "    pm.test('MCP JSON-RPC response format', () => {", "        pm.expect(json).to.have.property('jsonrpc');", "        pm.expect(json).to.have.property('id');", "        pm.expect(json.jsonrpc).to.equal('2.0');", "    });", "    pm.test('Enhanced tool execution via agents', () => {", "        if (json.result && json.result.tools) {", "            pm.expect(json.result.tools).to.be.an('array');", "            console.log('Available MCP tools:', json.result.tools.length);", "        }", "    });", "}"]}}]}]}, {"name": "WebSocket", "item": [{"name": "Get WebSocket Connection Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/mcp/session/{{mcp_session_id}}/websocket", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "mcp", "session", "{{mcp_session_id}}", "websocket"]}, "description": "Get WebSocket connection information for an MCP session"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains session_id', () => pm.expect(json).to.have.property('session_id'));", "pm.test('Contains websocket_url', () => pm.expect(json).to.have.property('websocket_url'));", "pm.test('Contains websocket_ready', () => pm.expect(json).to.have.property('websocket_ready'));", "pm.test('Contains connection_instructions', () => pm.expect(json).to.have.property('connection_instructions'));", "pm.test('WebSocket URL is valid', () => {", "    pm.expect(json.websocket_url).to.match(/^wss?:\\/\\/.+\\/api\\/v1\\/internal\\/mcp\\/ws\\/.+/);", "});"]}}]}]}]}, {"name": "Agents", "item": [{"name": "Invoke Agent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_name\": \"echo\",\n    \"input\": \"Hello from Post<PERSON>!\",\n    \"context\": {\n        \"sessionId\": \"test-session-{{$randomUUID}}\",\n        \"organizationId\": \"{{organization_id}}\",\n        \"userId\": \"test-user\",\n        \"metadata\": {\n            \"source\": \"postman\",\n            \"timestamp\": \"{{$isoTimestamp}}\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/agent/invoke", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "invoke"]}, "description": "Invoke an AI agent with specified input"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains output', () => pm.expect(json).to.have.property('output'));", "pm.test('Contains metadata', () => pm.expect(json).to.have.property('metadata'));", "pm.test('Metadata contains metrics', () => {", "    if (json.metadata) {", "        pm.expect(json.metadata).to.have.property('tokensUsed');", "        pm.expect(json.metadata).to.have.property('latencyMs');", "        pm.expect(json.metadata).to.have.property('cost');", "    }", "});"]}}]}, {"name": "Invoke Ask AI Agent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_name\": \"ask_ai\",\n    \"input\": {\n        \"input\": \"What are the latest security vulnerabilities?\",\n        \"sessionId\": \"test-session-{{$randomUUID}}\",\n        \"organizationId\": \"{{organization_id}}\",\n        \"userId\": \"test-user\",\n        \"metadata\": {\n            \"source\": \"postman\",\n            \"timestamp\": \"{{$isoTimestamp}}\",\n            \"query_type\": \"general_question\"\n        }\n    },\n    \"context\": {\n        \"sessionId\": \"test-session-{{$randomUUID}}\",\n        \"organizationId\": \"{{organization_id}}\",\n        \"userId\": \"test-user\",\n        \"requestId\": \"req-{{$randomUUID}}\",\n        \"timestamp\": \"{{$isoTimestamp}}\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/agent/invoke", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "invoke"]}, "description": "Invoke the Ask AI agent for cybersecurity questions and analysis. This agent processes natural language prompts and automatically executes OpenAI chat agent followed by a database query."}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains success flag', () => pm.expect(json).to.have.property('success'));", "pm.test('Contains result', () => pm.expect(json).to.have.property('result'));", "pm.test('Contains metadata', () => pm.expect(json).to.have.property('metadata'));", "pm.test('Success is true', () => pm.expect(json.success).to.be.true);", "pm.test('Result contains output', () => {", "    if (json.result) {", "        pm.expect(json.result).to.have.property('output');", "    }", "});", "pm.test('Metada<PERSON> contains agent name', () => {", "    if (json.metadata) {", "        pm.expect(json.metadata.agent_name).to.equal('ask_ai');", "        pm.expect(json.metadata).to.have.property('execution_time');", "        pm.expect(json.metadata).to.have.property('timestamp');", "    }", "});", "pm.test('Response contains chat and database results', () => {", "    if (json.result && json.result.output) {", "        pm.expect(json.result.output.response).to.be.a('string');", "        pm.expect(json.result.output).to.have.property('tool_used');", "        pm.expect(json.result.output).to.have.property('execution_time_ms');", "    }", "});"]}}]}, {"name": "Invoke Ask AI Agent (Simple String Input)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_name\": \"ask_ai\",\n    \"input\": \"Explain SQL injection vulnerabilities and how to prevent them\",\n    \"context\": {\n        \"sessionId\": \"test-session-{{$randomUUID}}\",\n        \"organizationId\": \"{{organization_id}}\",\n        \"userId\": \"test-user\",\n        \"metadata\": {\n            \"source\": \"postman\",\n            \"timestamp\": \"{{$isoTimestamp}}\",\n            \"query_type\": \"cybersecurity_education\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/agent/invoke", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "invoke"]}, "description": "Invoke the Ask AI agent with a simple string input. This demonstrates the agent's ability to handle natural language questions about cybersecurity topics."}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains success flag', () => pm.expect(json).to.have.property('success'));", "pm.test('Contains result', () => pm.expect(json).to.have.property('result'));", "pm.test('Success is true', () => pm.expect(json.success).to.be.true);", "pm.test('Response contains educational content', () => {", "    if (json.result && json.result.output) {", "        pm.expect(json.result.output.response).to.be.a('string');", "        pm.expect(json.result.output.response).to.include('SQL injection');", "        pm.expect(json.result.output).to.have.property('chat_response');", "        pm.expect(json.result.output).to.have.property('database_result');", "    }", "});"]}}]}, {"name": "Invoke Agent (Streaming)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_name\": \"echo\",\n    \"input\": \"Hello from <PERSON>man with streaming!\",\n    \"context\": {\n        \"sessionId\": \"test-session-{{$randomUUID}}\",\n        \"organizationId\": \"{{organization_id}}\",\n        \"userId\": \"test-user\",\n        \"metadata\": {\n            \"source\": \"postman-streaming\",\n            \"timestamp\": \"{{$isoTimestamp}}\"\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/agent/invoke", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "invoke"]}, "description": "Invoke an AI agent with streaming response (Server-Sent Events)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "pm.test('Content-Type is text/event-stream', () => {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('text/event-stream');", "});", "pm.test('Response contains streaming data', () => {", "    pm.expect(pm.response.text()).to.include('data:');", "});"]}}]}, {"name": "List Available Agents", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/agent/list", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "list"]}, "description": "Get list of all available AI agents"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains agents array', () => pm.expect(json).to.have.property('agents'));", "pm.test('Agents array is not empty', () => {", "    pm.expect(json.agents).to.be.an('array');", "    pm.expect(json.agents.length).to.be.greaterThan(0);", "});", "pm.test('Each agent has required properties', () => {", "    json.agents.forEach(agent => {", "        pm.expect(agent).to.have.property('type');", "        pm.expect(agent).to.have.property('metadata');", "    });", "});"]}}]}, {"name": "Get Agents Health", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/agent/health", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "health"]}, "description": "Get health status of all AI agents"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains status', () => pm.expect(json).to.have.property('status'));", "pm.test('Contains agents health', () => pm.expect(json).to.have.property('agents'));", "pm.test('Contains timestamp', () => pm.expect(json).to.have.property('timestamp'));", "pm.test('Status is valid', () => {", "    pm.expect(['healthy', 'unhealthy', 'degraded']).to.include(json.status);", "});", "pm.test('Each agent has health status', () => {", "    if (json.agents && typeof json.agents === 'object') {", "        Object.values(json.agents).forEach(agentHealth => {", "            pm.expect(agentHealth).to.have.property('status');", "        });", "    }", "});"]}}]}, {"name": "Get Agents Metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/agent/metrics", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "metrics"]}, "description": "Get performance metrics for all AI agents"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200', () => pm.response.to.have.status(200));", "var json = pm.response.json();", "pm.test('Contains metrics', () => pm.expect(json).to.have.property('metrics'));", "pm.test('Metrics is an object', () => {", "    pm.expect(json.metrics).to.be.an('object');", "});"]}}]}, {"name": "Get Specific Agent Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/agent/echo", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "echo"]}, "description": "Get details and metrics for a specific agent"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 200 or 404', () => {", "    pm.expect([200, 404]).to.include(pm.response.code);", "});", "if (pm.response.code === 200) {", "    var json = pm.response.json();", "    pm.test('Contains agent name', () => pm.expect(json).to.have.property('name'));", "    pm.test('Contains metadata', () => pm.expect(json).to.have.property('metadata'));", "    pm.test('Contains metrics', () => pm.expect(json).to.have.property('metrics'));", "} else if (pm.response.code === 404) {", "    var json = pm.response.json();", "    pm.test('Contains error message', () => pm.expect(json).to.have.property('error'));", "}"]}}]}, {"name": "Get Non-existent Agent (Error Test)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/internal/agent/non-existent-agent", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "non-existent-agent"]}, "description": "Test error handling for non-existent agent"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 404', () => pm.response.to.have.status(404));", "var json = pm.response.json();", "pm.test('Contains error', () => pm.expect(json).to.have.property('error'));", "pm.test('Contains message', () => pm.expect(json).to.have.property('message'));", "pm.test('Error indicates agent not found', () => {", "    pm.expect(json.error).to.equal('Agent not found');", "});"]}}]}, {"name": "Invoke Agent with Invalid Data (Error Test)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"agent_name\": \"\",\n    \"input\": {},\n    \"invalid_field\": \"this should cause validation error\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/internal/agent/invoke", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "agent", "invoke"]}, "description": "Test validation error handling for invalid agent invocation"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status 400', () => pm.response.to.have.status(400));", "var json = pm.response.json();", "pm.test('Contains validation error', () => {", "    pm.expect(json.message || json.error).to.include('validation');", "});"]}}]}]}, {"name": "Organization", "item": [{"name": "Get Organization Details", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Organization ID for tenant isolation"}], "url": {"raw": "{{base_url}}/api/v1/internal/organization/{{organization_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "internal", "organization", "{{organization_id}}"]}, "description": "Get details of an organization using the access token"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has organization details\", function () {", "    var jsonData = JSON.parse(responseBody);", "    pm.expect(jsonData).to.have.property('organization_id');", "    pm.expect(jsonData).to.have.property('organization_name');", "});"]}}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string", "description": "Base URL for the API"}, {"key": "access_token", "value": "", "type": "string", "description": "JWT access token"}, {"key": "user_email", "value": "<EMAIL>", "type": "string", "description": "Email of the user to authenticate"}, {"key": "organization_id", "value": "cm9of7eyo0006i1o45sqroxev", "type": "string", "description": "Organization ID for tenant isolation"}, {"key": "internal_api_secret", "value": "dev-secret-key-123", "type": "string", "description": "Secret key for internal API access"}, {"key": "mcp_session_id", "value": "", "type": "string", "description": "MCP session ID from session initialization"}, {"key": "mcp_session_token", "value": "", "type": "string", "description": "MCP session token for WebSocket authentication"}]}