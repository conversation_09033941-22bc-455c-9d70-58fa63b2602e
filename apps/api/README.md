# AskInfoSec API

A secure multi-tenant API for information security knowledge management and AI-powered question answering.

## Features

- **Multi-tenant Architecture**: Secure data isolation between organizations using Row Level Security
- **AI Integration**: OpenAI-powered document analysis and question answering
- **Document Processing**: Support for PDF, DOCX, and text files
- **Vector Search**: Semantic search capabilities using embeddings

## Architecture

### Core Components

- **Fastify Framework**: High-performance web framework
- **Drizzle ORM**: Modern TypeScript ORM with database access and Row Level Security
- **LangChain**: AI integration for document processing and embeddings
- **PostgreSQL**: Primary database with vector search capabilities

### Security Features

- **Row Level Security (RLS)**: Database-level tenant isolation
- **Organization-based Access Control**: All data access is scoped to organization ID
- **Environment Variable Protection**: Secure handling of API keys and credentials

## Development

This API is part of the AskInfoSec mono-repo workspace. From the workspace root:

```bash
# Start the API development server
$ pnpm api:dev

# Build the API
$ pnpm api:build

# Run API tests
$ pnpm api:test
```

Or from the API directory:

```bash
# Start development server
$ pnpm dev

# Build the project
$ pnpm build

# Run tests
$ pnpm test
```

## Environment Configuration

Copy `.env.example` to `.env` and update the values:

```bash
$ cp .env.example .env
```

See `.env.example` for all required environment variables.

## Docker

Build and run the API using Docker:

```bash
# Build the Docker image
$ docker build -t askinfosec-api .

# Run the container
$ docker run -p 8000:8000 askinfosec-api
```

Or use the development Docker Compose from the workspace root:

```bash
$ docker-compose -f docker-compose.dev.yml up
```

## API Documentation

See the `docs/` directory for detailed API documentation including:

- Authentication
- Middleware
- Routes
- Validation

## Project Structure

```
├── docs/                 # API documentation
├── src/
│   ├── api/              # Versioned API endpoints
│   │   └── v1/           # Version 1 API
│   │       ├── external/ # External/partner endpoints
│   │       └── internal/ # Internal/frontend endpoints
│   ├── core/             # Core functionality
│   ├── lib/              # Shared libraries
│   ├── plugins/          # Fastify plugins
│   ├── services/         # Business logic
│   ├── types/            # TypeScript type definitions
│   └── app.ts            # Application setup
├── test/                 # Tests
├── examples/             # Usage examples
├── package.json
├── tsconfig.json
└── Dockerfile
```
