const { ChatOpenAI } = require('@langchain/openai');
const { tool } = require('@langchain/core/tools');
const { z } = require('zod');

console.log('=== Testing createReactAgent isolation ===');

// Test 1: Basic LLM creation and bindTools
console.log('1. Testing basic LLM creation...');
const llm = new ChatOpenAI({ model: 'gpt-4o', temperature: 0.1 });
console.log('   LLM created successfully');
console.log('   bindTools type:', typeof llm.bindTools);

// Test 2: Simple tool creation
console.log('2. Testing tool creation...');
const testTool = tool(
  async input => {
    return `Result: ${input.value * 2}`;
  },
  {
    name: 'test_tool',
    description: 'A simple test tool',
    schema: z.object({
      value: z.number(),
    }),
  }
);
console.log('   Tool created successfully');

// Test 3: Try importing createReactAgent
console.log('3. Testing createReactAgent import...');
try {
  const { createReactAgent } = require('@langchain/langgraph/prebuilt');
  console.log('   createReactAgent imported successfully');

  // Test 4: Try creating the agent
  console.log('4. Testing createReactAgent creation...');
  try {
    const agent = createReactAgent({
      llm,
      tools: [testTool],
      name: 'test_agent',
    });
    console.log('   createReactAgent created successfully');
    console.log('   Agent type:', typeof agent);
    console.log('   Agent constructor:', agent.constructor.name);
  } catch (agentError) {
    console.log('   createReactAgent creation FAILED:', agentError.message);
    console.log('   Error stack:', agentError.stack);
  }
} catch (importError) {
  console.log('   createReactAgent import FAILED:', importError.message);
  console.log('   Available exports from @langchain/langgraph/prebuilt:');
  try {
    const prebuiltModule = require('@langchain/langgraph/prebuilt');
    console.log('   ', Object.keys(prebuiltModule));
  } catch (e) {
    console.log('   Could not inspect prebuilt module:', e.message);
  }
}

// Test 5: Check LangGraph version and dependencies
console.log('5. Checking package versions...');
try {
  const pkg = require('@langchain/langgraph/package.json');
  console.log('   @langchain/langgraph version:', pkg.version);
} catch (e) {
  console.log('   Could not read @langchain/langgraph version');
}

try {
  const corePkg = require('@langchain/core/package.json');
  console.log('   @langchain/core version:', corePkg.version);
} catch (e) {
  console.log('   Could not read @langchain/core version');
}

try {
  const openaiPkg = require('@langchain/openai/package.json');
  console.log('   @langchain/openai version:', openaiPkg.version);
} catch (e) {
  console.log('   Could not read @langchain/openai version');
}
