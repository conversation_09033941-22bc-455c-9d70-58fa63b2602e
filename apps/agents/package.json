{"name": "@anter/agents", "version": "0.0.1", "description": "AI agents for AskInfoSec platform", "main": "dist/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/types/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest", "test:coverage": "vitest --coverage", "clean": "rm -rf dist", "clean:all": "rm -rf dist node_modules"}, "dependencies": {"@anter/mcp-tools": "workspace:^", "@anter/shared-services": "workspace:*", "@langchain/core": "^0.3.61", "@langchain/langgraph": "^0.3.6", "@langchain/langgraph-supervisor": "^0.0.14", "@langchain/openai": "^0.5.0", "dotenv": "^17.0.1", "html-to-text": "^9.0.5", "langfuse": "^3.38.0", "langfuse-langchain": "^3.38.0", "langchain": "0.3.29", "openai": "^5.8.2"}, "devDependencies": {"@types/html-to-text": "^9.0.4", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "typescript": "^5.8.3", "vitest": "^3.2.4"}}