# @anter/agents

Enterprise-grade AI Agent system with LangChain integration and MCP support.

## Overview

This package provides a collection of specialized AI agents built on LangChain's mature OpenAI integration, featuring advanced orchestration, security, and observability capabilities. The system includes a robust MCP (Model Context Protocol) bridge for stateful AI interactions and production-ready LangChain-based OpenAI chat models.

## Features

- **Multi-Agent Framework**: Extensible agent system with factory pattern and registration
- **LangChain OpenAI Integration**: Production-ready OpenAI integration with SystemMessage and HumanMessage support
- **CommonJS Compatibility**: Stable, mature integration without ES module complications
- **MCP Bridge**: Seamless integration with Model Context Protocol for tool calling and stateful interactions
- **Security Framework**: Multi-layer protection with input sanitization and SQL injection prevention
- **Streaming Support**: Real-time response streaming for enhanced user experience
- **Performance Monitoring**: Comprehensive metrics and observability
- **ES Module Compatibility**: Dynamic imports for ES module support while maintaining CommonJS compatibility

## Installation

```bash
pnpm add @anter/agents
```

## Quick Start

```typescript
import { initializeAgentSystem, AgentFactory } from '@anter/agents';

// Initialize the agent system with MCP connection manager
const agentSystem = await initializeAgentSystem({
  connectionManager: mcpConnectionManager,
  logger: console,
});

// Create an agent
const agent = AgentFactory.getAgent('ask-ai');

// Use the agent
const result = await agent.invoke({
  input: 'What are the latest security vulnerabilities in our database?',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});

// Stream responses
for await (const chunk of agent.stream({
  input: 'Explain SQL injection vulnerabilities',
  context: { organizationId: 'org-123' },
})) {
  console.log(chunk.content);
}
```

## Available Agents

### AskAI Agent (`ask-ai`)

General-purpose AI agent that processes user prompts and automatically selects appropriate tools:

- **Database Queries**: Executes SQL queries with tenant isolation and security validation
- **OpenAI Integration**: Uses OpenAI chat agent for conversational responses
- **Multi-Tool Orchestration**: Intelligently routes requests to the best available tools
- **Security**: Input sanitization, SQL injection prevention, organization-based access control

```typescript
const askAIAgent = AgentFactory.getAgent('ask-ai');
const result = await askAIAgent.invoke({
  input: 'Show me files related to security policies',
  context: { organizationId: 'org-123' },
});
```

### OpenAI Chat Agent (`openai-chat-agent`)

LangChain-based OpenAI integration for reliable conversational AI:

- **LangChain ChatOpenAI**: Uses LangChain's mature ChatOpenAI model for stability
- **Message Types**: Proper SystemMessage and HumanMessage handling
- **Configurable Instructions**: Customizable system prompts and behavior
- **Context Awareness**: Maintains conversation context and memory
- **Error Handling**: Robust error handling with comprehensive logging
- **CommonJS Compatible**: No ES module complications, works reliably in all environments

```typescript
import { createChatAgent } from '@anter/agents';

const chatAgent = createChatAgent({
  model: 'gpt-4o-mini',
  instructions: 'You are a cybersecurity expert assistant.',
  temperature: 0.7,
});

const response = await chatAgent.chat('What is a zero-day vulnerability?');
```

### Echo Agent (`echo`)

Simple test agent for development and validation:

- **Input Validation**: Tests input processing and validation
- **Streaming Support**: Demonstrates streaming capabilities
- **Metrics**: Performance metrics collection and monitoring

```typescript
const echoAgent = AgentFactory.getAgent('echo');
const result = await echoAgent.invoke({
  input: 'Hello, world!',
  context: { organizationId: 'test-org' },
});
```

## Architecture

### Core Components

- **Base Framework**: Abstract agent classes and interfaces for extensibility
- **Agent Factory**: Type-safe agent creation and registration system
- **MCP Bridge**: Integration with Model Context Protocol for tool calling
- **Connection Manager**: Session pooling and database connection management
- **Security Framework**: Input validation, sanitization, and threat detection
- **Observability**: Metrics collection, logging, and performance monitoring

### MCP Integration

The MCP bridge provides seamless integration with external tools and services:

```typescript
// MCP bridge automatically handles:
// - Session management and pooling
// - Tool discovery and execution
// - Context synchronization
// - Error handling and retry logic
// - Security validation and tenant isolation

const bridge = new AgentMCPBridge(connectionManager);
const result = await bridge.executeToolCall(
  sessionId,
  'database_query',
  { query: 'SELECT id FROM file WHERE organization_id = $1' },
  context
);
```

### Agent Orchestration

The ask-ai agent demonstrates sophisticated orchestration patterns:

1. **Input Analysis**: Analyzes user input to determine intent and required tools
2. **Tool Selection**: Automatically selects appropriate tools (chat, database, direct response)
3. **Sequential Execution**: Executes chat agent first, then database queries
4. **Result Aggregation**: Combines results from multiple tools into cohesive responses

## Security Features

### Multi-Layer Protection

- **Input Sanitization**: XSS and injection attack prevention
- **SQL Injection Prevention**: Comprehensive SQL validation and parameterization
- **Tenant Isolation**: Organization-based data access control
- **Context Validation**: User and organization context verification
- **Conditional Security**: SQL validation only when using database tools

### Security Configuration

```typescript
// Security is automatically applied based on tool usage
const secureAgent = AgentFactory.getAgent('ask-ai');

// SQL validation only triggered for database operations
const dbResult = await secureAgent.invoke({
  input: 'SELECT * FROM users', // Triggers SQL validation
  context: { organizationId: 'org-123' },
});

// No SQL validation for general prompts
const chatResult = await secureAgent.invoke({
  input: 'What is cybersecurity?', // No SQL validation
  context: { organizationId: 'org-123' },
});
```

## Performance & Monitoring

### Metrics Collection

All agents include comprehensive metrics:

- **Execution Time**: Response latency tracking
- **Tool Usage**: Breakdown by tool type (database, chat, direct)
- **Error Rates**: Success/failure rates and error classification
- **Resource Usage**: Memory and CPU utilization
- **Security Events**: Detection and reporting of security threats

### Performance Optimization

- **Connection Pooling**: Efficient database connection reuse
- **Session Management**: MCP session pooling and lifecycle management
- **Caching**: Redis-based caching for frequently accessed data
- **Streaming**: Real-time response streaming to reduce perceived latency

## Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_CHAT_INSTRUCTIONS=custom_instructions

# Database Configuration
DATABASE_URL_RLS_USER=postgresql://user:password@localhost:5432/askinfosec

# Redis Configuration (optional - mock fallback available)
REDIS_URL=redis://localhost:6379

# MCP Configuration
MCP_SESSION_TIMEOUT_MINUTES=30
MCP_MAX_SESSIONS_PER_ORG=10
```

### Agent Configuration

```typescript
// Configure agents during initialization
const agentSystem = await initializeAgentSystem({
  connectionManager: mcpConnectionManager,
  logger: fastify.log,
  config: {
    agents: {
      'ask-ai': {
        maxConcurrency: 10,
        timeout: 30000,
        retries: 3,
      },
      'openai-chat-agent': {
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
      },
    },
  },
});
```

## Development

### Prerequisites

- Node.js 18+
- pnpm
- PostgreSQL database
- Redis (optional - mock fallback available)
- OpenAI API key

### Setup

```bash
# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Build the package
pnpm build
```

### Development Commands

```bash
# Build the package
pnpm build

# Run tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Check test coverage
pnpm test:coverage

# Clean build artifacts
pnpm clean

# Type checking
pnpm type-check

# Linting
pnpm lint
```

### Testing

The package includes comprehensive test coverage:

- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: End-to-end functionality testing
- **Security Tests**: Validation of security features
- **Performance Tests**: Load testing and benchmarking

```bash
# Run all tests
pnpm test

# Run specific test suites
pnpm test ask-ai-agent
pnpm test openai-chat-agent
pnpm test mcp-bridge

# Generate coverage report
pnpm test:coverage
```

## ES Module Compatibility

The package uses dynamic imports to support ES modules while maintaining CommonJS compatibility:

```typescript
// The OpenAI agents package is automatically loaded using dynamic imports
// No configuration required - works seamlessly in both environments

import { createChatAgent } from '@anter/agents';
// or
const { createChatAgent } = require('@anter/agents');
```

## Creating Custom Agents

### Agent Implementation

```typescript
import { AbstractAgent, AgentInput, AgentResult, AgentContext } from '@anter/agents';

export class CustomAgent extends AbstractAgent {
  constructor() {
    super({
      name: 'custom-agent',
      version: '0.0.1',
      description: 'Custom agent for specific use case',
    });
  }

  async invoke(input: AgentInput, context: AgentContext): Promise<AgentResult> {
    // Implement custom logic
    return {
      success: true,
      result: 'Custom agent response',
      metadata: { executionTime: Date.now() },
    };
  }

  async *stream(input: AgentInput, context: AgentContext): AsyncGenerator<any> {
    // Implement streaming logic
    yield { content: 'Streaming response...' };
  }
}
```

### Agent Registration

```typescript
import { AgentFactory } from '@anter/agents';
import { CustomAgent } from './custom-agent';

// Register the custom agent
AgentFactory.registerAgent('custom', () => new CustomAgent());

// Use the custom agent
const customAgent = AgentFactory.getAgent('custom');
```

## Troubleshooting

### Common Issues

1. **ES Module Import Errors**: The package automatically handles ES module compatibility through dynamic imports
2. **Database Connection Issues**: Ensure DATABASE_URL_RLS_USER is correctly configured
3. **OpenAI API Errors**: Verify OPENAI_API_KEY is set and valid
4. **Redis Connection**: Redis is optional - mock fallback is used when Redis is unavailable

### Debug Mode

```typescript
// Enable debug logging
const agentSystem = await initializeAgentSystem({
  connectionManager: mcpConnectionManager,
  logger: console,
  debug: true,
});
```

## Documentation

- [Implementation Plan](./docs/implementation-plan.md) - Detailed project roadmap and phases
- [Phase 2 Summary](./docs/phase2-summary.md) - Current implementation status
- [OpenAI Chat Agent](./src/agents/openai/chat-agent/README.md) - OpenAI SDK integration details

## Contributing

1. Follow the existing code patterns and TypeScript conventions
2. Write comprehensive tests for new features
3. Update documentation for any API changes
4. Ensure security best practices are followed
5. Add metrics and observability for new components

## License

ISC
