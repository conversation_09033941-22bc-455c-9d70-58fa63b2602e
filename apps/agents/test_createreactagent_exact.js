const { ChatOpenAI } = require('@langchain/openai');
const { tool } = require('@langchain/core/tools');
const { z } = require('zod');
const { Annotation } = require('@langchain/langgraph');

console.log('=== Testing exact failing scenario ===');

// Replicate the exact setup from the idiomatic supervisor
const llm = new ChatOpenAI({ model: 'gpt-4o', temperature: 0.1 });

// Create state schema like in the supervisor
const SupervisorStateAnnotation = Annotation.Root({
  messages: Annotation({
    reducer: (existing, newMessages) => [...existing, ...newMessages],
    default: () => [],
  }),
  sessionId: Annotation({
    reducer: (existing, newValue) => newValue || existing,
    default: () => 'default-session',
  }),
});

console.log('1. LLM and state schema created');

// Create a simple tool
const testTool = tool(
  async input => {
    return `Result: ${input.value}`;
  },
  {
    name: 'test_tool',
    description: 'A simple test tool',
    schema: z.object({
      value: z.string(),
    }),
  }
);

console.log('2. Tool created');

// Test different combinations
const { createReactAgent } = require('@langchain/langgraph/prebuilt');

console.log('3. Testing createReactAgent with prompt and stateSchema (like in failing code)...');
try {
  const agentWithBothParams = createReactAgent({
    llm,
    tools: [testTool],
    name: 'test_expert',
    prompt: `You are a test expert.`,
    stateSchema: SupervisorStateAnnotation,
  });
  console.log('   SUCCESS: createReactAgent with both prompt and stateSchema worked');
} catch (error) {
  console.log(
    '   FAILED: createReactAgent with both prompt and stateSchema failed:',
    error.message
  );
  console.log('   Error stack:', error.stack);
}

console.log('4. Testing createReactAgent with only stateSchema...');
try {
  const agentWithStateOnly = createReactAgent({
    llm,
    tools: [testTool],
    name: 'test_expert',
    stateSchema: SupervisorStateAnnotation,
  });
  console.log('   SUCCESS: createReactAgent with only stateSchema worked');
} catch (error) {
  console.log('   FAILED: createReactAgent with only stateSchema failed:', error.message);
}

console.log('5. Testing createReactAgent with messageModifier instead of prompt...');
try {
  const agentWithMessageModifier = createReactAgent({
    llm,
    tools: [testTool],
    name: 'test_expert',
    messageModifier: 'You are a test expert.',
    stateSchema: SupervisorStateAnnotation,
  });
  console.log('   SUCCESS: createReactAgent with messageModifier worked');
} catch (error) {
  console.log('   FAILED: createReactAgent with messageModifier failed:', error.message);
}
