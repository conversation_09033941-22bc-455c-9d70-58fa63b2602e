# API → Agent → MCP Tool End-to-End Flow

> **Scope**: This document tracks the exact call chain for a typical request that hits the `POST /api/v1/internal/agent/invoke` endpoint and ultimately executes the `get_all_documents` MCP tool. All file paths are relative to the workspace root.

---

## 1. High-Level Overview

1. **HTTP Layer** – A client sends an authenticated (JWT) request to **Fastify**.
2. **Route Handler** – Fastify delegates to the _agents service_ exposed by the **Agents plugin**.
3. **Agent Invocation** – The requested agent (e.g. **AskAI**) is instantiated (or reused) via **AgentFactory** and its `invoke()` method is called.
4. **MCP Bridge** – Inside the agent, a **MCPConnectionManager** locates/creates a session and forwards the tool call via **AgentMCPBridge**.
5. **MCP Server** – The in-process MCP server receives the JSON-RPC request, looks up the tool implementation in **packages/mcp-tools**, executes it, and returns the result.
6. **Response Aggregation** – The bridge parses the response, the agent finishes its pipeline (RAG, LLM, etc.), and Fastify responds to the HTTP client.

---

## 2. Detailed Call Sequence

| #   | Stage                   | Responsible Code                                      | Key Function              | Notes                                                |
| --- | ----------------------- | ----------------------------------------------------- | ------------------------- | ---------------------------------------------------- |
| 1   | **API Route**           | `apps/api/src/api/v1/internal/routes/agents/index.ts` | `invokeAgentRoute`        | Validates JWT, then calls `fastify.agents.invoke()`  |
| 2   | **Plugin Service**      | `apps/api/src/plugins/agents.ts`                      | `fastify.agents.invoke()` | Delegates to **AgentFactory**                        |
| 3   | **Agent Factory**       | `apps/agents/src/base/agent-factory.ts`               | `AgentFactory.create()`   | Returns singleton instance (initialized)             |
| 4   | **AskAI Agent**         | `apps/agents/src/agents/ask-ai/index.ts`              | `invoke()` → `doInvoke()` | Executes RAG pipeline                                |
| 5   | **Document Retrieval**  | same as above                                         | `retrieveAllDocuments()`  | Needs all org docs                                   |
| 6   | **Connection Manager**  | `apps/agents/src/integration/connection-manager.ts`   | `executeToolCall()`       | Gets/creates MCP session                             |
| 7   | **MCP Bridge**          | `apps/agents/src/integration/mcp-bridge.ts`           | `callToolDirect()`        | Sends JSON-RPC `tools/call`                          |
| 8   | **MCP Server**          | `apps/api/src/core/mcp/server.ts`                     | `handleJsonRpcMessage()`  | Dispatches to tool implementation                    |
| 9   | **Tool Implementation** | `packages/mcp-tools/src/tools/index.ts`               | `getAllDocuments()`       | Runs SQL via Drizzle, returns docs                   |
| 10  | **Return Path**         | bridge → connection mgr → agent → plugin → route      | —                         | Each layer unwraps/parses data and propagates upward |

---

## 3. Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant Fastify as Fastify API
    participant Route as Route Handler
    participant Plugin as Agents Plugin
    participant Factory as AgentFactory
    participant AskAI as AskAI Agent
    participant ConnMgr as MCPConnectionManager
    participant Bridge as AgentMCPBridge
    participant MCP as MCP Server
    participant Tool as get_all_documents

    Client->>Fastify: POST /api/v1/internal/agent/invoke (JWT)
    Fastify->>Route: validate & parse
    Route->>Plugin: fastify.agents.invoke()
    Plugin->>Factory: create("ask_ai")
    Factory-->>Plugin: AskAI instance
    Plugin->>AskAI: invoke(input)
    AskAI->>AskAI: retrieveAllDocuments()
    AskAI->>ConnMgr: executeToolCall("get_all_documents")
    ConnMgr->>Bridge: callToolDirect()
    Bridge->>MCP: JSON-RPC tools/call
    MCP->>Tool: getAllDocuments()
    Tool-->>MCP: { documents }
    MCP-->>Bridge: JSON-RPC result
    Bridge-->>ConnMgr: parsed result
    ConnMgr-->>AskAI: documents
    AskAI->>AskAI: RAG, LLM, etc.
    AskAI-->>Plugin: AgentResult
    Plugin-->>Route: AgentResult
    Route-->>Fastify: reply.send()
    Fastify-->>Client: HTTP 200 AgentResult
```

---

## 4. Opportunities for Simplification (Non-Breaking)

> _These ideas focus on reducing indirection while preserving current behaviour._

1. **Merge `MCPConnectionManager` & `AgentMCPBridge`**  
   They are currently thin wrappers around each other (manager pools sessions, bridge handles JSON-RPC). A single class `MCPClient` could expose `executeToolCall` with built-in pooling and JSON-RPC, eliminating two hops while keeping the public API unchanged for agents.

2. **Introduce Typed Tool Wrappers**  
   Generate TypeScript wrappers (e.g. `getAllDocuments(input, ctx)`) from MCP tool definitions. Agents would import and call functions directly instead of string-based names, catching schema errors at compile time.

3. **Collapse Plugin → Factory Hop**  
   The Fastify plugin currently delegates straight to `AgentFactory.create` each request. Caching or pre-registering agents at plugin init would remove this extra promise chain.

4. **Async/Await Boundary Reduction**  
   `callToolDirect` constructs a JSON-RPC payload only to send it to an _in-process_ server. Exposing the tool catalogue as plain functions during unit tests/dev could bypass JSON-RPC when `process.env.MCP_MODE === 'in_process'` while still supporting external servers in prod.

5. **Standardise Result Parsing**  
   The bridge had double-JSON parsing issues. Enforcing a single, canonical response shape (e.g. always `result.data`) would remove defensive parsing code across layers.

6. **Centralise Error Handling**  
   Push error classification to one layer (e.g. `MCPClient`) so callers only deal with domain-level errors, simplifying `connection-manager.ts` and agent code.

### Suggested Next Steps

| Priority | Action                                           | Effort | Risk   |
| -------- | ------------------------------------------------ | ------ | ------ |
| ⭐️      | Prototype `MCPClient` merging pooling + JSON-RPC | Low    | Low    |
| ⭐️      | Add compile-time generated tool wrappers         | Medium | Low    |
| ☆        | Cache agent instances in plugin init             | Low    | None   |
| ☆        | Implement in-process short-circuit for dev mode  | Medium | Medium |

Implementing the first two ⭐️ items would likely trim ~200 LOC and two async layers without affecting existing tests.

---

## 5. References

- `apps/api/src/api/v1/internal/routes/agents/index.ts`
- `apps/api/src/plugins/agents.ts`
- `apps/agents/src/base/agent-factory.ts`
- `apps/agents/src/agents/ask-ai/index.ts`
- `apps/agents/src/integration/connection-manager.ts`
- `apps/agents/src/integration/mcp-bridge.ts`
- `apps/api/src/core/mcp/server.ts`
- `packages/mcp-tools/src/tools/index.ts`

---

**Status**: _Documentation generated on demand – no code behaviour altered._
