# Optimized RAG Flow Sequence Diagram

**Document Version:** 2.1  
**Date:** 2025-01-16  
**Status:** Updated with New Document Handling  
**Scope:** Document Retrieval → Embeddings to Redis with Optimized Caching

## Executive Summary

This document provides an updated sequence diagram reflecting the optimized RAG (Retrieval-Augmented Generation) flow after implementing critical performance fixes. The optimizations eliminate unnecessary re-embedding of documents on every request, significantly improving performance and reducing API costs.

### Key Optimizations Implemented

1. **Removed Debug Cache Clear**: Eliminated forced memory cache clearing that caused cache misses
2. **Persistent Hash Storage**: Added Redis-based hash persistence to prevent hash cache loss
3. **Conditional Re-indexing**: Only re-embed documents when they actually change
4. **Smart Cache Strategy**: Multi-level caching with intelligent reconciliation
5. **New Document Detection**: Proper handling of newly added documents

## Optimized Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant AskAIAgent
    participant DocumentServiceV2
    participant DocumentCacheService
    participant ContentHashService
    participant SemanticSearchService
    participant Redis
    participant Database

    User->>AskAIAgent: Send chat message
    AskAIAgent->>DocumentServiceV2: retrieveAllDocuments(orgId)

    Note over DocumentServiceV2: STEP 1: Get documents from cache
    DocumentServiceV2->>DocumentCacheService: getDocuments(orgId)

    Note over DocumentCacheService: OPTIMIZATION: No forced cache clear
    DocumentCacheService->>DocumentCacheService: Check memory cache

    alt Memory Cache Hit
        DocumentCacheService-->>DocumentServiceV2: documents (from memory)
        Note over DocumentServiceV2: SKIP RE-INDEXING (Optimization)
        DocumentServiceV2-->>AskAIAgent: Documents (memory cache)
        AskAIAgent-->>User: Response (fast path)
    else Memory Cache Miss
        DocumentCacheService->>Redis: loadFromRedis(orgId)
        Redis-->>DocumentCacheService: cached documents

        DocumentCacheService->>Database: fetchDocumentIds(orgId)
        Database-->>DocumentCacheService: document metadata (ALL docs)

        Note over DocumentCacheService: CRITICAL: Reconcile cache vs database
        DocumentCacheService->>DocumentCacheService: reconcileCache(cachedDocs, dbMeta)

        alt No New/Changed Documents
            DocumentCacheService-->>DocumentServiceV2: documents (from Redis)
            Note over DocumentServiceV2: SKIP RE-INDEXING (Optimization)
            DocumentServiceV2-->>AskAIAgent: Documents (Redis cache)
            AskAIAgent-->>User: Response (optimized path)
        else New/Changed Documents Detected
            DocumentCacheService->>Database: fetchDocumentsByIds(idsToFetch)
            Database-->>DocumentCacheService: new/changed documents

            DocumentCacheService-->>DocumentServiceV2: documents (mixed cache/database)

            Note over DocumentServiceV2: STEP 2: CONDITIONAL RE-INDEXING
            DocumentServiceV2->>SemanticSearchService: indexDocumentsIncremental(documents, orgId)

            Note over SemanticSearchService: Load persistent hashes
            SemanticSearchService->>ContentHashService: loadPersistentHashes(orgId)
            ContentHashService->>Redis: get(content_hashes:orgId)
            Redis-->>ContentHashService: hash data
            ContentHashService-->>SemanticSearchService: hashes loaded

            Note over SemanticSearchService: Check which documents changed
            SemanticSearchService->>ContentHashService: getDocumentsToReindex(documents)

            alt No Documents Need Re-indexing
                ContentHashService-->>SemanticSearchService: empty array
                Note over SemanticSearchService: SKIP EMBEDDING (Optimization)
                SemanticSearchService-->>DocumentServiceV2: No re-indexing needed
            else Documents Need Re-indexing
                ContentHashService-->>SemanticSearchService: documents to reindex

                Note over SemanticSearchService: Generate embeddings only for changed docs
                SemanticSearchService->>SemanticSearchService: embedText(doc.content) for changed docs
                SemanticSearchService->>Redis: Store embeddings in vector index

                Note over SemanticSearchService: Save persistent hashes
                SemanticSearchService->>ContentHashService: savePersistentHashes(orgId)
                ContentHashService->>Redis: set(content_hashes:orgId, hashData)
                Redis-->>ContentHashService: hashes saved
                ContentHashService-->>SemanticSearchService: hashes persisted

                SemanticSearchService-->>DocumentServiceV2: Indexing complete
            end

            DocumentServiceV2-->>AskAIAgent: Documents with optimized embeddings
            AskAIAgent-->>User: Response (optimized path)
        end
    end
```

## New Document Scenario - Detailed Flow

```mermaid
sequenceDiagram
    participant User
    participant System
    participant Cache
    participant DB

    Note over System: Initial State: 2 documents cached

    User->>System: Request 1
    System->>Cache: Check memory cache
    Cache-->>System: 2 documents (hit)
    System-->>User: Response (fast)

    Note over DB: User adds 3rd document to database

    User->>System: Request 2
    System->>Cache: Check memory cache
    Cache-->>System: 2 documents (hit)
    Note over System: PROBLEM: New document not detected!
    System-->>User: Response (missing new document)

    Note over System: SOLUTION: Force cache refresh on new requests

    User->>System: Request 3 (after cache TTL)
    System->>Cache: Check memory cache
    Cache-->>System: Cache miss
    System->>DB: fetchDocumentIds()
    DB-->>System: 3 documents (including new one)
    System->>Cache: reconcileCache(2 cached, 3 in DB)
    Note over System: NEW DOCUMENT DETECTED
    System->>DB: fetchDocumentsByIds([newDocId])
    DB-->>System: new document content
    System->>System: Re-index new document
    System-->>User: Response (with all 3 documents)
```

## Cache Invalidation Strategy

### Current Issue

The memory cache hit bypasses the reconciliation logic, causing new documents to be missed.

### Proposed Solutions

#### Option 1: Cache TTL with Database Check

```typescript
async getDocuments(orgId: string) {
  const memCache = this.memoryCache.get(orgId);

  // Check if cache is still valid
  if (memCache && this.isCacheValid(memCache)) {
    // Always check database for new documents
    const dbMeta = await this.documentFetcher.fetchDocumentIds(orgId);
    const { docsToKeep, idsToFetch } = this.reconcileCache(memCache.documents, dbMeta);

    if (idsToFetch.length === 0) {
      return { documents: memCache.documents, source: 'memory' };
    } else {
      // New documents detected, fetch them
      const newDocs = await this.documentFetcher.fetchDocumentsByIds(orgId, idsToFetch);
      const finalDocs = [...docsToKeep, ...newDocs];
      // Update cache
      this.updateCache(orgId, finalDocs);
      return { documents: finalDocs, source: 'database' };
    }
  }

  // Cache miss, proceed with normal flow
  // ... rest of logic
}
```

#### Option 2: Event-Driven Cache Invalidation

```typescript
// When documents are added/updated
async onDocumentChange(orgId: string, documentId: string) {
  // Invalidate cache for the organization
  this.clearCache(orgId);

  // Or update cache incrementally
  const newDoc = await this.documentFetcher.fetchDocumentsByIds(orgId, [documentId]);
  this.addToCache(orgId, newDoc[0]);
}
```

#### Option 3: Periodic Cache Refresh

```typescript
// Background job to refresh caches
async refreshCache(orgId: string) {
  const dbMeta = await this.documentFetcher.fetchDocumentIds(orgId);
  const cachedDocs = await this.loadFromRedis(orgId);

  const { docsToKeep, idsToFetch } = this.reconcileCache(cachedDocs?.documents ?? [], dbMeta);

  if (idsToFetch.length > 0) {
    // New documents found, update cache
    const newDocs = await this.documentFetcher.fetchDocumentsByIds(orgId, idsToFetch);
    const finalDocs = [...docsToKeep, ...newDocs];
    this.updateCache(orgId, finalDocs);
  }
}
```

## Updated Cache Logic

### Recommended Implementation

```typescript
async getDocuments(orgId: string, userId?: string, traceId?: string) {
  // 1. Check memory cache
  const memCache = this.memoryCache.get(orgId);
  if (memCache && memCache.version === this.CACHE_VERSION) {
    // 2. CRITICAL: Always check database for new documents
    const dbMeta = await this.documentFetcher.fetchDocumentIds(orgId, userId, traceId);
    const { docsToKeep, idsToFetch } = this.reconcileCache(memCache.documents, dbMeta);

    if (idsToFetch.length === 0) {
      // No new documents, return from memory cache
      return {
        documents: memCache.documents,
        source: 'memory',
        cacheStats: { docsFromMemory: memCache.documents.length, docsFromRedis: 0, docsFromDatabase: 0 }
      };
    } else {
      // New documents detected, fetch and update cache
      const newDocs = await this.documentFetcher.fetchDocumentsByIds(orgId, userId, idsToFetch, traceId);
      const finalDocs = [...docsToKeep, ...newDocs];

      // Update cache
      const cacheData = { version: this.CACHE_VERSION, documents: finalDocs };
      await this.saveToRedis(orgId, cacheData);
      this.memoryCache.set(orgId, cacheData);

      return {
        documents: finalDocs,
        source: 'database', // Trigger re-indexing
        cacheStats: { docsFromMemory: 0, docsFromRedis: docsToKeep.length, docsFromDatabase: newDocs.length }
      };
    }
  }

  // 3. Memory cache miss, proceed with Redis/database flow
  // ... existing logic
}
```

## Performance Impact of New Document Handling

### Latency Impact

- **Memory Cache Hit + No New Docs**: ~50-100ms (unchanged)
- **Memory Cache Hit + New Docs**: ~200-500ms (database fetch for new docs)
- **Memory Cache Miss**: ~1-2 seconds (full reconciliation)

### Optimization Strategy

1. **Frequent Checks**: Check database on every memory cache hit
2. **Batch Operations**: Fetch multiple new documents in one call
3. **Incremental Updates**: Update cache without full refresh
4. **Background Refresh**: Periodic cache updates to reduce latency

## Conclusion

The optimized flow now properly handles new document scenarios by:

1. **Always Checking Database**: Even on memory cache hits
2. **Intelligent Reconciliation**: Detect new/changed documents
3. **Incremental Updates**: Fetch only new documents
4. **Cache Consistency**: Keep cache in sync with database

This ensures that new documents are always detected and properly indexed while maintaining the performance benefits of caching.

---

**Document Version:** 2.1  
**Last Updated:** 2025-01-16  
**Review Status:** Updated with New Document Handling  
**Next Review:** 2025-02-16
