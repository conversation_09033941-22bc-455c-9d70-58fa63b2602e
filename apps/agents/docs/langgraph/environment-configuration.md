# Environment Configuration Guide

## Overview

This guide covers all environment variables needed to configure the AskInfoSec agents system with Langfuse observability and LangGraph workflows.

## Required Environment Variables

### Langfuse Observability Configuration

```bash
# Langfuse API Keys (get from https://cloud.langfuse.com)
LANGFUSE_PUBLIC_KEY="pk-lf-..."
LANGFUSE_SECRET_KEY="sk-lf-..."

# Langfuse Instance Configuration
LANGFUSE_BASEURL="https://cloud.langfuse.com"
# Alternative US instance: https://us.cloud.langfuse.com
# Self-hosted: https://your-langfuse-instance.com

# Observability Settings
LANGFUSE_TRACING_ENABLED="true"
LANGFUSE_SAMPLE_RATE="1.0"
LANGFUSE_FLUSH_INTERVAL="1000"
```

### LangGraph Configuration

```bash
# LangGraph Workflow Settings
LANGGRAPH_ENABLED="true"
LANGGRAPH_USE_IDIOMATIC_SUPERVISOR="true"
LANGGRAPH_MAX_EXECUTION_TIME="30000"
LANGGRAPH_ENABLE_DEBUG="true"
LANGGRAPH_FALLBACK_ENABLED="true"

# Resource Management
LANGGRAPH_MAX_CONCURRENT_WORKFLOWS="10"
LANGGRAPH_MAX_WORKFLOW_DURATION="60000"
LANGGRAPH_MAX_STEPS_PER_WORKFLOW="50"

# Memory and Persistence
LANGGRAPH_ENABLE_PERSISTENCE="true"
LANGGRAPH_MAX_CONVERSATION_LENGTH="100"
LANGGRAPH_OUTPUT_MODE="last_message"
```

### OpenAI Configuration

```bash
# OpenAI API Key (required for LLM operations)
OPENAI_API_KEY="sk-..."

# Model Configuration
OPENAI_MODEL="gpt-4o"
OPENAI_TEMPERATURE="0.1"
OPENAI_MAX_TOKENS="4000"
```

## Environment-Specific Configurations

### Development Environment

```bash
# .env.development
NODE_ENV="development"

# Langfuse - Full tracing for debugging
LANGFUSE_SAMPLE_RATE="1.0"
LANGFUSE_TRACING_ENABLED="true"

# LangGraph - Debug mode enabled
LANGGRAPH_ENABLE_DEBUG="true"
LANGGRAPH_MAX_CONCURRENT_WORKFLOWS="5"

# Logging
LOG_LEVEL="debug"
```

### Staging Environment

```bash
# .env.staging
NODE_ENV="staging"

# Langfuse - Reduced sampling for cost control
LANGFUSE_SAMPLE_RATE="0.5"
LANGFUSE_TRACING_ENABLED="true"

# LangGraph - Production-like settings
LANGGRAPH_ENABLE_DEBUG="false"
LANGGRAPH_MAX_CONCURRENT_WORKFLOWS="20"

# Logging
LOG_LEVEL="info"
```

### Production Environment

```bash
# .env.production
NODE_ENV="production"

# Langfuse - Minimal sampling for cost efficiency
LANGFUSE_SAMPLE_RATE="0.1"  # 10% sampling
LANGFUSE_TRACING_ENABLED="true"

# LangGraph - Optimized for performance
LANGGRAPH_ENABLE_DEBUG="false"
LANGGRAPH_MAX_CONCURRENT_WORKFLOWS="50"
LANGGRAPH_MAX_WORKFLOW_DURATION="120000"  # 2 minutes

# Logging
LOG_LEVEL="warn"
```

### Testing Environment

```bash
# .env.test
NODE_ENV="test"

# Langfuse - Disabled for tests
LANGFUSE_TRACING_ENABLED="false"

# LangGraph - Test configuration
LANGGRAPH_ENABLE_DEBUG="false"
LANGGRAPH_FALLBACK_ENABLED="true"

# Logging
LOG_LEVEL="error"
```

## Configuration Variables Reference

### Langfuse Variables

| Variable                   | Type    | Default                      | Description                            |
| -------------------------- | ------- | ---------------------------- | -------------------------------------- |
| `LANGFUSE_PUBLIC_KEY`      | string  | -                            | Public API key from Langfuse dashboard |
| `LANGFUSE_SECRET_KEY`      | string  | -                            | Secret API key from Langfuse dashboard |
| `LANGFUSE_BASEURL`         | string  | `https://cloud.langfuse.com` | Langfuse instance URL                  |
| `LANGFUSE_TRACING_ENABLED` | boolean | `true`                       | Enable/disable tracing globally        |
| `LANGFUSE_SAMPLE_RATE`     | number  | `1.0`                        | Sampling rate (0.0 to 1.0)             |
| `LANGFUSE_FLUSH_INTERVAL`  | number  | `1000`                       | Flush interval in milliseconds         |

### LangGraph Variables

| Variable                             | Type    | Default        | Description                                    |
| ------------------------------------ | ------- | -------------- | ---------------------------------------------- |
| `LANGGRAPH_ENABLED`                  | boolean | `true`         | Enable LangGraph workflows                     |
| `LANGGRAPH_USE_IDIOMATIC_SUPERVISOR` | boolean | `true`         | Use idiomatic supervisor implementation        |
| `LANGGRAPH_MAX_EXECUTION_TIME`       | number  | `30000`        | Max workflow execution time (ms)               |
| `LANGGRAPH_ENABLE_DEBUG`             | boolean | `false`        | Enable debug logging                           |
| `LANGGRAPH_FALLBACK_ENABLED`         | boolean | `true`         | Enable fallback on failures                    |
| `LANGGRAPH_MAX_CONCURRENT_WORKFLOWS` | number  | `10`           | Max concurrent workflows                       |
| `LANGGRAPH_MAX_WORKFLOW_DURATION`    | number  | `60000`        | Max single workflow duration (ms)              |
| `LANGGRAPH_MAX_STEPS_PER_WORKFLOW`   | number  | `50`           | Max steps per workflow                         |
| `LANGGRAPH_ENABLE_PERSISTENCE`       | boolean | `true`         | Enable conversation persistence                |
| `LANGGRAPH_MAX_CONVERSATION_LENGTH`  | number  | `100`          | Max conversation history length                |
| `LANGGRAPH_OUTPUT_MODE`              | string  | `last_message` | Output mode (`last_message` or `full_history`) |

## Setup Instructions

### 1. Create Langfuse Account

1. Visit [https://cloud.langfuse.com](https://cloud.langfuse.com)
2. Sign up for an account
3. Create a new project
4. Copy the Public Key and Secret Key from the settings

### 2. Configure Environment File

1. Copy the relevant configuration for your environment
2. Replace placeholder values with your actual keys
3. Save as `.env.local` for local development

### 3. Verify Configuration

```bash
# Check if Langfuse is properly configured
npm run test:langfuse

# Verify LangGraph configuration
npm run test:langgraph
```

## Best Practices

### Security

- **Never commit API keys to version control**
- Use different Langfuse projects for different environments
- Rotate API keys regularly
- Use environment-specific key prefixes (dev-, staging-, prod-)

### Cost Optimization

- **Development**: Use `LANGFUSE_SAMPLE_RATE=1.0` for full visibility
- **Staging**: Use `LANGFUSE_SAMPLE_RATE=0.5` for cost balance
- **Production**: Use `LANGFUSE_SAMPLE_RATE=0.1` for cost efficiency
- Monitor usage in Langfuse dashboard

### Performance

- Set appropriate `LANGGRAPH_MAX_CONCURRENT_WORKFLOWS` based on your infrastructure
- Adjust `LANGFUSE_FLUSH_INTERVAL` based on latency requirements
- Use `LANGGRAPH_ENABLE_DEBUG=false` in production

### Monitoring

- Enable `LANGFUSE_TRACING_ENABLED=true` in all environments except testing
- Use appropriate log levels for each environment
- Monitor trace volume and costs in Langfuse dashboard

## Troubleshooting

### Common Issues

1. **Langfuse traces not appearing**

   - Verify API keys are correct
   - Check `LANGFUSE_TRACING_ENABLED=true`
   - Ensure network connectivity to Langfuse

2. **LangGraph workflows failing**

   - Check `OPENAI_API_KEY` is valid
   - Verify `LANGGRAPH_ENABLED=true`
   - Review debug logs if `LANGGRAPH_ENABLE_DEBUG=true`

3. **Performance issues**
   - Reduce `LANGFUSE_SAMPLE_RATE`
   - Increase `LANGGRAPH_MAX_CONCURRENT_WORKFLOWS`
   - Optimize `LANGGRAPH_MAX_EXECUTION_TIME`

### Debug Commands

```bash
# Test Langfuse connection
curl -X GET "${LANGFUSE_BASEURL}/api/public/health" \
  -H "Authorization: Bearer ${LANGFUSE_PUBLIC_KEY}"

# Verify environment variables
npm run config:verify

# Run integration tests
npm run test:integration
```

## Migration Guide

### From No Observability

1. Add Langfuse environment variables
2. Deploy with `LANGFUSE_SAMPLE_RATE=0.1` initially
3. Monitor traces and gradually increase sampling

### From Other Observability Tools

1. Configure Langfuse alongside existing tools
2. Compare trace data for validation
3. Gradually migrate dashboards and alerts
4. Remove old observability configuration

## Support

For configuration issues:

1. Check the [Langfuse Documentation](https://langfuse.com/docs)
2. Review the [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
3. Consult the project's troubleshooting guide
4. Contact the development team with specific error messages
