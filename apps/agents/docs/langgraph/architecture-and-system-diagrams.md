# LangGraph System Visualization with Langfuse Observability

## High-Level Flow with Observability

```mermaid
flowchart TD
  U["User / Client"] -->|"HTTPS Request"| API["askinfosec-api<br/>/api/v1"]
  API -->|"Invoke agent"| AskAIV2["AskAIAgentV2<br/>🔍 Creates TRACE"]
  AskAIV2 -->|"Orchestrate + traceId"| LGI["LangGraphIntegration<br/>🔍 Creates WORKFLOW SPAN"]
  LGI -->|"executeWorkflow() + span"| Supervisor["IdiomaticLangGraphSupervisor<br/>🔍 Creates SUPERVISOR SPAN"]

  subgraph "LangGraph Supervisor with Observability"
    Supervisor --> SearchExpert["search_expert<br/>(SearchAgent)<br/>🔍 Future: Expert Span"]
    Supervisor --> AnalysisExpert["analysis_expert<br/>(AnalysisAgent)<br/>🔍 Future: Expert Span"]
    Supervisor --> ResponseExpert["response_expert<br/>(ChatAgent)<br/>🔍 Future: Expert Span"]
  end

  SearchExpert -->|"Vector search"| VectorDB["Embeddings &<br/>SemanticSearchService"]
  AnalysisExpert -->|"Doc insights"| Analyzer["DocumentAnalyzerService"]
  ResponseExpert -->|"LLM"| OpenAI["OpenAI / LLM<br/>🔍 Future: Generation Tracking"]

  subgraph "Langfuse Cloud"
    LF["Langfuse Platform<br/>📊 Traces, Spans, Analytics"]
  end

  AskAIV2 -.->|"Async trace events"| LF
  LGI -.->|"Async span events"| LF
  Supervisor -.->|"Async span events"| LF

  classDef infra fill:#f9f,stroke:#333,stroke-width:1px,color:#000;
  classDef observability fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000;

  VectorDB:::infra
  OpenAI:::infra
  LF:::observability
```

## Component Architecture with Observability

```mermaid
classDiagram
    class AskAIAgentV2 {
      +invoke()
      +doInvoke() : Creates Langfuse TRACE
      +onDestroy() : Shuts down Langfuse
      +memory
      +langGraphIntegration : LangGraphIntegration
    }

    class LangGraphIntegration {
      +executeWorkflow() : Creates workflow SPAN
      +initializeSupervisor()
      +resourceManager : WorkflowResourceManager
      +idiomaticSupervisor : IdiomaticLangGraphSupervisor
    }

    class IdiomaticLangGraphSupervisor {
      +executeWorkflow() : Creates supervisor SPAN
      +searchAgentFactory
      +analysisAgentFactory
      +chatAgentFactory
    }

    class LangfuseClient {
      +getInstance() : Langfuse | null
      +trace() : Creates trace
      +span() : Creates span
      +shutdownAsync() : Cleanup
    }

    class WorkflowResourceManager
    class SearchAgent
    class AnalysisAgent
    class ChatAgent
    class SemanticSearchService
    class DocumentAnalyzerService
    class EmbeddingService

    AskAIAgentV2 --> LangGraphIntegration
    AskAIAgentV2 --> LangfuseClient
    LangGraphIntegration --> WorkflowResourceManager
    LangGraphIntegration --> IdiomaticLangGraphSupervisor
    LangGraphIntegration --> LangfuseClient
    IdiomaticLangGraphSupervisor --> SearchAgent
    IdiomaticLangGraphSupervisor --> AnalysisAgent
    IdiomaticLangGraphSupervisor --> ChatAgent
    IdiomaticLangGraphSupervisor --> LangfuseClient
    SearchAgent --> SemanticSearchService
    SemanticSearchService --> EmbeddingService
    AnalysisAgent --> DocumentAnalyzerService

    class LangfuseCloud {
      <<external>>
      +Trace Storage
      +Analytics Dashboard
      +Cost Tracking
    }

    LangfuseClient --> LangfuseCloud : sends traces
```

## Request Sequence with Observability

```mermaid
sequenceDiagram
  participant User as "User"
  participant API as "askinfosec-api"
  participant Agent as "AskAIAgentV2"
  participant LF as "LangfuseClient"
  participant LG as "LangGraphIntegration"
  participant Sup as "IdiomaticSupervisor"
  participant SR as "search_expert"
  participant AN as "analysis_expert"
  participant RE as "response_expert"
  participant DB as "Vector DB"
  participant OpenAI as "OpenAI LLM"
  participant Cloud as "Langfuse Cloud"

  User->>API: HTTPS POST /ask
  API->>Agent: invoke()

  rect rgb(225, 245, 254)
    Note over Agent,LF: Observability: Trace Creation
    Agent->>LF: trace.create('ask-ai-v2')
    LF-->>Agent: traceId
  end

  Agent->>LG: executeWorkflow(input + traceId)

  rect rgb(225, 245, 254)
    Note over LG,LF: Observability: Workflow Span
    LG->>LF: span.create('langgraph_workflow')
    LF-->>LG: workflowSpan
  end

  LG->>Sup: invoke(initialState + span)

  rect rgb(225, 245, 254)
    Note over Sup,LF: Observability: Supervisor Span
    Sup->>LF: span.create('idiomatic_supervisor')
    LF-->>Sup: supervisorSpan
  end

  Sup->>SR: Delegate if no docs
  SR->>DB: semanticSearch()
  SR-->>Sup: searchResults

  Sup->>AN: Delegate when results present
  AN->>OpenAI: analyzeDocuments()
  OpenAI-->>AN: analysis
  AN-->>Sup: analysisSummary

  Sup->>RE: Final response
  RE->>OpenAI: generateAnswer()
  OpenAI-->>RE: aiMessage
  RE-->>Sup: aiMessage

  rect rgb(225, 245, 254)
    Note over Sup,Cloud: Observability: Span Completion
    Sup->>LF: supervisorSpan.end(metadata)
    LF->>Cloud: Async span data
  end

  Sup-->>LG: {messages, metadata}

  rect rgb(225, 245, 254)
    Note over LG,Cloud: Observability: Workflow Completion
    LG->>LF: workflowSpan.end(metrics)
    LF->>Cloud: Async span data
  end

  LG-->>Agent: WorkflowResult

  rect rgb(225, 245, 254)
    Note over Agent,Cloud: Observability: Trace Completion
    Agent->>LF: trace.end(output)
    LF->>Cloud: Async trace data
  end

  Agent-->>API: answer JSON
  API-->>User: 200 OK response
```

## Langfuse Observability Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        A[AskAIAgentV2]
        L[LangGraphIntegration]
        S[IdiomaticSupervisor]
        E[Expert Agents]
    end

    subgraph "Observability Layer"
        LC[LangfuseClient Singleton]
        T[Traces]
        SP[Spans]
        G[Generations - Future]
    end

    subgraph "Langfuse Cloud"
        D[Dashboard]
        AN[Analytics]
        C[Cost Tracking]
        P[Performance Metrics]
    end

    A -->|Creates| T
    L -->|Creates| SP
    S -->|Creates| SP
    E -->|Future: Creates| G

    T --> LC
    SP --> LC
    G --> LC

    LC -->|Async Batching| D
    LC -->|Async Batching| AN
    LC -->|Async Batching| C
    LC -->|Async Batching| P

    classDef app fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef obs fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef cloud fill:#e1f5fe,stroke:#01579b,stroke-width:2px

    class A,L,S,E app
    class LC,T,SP,G obs
    class D,AN,C,P cloud
```

## Environment Configuration

### Development Setup

```bash
# .env.development
LANGFUSE_PUBLIC_KEY="pk-lf-dev-abc123..."
LANGFUSE_SECRET_KEY="sk-lf-dev-xyz789..."
LANGFUSE_BASEURL="https://cloud.langfuse.com"
LANGFUSE_SAMPLE_RATE="1.0"
LANGFUSE_TRACING_ENABLED="true"
```

### Production Setup

```bash
# .env.production
LANGFUSE_PUBLIC_KEY="pk-lf-prod-abc123..."
LANGFUSE_SECRET_KEY="sk-lf-prod-xyz789..."
LANGFUSE_BASEURL="https://us.cloud.langfuse.com"
LANGFUSE_SAMPLE_RATE="0.1"  # 10% sampling
LANGFUSE_TRACING_ENABLED="true"
```

### Testing Setup

```bash
# .env.test
LANGFUSE_TRACING_ENABLED="false"  # Disable in tests
```
