# Langfuse Integration Plan (JS/TS)

> Goal: Replace the provisional <PERSON><PERSON><PERSON> observability path with **Langfuse** for full-stack tracing, cost analytics and quality metrics across LangGraph workflows, RAG pipelines and OpenAI calls.

---

## 1 Deep-Dive Research Summary

1. **SDK Options**  
   • `langfuse` – low-level JS/TS client (Node ≥ 16, Deno, Edge).  
   • `langfuse-langchain` – ready-made CallbackHandler for LangChain/LangGraph.  
   • `observeOpen<PERSON><PERSON>` helper in `langfuse` to wrap the official `openai` SDK (≥ v4.0.0).

2. **Core Concepts**  
   • _Trace_ = execution root (maps 1-to-1 to a LangGraph workflow or RAG request).  
   • _Span_ = timed unit of work (ideal for search/analysis/response expert steps).  
   • _Generation_ = span subtype for LLM calls (auto-captures token usage & cost).  
   • _Score_ = evaluation / user feedback attached to a trace or observation.

3. **Nesting Strategy**  
   We can mirror the existing agentic graph:
   Trace → spans (`search_expert`, `analysis_expert`, `response_expert`) → generations (OpenAI calls inside each agent).

4. **Batching & Flush**  
   Events are queued; call `langfuse.shutdownAsync()` on process shutdown or in tests. Env overrides: `LANGFUSE_FLUSH_INTERVAL`, `LANGFUSE_SAMPLE_RATE`, `LANGFUSE_TRACING_ENABLED`.

5. **Env Vars**

   ```dotenv
   LANGFUSE_PUBLIC_KEY="pk-lf-…"
   LANGFUSE_SECRET_KEY="sk-lf-…"
   LANGFUSE_BASEURL="https://cloud.langfuse.com" # or us.cloud.langfuse.com/self-host
   LANGFUSE_SAMPLE_RATE="1"          # 0–1, optional
   LANGFUSE_TRACING_ENABLED="true"   # optional switch
   ```

6. **Minimal Code Snippets**

   ```ts
   import { Langfuse } from 'langfuse';
   export const lf = new Langfuse();

   const trace = lf.trace({ name: 'langgraph_workflow', userId, sessionId });
   const span = trace.span({ name: 'search_expert' });
   span.generation({ name: 'chat-completion', model: 'gpt-4o', input: prompt });
   ```

---

## 2 Integration Surface in Current Codebase

| Layer         | Candidate File                                       | Hook                                                                              |
| ------------- | ---------------------------------------------------- | --------------------------------------------------------------------------------- |
| API           | `apps/api/src/server.ts`                             | add request-level `sessionId`, forward to agents                                  |
| Agent         | `AskAIAgentV2.doInvoke()`                            | create **trace** before LangGraph / RAG execution                                 |
| Orchestration | `LangGraphIntegration.executeWorkflow()`             | if trace present → pass to supervisor                                             |
| Supervisor    | `IdiomaticLangGraphSupervisor`                       | create **spans** per expert step; wrap ChatOpenAI with Langfuse LangChain handler |
| OpenAI calls  | `sub-agents/openai/chat-agent`                       | wrap with `observeOpenAI`                                                         |
| Shutdown      | `LangGraphIntegration.destroy()` & Agent `onDestroy` | `lf.shutdownAsync()`                                                              |

---

## 3 Step-by-Step Implementation

### Phase 0 Dependencies & Bootstrap ✅ COMPLETED

1. ✅ `npm i langfuse langfuse-langchain` inside `apps/agents` workspace.
2. ✅ Create `observability/langfuse.ts` exporting a singleton client with env checks.

### Phase 1 Trace Creation at Agent Entry ✅ COMPLETED

1. ✅ Inside `AskAIAgentV2.doInvoke()`:
   • Import `lf` client.  
   • Before any processing: `const trace = lf.trace({ name: 'ask-ai-v2', sessionId, userId });`  
   • Store `trace.id` in `workflowInput` so deeper layers can nest spans.

### Phase 2 Resource-Managed Workflow Span ✅ COMPLETED

1. ✅ Modify `LangGraphIntegration.executeWorkflow()` to accept optional `parentTraceId`.
2. ✅ Wrap the entire workflow call in a **span**:
   ```ts
   const span = lf.span({ traceId: parentTraceId, name: 'langgraph_workflow' });
   try {
     /* run workflow */
   } finally {
     span.end();
   }
   ```

### Phase 3 Supervisor-Level Instrumentation ✅ COMPLETED

1. ✅ In `IdiomaticLangGraphSupervisor` constructor, accept optional `rootSpan`.
2. ✅ During router decision, before invoking each expert runnable, create a span child:  
   `const stepSpan = rootSpan.span({ name: agentName });`  
   Ensure `stepSpan.end()` after runnable resolves.

### Phase 4 Automatic LLM Generation Capture 🔄 IN PROGRESS

1. ⏳ Update `sub-agents/openai/chat-agent`:
   • Replace `new OpenAI()` with `observeOpenAI(new OpenAI(), { parent: currentSpan })`.
2. ⏳ For LangChain agents inside supervisor, attach `new CallbackHandler({ root: stepSpan })` to `invoke()` options.

### Phase 5 Flush & Shutdown ✅ COMPLETED

1. ✅ Extend existing shutdown flows (`LangGraphIntegration.destroy`, Agent `onDestroy`) to call `await lf.shutdownAsync();`
2. ✅ Add Jest/global test teardown to flush events.

### Phase 6 Config & Docs ✅ COMPLETED

1. ✅ Add `.env.example` entries for Langfuse keys + sample rate.
2. ✅ Update README & IMPLEMENTATION_SUMMARY with observability section.

### Phase 7 Optional Enhancements ⏳ PENDING

- ⏳ **Cost Analytics**: surface `generation.usage` in existing metrics service.
- ⏳ **Prompt Management**: link generations to managed prompts when we migrate to Langfuse's prompt store.
- ⏳ **User Feedback**: pipe UI thumb-up/down to `lf.score()`.

---

## IMPLEMENTATION STATUS (Current Session)

### ✅ COMPLETED FEATURES:

1. **Langfuse Bootstrap Client** (`src/observability/langfuse.ts`)

   - Singleton pattern with environment variable configuration
   - Proper error handling and fallback to null when not configured
   - Support for all Langfuse environment variables (keys, baseURL, sample rate, etc.)

2. **Agent-Level Trace Creation** (`src/agents/ask-ai-v2/index.ts`)

   - Integrated trace creation at the beginning of `doInvoke()` method
   - Automatic trace completion with success/error metadata
   - Proper error handling to prevent trace failures from affecting agent execution

3. **LangGraph Integration Span** (`src/integration/langgraph/langgraph-integration.ts`)

   - Created workflow-level spans for the entire LangGraph execution
   - Proper span completion with execution metrics and error handling
   - Resource management integration

4. **Supervisor-Level Spans** (`src/orchestration/langgraph/idiomatic-supervisor.ts`)

   - Added supervisor-level span creation for workflow orchestration
   - Integrated with the existing resource management system
   - Comprehensive metadata collection (execution time, documents processed, etc.)

5. **Clean Shutdown**

   - Added Langfuse client shutdown to the agent's `onDestroy()` method
   - Ensures proper flushing of traces before application shutdown

6. **Type Safety & Integration**
   - Updated `WorkflowInput` interface to include optional `traceId`
   - Fixed all TypeScript compilation issues
   - Proper error handling throughout the integration

### ✅ BUILD STATUS:

- All TypeScript compilation completed without errors
- No linting errors remaining
- Integration properly handles missing environment variables (graceful degradation)
- Comprehensive documentation and configuration guides created

### 🎯 NEXT STEPS:

1. **Phase 4**: Add LLM-level generation capture with `observeOpenAI`
2. **Phase 7**: Implement cost analytics and user feedback integration
3. **Production Deployment**: Deploy with proper Langfuse configuration

---

## 4 Roll-Out Strategy

1. **Dev → Staging** with `SAMPLE_RATE=0.2` to limit noise.
2. Verify traces render correctly (nested spans, token usage).
3. Enable in production behind `LANGFUSE_TRACING_ENABLED` feature flag.
4. Remove legacy metrics once parity confirmed.

---

## 5 Risk & Mitigation

| Risk                                | Mitigation                                                |
| ----------------------------------- | --------------------------------------------------------- |
| Extra latency from network batches  | SDK batches async; keep default `flushInterval`.          |
| Secret key leakage in client bundle | Integration only in Node back-end; never ship to browser. |
| High volume costs                   | Use `sampleRate`, bulk export & retention controls.       |
| Test flakiness                      | Mock Langfuse or disable via env in test scripts.         |

---

## 6 Timeline (2-week Sprint)

1. **Day 1-2** Dependencies, bootstrap client.
2. **Day 3-4** Agent & LangGraph integration wiring.
3. **Day 5-6** Supervisor & OpenAI instrumentation.
4. **Day 7** Shutdown logic + env config.
5. **Day 8-9** Unit & integration tests, load test.
6. **Day 10** Docs, PR review, deploy to staging.

---

_Prepared by: Engineering – June 2025_
