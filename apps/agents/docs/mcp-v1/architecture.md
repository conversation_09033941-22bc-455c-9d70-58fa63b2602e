# System Architecture

This document provides a comprehensive overview of the AI Agents system architecture, design patterns, and component interactions.

## Architecture Overview

The AI Agents system follows a layered architecture with clean separation of concerns:

```mermaid
graph TB
    subgraph "API Layer"
        API[Fastify API Server]
        AUTH[JWT Authentication]
        ROUTES[Agent Routes]
    end

    subgraph "Agent Layer"
        FACTORY[Agent Factory]
        ASKAI[AskAI Agent]
        ECHO[Echo Agent]
        CHAT[OpenAI Chat Agent]
    end

    subgraph "Service Layer"
        DOC[Document Service]
        EMB[Embedding Service]
        SEARCH[Semantic Search Service]
        PROMPT[Prompt Builder Service]
        METRICS[Metrics Service]
    end

    subgraph "Integration Layer"
        MCP_MGR[MCP Connection Manager]
        MCP_BRIDGE[MCP Bridge]
        ADAPTERS[Infrastructure Adapters]
    end

    subgraph "Tool Layer"
        REGISTRY[Tool Registry]
        DB_TOOLS[Database Tools]
        DOC_TOOLS[Document Tools]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        REDIS[(Redis)]
        OPENAI[OpenAI API]
        FILES[File Storage]
    end

    API --> FACTORY
    FACTORY --> ASKAI
    FACTORY --> ECHO
    FACTORY --> CHAT

    ASKAI --> DOC
    ASKAI --> EMB
    ASKAI --> <PERSON>ARCH
    ASKAI --> PROMPT
    ASKAI --> METRICS

    ASKAI --> MCP_MGR
    MCP_MGR --> MCP_BRIDGE
    MCP_BRIDGE --> ADAPTERS
    ADAPTERS --> REGISTRY
    REGISTRY --> DB_TOOLS
    REGISTRY --> DOC_TOOLS

    DB_TOOLS --> DB
    DOC_TOOLS --> DB
    EMB --> OPENAI
    CHAT --> OPENAI
    DOC --> REDIS

    style ASKAI fill:#e1f5fe
    style FACTORY fill:#f3e5f5
    style REGISTRY fill:#e8f5e8
    style DB fill:#fce4ec
```

## Core Components

### 1. Agent Framework

#### Agent Factory

**Purpose**: Central factory for agent creation and registration

```typescript
// Location: apps/agents/src/base/agent-factory.ts
export class AgentFactory {
  private static registry = new Map<string, AgentRegistryEntry>();

  static register(type: string, factory: AgentFactoryFunction): void;
  static create<T extends BaseAgent>(type: string, config: AgentConfig): Promise<T>;
  static getRegisteredTypes(): string[];
}
```

**Key Features**:

- Type-safe agent registration
- Dependency injection support
- Batch agent creation
- Metadata tracking

#### Abstract Agent Base Class

**Purpose**: Common functionality for all agents

```typescript
// Location: apps/agents/src/base/agent.ts
export abstract class AbstractAgent implements BaseAgent {
  // Lifecycle management
  async initialize(config: AgentConfig): Promise<void>;
  async destroy(): Promise<void>;

  // Core execution
  async invoke(input: AgentInput): Promise<AgentResult>;
  async *stream(input: AgentInput): AsyncGenerator<AgentChunk>;

  // Health and metrics
  async healthCheck(): Promise<HealthStatus>;
  getMetrics(): AgentMetrics;
}
```

**Built-in Features**:

- Streaming support
- Metrics collection
- Health monitoring
- Error handling
- Memory management

### 2. AskAI Agent Architecture

The AskAI agent implements a modular RAG (Retrieval-Augmented Generation) pipeline:

```mermaid
sequenceDiagram
    participant User
    participant AskAI as AskAI Agent
    participant DocSvc as Document Service
    participant EmbSvc as Embedding Service
    participant SearchSvc as Semantic Search
    participant PromptSvc as Prompt Builder
    participant ChatAgent as OpenAI Chat Agent
    participant MCP as MCP Tools

    User->>AskAI: invoke(query)
    AskAI->>DocSvc: retrieveAllDocuments()
    DocSvc->>MCP: get_all_documents
    MCP-->>DocSvc: documents
    DocSvc-->>AskAI: processed documents

    AskAI->>SearchSvc: findRelevantDocs()
    SearchSvc->>EmbSvc: generateQueryEmbedding()
    SearchSvc->>EmbSvc: getDocumentEmbeddings()
    SearchSvc-->>AskAI: ranked documents

    AskAI->>PromptSvc: buildRAGPrompt()
    PromptSvc-->>AskAI: enhanced prompt

    AskAI->>ChatAgent: invoke(prompt)
    ChatAgent-->>AskAI: response

    AskAI-->>User: final result
```

#### Service Components

**Document Service** (`apps/agents/src/agents/ask-ai/services/document.service.ts`)

- Document retrieval with MCP integration
- Intelligent caching (30-minute TTL)
- Content processing and extraction
- Binary file handling

**Embedding Service** (`apps/agents/src/agents/ask-ai/services/embedding.service.ts`)

- OpenAI embeddings generation
- Vector similarity calculations
- Embedding caching and persistence
- Batch processing optimization

**Semantic Search Service** (`apps/agents/src/agents/ask-ai/services/semantic-search.service.ts`)

- Document ranking by relevance
- Top-k selection with fallback
- Similarity scoring and filtering

**Prompt Builder Service** (`apps/agents/src/agents/ask-ai/services/prompt-builder.service.ts`)

- RAG prompt construction
- Token management and optimization
- Context formatting and truncation

**Metrics Service** (`apps/agents/src/agents/ask-ai/services/metrics.service.ts`)

- Performance tracking
- RAG-specific metrics
- Success rate monitoring

### 3. MCP Integration Layer

The MCP integration provides a clean separation between agent logic and tool implementations:

#### Connection Manager

**Purpose**: Session pooling and MCP bridge coordination

```typescript
// Location: apps/agents/src/integration/mcp-server/connection-manager.ts
export class MCPConnectionManager {
  // Session management
  async getOrCreateSession(organizationId: string, userId?: string): Promise<SessionInfo>;

  // Tool execution
  async executeToolCall(
    organizationId: string,
    toolName: string,
    args: any
  ): Promise<AgentMCPResult>;

  // Health monitoring
  async cleanupExpiredSessions(): Promise<number>;
  getMetrics(): ConnectionMetrics;
}
```

**Features**:

- Session pooling with TTL
- Health monitoring
- Connection metrics
- Automatic cleanup

#### MCP Bridge

**Purpose**: JSON-RPC communication with tool layer

```typescript
// Location: apps/agents/src/integration/mcp-server/mcp-bridge.ts
export class AgentMCPBridge {
  async callToolDirect(sessionId: string, toolName: string, args: any): Promise<AgentMCPResult>;
  async createAgentSession(organizationId: string, userId?: string): Promise<MCPSession>;
  async cleanupExpiredSessions(): Promise<number>;
}
```

### 4. Tool Architecture

Tools are consolidated in the `@anter/mcp-tools` package:

```mermaid
graph LR
    subgraph "Tool Package (@anter/mcp-tools)"
        CORE[Core Interfaces]
        REGISTRY[Tool Registry]
        ABSTRACT[Abstract Base Classes]
        TOOLS[Concrete Tools]
    end

    subgraph "Agent Package (@anter/agents)"
        PROVIDERS[Infrastructure Providers]
        ADAPTERS[Database/Session Adapters]
        CONNECTION[Connection Manager]
    end

    CONNECTION --> PROVIDERS
    PROVIDERS --> ADAPTERS
    ADAPTERS --> REGISTRY
    REGISTRY --> ABSTRACT
    ABSTRACT --> TOOLS

    style CORE fill:#e8f5e8
    style PROVIDERS fill:#fff3e0
```

#### Tool Registry

**Purpose**: Centralized tool discovery and management

```typescript
// Location: packages/mcp-tools/src/core/tool-registry.ts
export class DefaultMCPToolRegistry implements MCPToolRegistry {
  register(tool: MCPTool): void;
  getTool(name: string): MCPTool | undefined;
  listTools(): MCPToolInfo[];
  execute(name: string, params: any, context: ToolExecutionContext): Promise<any>;
}
```

#### Infrastructure Adapters

**Purpose**: Bridge between tool logic and infrastructure

```typescript
// Database Provider
export class AgentsDatabaseProvider implements DatabaseProvider {
  async executeQuery(query: string, organizationId: string): Promise<QueryResult>;
  async validateQuery(query: string): Promise<boolean>;
}

// Session Provider
export class AgentsSessionProvider implements SessionProvider {
  async getOrganizationId(sessionId: string): Promise<string | null>;
  async getUserId(sessionId: string): Promise<string | null>;
}
```

## Data Flow Patterns

### 1. Agent Invocation Flow

```mermaid
sequenceDiagram
    participant API as Fastify API
    participant Factory as Agent Factory
    participant Agent as AskAI Agent
    participant MCP as MCP Manager
    participant Tools as MCP Tools
    participant DB as Database

    API->>Factory: create('ask-ai')
    Factory-->>API: agent instance

    API->>Agent: invoke(input, context)
    Agent->>Agent: extractUserInput()
    Agent->>Agent: retrieveAllDocuments()
    Agent->>MCP: executeToolCall('get_all_documents')
    MCP->>Tools: JSON-RPC call
    Tools->>DB: SQL query with RLS
    DB-->>Tools: results
    Tools-->>MCP: JSON-RPC response
    MCP-->>Agent: processed documents

    Agent->>Agent: findRelevantDocs()
    Agent->>Agent: buildRAGPrompt()
    Agent->>Agent: callChatAgent()
    Agent-->>API: final result
```

### 2. Document Processing Flow

```mermaid
graph TD
    START[Document Request] --> CACHE{Check Cache}
    CACHE -->|Hit| RETURN[Return Cached]
    CACHE -->|Miss| RETRIEVE[MCP Tool Call]
    RETRIEVE --> PROCESS[Process Content]
    PROCESS --> BINARY{Binary File?}
    BINARY -->|Yes| EXTRACT[Text Extraction]
    BINARY -->|No| SANITIZE[Content Sanitization]
    EXTRACT --> SANITIZE
    SANITIZE --> STORE[Cache Results]
    STORE --> RETURN

    style CACHE fill:#e1f5fe
    style EXTRACT fill:#fff3e0
    style SANITIZE fill:#f3e5f5
```

### 3. Semantic Search Flow

```mermaid
graph TD
    QUERY[User Query] --> EMBED_Q[Generate Query Embedding]
    DOCS[Retrieved Documents] --> EMBED_D[Generate Document Embeddings]
    EMBED_Q --> SIMILARITY[Calculate Similarity Scores]
    EMBED_D --> SIMILARITY
    SIMILARITY --> RANK[Rank by Relevance]
    RANK --> TOPK[Select Top-K Documents]
    TOPK --> CONTEXT[Build Context]

    style EMBED_Q fill:#e1f5fe
    style EMBED_D fill:#e1f5fe
    style SIMILARITY fill:#f3e5f5
```

## Security Architecture

### 1. Multi-Tenant Isolation

```mermaid
graph TB
    REQUEST[Incoming Request] --> JWT[JWT Validation]
    JWT --> ORG[Extract Organization ID]
    ORG --> SESSION[Create MCP Session]
    SESSION --> CONTEXT[Set DB Context]
    CONTEXT --> RLS[Apply Row Level Security]
    RLS --> QUERY[Execute Query]

    subgraph "Database Layer"
        QUERY --> FILTER[Filter by Organization]
        FILTER --> RESULTS[Tenant-Isolated Results]
    end

    style JWT fill:#fce4ec
    style RLS fill:#fce4ec
    style FILTER fill:#fce4ec
```

### 2. Input Validation Pipeline

```mermaid
graph LR
    INPUT[User Input] --> SANITIZE[Sanitization]
    SANITIZE --> SQL_CHECK[SQL Injection Check]
    SQL_CHECK --> XSS_CHECK[XSS Prevention]
    XSS_CHECK --> CONTEXT_VAL[Context Validation]
    CONTEXT_VAL --> SAFE[Safe for Processing]

    SQL_CHECK -->|Blocked| REJECT[Reject Request]
    XSS_CHECK -->|Blocked| REJECT
    CONTEXT_VAL -->|Invalid| REJECT

    style SANITIZE fill:#e8f5e8
    style SQL_CHECK fill:#fce4ec
    style XSS_CHECK fill:#fce4ec
```

## Performance Optimizations

### 1. Caching Strategy

**Document Cache**

- 30-minute TTL for organizational documents
- Memory-based with automatic cleanup
- Size limits and eviction policies

**Embedding Cache**

- Persistent caching of document embeddings
- Organization-specific cache keys
- TTL-based expiration with cleanup

**Connection Pooling**

- MCP session pooling per organization
- Configurable pool sizes and TTLs
- Health monitoring and cleanup

### 2. Memory Management

```mermaid
graph TB
    subgraph "Memory Hierarchy"
        L1[L1: Process Cache<br/>Microseconds]
        L2[L2: Redis Cache<br/>Milliseconds]
        L3[L3: Vector Store<br/>Seconds]
        L4[L4: Cold Storage<br/>Minutes]
    end

    REQUEST[Request] --> L1
    L1 -->|Miss| L2
    L2 -->|Miss| L3
    L3 -->|Miss| L4
    L4 -->|Miss| SOURCE[Data Source]

    style L1 fill:#e1f5fe
    style L2 fill:#f3e5f5
    style L3 fill:#fff3e0
    style L4 fill:#fce4ec
```

## Error Handling Strategy

### 1. Error Propagation

```mermaid
graph TD
    ERROR[Error Occurs] --> CLASSIFY[Classify Error Type]
    CLASSIFY --> RETRYABLE{Retryable?}
    RETRYABLE -->|Yes| RETRY[Apply Retry Logic]
    RETRYABLE -->|No| LOG[Log Error]
    RETRY --> SUCCESS{Success?}
    SUCCESS -->|Yes| CONTINUE[Continue Processing]
    SUCCESS -->|No| BACKOFF[Exponential Backoff]
    BACKOFF --> RETRY
    LOG --> FALLBACK[Apply Fallback]
    FALLBACK --> GRACEFUL[Graceful Degradation]

    style ERROR fill:#fce4ec
    style RETRY fill:#fff3e0
    style FALLBACK fill:#e8f5e8
```

### 2. Circuit Breaker Pattern

The system implements circuit breakers for external dependencies:

- **OpenAI API**: Circuit breaker with fallback to cached responses
- **Database**: Connection pool with health checks
- **MCP Tools**: Timeout handling with graceful degradation

## Observability

### 1. Metrics Collection

```typescript
// Agent-level metrics
interface AgentMetrics {
  invocations: number;
  errors: number;
  avgLatency: number;
  p95Latency: number;
  tokensUsed: number;
  cost: number;
}

// RAG-specific metrics
interface RAGMetrics {
  documentsRetrieved: number;
  semanticMatches: number;
  embeddingCalls: number;
  contextTokens: number;
}
```

### 2. Logging Strategy

- **Structured Logging**: JSON format with correlation IDs
- **Privacy Protection**: PII filtering and sensitive data masking
- **Performance Tracking**: Request tracing with timing information
- **Error Context**: Detailed error information with stack traces

## Deployment Architecture

### 1. Package Structure

```
@anter/agents/
├── src/
│   ├── agents/          # Agent implementations
│   ├── base/            # Core framework
│   ├── integration/     # MCP integration
│   ├── observability/   # Metrics and logging
│   └── validation/      # Security validation

@anter/mcp-tools/
├── src/
│   ├── core/           # Tool interfaces
│   ├── tools/          # Tool implementations
│   ├── utils/          # Utility functions
│   └── types/          # Type definitions
```

### 2. Dependencies

**Core Dependencies**:

- `@langchain/core`: LangChain framework
- `@langchain/openai`: OpenAI integration
- `@modelcontextprotocol/sdk`: MCP protocol support
- `zod`: Schema validation

**Tool Dependencies**:

- `pdf-parse`: PDF text extraction
- `mammoth`: DOCX text extraction
- `xlsx`: Excel spreadsheet processing

## Best Practices

### 1. Agent Development

- Extend `AbstractAgent` for common functionality
- Implement proper error handling and fallbacks
- Use dependency injection for testability
- Follow the service layer pattern for complex agents

### 2. Tool Development

- Implement the `MCPTool` interface
- Use dependency injection for infrastructure
- Validate inputs and sanitize outputs
- Include comprehensive error handling

### 3. Security

- Always validate user inputs
- Implement tenant isolation at the database level
- Use secure session management
- Regular security audits and testing

### 4. Performance

- Implement intelligent caching strategies
- Use connection pooling for external resources
- Monitor and optimize database queries
- Implement streaming for long-running operations

## Future Enhancements

### 1. Planned Features

- Multi-agent orchestration
- Advanced workflow engine
- Real-time collaboration
- Enhanced security features

### 2. Scalability Improvements

- Horizontal scaling support
- Advanced caching strategies
- Database sharding
- Edge deployment support

This architecture provides a solid foundation for enterprise AI applications with proper separation of concerns, security, and performance optimization.
