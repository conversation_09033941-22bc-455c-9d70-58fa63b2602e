# @anter/agents

Enterprise-grade AI Agent system with advanced RAG capabilities, MCP integration, and production-ready architecture.

## Overview

The `@anter/agents` package provides a sophisticated AI agent framework built on modern patterns with enterprise-grade security, observability, and performance. The system features a modular architecture with clean separation of concerns, comprehensive MCP (Model Context Protocol) integration, and specialized agents for various use cases.

## Key Features

- **🤖 Intelligent Agents**: RAG-enabled AskAI agent with semantic search and context-aware responses
- **🔧 MCP Integration**: Complete Model Context Protocol support with tool consolidation
- **🏗️ Modular Architecture**: Clean separation between agent logic, tools, and infrastructure
- **🔒 Enterprise Security**: Multi-tenant isolation, SQL injection prevention, input sanitization
- **📊 Observability**: Comprehensive metrics, logging, and performance monitoring
- **⚡ High Performance**: Connection pooling, caching, streaming responses
- **🧪 Production Ready**: 46+ tests, zero build errors, comprehensive error handling

## Quick Start

```typescript
import { initializeAgentSystem, AgentFactory } from '@anter/agents';

// Initialize the agent system
const agentSystem = await initializeAgentSystem(fastify);

// Create and use an agent
const askAI = AgentFactory.getAgent('ask-ai');
const result = await askAI.invoke({
  input: 'What are the security policies in our documents?',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});

console.log(result.output);
```

## Architecture Overview

```mermaid
graph TB
    subgraph "Agent Layer"
        A1[AskAI Agent<br/>RAG + Semantic Search]
        A2[Echo Agent<br/>Testing & Validation]
        A3[OpenAI Chat Agent<br/>LangChain Integration]
    end

    subgraph "Core Framework"
        AF[Agent Factory<br/>Registration & Creation]
        AM[Agent Memory<br/>Hierarchical Storage]
        AO[Observability<br/>Metrics & Logging]
    end

    subgraph "MCP Integration Layer"
        CM[Connection Manager<br/>Session Pooling]
        BR[MCP Bridge<br/>Tool Execution]
        TA[Tool Adapters<br/>Database & Session]
    end

    subgraph "Tool Layer (@anter/mcp-tools)"
        TR[Tool Registry<br/>Discovery & Management]
        DT[Database Tools<br/>Query & Documents]
        TH[Tool Handlers<br/>Execution & Validation]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL<br/>Multi-tenant)]
        RD[(Redis<br/>Caching)]
        AI[OpenAI API<br/>LangChain]
    end

    A1 --> AF
    A2 --> AF
    A3 --> AF
    AF --> AM
    AF --> AO
    A1 --> CM
    CM --> BR
    BR --> TA
    TA --> TR
    TR --> DT
    DT --> TH
    TH --> DB
    A1 --> AI
    AM --> RD

    style A1 fill:#e1f5fe
    style AF fill:#f3e5f5
    style CM fill:#fff3e0
    style TR fill:#e8f5e8
    style DB fill:#fce4ec
```

## Available Agents

### AskAI Agent (`ask-ai`)

**General-purpose AI agent with RAG capabilities**

- **🧠 Semantic Search**: OpenAI embeddings with similarity ranking
- **📄 Document Processing**: Full-text extraction from PDF, DOCX, TXT files
- **🔍 Database Queries**: Secure SQL execution with tenant isolation
- **💬 Conversational AI**: LangChain-powered chat responses
- **🎯 Context-Aware**: Organization-specific document context

```typescript
const result = await askAI.invoke({
  input: 'Find security vulnerabilities in our codebase',
  context: { organizationId: 'org-123' },
});
```

### Echo Agent (`echo`)

**Testing and validation agent**

- **🔄 Request Echo**: Returns formatted input for testing
- **📊 Metrics Testing**: Performance measurement capabilities
- **🌊 Streaming Support**: Real-time response demonstration

### OpenAI Chat Agent (`openai-chat-agent`)

**LangChain-based conversational AI**

- **💬 Natural Language**: Advanced conversational capabilities
- **⚙️ Configurable**: Custom instructions and behavior
- **🔒 Secure**: Input validation and error handling
- **🚀 Production Ready**: Built on mature LangChain framework

## MCP Tools Integration

The system leverages the **Model Context Protocol** for seamless tool integration:

### Tool Architecture

- **Lightweight Package**: `@anter/mcp-tools` contains pure tool logic
- **Dependency Injection**: Clean separation between tools and infrastructure
- **Type Safety**: Comprehensive TypeScript interfaces throughout
- **JSON-RPC Compliant**: Full MCP specification adherence

### Available Tools

- **Query Database**: Secure SQL execution with injection prevention
- **Get All Documents**: Document retrieval with filtering and pagination
- **Extensible**: Easy to add new tools without infrastructure changes

## Performance & Scalability

### Performance Metrics

- **Response Time**: P95 < 2s with streaming support
- **Concurrent Sessions**: 100+ concurrent MCP sessions
- **Database Performance**: Connection pooling with tenant isolation
- **Memory Efficiency**: Intelligent caching with TTL management

### Scalability Features

- **Session Pooling**: Efficient resource utilization
- **Document Caching**: 30-minute TTL with intelligent cleanup
- **Embedding Cache**: Persistent semantic embeddings
- **Streaming Responses**: Real-time user experience

## Security & Compliance

### Multi-Tenant Security

- **Row Level Security**: PostgreSQL RLS with organization isolation
- **Context Validation**: User and organization verification
- **Session Management**: Secure session lifecycle management

### Input Protection

- **SQL Injection Prevention**: Comprehensive query validation
- **XSS Protection**: Input sanitization and output filtering
- **Content Sanitization**: Binary content handling with text extraction

## Documentation

- **[Architecture Guide](./architecture.md)** - Complete system architecture and design patterns
- **[MCP Integration](./mcp-integration.md)** - MCP tools architecture and integration patterns
- **[Agents Guide](./agents-guide.md)** - Detailed agent capabilities and usage
- **[Developer Guide](./developer-guide.md)** - Creating custom agents and tools

## Development

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build

# Run tests
pnpm test

# Type checking
pnpm type-check
```

## Contributing

1. Follow existing patterns and TypeScript conventions
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Ensure security best practices
5. Add observability for new components

## License

ISC
