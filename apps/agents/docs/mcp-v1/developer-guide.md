# Developer Guide

This guide provides comprehensive instructions for creating custom agents, extending existing functionality, and integrating with the AI Agents system.

## Creating Custom Agents

### Basic Agent Structure

All agents must extend the `AbstractAgent` base class and implement the required methods:

```typescript
import { AbstractAgent, <PERSON>In<PERSON>, AgentR<PERSON>ult, Agent<PERSON>hunk } from '@anter/agents';

export class CustomAgent extends AbstractAgent {
  async invoke(input: AgentInput): Promise<AgentResult> {
    // Implementation here
  }

  async *stream(input: AgentInput): AsyncGenerator<AgentChunk> {
    // Streaming implementation here
  }

  async healthCheck(): Promise<HealthStatus> {
    // Health check implementation
  }
}
```

### Agent Registration

Register your agent with the factory system:

```typescript
import { AgentFactory } from '@anter/agents';

// Register the agent
AgentFactory.register('custom-agent', {
  create: async config => new CustomAgent(config),
  metadata: {
    name: 'Custom Agent',
    description: 'A custom agent for specific use cases',
    version: '0.0.1',
    capabilities: ['text-processing', 'analysis'],
  },
});
```

### Complete Example: Weather Agent

```typescript
import { AbstractAgent, <PERSON>In<PERSON>, AgentR<PERSON>ult, AgentChunk } from '@anter/agents';
import axios from 'axios';

interface WeatherAgentConfig {
  apiKey: string;
  defaultLocation: string;
  timeout: number;
}

export class WeatherAgent extends AbstractAgent {
  private apiKey: string;
  private defaultLocation: string;
  private timeout: number;

  constructor(config: WeatherAgentConfig) {
    super(config);
    this.apiKey = config.apiKey;
    this.defaultLocation = config.defaultLocation;
    this.timeout = config.timeout || 5000;
  }

  async invoke(input: AgentInput): Promise<AgentResult> {
    const startTime = Date.now();

    try {
      const userInput = this.extractUserInput(input);
      const location = this.extractLocation(userInput) || this.defaultLocation;

      // Fetch weather data
      const weatherData = await this.fetchWeatherData(location);

      // Format response
      const response = this.formatWeatherResponse(weatherData);

      return {
        success: true,
        output: response,
        metadata: {
          agentType: 'weather',
          location: location,
          processingTimeMs: Date.now() - startTime,
          dataSource: 'external-api',
        },
      };
    } catch (error) {
      return {
        success: false,
        output: `Failed to get weather information: ${error.message}`,
        metadata: {
          agentType: 'weather',
          error: error.message,
          processingTimeMs: Date.now() - startTime,
        },
      };
    }
  }

  async *stream(input: AgentInput): AsyncGenerator<AgentChunk> {
    const result = await this.invoke(input);

    // Stream the response word by word
    const words = result.output.split(' ');
    for (const word of words) {
      yield {
        type: 'text',
        content: word + ' ',
        metadata: {
          wordIndex: words.indexOf(word),
          totalWords: words.length,
        },
      };

      // Simulate streaming delay
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  private async fetchWeatherData(location: string): Promise<any> {
    const response = await axios.get(`https://api.weatherapi.com/v1/current.json`, {
      params: {
        key: this.apiKey,
        q: location,
        aqi: 'no',
      },
      timeout: this.timeout,
    });

    return response.data;
  }

  private formatWeatherResponse(data: any): string {
    const { location, current } = data;

    return `Current weather in ${location.name}, ${location.country}:
- Temperature: ${current.temp_c}°C (${current.temp_f}°F)
- Condition: ${current.condition.text}
- Humidity: ${current.humidity}%
- Wind: ${current.wind_kph} km/h ${current.wind_dir}
- Feels like: ${current.feelslike_c}°C`;
  }

  private extractLocation(input: string): string | null {
    // Simple regex to extract location from input
    const locationMatch = input.match(/(?:in|for|at)\s+([a-zA-Z\s,]+)/i);
    return locationMatch ? locationMatch[1].trim() : null;
  }

  async healthCheck(): Promise<HealthStatus> {
    try {
      // Test API connectivity
      await this.fetchWeatherData(this.defaultLocation);
      return { status: 'healthy', message: 'Weather API accessible' };
    } catch (error) {
      return { status: 'unhealthy', message: `Weather API error: ${error.message}` };
    }
  }
}
```

## Creating MCP Tools

### Basic Tool Structure

All MCP tools must implement the `MCPTool` interface:

```typescript
import { MCPTool, ToolExecutionContext } from '@anter/mcp-tools';

export class CustomTool implements MCPTool {
  readonly name = 'custom_tool';
  readonly description = 'A custom tool for specific operations';
  readonly inputSchema = {
    type: 'object',
    properties: {
      parameter1: { type: 'string', description: 'First parameter' },
      parameter2: { type: 'number', description: 'Second parameter' },
    },
    required: ['parameter1'],
  };

  async execute(params: any, context: ToolExecutionContext): Promise<any> {
    // Tool implementation here
  }
}
```

### Complete Example: Text Analysis Tool

```typescript
import { MCPTool, ToolExecutionContext } from '@anter/mcp-tools';

interface TextAnalysisParams {
  text: string;
  analysis_type: 'sentiment' | 'readability' | 'keywords';
}

export class TextAnalysisTool implements MCPTool {
  readonly name = 'analyze_text';
  readonly description = 'Analyze text for sentiment, readability, or extract keywords';
  readonly inputSchema = {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description: 'Text to analyze',
        minLength: 1,
        maxLength: 10000,
      },
      analysis_type: {
        type: 'string',
        enum: ['sentiment', 'readability', 'keywords'],
        description: 'Type of analysis to perform',
      },
    },
    required: ['text', 'analysis_type'],
  };

  async execute(params: TextAnalysisParams, context: ToolExecutionContext): Promise<any> {
    const startTime = Date.now();

    try {
      // Validate input
      this.validateInput(params);

      // Perform analysis based on type
      let result;
      switch (params.analysis_type) {
        case 'sentiment':
          result = await this.analyzeSentiment(params.text);
          break;
        case 'readability':
          result = await this.analyzeReadability(params.text);
          break;
        case 'keywords':
          result = await this.extractKeywords(params.text);
          break;
        default:
          throw new Error(`Unsupported analysis type: ${params.analysis_type}`);
      }

      return {
        success: true,
        data: {
          analysis_type: params.analysis_type,
          text_length: params.text.length,
          result: result,
          execution_time_ms: Date.now() - startTime,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          message: error.message,
          execution_time_ms: Date.now() - startTime,
        },
      };
    }
  }

  private validateInput(params: TextAnalysisParams): void {
    if (!params.text || params.text.trim().length === 0) {
      throw new Error('Text parameter is required and cannot be empty');
    }

    if (params.text.length > 10000) {
      throw new Error('Text is too long (max 10,000 characters)');
    }
  }

  private async analyzeSentiment(text: string): Promise<any> {
    // Simple sentiment analysis (in real implementation, use NLP library)
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];

    const words = text.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;

    let sentiment: 'positive' | 'negative' | 'neutral';
    let confidence: number;

    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      confidence = positiveCount / (positiveCount + negativeCount);
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative';
      confidence = negativeCount / (positiveCount + negativeCount);
    } else {
      sentiment = 'neutral';
      confidence = 0.5;
    }

    return {
      sentiment,
      confidence: Math.round(confidence * 100) / 100,
      positive_words_found: positiveCount,
      negative_words_found: negativeCount,
    };
  }

  private async analyzeReadability(text: string): Promise<any> {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0);

    // Flesch Reading Ease Score
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    const fleschScore = 206.835 - 1.015 * avgSentenceLength - 84.6 * avgSyllablesPerWord;

    let readabilityLevel: string;
    if (fleschScore >= 90) readabilityLevel = 'Very Easy';
    else if (fleschScore >= 80) readabilityLevel = 'Easy';
    else if (fleschScore >= 70) readabilityLevel = 'Fairly Easy';
    else if (fleschScore >= 60) readabilityLevel = 'Standard';
    else if (fleschScore >= 50) readabilityLevel = 'Fairly Difficult';
    else if (fleschScore >= 30) readabilityLevel = 'Difficult';
    else readabilityLevel = 'Very Difficult';

    return {
      flesch_score: Math.round(fleschScore * 100) / 100,
      readability_level: readabilityLevel,
      word_count: words.length,
      sentence_count: sentences.length,
      avg_sentence_length: Math.round(avgSentenceLength * 100) / 100,
      avg_syllables_per_word: Math.round(avgSyllablesPerWord * 100) / 100,
    };
  }

  private async extractKeywords(text: string): Promise<any> {
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);

    // Simple keyword extraction (count frequency)
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // Sort by frequency and take top 10
    const keywords = Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word, count]) => ({ word, frequency: count }));

    return {
      total_words: words.length,
      unique_words: wordCount.size,
      top_keywords: keywords,
    };
  }

  private countSyllables(word: string): number {
    // Simple syllable counting
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Handle edge cases
    if (word.endsWith('e')) count--;
    if (count === 0) count = 1;

    return count;
  }
}
```

### Tool Registration

Register your tool with the MCP registry:

```typescript
import { DefaultMCPToolRegistry } from '@anter/mcp-tools';

const registry = new DefaultMCPToolRegistry();
registry.register(new TextAnalysisTool());
```

## Agent Service Patterns

### Service-Based Architecture

For complex agents, organize functionality into services:

```typescript
// Base service class
abstract class BaseService {
  protected config: any;
  protected logger: Logger;

  constructor(config: any, logger: Logger) {
    this.config = config;
    this.logger = logger;
  }

  abstract initialize(): Promise<void>;
  abstract healthCheck(): Promise<HealthStatus>;
}

// Example: Email service
export class EmailService extends BaseService {
  private transporter: any;

  async initialize(): Promise<void> {
    // Initialize email transporter
    this.transporter = createTransporter(this.config.email);
  }

  async sendEmail(to: string, subject: string, body: string): Promise<boolean> {
    try {
      await this.transporter.sendMail({ to, subject, text: body });
      return true;
    } catch (error) {
      this.logger.error('Email sending failed:', error);
      return false;
    }
  }

  async healthCheck(): Promise<HealthStatus> {
    try {
      await this.transporter.verify();
      return { status: 'healthy', message: 'Email service ready' };
    } catch (error) {
      return { status: 'unhealthy', message: `Email service error: ${error.message}` };
    }
  }
}

// Agent using services
export class NotificationAgent extends AbstractAgent {
  private emailService: EmailService;
  private smsService: SMSService;

  constructor(config: NotificationAgentConfig) {
    super(config);
    this.emailService = new EmailService(config, this.logger);
    this.smsService = new SMSService(config, this.logger);
  }

  async initialize(): Promise<void> {
    await super.initialize();
    await Promise.all([this.emailService.initialize(), this.smsService.initialize()]);
  }

  async invoke(input: AgentInput): Promise<AgentResult> {
    const request = this.parseNotificationRequest(input);

    switch (request.type) {
      case 'email':
        return await this.sendEmailNotification(request);
      case 'sms':
        return await this.sendSMSNotification(request);
      default:
        return this.createErrorResult('Unsupported notification type');
    }
  }
}
```

### Dependency Injection Pattern

Use dependency injection for better testability:

```typescript
// Define interfaces
interface DatabaseProvider {
  query(sql: string, params: any[]): Promise<any>;
}

interface CacheProvider {
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
}

// Agent with dependency injection
export class DataAnalysisAgent extends AbstractAgent {
  constructor(
    config: DataAnalysisAgentConfig,
    private dbProvider: DatabaseProvider,
    private cacheProvider: CacheProvider
  ) {
    super(config);
  }

  async invoke(input: AgentInput): Promise<AgentResult> {
    const query = this.extractQuery(input);
    const cacheKey = this.generateCacheKey(query);

    // Check cache first
    let result = await this.cacheProvider.get(cacheKey);

    if (!result) {
      // Execute analysis
      result = await this.performAnalysis(query);

      // Cache result
      await this.cacheProvider.set(cacheKey, result, 3600); // 1 hour TTL
    }

    return {
      success: true,
      output: this.formatAnalysisResult(result),
      metadata: {
        cached: !!result,
        analysisType: query.type,
      },
    };
  }
}
```

## Testing Patterns

### Unit Testing

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WeatherAgent } from './weather-agent';

describe('WeatherAgent', () => {
  let agent: WeatherAgent;
  let mockAxios: any;

  beforeEach(() => {
    mockAxios = vi.mocked(axios);
    agent = new WeatherAgent({
      apiKey: 'test-key',
      defaultLocation: 'New York',
      timeout: 5000,
    });
  });

  it('should return weather information for valid location', async () => {
    // Mock API response
    mockAxios.get.mockResolvedValue({
      data: {
        location: { name: 'New York', country: 'USA' },
        current: {
          temp_c: 20,
          temp_f: 68,
          condition: { text: 'Sunny' },
          humidity: 60,
          wind_kph: 10,
          wind_dir: 'NW',
          feelslike_c: 22,
        },
      },
    });

    const result = await agent.invoke({
      input: 'What is the weather in New York?',
      context: { organizationId: 'test-org' },
    });

    expect(result.success).toBe(true);
    expect(result.output).toContain('New York');
    expect(result.output).toContain('20°C');
    expect(result.metadata.location).toBe('New York');
  });

  it('should handle API errors gracefully', async () => {
    mockAxios.get.mockRejectedValue(new Error('API Error'));

    const result = await agent.invoke({
      input: 'What is the weather?',
      context: { organizationId: 'test-org' },
    });

    expect(result.success).toBe(false);
    expect(result.output).toContain('Failed to get weather information');
  });
});
```

### Integration Testing

```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { AgentFactory } from '@anter/agents';

describe('Agent Integration Tests', () => {
  beforeAll(async () => {
    // Setup test environment
    await setupTestDatabase();
    await setupTestServices();
  });

  afterAll(async () => {
    // Cleanup
    await cleanupTestDatabase();
    await cleanupTestServices();
  });

  it('should handle end-to-end workflow', async () => {
    const agent = AgentFactory.getAgent('ask-ai');

    const result = await agent.invoke({
      input: 'What are our security policies?',
      context: {
        organizationId: 'test-org',
        userId: 'test-user',
      },
    });

    expect(result.success).toBe(true);
    expect(result.output).toBeDefined();
    expect(result.metadata).toBeDefined();
  });
});
```

### Mock Services

```typescript
// Mock MCP Connection Manager
export class MockMCPConnectionManager {
  private mockResponses = new Map<string, any>();

  setMockResponse(toolName: string, response: any): void {
    this.mockResponses.set(toolName, response);
  }

  async executeToolCall(organizationId: string, toolName: string, args: any): Promise<any> {
    const mockResponse = this.mockResponses.get(toolName);
    if (mockResponse) {
      return mockResponse;
    }

    return {
      success: true,
      data: { message: `Mock response for ${toolName}` },
    };
  }
}

// Use in tests
const mockManager = new MockMCPConnectionManager();
mockManager.setMockResponse('get_all_documents', {
  success: true,
  data: {
    documents: [{ id: '1', name: 'Test Document', content: 'Test content' }],
  },
});
```

## Performance Optimization

### Caching Strategies

```typescript
// Multi-level cache implementation
export class MultiLevelCache {
  private l1Cache = new Map<string, CacheEntry>(); // Memory cache
  private l2Cache: RedisCache; // Redis cache

  constructor(redisConfig: RedisConfig) {
    this.l2Cache = new RedisCache(redisConfig);
  }

  async get(key: string): Promise<any> {
    // Check L1 cache first
    const l1Entry = this.l1Cache.get(key);
    if (l1Entry && !this.isExpired(l1Entry)) {
      return l1Entry.value;
    }

    // Check L2 cache
    const l2Value = await this.l2Cache.get(key);
    if (l2Value) {
      // Populate L1 cache
      this.l1Cache.set(key, {
        value: l2Value,
        timestamp: Date.now(),
        ttl: 300000, // 5 minutes
      });
      return l2Value;
    }

    return null;
  }

  async set(key: string, value: any, ttl: number = 3600000): Promise<void> {
    // Set in both caches
    this.l1Cache.set(key, { value, timestamp: Date.now(), ttl: Math.min(ttl, 300000) });
    await this.l2Cache.set(key, value, ttl);
  }
}
```

### Connection Pooling

```typescript
// Database connection pool
export class DatabasePool {
  private pool: Pool;
  private metrics = {
    activeConnections: 0,
    totalQueries: 0,
    avgQueryTime: 0,
  };

  constructor(config: PoolConfig) {
    this.pool = createPool(config);
  }

  async query(sql: string, params: any[] = []): Promise<any> {
    const startTime = Date.now();
    this.metrics.activeConnections++;

    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(sql, params);
        return result;
      } finally {
        client.release();
      }
    } finally {
      this.metrics.activeConnections--;
      this.metrics.totalQueries++;

      const queryTime = Date.now() - startTime;
      this.metrics.avgQueryTime =
        (this.metrics.avgQueryTime * (this.metrics.totalQueries - 1) + queryTime) /
        this.metrics.totalQueries;
    }
  }

  getMetrics(): any {
    return { ...this.metrics };
  }
}
```

### Batch Processing

```typescript
// Batch processor for efficient operations
export class BatchProcessor<T, R> {
  private queue: T[] = [];
  private processing = false;
  private batchSize: number;
  private maxWaitTime: number;
  private processor: (items: T[]) => Promise<R[]>;

  constructor(
    processor: (items: T[]) => Promise<R[]>,
    batchSize: number = 10,
    maxWaitTime: number = 1000
  ) {
    this.processor = processor;
    this.batchSize = batchSize;
    this.maxWaitTime = maxWaitTime;
  }

  async add(item: T): Promise<R> {
    return new Promise((resolve, reject) => {
      this.queue.push({ item, resolve, reject });
      this.scheduleProcessing();
    });
  }

  private scheduleProcessing(): void {
    if (this.processing) return;

    if (this.queue.length >= this.batchSize) {
      this.processBatch();
    } else {
      setTimeout(() => {
        if (this.queue.length > 0) {
          this.processBatch();
        }
      }, this.maxWaitTime);
    }
  }

  private async processBatch(): Promise<void> {
    if (this.processing || this.queue.length === 0) return;

    this.processing = true;
    const batch = this.queue.splice(0, this.batchSize);

    try {
      const items = batch.map(b => b.item);
      const results = await this.processor(items);

      batch.forEach((b, index) => {
        b.resolve(results[index]);
      });
    } catch (error) {
      batch.forEach(b => b.reject(error));
    } finally {
      this.processing = false;

      // Process next batch if queue is not empty
      if (this.queue.length > 0) {
        this.scheduleProcessing();
      }
    }
  }
}
```

## Error Handling Patterns

### Circuit Breaker

```typescript
enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

export class CircuitBreaker {
  private state = CircuitState.CLOSED;
  private failureCount = 0;
  private lastFailureTime: number | null = null;
  private successCount = 0;

  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000,
    private successThreshold: number = 3
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;

    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= this.successThreshold) {
        this.state = CircuitState.CLOSED;
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.failureThreshold) {
      this.state = CircuitState.OPEN;
    }
  }

  private shouldAttemptReset(): boolean {
    return (
      this.lastFailureTime !== null && Date.now() - this.lastFailureTime >= this.recoveryTimeout
    );
  }
}
```

### Retry with Exponential Backoff

```typescript
export class RetryManager {
  static async retry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 30000,
    backoffFactor: number = 2
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);

        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.1 * delay;
        const finalDelay = delay + jitter;

        await new Promise(resolve => setTimeout(resolve, finalDelay));
      }
    }

    throw lastError!;
  }
}
```

## Configuration Management

### Environment-based Configuration

```typescript
export interface AgentConfig {
  // Core settings
  name: string;
  version: string;
  timeout: number;
  retryAttempts: number;

  // External services
  openai: {
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };

  database: {
    connectionString: string;
    poolSize: number;
    timeout: number;
  };

  cache: {
    provider: 'redis' | 'memory';
    ttl: number;
    maxSize?: number;
    redis?: {
      host: string;
      port: number;
      password?: string;
    };
  };

  // Monitoring
  metrics: {
    enabled: boolean;
    endpoint?: string;
    interval: number;
  };

  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    format: 'json' | 'text';
  };
}

export function loadConfig(): AgentConfig {
  return {
    name: process.env.AGENT_NAME || 'unknown',
    version: process.env.AGENT_VERSION || '1.0.0',
    timeout: parseInt(process.env.AGENT_TIMEOUT || '30000'),
    retryAttempts: parseInt(process.env.AGENT_RETRY_ATTEMPTS || '3'),

    openai: {
      apiKey: process.env.OPENAI_API_KEY || '',
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4000'),
    },

    database: {
      connectionString: process.env.DATABASE_URL || '',
      poolSize: parseInt(process.env.DATABASE_POOL_SIZE || '10'),
      timeout: parseInt(process.env.DATABASE_TIMEOUT || '30000'),
    },

    cache: {
      provider: (process.env.CACHE_PROVIDER as any) || 'memory',
      ttl: parseInt(process.env.CACHE_TTL || '3600000'),
      maxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
    },

    metrics: {
      enabled: process.env.METRICS_ENABLED === 'true',
      endpoint: process.env.METRICS_ENDPOINT,
      interval: parseInt(process.env.METRICS_INTERVAL || '60000'),
    },

    logging: {
      level: (process.env.LOG_LEVEL as any) || 'info',
      format: (process.env.LOG_FORMAT as any) || 'json',
    },
  };
}
```

## Deployment Best Practices

### Docker Configuration

```dockerfile
# Multi-stage build for agents
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S agents -u 1001

WORKDIR /app

# Copy dependencies and application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=agents:nodejs . .

# Set environment
ENV NODE_ENV=production
ENV AGENT_USER=agents

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node health-check.js

USER agents

EXPOSE 3000

CMD ["node", "dist/index.js"]
```

### Kubernetes Deployment

```yaml
# Agent deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-agents
  labels:
    app: ai-agents
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-agents
  template:
    metadata:
      labels:
        app: ai-agents
    spec:
      containers:
        - name: ai-agents
          image: askinfosec/ai-agents:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: 'production'
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: openai-secret
                  key: api-key
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-secret
                  key: connection-string
          resources:
            requests:
              memory: '256Mi'
              cpu: '250m'
            limits:
              memory: '512Mi'
              cpu: '500m'
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: ai-agents-service
spec:
  selector:
    app: ai-agents
  ports:
    - port: 80
      targetPort: 3000
  type: ClusterIP
```

This comprehensive developer guide provides all the patterns and examples needed to create robust, scalable AI agents and tools within the askinfosec ecosystem.
