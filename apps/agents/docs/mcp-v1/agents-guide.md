# Agents Guide

This document provides detailed information about available agents, their capabilities, usage patterns, and configuration options.

## Available Agents

### AskAI Agent (`ask-ai`)

The AskAI agent is a sophisticated RAG (Retrieval-Augmented Generation) system that combines semantic search, document processing, and conversational AI to provide intelligent responses based on organizational documents.

#### Architecture Overview

```mermaid
graph TB
    subgraph "AskAI Agent"
        INPUT[User Input Processing]
        DOC_SVC[Document Service]
        EMBED_SVC[Embedding Service]
        SEARCH_SVC[Semantic Search Service]
        PROMPT_SVC[Prompt Builder Service]
        METRICS_SVC[Metrics Service]
        CHAT_AGENT[OpenAI Chat Agent]
    end

    subgraph "External Services"
        MCP[MCP Tools]
        OPENAI[OpenAI API]
        CACHE[Redis Cache]
    end

    INPUT --> DOC_SVC
    DOC_SVC --> MCP
    DOC_SVC --> CACHE

    INPUT --> SEARCH_SVC
    SEARCH_SVC --> EMBED_SVC
    EMBED_SVC --> OPEN<PERSON>I

    SEARCH_SVC --> PROMPT_SVC
    PROMPT_SVC --> CHAT_AGENT
    CHAT_AGENT --> <PERSON><PERSON><PERSON><PERSON><PERSON>

    INPUT --> METRICS_SVC

    style INPUT fill:#e1f5fe
    style SEARCH_SVC fill:#f3e5f5
    style CHAT_AGENT fill:#e8f5e8
```

#### Key Features

**🧠 Semantic Search**

- OpenAI embeddings (text-embedding-3-small)
- Vector similarity calculations with cosine similarity
- Top-k document selection with relevance scoring
- Fallback mechanisms for low-relevance scenarios

**📄 Document Processing**

- Multi-format support: PDF, DOCX, TXT, HTML, JSON, CSV
- Binary file text extraction with fallback handling
- Content sanitization and truncation
- Intelligent caching with 30-minute TTL

**💬 Conversational AI**

- LangChain-powered chat responses
- Context-aware prompt construction
- Token management and optimization
- Streaming response support

**🎯 Context-Aware Processing**

- Organization-specific document context
- User and session-based customization
- Multi-tenant data isolation
- Intelligent prompt augmentation

#### Service Components

##### Document Service

**Purpose**: Manages document retrieval and caching

```typescript
// Location: apps/agents/src/agents/ask-ai/services/document.service.ts

export class DocumentService {
  private documentCache = new Map<string, CachedDocuments>();

  async retrieveAllDocuments(organizationId: string): Promise<ProcessedDocument[]> {
    // Check cache first
    const cached = this.documentCache.get(organizationId);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.documents;
    }

    // Retrieve from MCP tools
    const result = await this.mcpConnectionManager.executeToolCall(
      organizationId,
      'get_all_documents',
      { limit: 100 }
    );

    if (result.success && result.data?.documents) {
      // Cache results
      this.documentCache.set(organizationId, {
        documents: result.data.documents,
        timestamp: Date.now(),
      });

      return result.data.documents;
    }

    return [];
  }
}
```

**Features**:

- Intelligent caching with TTL-based expiration
- MCP tool integration for data retrieval
- Error handling with graceful fallbacks
- Memory management with automatic cleanup

##### Embedding Service

**Purpose**: Generates and manages document embeddings

```typescript
// Location: apps/agents/src/agents/ask-ai/services/embedding.service.ts

export class EmbeddingService {
  private embeddingCache = new Map<string, CachedEmbedding>();

  async generateQueryEmbedding(query: string): Promise<number[]> {
    const cacheKey = `query:${this.hashString(query)}`;

    // Check cache
    const cached = this.embeddingCache.get(cacheKey);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.embedding;
    }

    // Generate new embedding
    const response = await this.openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: query,
      dimensions: 1536,
    });

    const embedding = response.data[0].embedding;

    // Cache result
    this.embeddingCache.set(cacheKey, {
      embedding,
      timestamp: Date.now(),
    });

    return embedding;
  }
}
```

**Features**:

- OpenAI text-embedding-3-small model
- Persistent embedding caching
- Batch processing optimization
- Similarity calculation utilities

##### Semantic Search Service

**Purpose**: Ranks documents by relevance to user queries

```typescript
// Location: apps/agents/src/agents/ask-ai/services/semantic-search.service.ts

export class SemanticSearchService {
  async findRelevantDocuments(
    query: string,
    documents: ProcessedDocument[],
    topK: number = 5
  ): Promise<RankedDocument[]> {
    // Generate query embedding
    const queryEmbedding = await this.embeddingService.generateQueryEmbedding(query);

    // Calculate similarities
    const similarities = await Promise.all(
      documents.map(async doc => {
        const docEmbedding = await this.embeddingService.getDocumentEmbedding(doc);
        const similarity = this.calculateCosineSimilarity(queryEmbedding, docEmbedding);

        return {
          document: doc,
          similarity,
          rank: 0, // Will be set after sorting
        };
      })
    );

    // Sort by similarity and assign ranks
    similarities.sort((a, b) => b.similarity - a.similarity);
    similarities.forEach((item, index) => {
      item.rank = index + 1;
    });

    return similarities.slice(0, topK);
  }
}
```

**Features**:

- Cosine similarity scoring
- Top-k document selection
- Relevance threshold filtering
- Fallback strategies for edge cases

##### Prompt Builder Service

**Purpose**: Constructs optimized prompts for the chat agent

```typescript
// Location: apps/agents/src/agents/ask-ai/services/prompt-builder.service.ts

export class PromptBuilderService {
  buildRAGPrompt(
    userQuery: string,
    relevantDocs: RankedDocument[],
    conversationHistory?: ConversationTurn[]
  ): string {
    const contextParts = relevantDocs.map(
      doc => `[Document: ${doc.document.name}]\n${doc.document.contentPreview}`
    );

    const contextSection = contextParts.join('\n\n');

    return `You are an AI assistant helping with questions about organizational documents.

Context from relevant documents:
${contextSection}

${conversationHistory ? this.formatConversationHistory(conversationHistory) : ''}

User Question: ${userQuery}

Please provide a helpful response based on the context provided. If the context doesn't contain enough information to answer the question, please say so and suggest what additional information might be needed.`;
  }
}
```

**Features**:

- Context-aware prompt construction
- Token management and optimization
- Conversation history integration
- Template-based prompt generation

#### Usage Examples

##### Basic Query

```typescript
import { AgentFactory } from '@anter/agents';

const askAI = AgentFactory.getAgent('ask-ai');

const result = await askAI.invoke({
  input: 'What are our data retention policies?',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});

console.log(result.output);
// "Based on your organizational documents, your data retention policies include..."
```

##### Advanced Query with Streaming

```typescript
const stream = askAI.stream({
  input: 'Explain our security incident response procedures',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});

for await (const chunk of stream) {
  if (chunk.type === 'text') {
    process.stdout.write(chunk.content);
  }
}
```

##### Complex Analysis Query

```typescript
const result = await askAI.invoke({
  input: 'Compare our current security policies with industry best practices and identify gaps',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});
```

#### Configuration Options

```typescript
interface AskAIConfig {
  // Document retrieval settings
  maxDocuments: number; // Default: 100
  cacheTimeout: number; // Default: 30 minutes

  // Semantic search settings
  topK: number; // Default: 5
  similarityThreshold: number; // Default: 0.7

  // Embedding settings
  embeddingModel: string; // Default: 'text-embedding-3-small'
  embeddingDimensions: number; // Default: 1536

  // Chat settings
  chatModel: string; // Default: 'gpt-4o-mini'
  maxTokens: number; // Default: 4000
  temperature: number; // Default: 0.7

  // Performance settings
  timeout: number; // Default: 30000ms
  retryAttempts: number; // Default: 3
}
```

#### Performance Metrics

The AskAI agent tracks comprehensive metrics:

```typescript
interface AskAIMetrics {
  // General metrics
  totalInvocations: number;
  successRate: number;
  averageLatency: number;

  // Document retrieval metrics
  documentsRetrieved: number;
  cacheHitRate: number;

  // Semantic search metrics
  averageDocumentRelevance: number;
  embeddingCalls: number;

  // Chat agent metrics
  tokensUsed: number;
  chatLatency: number;

  // Error metrics
  errorsByType: Record<string, number>;
}
```

### Echo Agent (`echo`)

A simple testing and validation agent that echoes back formatted input with metrics.

#### Purpose

The Echo agent serves as:

- **Testing Framework**: Validates agent system functionality
- **Performance Baseline**: Measures system overhead
- **Development Tool**: Debugging and development assistance
- **Health Check**: System availability monitoring

#### Implementation

```typescript
// Location: apps/agents/src/agents/echo/index.ts

export class EchoAgent extends AbstractAgent {
  async invoke(input: AgentInput): Promise<AgentResult> {
    const startTime = Date.now();

    // Process input
    const userInput = this.extractUserInput(input);
    const context = input.context || {};

    // Format response
    const response = {
      message: 'Echo Agent Response',
      receivedInput: userInput,
      context: context,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - startTime,
    };

    return {
      success: true,
      output: JSON.stringify(response, null, 2),
      metadata: {
        agentType: 'echo',
        processingTimeMs: Date.now() - startTime,
        inputLength: userInput.length,
      },
    };
  }

  async *stream(input: AgentInput): AsyncGenerator<AgentChunk> {
    const result = await this.invoke(input);

    // Stream the response in chunks
    const chunks = result.output.split('\n');
    for (const chunk of chunks) {
      yield {
        type: 'text',
        content: chunk + '\n',
        metadata: { chunkIndex: chunks.indexOf(chunk) },
      };

      // Small delay to simulate streaming
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
}
```

#### Usage Examples

```typescript
const echo = AgentFactory.getAgent('echo');

const result = await echo.invoke({
  input: 'Test message',
  context: { organizationId: 'test-org' },
});

console.log(result.output);
// {
//   "message": "Echo Agent Response",
//   "receivedInput": "Test message",
//   "context": { "organizationId": "test-org" },
//   "timestamp": "2024-01-15T10:30:00.000Z",
//   "processingTime": 5
// }
```

### OpenAI Chat Agent (`openai-chat-agent`)

A LangChain-based conversational AI agent for general-purpose chat interactions.

#### Architecture

```mermaid
graph LR
    INPUT[User Input] --> LANGCHAIN[LangChain ChatOpenAI]
    LANGCHAIN --> OPENAI[OpenAI API]
    OPENAI --> RESPONSE[Formatted Response]

    subgraph "Configuration"
        MODEL[Model: gpt-4o-mini]
        TEMP[Temperature: 0.7]
        TOKENS[Max Tokens: 4000]
    end

    LANGCHAIN --> MODEL
    LANGCHAIN --> TEMP
    LANGCHAIN --> TOKENS

    style LANGCHAIN fill:#e1f5fe
    style OPENAI fill:#f3e5f5
```

#### Key Features

**💬 Natural Language Processing**

- GPT-4o-mini model for cost-effective responses
- Configurable temperature and token limits
- Context-aware conversations
- Streaming response support

**⚙️ Configurable Behavior**

- Custom system instructions
- Environment-based configuration
- Flexible prompt templates
- Error handling and recovery

**🔒 Enterprise Ready**

- Input validation and sanitization
- Rate limiting and timeout handling
- Comprehensive error handling
- Metrics and monitoring

#### Implementation

```typescript
// Location: apps/agents/src/sub-agents/openai/chat-agent/index.ts

export class OpenAIChatAgent extends AbstractAgent {
  private chatModel: ChatOpenAI;

  constructor(config: OpenAIChatAgentConfig) {
    super(config);

    this.chatModel = new ChatOpenAI({
      model: config.model || 'gpt-4o-mini',
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 4000,
      apiKey: config.apiKey,
    });
  }

  async invoke(input: AgentInput): Promise<AgentResult> {
    try {
      const userInput = this.extractUserInput(input);

      // Build prompt with system instructions
      const messages = [
        new SystemMessage(this.config.instructions || DEFAULT_INSTRUCTIONS),
        new HumanMessage(userInput),
      ];

      // Get response from OpenAI
      const response = await this.chatModel.invoke(messages);

      return {
        success: true,
        output: response.content,
        metadata: {
          model: this.config.model,
          tokensUsed: response.usage?.totalTokens || 0,
          latency: Date.now() - startTime,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

#### Configuration

```typescript
interface OpenAIChatAgentConfig {
  model: string; // Default: 'gpt-4o-mini'
  temperature: number; // Default: 0.7
  maxTokens: number; // Default: 4000
  instructions: string; // Custom system instructions
  apiKey: string; // OpenAI API key
  timeout: number; // Default: 30000ms
}
```

#### Usage Examples

```typescript
const chatAgent = AgentFactory.getAgent('openai-chat-agent');

const result = await chatAgent.invoke({
  input: 'Explain the benefits of microservices architecture',
  context: {
    organizationId: 'org-123',
    userId: 'user-456',
  },
});

console.log(result.output);
// "Microservices architecture offers several key benefits..."
```

## Agent Selection Guide

### When to Use AskAI Agent

**Best For**:

- Document-based question answering
- Knowledge base queries
- Organizational information retrieval
- Context-aware responses based on company documents
- Compliance and policy questions

**Examples**:

```typescript
// Policy questions
'What is our remote work policy?';

// Compliance queries
'What are our GDPR compliance requirements?';

// Technical documentation
'How do we handle API rate limiting?';

// Security procedures
'What should I do if I suspect a security breach?';
```

### When to Use Echo Agent

**Best For**:

- System testing and validation
- Performance benchmarking
- Development and debugging
- Health checks and monitoring
- Input/output validation

**Examples**:

```typescript
// System health check
await echo.invoke({ input: 'health check' });

// Performance testing
const startTime = Date.now();
await echo.invoke({ input: 'performance test' });
const latency = Date.now() - startTime;

// Input validation
await echo.invoke({
  input: 'test input',
  context: { test: true },
});
```

### When to Use OpenAI Chat Agent

**Best For**:

- General conversational AI
- Creative writing and brainstorming
- Code generation and review
- General knowledge questions
- Tasks not requiring specific organizational context

**Examples**:

```typescript
// General questions
'Explain quantum computing in simple terms';

// Code generation
'Write a Python function to calculate fibonacci numbers';

// Creative tasks
'Write a professional email requesting a meeting';

// Problem solving
"Help me debug this JavaScript error: TypeError: Cannot read property 'length' of undefined";
```

## Agent Integration Patterns

### Sequential Agent Execution

```typescript
// AskAI agent uses chat agent internally
export class AskAIAgent extends AbstractAgent {
  async invoke(input: AgentInput): Promise<AgentResult> {
    // 1. Retrieve relevant documents
    const documents = await this.documentService.retrieveAllDocuments(orgId);

    // 2. Find relevant content
    const relevantDocs = await this.semanticSearchService.findRelevantDocuments(
      userInput,
      documents
    );

    // 3. Build enhanced prompt
    const enhancedPrompt = this.promptBuilderService.buildRAGPrompt(userInput, relevantDocs);

    // 4. Call chat agent with enhanced context
    const chatResult = await this.chatAgent.invoke({
      input: enhancedPrompt,
      context: input.context,
    });

    return chatResult;
  }
}
```

### Parallel Agent Execution

```typescript
// Execute multiple agents concurrently
async function parallelAgentExecution(query: string, context: AgentContext) {
  const [askAIResult, chatResult] = await Promise.all([
    AgentFactory.getAgent('ask-ai').invoke({ input: query, context }),
    AgentFactory.getAgent('openai-chat-agent').invoke({ input: query, context }),
  ]);

  return {
    documentBased: askAIResult,
    general: chatResult,
  };
}
```

### Agent Composition

```typescript
// Compose agents for complex workflows
class CompositeAgent extends AbstractAgent {
  async invoke(input: AgentInput): Promise<AgentResult> {
    const userQuery = this.extractUserInput(input);

    // Determine intent
    const intent = await this.classifyIntent(userQuery);

    switch (intent) {
      case 'document_query':
        return await this.askAIAgent.invoke(input);

      case 'general_chat':
        return await this.chatAgent.invoke(input);

      case 'system_test':
        return await this.echoAgent.invoke(input);

      default:
        // Default to AskAI for organization-specific queries
        return await this.askAIAgent.invoke(input);
    }
  }
}
```

## Performance Optimization

### Caching Strategies

**Document Caching**:

```typescript
// 30-minute TTL for document cache
const CACHE_TTL = 30 * 60 * 1000; // 30 minutes

class CacheManager {
  private cache = new Map<string, CachedData>();

  get(key: string): CachedData | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached;
    }
    return null;
  }

  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }
}
```

**Connection Pooling**:

```typescript
// MCP session pooling
class SessionPool {
  private sessions = new Map<string, MCPSession>();

  async getSession(organizationId: string): Promise<MCPSession> {
    let session = this.sessions.get(organizationId);

    if (!session || this.isExpired(session)) {
      session = await this.createSession(organizationId);
      this.sessions.set(organizationId, session);
    }

    return session;
  }
}
```

### Error Handling Best Practices

```typescript
class RobustAgent extends AbstractAgent {
  async invoke(input: AgentInput): Promise<AgentResult> {
    try {
      return await this.executeWithRetry(input);
    } catch (error) {
      return this.handleError(error, input);
    }
  }

  private async executeWithRetry(input: AgentInput, attempts: number = 3): Promise<AgentResult> {
    for (let i = 0; i < attempts; i++) {
      try {
        return await this.executeInternal(input);
      } catch (error) {
        if (i === attempts - 1) throw error;

        // Exponential backoff
        await this.delay(Math.pow(2, i) * 1000);
      }
    }

    throw new Error('Max retry attempts exceeded');
  }
}
```

## Monitoring and Metrics

### Agent Metrics Collection

```typescript
interface AgentMetrics {
  // Performance metrics
  totalInvocations: number;
  successfulInvocations: number;
  failedInvocations: number;
  averageLatency: number;
  p95Latency: number;

  // Resource usage
  totalTokensUsed: number;
  averageTokensPerRequest: number;
  totalCost: number;

  // Cache metrics
  cacheHitRate: number;
  cacheMissRate: number;

  // Error metrics
  errorsByType: Record<string, number>;
  errorRate: number;
}
```

### Health Checks

```typescript
class AgentHealthCheck {
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.all([
      this.checkDatabase(),
      this.checkOpenAI(),
      this.checkMCPTools(),
      this.checkCache(),
    ]);

    return {
      status: checks.every(c => c.healthy) ? 'healthy' : 'unhealthy',
      checks: checks,
    };
  }
}
```

This comprehensive guide provides the foundation for understanding and effectively using the available agents in the AI Agents system.
