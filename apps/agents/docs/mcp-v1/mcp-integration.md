# MCP Integration Architecture

This document details the Model Context Protocol (MCP) integration architecture, tool consolidation patterns, and implementation details.

## Overview

The MCP integration provides a clean separation between agent logic and tool implementations, following the Model Context Protocol specification for stateful AI interactions. The architecture consolidates all tools in the `@anter/mcp-tools` package while maintaining infrastructure adapters in the agents project.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Agents Package (@anter/agents)"
        ASKAI[AskAI Agent]
        CONNECTION[Connection Manager]
        BRIDGE[MCP Bridge]

        subgraph "Infrastructure Adapters"
            DB_ADAPTER[Database Provider]
            SESSION_ADAPTER[Session Provider]
        end
    end

    subgraph "MCP Tools Package (@anter/mcp-tools)"
        REGISTRY[Tool Registry]

        subgraph "Core Framework"
            INTERFACES[MCP Interfaces]
            HANDLER[Tool Handler]
            ABSTRACT[Abstract Base Classes]
        end

        subgraph "Database Tools"
            QUERY_TOOL[Query Database Tool]
            DOCS_TOOL[Get All Documents Tool]
        end

        subgraph "Utilities"
            PROCESSOR[Document Processor]
            EXTRACTOR[File Text Extractor]
            VALIDATOR[Query Validator]
        end
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        REDIS[(Redis)]
    end

    ASKAI --> CONNECTION
    CONNECTION --> BRIDGE
    BRIDGE --> DB_ADAPTER
    BRIDGE --> SESSION_ADAPTER

    DB_ADAPTER --> REGISTRY
    SESSION_ADAPTER --> REGISTRY

    REGISTRY --> INTERFACES
    REGISTRY --> QUERY_TOOL
    REGISTRY --> DOCS_TOOL

    QUERY_TOOL --> ABSTRACT
    DOCS_TOOL --> ABSTRACT
    ABSTRACT --> HANDLER

    QUERY_TOOL --> VALIDATOR
    DOCS_TOOL --> PROCESSOR
    PROCESSOR --> EXTRACTOR

    QUERY_TOOL --> DB
    DOCS_TOOL --> DB

    style ASKAI fill:#e1f5fe
    style REGISTRY fill:#e8f5e8
    style DB_ADAPTER fill:#fff3e0
    style DB fill:#fce4ec
```

## Package Architecture

### Tool Package Structure

```
@anter/mcp-tools/
├── src/
│   ├── core/                    # Core MCP framework
│   │   ├── tool-handler.ts      # MCP interfaces and JSON-RPC types
│   │   ├── tool-registry.ts     # Tool registration and discovery
│   │   └── index.ts             # Core exports
│   ├── tools/                   # Tool implementations
│   │   ├── database/            # Database-related tools
│   │   │   ├── abstract-database-tool.ts    # Base class
│   │   │   ├── query-database-tool.ts       # SQL query execution
│   │   │   ├── get-all-documents-tool.ts    # Document retrieval
│   │   │   └── index.ts         # Database tools exports
│   │   └── index.ts             # All tools exports
│   ├── utils/                   # Utility functions
│   │   ├── document-processor.ts        # Document processing
│   │   ├── file-text-extractor.ts      # Binary file extraction
│   │   ├── query-validator.ts           # SQL validation
│   │   └── index.ts             # Utility exports
│   ├── types/                   # Type definitions
│   │   ├── index.ts             # Common types
│   │   ├── session.ts           # Session types
│   │   └── task.ts              # Task types
│   └── index.ts                 # Main package exports
```

### Infrastructure Adapters Structure

```
@anter/agents/
├── src/integration/mcp-server/
│   ├── adapters/                # Infrastructure adapters
│   │   ├── database-provider.ts # Database infrastructure
│   │   ├── session-provider.ts  # Session infrastructure
│   │   └── index.ts             # Adapter exports
│   ├── connection-manager.ts    # Session pooling
│   └── mcp-bridge.ts           # JSON-RPC bridge
```

## Core MCP Framework

### Tool Handler Interface

The core MCP interfaces follow the Model Context Protocol specification:

```typescript
// Location: packages/mcp-tools/src/core/tool-handler.ts

// JSON-RPC 2.0 Message Types
interface JsonRpcRequest {
  jsonrpc: '2.0';
  id: string | number;
  method: string;
  params?: any;
}

interface JsonRpcResponse {
  jsonrpc: '2.0';
  id: string | number;
  result?: any;
  error?: JsonRpcError;
}

// MCP Tool Interface
interface MCPTool {
  readonly name: string;
  readonly description: string;
  readonly inputSchema: any;

  execute(params: any, context: ToolExecutionContext): Promise<any>;
}

// Tool Execution Context
interface ToolExecutionContext {
  sessionId: string;
  organizationId: string;
  userId?: string;
  executionTime?: number;
}
```

### Tool Registry System

The registry provides centralized tool management:

```typescript
// Location: packages/mcp-tools/src/core/tool-registry.ts

export class DefaultMCPToolRegistry implements MCPToolRegistry {
  private tools = new Map<string, MCPTool>();

  register(tool: MCPTool): void {
    if (this.tools.has(tool.name)) {
      throw new Error(`Tool ${tool.name} is already registered`);
    }
    this.tools.set(tool.name, tool);
  }

  async execute(name: string, params: any, context: ToolExecutionContext): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool ${name} not found`);
    }

    return await tool.execute(params, context);
  }

  listTools(): MCPToolInfo[] {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
    }));
  }
}
```

## Database Tools Implementation

### Abstract Base Class

All database tools extend a common base class:

```typescript
// Location: packages/mcp-tools/src/tools/database/abstract-database-tool.ts

export abstract class AbstractDatabaseTool implements MCPTool {
  constructor(
    protected databaseProvider: DatabaseProvider,
    protected sessionProvider: SessionProvider
  ) {}

  abstract readonly name: string;
  abstract readonly description: string;
  abstract readonly inputSchema: any;

  async execute(params: any, context: ToolExecutionContext): Promise<any> {
    // Validate session and get organization context
    const organizationId = await this.sessionProvider.getOrganizationId(context.sessionId);
    if (!organizationId) {
      throw new Error('Invalid session or organization not found');
    }

    // Execute tool-specific logic
    return await this.executeInternal(params, organizationId, context);
  }

  protected abstract executeInternal(
    params: any,
    organizationId: string,
    context: ToolExecutionContext
  ): Promise<any>;
}
```

### Query Database Tool

Executes secure SQL queries with comprehensive validation:

```typescript
// Location: packages/mcp-tools/src/tools/database/query-database-tool.ts

export class QueryDatabaseTool extends AbstractDatabaseTool {
  readonly name = 'query_database';
  readonly description = 'Execute SELECT queries against the database with organization isolation';
  readonly inputSchema = {
    type: 'object',
    properties: {
      query: { type: 'string', description: 'SQL SELECT query to execute' },
    },
    required: ['query'],
  };

  protected async executeInternal(
    params: { query: string },
    organizationId: string,
    context: ToolExecutionContext
  ): Promise<any> {
    const startTime = Date.now();

    try {
      // Validate SQL query safety
      const isValid = await this.databaseProvider.validateQuery(params.query);
      if (!isValid) {
        throw new Error('Query failed safety validation');
      }

      // Execute query with tenant isolation
      const result = await this.databaseProvider.executeQuery(params.query, organizationId);

      return {
        success: true,
        data: {
          rows: result.rows,
          rowCount: result.rowCount,
          executionTimeMs: Date.now() - startTime,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          executionTimeMs: Date.now() - startTime,
        },
      };
    }
  }
}
```

### Get All Documents Tool

Retrieves and processes organizational documents:

```typescript
// Location: packages/mcp-tools/src/tools/database/get-all-documents-tool.ts

export class GetAllDocumentsTool extends AbstractDatabaseTool {
  readonly name = 'get_all_documents';
  readonly description = 'Retrieve all documents for an organization with filtering options';
  readonly inputSchema = {
    type: 'object',
    properties: {
      limit: { type: 'number', default: 50 },
      offset: { type: 'number', default: 0 },
      documentType: { type: 'string' },
      fileType: { type: 'string' },
    },
  };

  protected async executeInternal(
    params: any,
    organizationId: string,
    context: ToolExecutionContext
  ): Promise<any> {
    try {
      const result = await this.databaseProvider.executeQuery(
        this.buildDocumentQuery(params),
        organizationId
      );

      // Process documents with text extraction
      const processedDocs = await Promise.all(
        result.rows.map(row => this.documentProcessor.processDocumentForAPI(row))
      );

      return {
        success: true,
        data: {
          documents: processedDocs,
          pagination: {
            total: result.rowCount,
            limit: params.limit || 50,
            offset: params.offset || 0,
            hasMore: (params.offset || 0) + (params.limit || 50) < result.rowCount,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: { message: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }
}
```

## Infrastructure Adapters

### Database Provider

Bridges tool logic with database infrastructure:

```typescript
// Location: apps/agents/src/integration/mcp-server/adapters/database-provider.ts

export class AgentsDatabaseProvider implements DatabaseProvider {
  constructor(
    private dbProvider: {
      dbWithTenant(orgId: string): Promise<any>;
      log: Logger;
    }
  ) {}

  async executeQuery(query: string, organizationId: string): Promise<QueryResult> {
    try {
      // Get tenant-isolated database connection
      const db = await this.dbProvider.dbWithTenant(organizationId);

      // Execute query with multiple fallback methods
      let result;
      try {
        result = await db.execute(sql.raw(query));
      } catch (error) {
        // Fallback to function-based execution
        result = await db.execute(query);
      }

      return {
        rows: Array.isArray(result) ? result : result.rows || [],
        rowCount: Array.isArray(result) ? result.length : result.rowCount || 0,
        fields: result.fields,
      };
    } catch (error) {
      this.dbProvider.log.error(error, 'Database query execution failed');
      throw error;
    }
  }

  async validateQuery(query: string): Promise<boolean> {
    // SQL injection prevention
    const cleanQuery = query.trim().toLowerCase();

    // Must be SELECT statement
    if (!cleanQuery.startsWith('select')) {
      return false;
    }

    // Block dangerous keywords
    const dangerousPatterns = [
      /\b(drop|delete|insert|update|alter|create|truncate)\b/i,
      /\b(exec|execute|sp_)\b/i,
      /\b(union\s+select)\b/i,
      /\b(information_schema|pg_)\b/i,
    ];

    return !dangerousPatterns.some(pattern => pattern.test(query));
  }
}
```

### Session Provider

Manages session context and organization mapping:

```typescript
// Location: apps/agents/src/integration/mcp-server/adapters/session-provider.ts

export class AgentsSessionProvider implements SessionProvider {
  constructor(
    private sessionLookup: {
      findSessionById(sessionId: string): { organizationId: string; userId?: string } | null;
    }
  ) {}

  async getOrganizationId(sessionId: string): Promise<string | null> {
    const session = this.sessionLookup.findSessionById(sessionId);
    return session?.organizationId || null;
  }

  async getUserId(sessionId: string): Promise<string | null> {
    const session = this.sessionLookup.findSessionById(sessionId);
    return session?.userId || null;
  }
}
```

## Connection Management

### MCP Connection Manager

Handles session pooling and tool execution coordination:

```mermaid
sequenceDiagram
    participant Agent as AskAI Agent
    participant CM as Connection Manager
    participant Bridge as MCP Bridge
    participant Registry as Tool Registry
    participant Tool as Database Tool
    participant DB as Database

    Agent->>CM: executeToolCall(orgId, 'get_all_documents', args)
    CM->>CM: getOrCreateSession(orgId)
    CM->>Bridge: callToolDirect(sessionId, toolName, args)
    Bridge->>Registry: execute(toolName, args, context)
    Registry->>Tool: execute(args, context)
    Tool->>DB: executeQuery(sql, orgId)
    DB-->>Tool: results
    Tool-->>Registry: processed results
    Registry-->>Bridge: JSON-RPC response
    Bridge-->>CM: parsed result
    CM-->>Agent: final result
```

```typescript
// Location: apps/agents/src/integration/mcp-server/connection-manager.ts

export class MCPConnectionManager {
  async executeToolCall(
    organizationId: string,
    toolName: string,
    args: any,
    userId?: string
  ): Promise<AgentMCPResult> {
    try {
      // Get or create session for organization
      const session = await this.getOrCreateSession(organizationId, userId);

      // Execute tool via bridge
      const result = await this.bridge.callToolDirect(session.sessionId, toolName, args, {
        timeout: 30000,
        retryAttempts: 2,
      });

      // Update session usage
      session.lastUsed = new Date();
      session.usageCount++;

      return result;
    } catch (error) {
      this.updateFailureMetrics(session, error);
      throw error;
    }
  }
}
```

### Session Pooling Strategy

```mermaid
graph TD
    REQUEST[Tool Request] --> POOL{Check Pool}
    POOL -->|Available| REUSE[Reuse Session]
    POOL -->|None| CREATE[Create Session]
    CREATE --> VALIDATE[Validate Session]
    REUSE --> VALIDATE
    VALIDATE --> EXECUTE[Execute Tool]
    EXECUTE --> UPDATE[Update Usage]
    UPDATE --> CLEANUP{TTL Expired?}
    CLEANUP -->|Yes| REMOVE[Remove Session]
    CLEANUP -->|No| POOL

    style POOL fill:#e1f5fe
    style CREATE fill:#f3e5f5
    style EXECUTE fill:#e8f5e8
```

## Security Implementation

### SQL Injection Prevention

Multi-layer SQL validation approach:

```typescript
export class QueryValidator {
  static validateQuery(query: string): ValidationResult {
    const cleanQuery = query.trim().toLowerCase();

    // 1. Operation validation
    if (!cleanQuery.startsWith('select')) {
      return { valid: false, error: 'Only SELECT statements are allowed' };
    }

    // 2. Dangerous keyword detection
    const dangerousPatterns = [
      /\b(drop|delete|insert|update|alter|create|truncate)\b/i,
      /\b(exec|execute|sp_)\b/i,
      /\b(union\s+select)\b/i,
      /\b(information_schema|pg_)\b/i,
      /\b(pg_sleep|waitfor|benchmark)\b/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        return { valid: false, error: 'Query contains prohibited patterns' };
      }
    }

    // 3. Comment and string escape validation
    if (query.includes('--') || query.includes('/*') || /(['"]);/.test(query)) {
      return { valid: false, error: 'Query contains suspicious comment or escape patterns' };
    }

    return { valid: true };
  }
}
```

### Tenant Isolation Flow

```mermaid
sequenceDiagram
    participant Tool as Database Tool
    participant Provider as Database Provider
    participant DB as PostgreSQL

    Tool->>Provider: executeQuery(sql, organizationId)
    Provider->>DB: dbWithTenant(organizationId)
    DB->>DB: SET app.current_organization_id = organizationId
    DB->>DB: SET app.bypass_rls = 'off'
    DB-->>Provider: tenant-isolated connection
    Provider->>DB: execute(query)
    Note over DB: RLS filters results by organization
    DB-->>Provider: filtered results
    Provider-->>Tool: organization-specific data
```

## Performance Optimizations

### Caching Strategy

**Tool Result Caching**:

- Document retrieval results cached per organization
- 30-minute TTL with intelligent cleanup
- Memory-based with size limits

**Connection Pooling**:

- Session pooling per organization
- Configurable pool sizes and TTLs
- Health monitoring and automatic cleanup

**Batch Processing**:

- Document processing in batches
- Embedding generation optimization
- Parallel text extraction

### Metrics and Monitoring

```typescript
interface ToolMetrics {
  executionTime: number;
  success: boolean;
  organizationId: string;
  toolName: string;
  resultSize: number;
  cacheHit: boolean;
}

interface ConnectionMetrics {
  totalSessions: number;
  activeSessions: number;
  sessionCreationRate: number;
  averageSessionDuration: number;
  poolUtilization: number;
}
```

## Error Handling Patterns

### Tool-Level Error Handling

```typescript
// Standardized error response format
interface ToolErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    retryable: boolean;
    executionTimeMs?: number;
  };
}

// Error classification
enum ToolErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### Graceful Degradation

```mermaid
graph TD
    ERROR[Tool Error] --> CLASSIFY{Error Type}
    CLASSIFY -->|Retryable| RETRY[Retry with Backoff]
    CLASSIFY -->|Non-retryable| FALLBACK[Apply Fallback]
    RETRY --> SUCCESS{Success?}
    SUCCESS -->|Yes| RETURN[Return Result]
    SUCCESS -->|No| BACKOFF[Exponential Backoff]
    BACKOFF --> RETRY
    FALLBACK --> CACHE{Cache Available?}
    CACHE -->|Yes| CACHED[Return Cached Result]
    CACHE -->|No| MINIMAL[Return Minimal Response]

    style ERROR fill:#fce4ec
    style FALLBACK fill:#e8f5e8
    style CACHED fill:#e1f5fe
```

## Testing Strategy

### Tool Testing

```typescript
// Example tool test
describe('QueryDatabaseTool', () => {
  it('should execute valid SELECT queries', async () => {
    const mockProvider = createMockDatabaseProvider();
    const tool = new QueryDatabaseTool(mockProvider, mockSessionProvider);

    const result = await tool.execute(
      { query: 'SELECT id FROM documents LIMIT 10' },
      { sessionId: 'test-session', organizationId: 'org-123' }
    );

    expect(result.success).toBe(true);
    expect(result.data.rows).toBeDefined();
  });

  it('should reject unsafe queries', async () => {
    const tool = new QueryDatabaseTool(mockProvider, mockSessionProvider);

    const result = await tool.execute(
      { query: 'DROP TABLE documents' },
      { sessionId: 'test-session', organizationId: 'org-123' }
    );

    expect(result.success).toBe(false);
    expect(result.error.message).toContain('safety validation');
  });
});
```

### Integration Testing

- **End-to-end tool execution**: Agent → Connection Manager → Tools → Database
- **Session management**: Pool creation, cleanup, health checks
- **Error scenarios**: Network failures, database timeouts, invalid queries
- **Performance tests**: Concurrent execution, memory usage, latency

## Migration Benefits

The MCP tools consolidation provides several key benefits:

### 1. **Single Source of Truth**

- All tool logic centralized in `@anter/mcp-tools`
- Eliminated code duplication between packages
- Consistent tool behavior across environments

### 2. **Clean Architecture**

- Perfect separation between tool logic and infrastructure
- Dependency injection enables easy testing and flexibility
- MCP specification compliance ensures interoperability

### 3. **Enhanced Capabilities**

- Execution timing and performance monitoring
- Advanced filtering and pagination support
- Comprehensive error handling with structured responses

### 4. **Developer Experience**

- Easy to add new tools without touching infrastructure
- Clear interfaces and comprehensive TypeScript support
- Extensive test coverage with realistic scenarios

This MCP integration architecture provides a robust, scalable foundation for AI tool execution with enterprise-grade security, performance, and maintainability.
