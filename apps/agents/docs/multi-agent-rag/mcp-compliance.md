# MCP Compliance Implementation Guide

## Overview

This document outlines the implementation strategy for ensuring full compliance with the Model Context Protocol (MCP) v1.0 specification while integrating with the multi-agent RAG system.

## MCP Protocol Overview

The Model Context Protocol (MCP) is an open standard for connecting AI models to external data sources and tools. It enables:

- **Standardized Communication**: Between AI applications and context providers
- **Tool Integration**: Seamless access to external tools and resources
- **Resource Management**: Efficient handling of data sources and capabilities
- **Interoperability**: Cross-platform compatibility

## Current MCP Implementation Analysis

### Existing Components

Based on memory [[memory:4881630511761841289]], the current system has:

- ✅ **MCP Tools Package**: `@anter/mcp-tools` with 46/46 passing tests
- ✅ **Clean Architecture**: Separation between tool logic and infrastructure
- ✅ **Core Functionality**: Database tools, document processing, task management
- ✅ **Type Safety**: Comprehensive TypeScript implementation

### Current MCP Tools Structure

```
packages/mcp-tools/
├── src/
│   ├── core/
│   │   ├── tool-handler.ts         # Tool execution logic
│   │   ├── tool-registry.ts        # Tool discovery and management
│   │   └── index.ts
│   ├── tools/
│   │   ├── database/
│   │   │   ├── get-all-documents-tool.ts
│   │   │   ├── query-database-tool.ts
│   │   │   └── abstract-database-tool.ts
│   │   └── index.ts
│   ├── types/
│   │   ├── index.ts                # Core MCP types
│   │   ├── session.ts              # Session management
│   │   └── task.ts                 # Task coordination
│   └── utils/
│       ├── document-processor.ts   # Document handling
│       ├── file-text-extractor.ts  # Content extraction
│       └── query-validator.ts      # Input validation
```

## MCP v1.0 Compliance Requirements

### 1. Core Protocol Messages

#### Initialize Messages

```typescript
interface InitializeRequest {
  method: 'initialize';
  params: {
    protocolVersion: string;
    capabilities: ClientCapabilities;
    clientInfo: {
      name: string;
      version: string;
    };
  };
}

interface InitializeResponse {
  protocolVersion: string;
  capabilities: ServerCapabilities;
  serverInfo: {
    name: string;
    version: string;
  };
}
```

#### Tool Messages

```typescript
interface ListToolsRequest {
  method: 'tools/list';
  params?: {
    cursor?: string;
  };
}

interface CallToolRequest {
  method: 'tools/call';
  params: {
    name: string;
    arguments?: Record<string, any>;
  };
}
```

#### Resource Messages

```typescript
interface ListResourcesRequest {
  method: 'resources/list';
  params?: {
    cursor?: string;
  };
}

interface ReadResourceRequest {
  method: 'resources/read';
  params: {
    uri: string;
  };
}
```

### 2. Capability Advertisement

```typescript
interface ServerCapabilities {
  tools?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  prompts?: {
    listChanged?: boolean;
  };
  logging?: {
    level?: LogLevel;
  };
}

interface ClientCapabilities {
  roots?: {
    listChanged?: boolean;
  };
  sampling?: {};
}
```

## Enhanced MCP Server Implementation

### 1. MCP Server Enhancement

```typescript
// packages/mcp-tools/src/core/mcp-server-enhanced.ts

import {
  MCPServer,
  InitializeRequest,
  InitializeResponse,
  ListToolsRequest,
  CallToolRequest,
  ListResourcesRequest,
  ReadResourceRequest,
  ServerCapabilities,
  LogLevel,
} from '@modelcontextprotocol/sdk';

export class EnhancedMCPServer extends MCPServer {
  private toolRegistry: ToolRegistry;
  private resourceManager: ResourceManager;
  private capabilityManager: CapabilityManager;
  private logger: MCPLogger;

  constructor(options: MCPServerOptions) {
    super(options);
    this.toolRegistry = new ToolRegistry();
    this.resourceManager = new ResourceManager();
    this.capabilityManager = new CapabilityManager();
    this.logger = new MCPLogger(options.logLevel || 'info');
  }

  async handleInitialize(request: InitializeRequest): Promise<InitializeResponse> {
    this.logger.info('Initializing MCP server', {
      client: request.params.clientInfo,
      protocolVersion: request.params.protocolVersion,
    });

    // Validate protocol version compatibility
    const supportedVersions = ['1.0.0', '1.0'];
    if (!supportedVersions.includes(request.params.protocolVersion)) {
      throw new Error(`Unsupported protocol version: ${request.params.protocolVersion}`);
    }

    // Negotiate capabilities
    const negotiatedCapabilities = this.capabilityManager.negotiate(
      request.params.capabilities,
      this.getSupportedCapabilities()
    );

    return {
      protocolversion: '0.0.1',
      capabilities: negotiatedCapabilities,
      serverInfo: {
        name: 'AskInfosec MCP Server',
        version: '2.0.0',
      },
    };
  }

  async handleListTools(request: ListToolsRequest): Promise<ListToolsResponse> {
    const tools = await this.toolRegistry.listTools(request.params?.cursor);

    return {
      tools: tools.map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
      })),
      nextCursor:
        tools.length >= this.toolRegistry.pageSize
          ? this.toolRegistry.getNextCursor(tools)
          : undefined,
    };
  }

  async handleCallTool(request: CallToolRequest): Promise<CallToolResponse> {
    const toolName = request.params.name;
    const arguments_ = request.params.arguments || {};

    this.logger.debug('Calling tool', { toolName, arguments: arguments_ });

    try {
      // Validate tool exists
      const tool = await this.toolRegistry.getTool(toolName);
      if (!tool) {
        throw new Error(`Tool not found: ${toolName}`);
      }

      // Validate arguments against schema
      const validationResult = tool.validateArguments(arguments_);
      if (!validationResult.valid) {
        throw new Error(`Invalid arguments: ${validationResult.errors.join(', ')}`);
      }

      // Execute tool with timeout and monitoring
      const startTime = Date.now();
      const result = await this.executeToolWithTimeout(tool, arguments_);
      const executionTime = Date.now() - startTime;

      // Log execution metrics
      this.logger.info('Tool executed successfully', {
        toolName,
        executionTime,
        resultSize: JSON.stringify(result).length,
      });

      return {
        content: [
          {
            type: 'text',
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error('Tool execution failed', {
        toolName,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Tool execution failed: ${error.message}`);
    }
  }

  async handleListResources(request: ListResourcesRequest): Promise<ListResourcesResponse> {
    const resources = await this.resourceManager.listResources(request.params?.cursor);

    return {
      resources: resources.map(resource => ({
        uri: resource.uri,
        name: resource.name,
        description: resource.description,
        mimeType: resource.mimeType,
      })),
      nextCursor:
        resources.length >= this.resourceManager.pageSize
          ? this.resourceManager.getNextCursor(resources)
          : undefined,
    };
  }

  async handleReadResource(request: ReadResourceRequest): Promise<ReadResourceResponse> {
    const uri = request.params.uri;

    this.logger.debug('Reading resource', { uri });

    try {
      const resource = await this.resourceManager.readResource(uri);

      return {
        contents: [
          {
            uri: uri,
            mimeType: resource.mimeType,
            text: resource.content,
          },
        ],
      };
    } catch (error) {
      this.logger.error('Resource read failed', { uri, error: error.message });
      throw new Error(`Failed to read resource: ${error.message}`);
    }
  }

  private getSupportedCapabilities(): ServerCapabilities {
    return {
      tools: {
        listChanged: true,
      },
      resources: {
        subscribe: true,
        listChanged: true,
      },
      prompts: {
        listChanged: true,
      },
      logging: {
        level: 'debug' as LogLevel,
      },
    };
  }

  private async executeToolWithTimeout(
    tool: Tool,
    arguments_: Record<string, any>,
    timeoutMs: number = 30000
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      tool
        .execute(arguments_)
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }
}
```

### 2. Enhanced Tool Registry

```typescript
// packages/mcp-tools/src/core/enhanced-tool-registry.ts

export class EnhancedToolRegistry extends ToolRegistry {
  private tools: Map<string, EnhancedToolDefinition> = new Map();
  private categories: Map<string, string[]> = new Map();
  private metrics: ToolMetrics = new ToolMetrics();
  public readonly pageSize = 50;

  async registerTool(tool: EnhancedToolDefinition): Promise<void> {
    // Validate tool definition
    this.validateToolDefinition(tool);

    // Register tool
    this.tools.set(tool.name, tool);

    // Update categories
    if (!this.categories.has(tool.category)) {
      this.categories.set(tool.category, []);
    }
    this.categories.get(tool.category)!.push(tool.name);

    // Initialize metrics
    this.metrics.initializeTool(tool.name);
  }

  async listTools(cursor?: string, category?: string): Promise<EnhancedToolDefinition[]> {
    let tools = Array.from(this.tools.values());

    // Filter by category if specified
    if (category && this.categories.has(category)) {
      const categoryTools = this.categories.get(category)!;
      tools = tools.filter(tool => categoryTools.includes(tool.name));
    }

    // Sort by priority and usage
    tools.sort((a, b) => {
      // Primary sort: priority (higher first)
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }

      // Secondary sort: usage frequency
      const aUsage = this.metrics.getUsageCount(a.name);
      const bUsage = this.metrics.getUsageCount(b.name);
      return bUsage - aUsage;
    });

    // Handle pagination
    const startIndex = cursor ? this.decodeCursor(cursor) : 0;
    const endIndex = Math.min(startIndex + this.pageSize, tools.length);

    return tools.slice(startIndex, endIndex);
  }

  async getTool(name: string): Promise<EnhancedToolDefinition | null> {
    return this.tools.get(name) || null;
  }

  async getToolsByCategory(category: string): Promise<EnhancedToolDefinition[]> {
    if (!this.categories.has(category)) {
      return [];
    }

    const toolNames = this.categories.get(category)!;
    return toolNames.map(name => this.tools.get(name)!).filter(Boolean);
  }

  getNextCursor(tools: EnhancedToolDefinition[]): string {
    // Simple cursor implementation - in production, use more robust pagination
    return Buffer.from(tools.length.toString()).toString('base64');
  }

  private decodeCursor(cursor: string): number {
    try {
      return parseInt(Buffer.from(cursor, 'base64').toString(), 10) || 0;
    } catch {
      return 0;
    }
  }

  private validateToolDefinition(tool: EnhancedToolDefinition): void {
    if (!tool.name || typeof tool.name !== 'string') {
      throw new Error('Tool name is required and must be a string');
    }

    if (!tool.description || typeof tool.description !== 'string') {
      throw new Error('Tool description is required and must be a string');
    }

    if (!tool.inputSchema || typeof tool.inputSchema !== 'object') {
      throw new Error('Tool inputSchema is required and must be an object');
    }

    if (!tool.category || !['database', 'search', 'analysis', 'external'].includes(tool.category)) {
      throw new Error('Tool category must be one of: database, search, analysis, external');
    }
  }
}

interface EnhancedToolDefinition extends ToolDefinition {
  category: 'database' | 'search' | 'analysis' | 'external';
  priority: number;
  performance: {
    averageExecutionTime: number;
    successRate: number;
    lastExecuted: Date;
  };
  usage: {
    totalCalls: number;
    uniqueUsers: number;
    errorCount: number;
  };
  metadata: {
    version: string;
    author: string;
    tags: string[];
    documentation: string;
  };
}
```

### 3. Resource Manager Implementation

```typescript
// packages/mcp-tools/src/core/resource-manager.ts

export class ResourceManager {
  private resources: Map<string, ResourceDefinition> = new Map();
  private subscribers: Map<string, Set<string>> = new Map();
  public readonly pageSize = 50;

  constructor(
    private documentService: DocumentService,
    private embeddingService: EmbeddingService
  ) {
    this.initializeResources();
  }

  private initializeResources(): void {
    // Register built-in resources
    this.registerResource({
      uri: 'askinfosec://documents',
      name: 'Document Collection',
      description: 'Access to organization documents and knowledge base',
      mimeType: 'application/json',
      handler: this.handleDocumentResource.bind(this),
    });

    this.registerResource({
      uri: 'askinfosec://embeddings',
      name: 'Embedding Service',
      description: 'Vector embeddings for semantic search and similarity',
      mimeType: 'application/json',
      handler: this.handleEmbeddingResource.bind(this),
    });

    this.registerResource({
      uri: 'askinfosec://metrics',
      name: 'System Metrics',
      description: 'Performance and usage metrics for the system',
      mimeType: 'application/json',
      handler: this.handleMetricsResource.bind(this),
    });
  }

  async listResources(cursor?: string): Promise<ResourceDefinition[]> {
    const resources = Array.from(this.resources.values());

    // Sort by priority and usage
    resources.sort((a, b) => a.name.localeCompare(b.name));

    // Handle pagination
    const startIndex = cursor ? this.decodeCursor(cursor) : 0;
    const endIndex = Math.min(startIndex + this.pageSize, resources.length);

    return resources.slice(startIndex, endIndex);
  }

  async readResource(uri: string): Promise<ResourceContent> {
    const resource = this.resources.get(uri);
    if (!resource) {
      throw new Error(`Resource not found: ${uri}`);
    }

    try {
      const content = await resource.handler();

      return {
        mimeType: resource.mimeType,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
      };
    } catch (error) {
      throw new Error(`Failed to read resource ${uri}: ${error.message}`);
    }
  }

  async subscribeToResource(uri: string, subscriberId: string): Promise<void> {
    if (!this.resources.has(uri)) {
      throw new Error(`Resource not found: ${uri}`);
    }

    if (!this.subscribers.has(uri)) {
      this.subscribers.set(uri, new Set());
    }

    this.subscribers.get(uri)!.add(subscriberId);
  }

  async unsubscribeFromResource(uri: string, subscriberId: string): Promise<void> {
    const subscribers = this.subscribers.get(uri);
    if (subscribers) {
      subscribers.delete(subscriberId);
    }
  }

  private async handleDocumentResource(): Promise<any> {
    // This would typically be called with organization/user context
    // For MCP compliance demo, return sample structure
    return {
      type: 'document_collection',
      description: 'Organization document collection',
      totalDocuments: await this.documentService.getDocumentCount(),
      categories: await this.documentService.getCategories(),
      lastUpdated: new Date().toISOString(),
      accessMethods: [
        {
          method: 'get-all-documents-tool',
          description: 'Retrieve all documents for an organization',
        },
        {
          method: 'query-database-tool',
          description: 'Search documents with SQL-like queries',
        },
      ],
    };
  }

  private async handleEmbeddingResource(): Promise<any> {
    return {
      type: 'embedding_service',
      description: 'Vector embedding service for semantic search',
      model: this.embeddingService.getModelInfo(),
      dimensions: this.embeddingService.getDimensions(),
      supportedFormats: ['text', 'document'],
      performance: {
        averageProcessingTime: '150ms',
        maxBatchSize: 100,
      },
    };
  }

  private async handleMetricsResource(): Promise<any> {
    return {
      type: 'system_metrics',
      description: 'Real-time system performance metrics',
      metrics: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        toolCallsToday: 0, // This would come from actual metrics service
        averageResponseTime: 0,
        errorRate: 0,
      },
      timestamp: new Date().toISOString(),
    };
  }

  private registerResource(resource: ResourceDefinition): void {
    this.resources.set(resource.uri, resource);
  }

  getNextCursor(resources: ResourceDefinition[]): string {
    return Buffer.from(resources.length.toString()).toString('base64');
  }

  private decodeCursor(cursor: string): number {
    try {
      return parseInt(Buffer.from(cursor, 'base64').toString(), 10) || 0;
    } catch {
      return 0;
    }
  }
}

interface ResourceDefinition {
  uri: string;
  name: string;
  description: string;
  mimeType: string;
  handler: () => Promise<any>;
}

interface ResourceContent {
  mimeType: string;
  content: string;
}
```

## Integration with Multi-Agent System

### 1. MCP Bridge for Agent Communication

```typescript
// apps/agents/src/integration/mcp-bridge-enhanced.ts

export class EnhancedMCPBridge extends MCPBridge {
  private mcpServer: EnhancedMCPServer;
  private toolRegistry: EnhancedToolRegistry;
  private agentOrchestrator: LangGraphSupervisor;

  constructor(connectionManager: MCPConnectionManager, agentOrchestrator: LangGraphSupervisor) {
    super(connectionManager);
    this.agentOrchestrator = agentOrchestrator;
    this.initializeMCPServer();
  }

  async callTool(toolName: string, parameters: any, context: AgentContext): Promise<any> {
    // Enhanced tool calling with agent context
    const tool = await this.toolRegistry.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    // Add agent context to tool parameters
    const enhancedParameters = {
      ...parameters,
      _agentContext: {
        agentId: context.agentId,
        sessionId: context.sessionId,
        userId: context.userId,
        organizationId: context.organizationId,
        timestamp: new Date().toISOString(),
      },
    };

    // Execute tool through MCP server
    const request: CallToolRequest = {
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: enhancedParameters,
      },
    };

    const response = await this.mcpServer.handleCallTool(request);

    // Update tool metrics
    await this.updateToolMetrics(toolName, context, response);

    return response;
  }

  async getAvailableTools(category?: string): Promise<ToolDefinition[]> {
    const request: ListToolsRequest = { method: 'tools/list' };
    const response = await this.mcpServer.handleListTools(request);

    return response.tools.filter(tool => !category || tool.category === category);
  }

  private async updateToolMetrics(
    toolName: string,
    context: AgentContext,
    response: CallToolResponse
  ): Promise<void> {
    // Update usage statistics
    await this.toolRegistry.recordToolUsage(toolName, {
      agentId: context.agentId,
      executionTime: response.executionTime,
      success: !response.error,
      timestamp: new Date(),
    });
  }

  private initializeMCPServer(): void {
    this.mcpServer = new EnhancedMCPServer({
      name: 'AskInfosec MCP Server',
      version: '2.0.0',
      logLevel: 'info',
    });

    this.toolRegistry = new EnhancedToolRegistry();

    // Register existing tools with enhanced metadata
    this.registerExistingTools();
  }

  private async registerExistingTools(): Promise<void> {
    // Register database tools
    await this.toolRegistry.registerTool({
      name: 'get-all-documents-tool',
      description: 'Retrieve all documents for an organization',
      category: 'database',
      priority: 10,
      inputSchema: {
        type: 'object',
        properties: {
          organizationId: { type: 'string' },
          userId: { type: 'string' },
        },
        required: ['organizationId'],
      },
      performance: {
        averageExecutionTime: 500,
        successRate: 0.99,
        lastExecuted: new Date(),
      },
      usage: {
        totalCalls: 0,
        uniqueUsers: 0,
        errorCount: 0,
      },
      metadata: {
        version: '2.0.0',
        author: 'AskInfosec Team',
        tags: ['database', 'documents', 'retrieval'],
        documentation: 'https://docs.askinfosec.com/tools/get-all-documents',
      },
    });

    await this.toolRegistry.registerTool({
      name: 'query-database-tool',
      description: 'Execute SQL-like queries on the document database',
      category: 'database',
      priority: 9,
      inputSchema: {
        type: 'object',
        properties: {
          query: { type: 'string' },
          organizationId: { type: 'string' },
        },
        required: ['query', 'organizationId'],
      },
      performance: {
        averageExecutionTime: 300,
        successRate: 0.95,
        lastExecuted: new Date(),
      },
      usage: {
        totalCalls: 0,
        uniqueUsers: 0,
        errorCount: 0,
      },
      metadata: {
        version: '2.0.0',
        author: 'AskInfosec Team',
        tags: ['database', 'query', 'search'],
        documentation: 'https://docs.askinfosec.com/tools/query-database',
      },
    });
  }
}
```

## Testing MCP Compliance

### 1. Protocol Compliance Tests

```typescript
// packages/mcp-tools/test/mcp-compliance.test.ts

describe('MCP Protocol Compliance', () => {
  let mcpServer: EnhancedMCPServer;

  beforeEach(() => {
    mcpServer = new EnhancedMCPServer({
      name: 'Test Server',
      version: '0.0.1',
    });
  });

  describe('Initialization', () => {
    it('should handle initialize request correctly', async () => {
      const request: InitializeRequest = {
        method: 'initialize',
        params: {
          protocolversion: '0.0.1',
          capabilities: {},
          clientInfo: {
            name: 'Test Client',
            version: '0.0.1',
          },
        },
      };

      const response = await mcpServer.handleInitialize(request);

      expect(response.protocolVersion).toBe('1.0.0');
      expect(response.serverInfo.name).toBe('AskInfosec MCP Server');
      expect(response.capabilities).toBeDefined();
    });

    it('should reject unsupported protocol versions', async () => {
      const request: InitializeRequest = {
        method: 'initialize',
        params: {
          protocolVersion: '2.0.0',
          capabilities: {},
          clientInfo: { name: 'Test', version: '0.0.1' },
        },
      };

      await expect(mcpServer.handleInitialize(request)).rejects.toThrow(
        'Unsupported protocol version'
      );
    });
  });

  describe('Tool Management', () => {
    it('should list tools correctly', async () => {
      const request: ListToolsRequest = { method: 'tools/list' };
      const response = await mcpServer.handleListTools(request);

      expect(Array.isArray(response.tools)).toBe(true);
      expect(response.tools.length).toBeGreaterThan(0);

      // Validate tool structure
      response.tools.forEach(tool => {
        expect(tool.name).toBeDefined();
        expect(tool.description).toBeDefined();
        expect(tool.inputSchema).toBeDefined();
      });
    });

    it('should execute tools correctly', async () => {
      const request: CallToolRequest = {
        method: 'tools/call',
        params: {
          name: 'get-all-documents-tool',
          arguments: {
            organizationId: 'test-org',
            userId: 'test-user',
          },
        },
      };

      const response = await mcpServer.handleCallTool(request);

      expect(response.content).toBeDefined();
      expect(Array.isArray(response.content)).toBe(true);
      expect(response.content.length).toBeGreaterThan(0);
    });

    it('should handle invalid tool calls', async () => {
      const request: CallToolRequest = {
        method: 'tools/call',
        params: {
          name: 'non-existent-tool',
          arguments: {},
        },
      };

      await expect(mcpServer.handleCallTool(request)).rejects.toThrow('Tool not found');
    });
  });

  describe('Resource Management', () => {
    it('should list resources correctly', async () => {
      const request: ListResourcesRequest = { method: 'resources/list' };
      const response = await mcpServer.handleListResources(request);

      expect(Array.isArray(response.resources)).toBe(true);

      // Validate resource structure
      response.resources.forEach(resource => {
        expect(resource.uri).toBeDefined();
        expect(resource.name).toBeDefined();
        expect(resource.description).toBeDefined();
        expect(resource.mimeType).toBeDefined();
      });
    });

    it('should read resources correctly', async () => {
      const request: ReadResourceRequest = {
        method: 'resources/read',
        params: {
          uri: 'askinfosec://documents',
        },
      };

      const response = await mcpServer.handleReadResource(request);

      expect(response.contents).toBeDefined();
      expect(Array.isArray(response.contents)).toBe(true);
      expect(response.contents.length).toBeGreaterThan(0);
    });
  });
});
```

### 2. Integration Tests

```typescript
describe('MCP Integration with Multi-Agent System', () => {
  let mcpBridge: EnhancedMCPBridge;
  let agentOrchestrator: LangGraphSupervisor;

  beforeEach(() => {
    agentOrchestrator = new LangGraphSupervisor();
    mcpBridge = new EnhancedMCPBridge(connectionManager, agentOrchestrator);
  });

  it('should integrate MCP tools with agent workflows', async () => {
    const workflowInput: WorkflowInput = {
      sessionId: 'test-session',
      userInput: 'Get all documents',
      organizationId: 'test-org',
      userId: 'test-user',
      metadata: {},
    };

    const result = await agentOrchestrator.executeWorkflow(workflowInput);

    expect(result.response).toBeDefined();
    expect(result.toolsUsed).toContain('get-all-documents-tool');
    expect(result.mcpCompliant).toBe(true);
  });

  it('should handle MCP tool failures gracefully', async () => {
    // Test with invalid parameters
    const context: AgentContext = {
      agentId: 'test-agent',
      sessionId: 'test-session',
      userId: 'test-user',
      organizationId: 'invalid-org',
    };

    await expect(mcpBridge.callTool('get-all-documents-tool', {}, context)).rejects.toThrow();
  });
});
```

## Deployment and Monitoring

### 1. MCP Server Deployment

```yaml
# docker-compose.mcp.yml
version: '3.8'
services:
  mcp-server:
    build:
      context: .
      dockerfile: packages/mcp-tools/Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - MCP_LOG_LEVEL=info
      - MCP_MAX_CONNECTIONS=100
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3
```

### 2. Monitoring and Metrics

```typescript
interface MCPMetrics {
  connections: {
    active: number;
    total: number;
    failed: number;
  };
  tools: {
    totalCalls: number;
    successRate: number;
    averageExecutionTime: number;
    errorCount: number;
  };
  resources: {
    totalReads: number;
    cacheHitRate: number;
    averageReadTime: number;
  };
  protocol: {
    messagesSent: number;
    messagesReceived: number;
    protocolErrors: number;
  };
}
```

## Conclusion

This implementation ensures full MCP v1.0 compliance while seamlessly integrating with the multi-agent RAG system. Key benefits:

1. **Standards Compliance**: Full adherence to MCP specification
2. **Enhanced Capabilities**: Advanced tool and resource management
3. **Performance Monitoring**: Comprehensive metrics and observability
4. **Scalability**: Support for high-volume tool usage
5. **Security**: Proper validation and error handling
6. **Interoperability**: Compatible with other MCP-compliant systems

The implementation maintains backward compatibility while providing a foundation for future MCP protocol enhancements.

---

**Document Version**: 1.0  
**Created**: June 27, 2025  
**MCP Version**: 1.0.0  
**Review Cycle**: Monthly for protocol updates
