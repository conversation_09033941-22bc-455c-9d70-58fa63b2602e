# AI Agent System Technical Specification

**Document Version:** 1.3  
**Date:** 2025-08-08  
**Status:** Current (Unified RAG + Central Confidence Gate)

## Table of Contents

1. Executive Summary
2. Architecture Overview
3. API Layer (Ingress)
4. Unified RAG Pipeline
5. Response Strategies (Delivery)
6. Streaming Model and Events
7. Types and Config
8. Observability
9. Security & Performance

## 1. Executive Summary

- **Unified RAG Core**: Shared pipeline implemented in `UnifiedRAGExecutor` for both single-shot and streaming.
- **Central Confidence Gate**: Implemented in `UnifiedResponderImpl`; gates based on semantic search `averageScore`. Low-confidence returns a fallback without calling the LLM.
- **LLM Integration**: OpenAI `gpt-4o-mini` via LangChain. Prompts loaded dynamically from Langfuse through `langfusePromptAdapter`.
- **Streaming**: Clean content-only streaming (no confidence tokens); chunk types: `stream_started`, `content`, `complete`, `error`.
- **Memory**: Session-scoped (last 10 entries) when `sessionId` exists.
- **Search**: Redis-backed vector index via `@anter/mcp-tools` (`SemanticSearchServiceEnhanced`). No runtime document embedding in this flow.

## 2. Architecture Overview

- `AskAIAgent` orchestrates request handling and chooses response mode (`single-shot` or `streaming`).
- `UnifiedRAGExecutor` handles: conversation history, user input storage, semantic search, and unified context assembly.
- `ResponseStrategyFactory` returns delivery adapters that delegate to `UnifiedResponderImpl`.
- `UnifiedResponderImpl` performs the confidence gate, builds context, and invokes `ChatGenerationService` or `StreamingChatGenerationService`.
- Prompt configuration from Langfuse; observability via Langfuse spans.

## 3. API Layer (Ingress)

- Endpoints:
  - POST `/api/v1/external/agent/invoke` (single-shot)
  - POST `/api/v1/external/agent/stream` (SSE)
- Auth: `x-api-key` header.
- Streaming delivery: SSE events (`content` and `complete`).

## 4. Unified RAG Pipeline

`UnifiedRAGExecutor.execute(input, sessionId, config, traceId)`:

- Load conversation history (last 10) if `sessionId`.
- Store user input if `sessionId`.
- Execute semantic search against Redis vector index with `topK`.
- Compute `averageScore`, `relevanceQuality`, `enrichedContext`.
- Build `UnifiedRAGContext` with execution metadata.

Unified search output shape:

```ts
interface UnifiedSemanticSearchResult {
  averageScore: number;
  enrichedContext: string;
  metadata: {
    resultsCount: number;
    relevanceQuality: 'high' | 'medium' | 'low';
    syncPerformed: boolean;
    queries: string[];
    executionTime: number;
  };
}
```

## 5. Response Strategies (Delivery)

- Factory: `ResponseStrategyFactory.createStrategy('single-shot' | 'streaming', promptConfigParams)`.
- Core: `UnifiedResponderImpl` with central confidence gate:
  - Low-confidence if no results or `averageScore < 0.6` → fallback text.
  - Otherwise, proceed.
- Single-shot: `ChatGenerationService` builds messages: system prompt (Langfuse) + optional history + `{context}` + `{query}` → OpenAI synchronous call.
- Streaming: `StreamingChatGenerationService` emits chunks from OpenAI stream; performance tracked.

Fallback text template:

```text
I might need a bit more detail to be precise.

<has/has not> enough relevant context...

Could you clarify the exact policy, framework, or context you want me to focus on?
```

## 6. Streaming Model and Events

Types in `streaming-types.ts`:

```ts
type StreamingChunkType = 'stream_started' | 'confidence' | 'content' | 'complete' | 'error';
interface StreamingChunk {
  type: StreamingChunkType;
  content: string;
  metadata?: Record<string, any>;
}
```

Events used currently:

- `stream_started` → initial handshake
- `content` → incremental tokens
- `complete` → final stats
- `error` → structured failure

Note: `confidence` exists in the type but is not emitted—confidence is handled by the central gate before streaming.

## 7. Types and Config

Key execution config (`UnifiedRAGConfig`):

```ts
interface UnifiedRAGConfig {
  confidenceThreshold: number;
  topK: number;
  useExpansion: boolean;
  promptType: 'text' | 'chat';
  fallbackMessage: string;
  streamingTimeout?: number;
  maxRetries?: number;
  chunkTimeout?: number;
}
```

Response options (`ResponseOptions`):

```ts
interface ResponseOptions {
  streaming?: boolean;
  maxTokens?: number;
  temperature?: number;
  topK?: number;
  model?: string;
  promptType?: 'text' | 'chat';
  streamingTimeout?: number;
  maxRetries?: number;
  chunkTimeout?: number;
}
```

Prompt config acquisition:

```ts
interface PromptConfigParams {
  organizationId: string;
  promptName: string; // e.g., process.env.LANGFUSE_DEFAULT_PROMPT || 'system-default'
  version: number;
  projectId?: string;
}
```

## 8. Observability

Langfuse spans used:

- `ask_ai_agent_execution` / `ask_ai_agent_streaming`
- `unified_rag_execution`
- `semantic_search_execution`
- `chat_generation`
- `streaming_chat_generation`
- `actual_streaming_generation`

Streaming metrics: `totalChunks`, `totalTimeMs`, `averageChunkTimeMs`, `maxChunkTimeMs`, `minChunkTimeMs`, `chunksPerSecond`, `streamingEfficiency`.

## 9. Security & Performance

- API key auth + rate limiting.
- Input validation and robust prompt composition.
- Search-only at runtime; embeddings assumed pre-indexed in Redis via `@anter/mcp-tools`.
- Memory bounded to last 10 messages per session.
- Configurable timeouts and retries for streaming.
