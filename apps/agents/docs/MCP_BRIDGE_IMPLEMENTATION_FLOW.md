# MCP Bridge Implementation Flow Documentation

## Overview

This document explains how the Agent MCP Bridge hooks into actual database and tool implementations, tracing the complete call flow from agent request to database execution.

## Architecture Components

### 1. Interface Definitions (`mcp-bridge.ts`)

```typescript
export interface MCPServerInterface {
  handleJsonRpcMessage(sessionId: string, message: any): Promise<any>;
}

export interface MCPSessionManagerInterface {
  validateSession(sessionId: string): Promise<MCPSession | null>;
  createJWTSession(payload: JWTPayload): Promise<MCPSession>;
}
```

### 2. Connection Manager Implementation (`connection-manager.ts`)

The `MCPConnectionManager` class provides the concrete implementations of these interfaces:

#### A. In-Process MCP Server Implementation

**Location:** `apps/agents/src/integration/connection-manager.ts` (lines 53-212)

```typescript
const realMCPServer = {
  async handleJsonRpcMessage(sessionId: string, message: any): Promise<any> {
    // Tool-specific implementations
    if (message.method === 'tools/call' && message.params?.name === 'query_database') {
      // Direct database execution with tenant isolation
      const db = await self.databaseProvider.dbWithTenant(orgId);
      const result = await db.execute(query);
      // Return JSON-RPC formatted response
    }

    if (message.method === 'tools/call' && message.params?.name === 'get_all_documents') {
      // Document retrieval with raw SQL
      const db = await self.databaseProvider.dbWithTenant(orgId);
      const docsResult = await db.execute(documentQuery);
      // Return formatted document data
    }
  },
};
```

#### B. Mock Session Manager Implementation

**Location:** `apps/agents/src/integration/connection-manager.ts` (lines 240-262)

```typescript
const mockSessionManager = {
  async validateSession(sessionId: string): Promise<MCPSession | null> {
    // Simple validation for agent context
    if (sessionId && sessionId.startsWith('session_')) {
      return mockSessionObject;
    }
    return null;
  },

  async createJWTSession(payload: any): Promise<MCPSession> {
    // Generate session for organization
    return {
      id: `session_${Date.now()}_${payload.organization_id}`,
      organizationId: payload.organization_id,
      // ...
    };
  },
};
```

### 3. Database Provider Integration

#### A. Interface Definition

**Location:** `apps/agents/src/integration/connection-manager.ts` (lines 4-7)

```typescript
interface DatabaseProvider {
  dbWithTenant(orgId: string): Promise<any>;
  log: Logger;
}
```

#### B. Actual Implementation Hook

**Location:** `apps/agents/src/agents/registry.ts` (line 28)

```typescript
// The 'fastify' instance passed here IS the database provider
const connectionManager = new MCPConnectionManager(fastify);
```

**The Fastify instance provides:**

- `fastify.dbWithTenant(orgId)` - Database connection with Row Level Security
- `fastify.log` - Logging interface

#### C. Database Implementation

**Location:** `apps/api/src/lib/drizzle/drizzle.ts` (lines 28-34)

```typescript
fastify.decorate('dbWithTenant', async function (orgId: string) {
  // Set PostgreSQL session variables for RLS
  await pool.query(`SELECT set_config('app.current_organization_id', $1, TRUE)`, [orgId]);
  await pool.query(`SELECT set_config('app.bypass_rls', 'off', TRUE)`);
  return db; // Returns Drizzle ORM instance
});
```

## Complete Call Flow

### Agent Request to Database Execution

```
1. Agent makes request
   ↓
2. DocumentService.retrieveAllDocuments()
   → calls connectionManager.executeToolCall('get_all_documents', args)
   ↓
3. MCPConnectionManager.executeToolCall()
   → calls bridge.callToolDirect(sessionId, toolName, args)
   ↓
4. AgentMCPBridge.callToolDirect()
   → creates JSON-RPC message
   → calls mcpServer.handleJsonRpcMessage(sessionId, jsonRpcMessage)
   ↓
5. realMCPServer.handleJsonRpcMessage() [IN-PROCESS IMPLEMENTATION]
   → extracts organization ID from session
   → calls databaseProvider.dbWithTenant(orgId)
   ↓
6. fastify.dbWithTenant(orgId) [ACTUAL DATABASE CONNECTION]
   → sets PostgreSQL session variables for RLS
   → returns Drizzle ORM instance
   ↓
7. db.execute(query) [REAL DATABASE EXECUTION]
   → executes SQL against PostgreSQL with tenant isolation
   → returns query results
   ↓
8. Response flows back through the chain
   → JSON-RPC response → bridge result → agent processing
```

## Key Implementation Details

### 1. Tenant Isolation

The database connection uses PostgreSQL Row Level Security (RLS):

```sql
-- Set for each request
SELECT set_config('app.current_organization_id', $1, TRUE);
SELECT set_config('app.bypass_rls', 'off', TRUE);
```

### 2. Two Separate MCP Implementations

| Component            | Location                          | Purpose                                   | Database Access                     |
| -------------------- | --------------------------------- | ----------------------------------------- | ----------------------------------- |
| **Agent MCP Bridge** | `connection-manager.ts`           | In-process tool execution for agents      | Direct via `fastify.dbWithTenant()` |
| **API MCP Server**   | `apps/api/src/core/mcp/server.ts` | WebSocket MCP server for external clients | Via `MCPTools` class                |

### 3. Session Management

- **Agent Side:** Mock sessions for internal tool calls
- **API Side:** Full JWT-based session validation
- **Bridge Purpose:** Abstracts session complexity for agents

### 4. Tool Implementation Differences

| Tool                | Agent Implementation               | API Implementation                                    |
| ------------------- | ---------------------------------- | ----------------------------------------------------- |
| `get_all_documents` | Raw SQL in `connection-manager.ts` | `MCPTools.getAllDocuments()` with `DocumentProcessor` |
| `query_database`    | Direct SQL execution               | Validated through `MCPTools.queryDatabase()`          |

## Registration and Initialization

### 1. Agent System Startup

**Location:** `apps/agents/src/agents/registry.ts`

```typescript
export function registerBuiltInAgents(fastify?: any): void {
  if (fastify) {
    AgentFactory.register('ask_ai', async _config => {
      // fastify instance provides database access
      const connectionManager = new MCPConnectionManager(fastify);
      const agent = new AskAIAgent(connectionManager);
      return agent;
    });
  }
}
```

### 2. Database Plugin Registration

**Location:** `apps/api/src/lib/drizzle/drizzle.ts`

```typescript
// Registers dbWithTenant decorator on Fastify instance
const drizzlePlugin: FastifyPluginAsync = async fastify => {
  fastify.decorate('dbWithTenant', async function (orgId: string) {
    // Implementation
  });
};
```

## Summary

The MCP Bridge hooks to actual implementations through:

1. **Dependency Injection:** Fastify instance passed as `DatabaseProvider`
2. **Interface Abstraction:** `MCPServerInterface` implemented in-process
3. **Direct Database Access:** `fastify.dbWithTenant()` provides real PostgreSQL connections
4. **Tenant Isolation:** RLS enforced at the database session level
5. **Tool Routing:** JSON-RPC messages routed to specific tool implementations

This design allows agents to execute database operations through a clean MCP interface while maintaining security, tenant isolation, and performance through direct database connections.
