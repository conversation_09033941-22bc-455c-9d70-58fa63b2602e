# AskAI Agent Architecture Diagram

## Modular Architecture Overview

This diagram shows the refactored AskAI agent architecture with specialized services:

```mermaid
graph TD
    A["AskAIAgent<br/>(Orchestrator)<br/>~250 lines"] --> B["DocumentService<br/>• Document retrieval<br/>• MCP integration<br/>• Caching & cleanup"]
    A --> C["EmbeddingService<br/>• OpenAI embeddings<br/>• Vector similarity<br/>• Embedding cache"]
    A --> D["SemanticSearchService<br/>• Document ranking<br/>• Top-k selection<br/>• Fallback handling"]
    A --> E["PromptBuilderService<br/>• RAG prompt construction<br/>• Token management<br/>• Context optimization"]
    A --> F["MetricsService<br/>• Performance tracking<br/>• RAG metrics<br/>• Success rates"]

    B --> G["types.ts<br/>Shared interfaces"]
    C --> G
    D --> G
    E --> G
    F --> G

    A --> H["Chat Agent<br/>(<PERSON><PERSON><PERSON><PERSON>)"]
    A --> I["MCP Connection<br/>Manager"]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#f3e5f5
    style G fill:#fff3e0
```

## Key Components

- **AskAIAgent**: Slim orchestrator that coordinates between services
- **DocumentService**: Handles document retrieval and caching
- **EmbeddingService**: Manages OpenAI embeddings and similarity calculations
- **SemanticSearchService**: Performs document ranking and search
- **PromptBuilderService**: Constructs RAG prompts with token management
- **MetricsService**: Tracks performance and metrics
- **types.ts**: Shared type definitions across all services

## Benefits

- **Separation of Concerns**: Each service has a single responsibility
- **Testability**: Services can be mocked and tested independently
- **Maintainability**: Clean, modular architecture
- **Reusability**: Services can be used by other components
- **Reliability** (2025-06): Cycle-safe workflow and resource limits prevent crashes under load

# AskAI Agent V2 - LangGraph Supervisor Integration

## Overview

The diagram and notes below reflect the June 2025 update where `AskAIAgentV2` now routes complex
queries to a **cycle-safe** LangGraph workflow powered by `LangGraphSupervisor`.

Key highlights:

1. `completedSteps` loop-protection eliminates previous infinite-loop issues.
2. `WorkflowResourceManager` enforces concurrency, runtime and step limits.
3. Automatic fallback to classic RAG when LangGraph is disabled or fails.

## Corrected Architecture Flow

### Before (Broken)

- `AskAIAgentV2` → `LangGraphIntegration` → **Simulated Workflow** → Response
- The `LangGraphSupervisor` existed but was never used
- Workflow execution was simulated rather than using real LangGraph patterns

### After (Fixed)

- `AskAIAgentV2` → `LangGraphIntegration` → `LangGraphSupervisor` → **Real Multi-Agent Workflow** → Response

## Complete Execution Flow

```mermaid
graph TD
    A[User Request] --> B[AskAIAgentV2.doInvoke]
    B --> C{LangGraphIntegration.isEnabled?}

    C -->|Yes| D[LangGraphIntegration.executeWorkflow]
    C -->|No| E[Traditional RAG Pipeline]

    D --> F[WorkflowResourceManager.acquireSlot]
    F --> G[executeWorkflowWithResource]
    G --> H[LangGraphSupervisor.executeWorkflow]

    H --> I[StateGraph Workflow]
    I --> J[Supervisor Node - Router]

    J --> K{Routing Decision}
    K -->|No Documents| L[Search Agent]
    K -->|Need Analysis| M[Analysis Agent]
    K -->|Ready for Response| N[Chat Agent]
    K -->|Complete| O[END]

    L --> L1[DocumentService.retrieveAllDocuments]
    L1 --> L2[SemanticSearchService.indexDocuments]
    L2 --> L3[SemanticSearchService.search]
    L3 --> L4[Return to Supervisor]
    L4 --> J

    M --> M1[Document Analysis]
    M1 --> M2[Generate Analysis Metadata]
    M2 --> M3[Return to Supervisor]
    M3 --> J

    N --> N1[Build Context from Search Results]
    N1 --> N2[ChatAgent.invoke]
    N2 --> N3[Generate Final Response]
    N3 --> N4[Return to Supervisor]
    N4 --> J

    O --> P[Extract AI Response]
    P --> Q[Return WorkflowResult]
    Q --> R[Transform to AgentResult]
    R --> S[Final Response to User]

    E --> T[Traditional DocumentService.retrieveAllDocuments]
    T --> U[SemanticSearchService.search]
    U --> V[ChatAgent.invoke]
    V --> W[Traditional Response]

    subgraph "LangGraph Multi-Agent Workflow"
        I
        J
        K
        L
        M
        N
        O
        L1
        L2
        L3
        L4
        M1
        M2
        M3
        N1
        N2
        N3
        N4
    end

    style H fill:#e1f5fe
    style I fill:#f3e5f5
    style J fill:#e8f5e8
    style L fill:#fff3e0
    style M fill:#fff3e0
    style N fill:#fff3e0
    style Q fill:#c8e6c9
```

## Detailed Step-by-Step Process

### 1. Agent Invocation

```typescript
// User calls AskAIAgentV2
const result = await agent.invoke({
  input: 'What are the best cybersecurity practices?',
  organizationId: 'my-org',
  userId: 'user-123',
});
```

### 2. LangGraph Check

```typescript
// In AskAIAgentV2.doInvoke()
if (LangGraphIntegration.isEnabled()) {
  // Use LangGraph supervisor workflow
  const workflowInput = {
    sessionId,
    input: this.extractUserInput(input),
    organizationId: input.organizationId,
    userId: input.userId,
  };
  const result = await this.langGraphIntegration.executeWorkflow(workflowInput);
}
```

### 3. Resource Management

```typescript
// In LangGraphIntegration.executeWorkflow()
resource = await this.resourceManager.acquireWorkflowSlot(workflowId);
const result = await this.executeWorkflowWithResource(input, workflowId, resource);
```

### 4. Supervisor Execution

```typescript
// In executeWorkflowWithResource()
if (!this.supervisor) {
  throw new Error('LangGraph supervisor not initialized');
}
const result = await this.supervisor.executeWorkflow(input);
```

### 5. Multi-Agent Workflow

The supervisor orchestrates multiple specialized agents:

#### Search Agent

- Retrieves documents from the organization
- Indexes documents in semantic search service
- Performs vector similarity search
- Returns relevant passages

#### Analysis Agent (Optional)

- Analyzes retrieved documents
- Generates summary statistics
- Provides metadata about document relevance

#### Chat Agent

- Builds context from search results
- Generates final response using LLM
- Returns AI-generated answer

### 6. Response Transformation

```typescript
// Transform supervisor result to AgentResult format
return {
  output: {
    response: result.output,
    contextDocIds: result.contextDocIds || [],
    embeddingCalls: result.embeddingCalls || 0,
    documentCount: result.documentCount || 0,
    conversationEntries: 0,
  } as AskAIOutput,
  metadata: {
    ...result.metadata,
    latencyMs: result.executionTime,
  },
};
```

## Key Integrations

### 1. Initialization

```typescript
// In AskAIAgentV2 constructor
this.langGraphIntegration = new LangGraphIntegration({
  enableDebug: process.env.NODE_ENV === 'development',
  maxConcurrentWorkflows: 5,
  maxWorkflowDuration: 30000,
  maxStepsPerWorkflow: 10,
  fallbackEnabled: true,
});

// Initialize the supervisor with connection manager
this.langGraphIntegration.initializeSupervisor(this.connectionManager);
```

### 2. Service Dependencies

The supervisor uses these services through dependency injection:

- **MCPConnectionManager**: Database and tool access
- **DocumentServiceV2**: Document retrieval and processing
- **SemanticSearchService**: Vector-based similarity search
- **EmbeddingService**: Text-to-vector conversion
- **ChatAgent**: LLM-powered response generation

### 3. State Management

The LangGraph workflow maintains state across agent transitions:

```typescript
type SupervisorState = {
  messages: BaseMessage[];
  sessionId: string;
  organizationId: string;
  userId?: string;
  documents: any[];
  searchResults: any[];
  analysis?: any;
  currentStep: string;
  metadata: Record<string, any>;
};
```

## Error Handling & Fallbacks

### 1. LangGraph Disabled

If LangGraph is disabled, the agent falls back to traditional RAG pipeline:

```typescript
else {
  console.log('[AskAIAgentV2] LangGraph integration disabled, using traditional RAG pipeline');
  // Traditional workflow...
}
```

### 2. LangGraph Execution Failure

If LangGraph fails, it gracefully falls back:

```typescript
catch (error) {
  console.warn('[AskAIAgentV2] LangGraph execution failed, falling back to traditional RAG:', error);
  // Continue to traditional execution
}
```

### 3. Supervisor Fallback

The supervisor itself has fallback mechanisms:

```typescript
if (this.config.fallbackEnabled) {
  return this.createFallbackResult(input, error as Error);
}
```

## Performance Features

- **Concurrency Control**: Resource manager limits concurrent workflows
- **Timeout Protection**: Maximum execution time enforcement
- **Resource Cleanup**: Automatic cleanup of workflow resources
- **Metrics Tracking**: Comprehensive performance monitoring
- **Debug Logging**: Detailed execution tracing when enabled

## Benefits of This Architecture

1. **True Multi-Agent Coordination**: Real supervisor pattern with agent handoffs
2. **Scalable Resource Management**: Production-grade concurrency control
3. **Graceful Degradation**: Multiple fallback levels for reliability
4. **Enhanced Observability**: Comprehensive logging and metrics
5. **Flexible Routing**: Intelligent decision-making based on context
6. **Specialized Agents**: Each agent has a specific responsibility

This implementation now provides a robust, production-ready multi-agent RAG system that properly leverages LangGraph's supervisor architecture while maintaining backward compatibility with traditional RAG workflows.

**Last Updated:** 2025-07-01
