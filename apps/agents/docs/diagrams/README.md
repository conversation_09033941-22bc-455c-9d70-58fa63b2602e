# Project Diagrams

This directory contains Mermaid diagrams for visualizing the project architecture and workflows.

## Standards for Diagrams

### 1. **When to Create Diagrams**

- Major architectural changes or refactoring
- New feature implementations with complex workflows
- System integration points
- API flow documentation
- Database schema changes

### 2. **Naming Convention**

- Use kebab-case: `component-name-type.md`
- Examples:
  - `askai-agent-architecture.md`
  - `mcp-integration-flow.md`
  - `rag-pipeline-workflow.md`

### 3. **Diagram Types to Use**

#### **Architecture Diagrams**

```mermaid
graph TD
    A[Component] --> B[Service]
    style A fill:#e1f5fe
```

#### **Sequence Diagrams**

```mermaid
sequenceDiagram
    participant User
    participant Agent
    participant Service
    User->>Agent: Request
    Agent->>Service: Process
    Service-->>Agent: Response
    Agent-->>User: Result
```

#### **Flowcharts**

```mermaid
flowchart TD
    Start --> Decision{Condition?}
    Decision -->|Yes| Action1
    Decision -->|No| Action2
```

### 4. **Color Scheme**

Use consistent colors across all diagrams:

- **Primary Components**: `#e1f5fe` (light blue)
- **Services**: `#f3e5f5` (light purple)
- **Shared/Common**: `#fff3e0` (light orange)
- **External**: `#e8f5e8` (light green)
- **Data/Storage**: `#fce4ec` (light pink)

### 5. **Required Sections in Diagram Files**

1. **Title and Description**
2. **Mermaid Code Block**
3. **Key Components Explanation**
4. **Benefits/Purpose**
5. **Last Updated Date**

> **Tip (2025-07)**: Keep the _Last Updated_ line current whenever code changes affect the underlying architecture. This helps reviewers quickly verify documentation freshness.

## Tools for Viewing/Editing

### **Development**

- **VS Code**: Install "Mermaid Preview" extension
- **GitHub**: Automatic rendering in markdown files
- **Mermaid Live Editor**: https://mermaid.live/ (for quick edits)

### **Exporting**

- **PNG/SVG**: Use Mermaid Live Editor
- **PDF**: Use browser print-to-PDF from rendered diagram

## Template

Copy this template for new diagrams:

````markdown
# [Component Name] [Diagram Type]

## Overview

Brief description of what this diagram shows.

## Diagram

```mermaid
graph TD
    A[Component] --> B[Service]
    style A fill:#e1f5fe
```
````

```

## Key Components

- **Component**: Description
- **Service**: Description

## Benefits/Purpose

- Why this diagram is useful
- What it helps understand

## Last Updated

YYYY-MM-DD

```

## Maintenance

- Update diagrams when architecture changes
- Review diagrams during code reviews
- Archive outdated diagrams in `archived/` folder

**Last Updated:** 2025-07-01
