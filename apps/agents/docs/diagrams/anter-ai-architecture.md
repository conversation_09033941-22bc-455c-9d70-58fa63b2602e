# Anter AI Multi-Agent System Architecture

## System Overview

The Anter AI Multi-Agent System is a sophisticated, production-ready architecture that combines intelligent workflow orchestration, specialized domain agents, and a comprehensive MCP (Model Context Protocol) integration layer. This system represents the evolution from simple AI assistance to intelligent, context-aware, multi-agent coordination.

## Core Architecture Principles

- **Intelligence-First Design**: Every request flows through the Intelligence Layer for intent analysis and optimal routing
- **Domain Specialization**: Dedicated agents for specific domains (Document Analysis, Search, etc.)
- **Dynamic Workflow Selection**: Multiple workflow types (Simple, Multi-Agent, ReAct) with intelligent selection
- **Cycle-Safe Orchestration**: `LangGraphSupervisor` now tracks `completedSteps` to guarantee each agent runs at most once per workflow – eliminating infinite loops and recursion limit errors
- **Intelligent Workflow Selection**: Multiple workflow types (Simple, Multi-Agent, ReAct) with on-the-fly choice based on query complexity
- **Unified MCP Integration**: Consolidated tool management through @anter/mcp-tools package
- **Advanced Prompt Engineering**: Context-aware prompt building with domain role adaptation
- **Production-Ready Infrastructure**: Session pooling, connection management, and comprehensive monitoring

## 1. API Layer

### FastifyJS API Server

- **Location**: `apps/api/src/app.ts`
- **Features**:
  - High-performance HTTP server with built-in validation
  - JWT authentication with organization-based multi-tenancy
  - Comprehensive middleware stack for security and logging
  - OpenAPI documentation and testing support

### API-Agents MCP Bridge

- **Location**: `apps/api/src/core/mcp/agents-bridge.ts`
- **Purpose**: Seamless integration between API infrastructure and agents
- **Features**:
  - Session format conversion (API ↔ Agents)
  - Connection pooling delegation
  - Authentication bridging
  - Error handling and monitoring

## 2. Orchestration Layer

### LangGraph Supervisor

- **Location**: `apps/agents/src/orchestration/langgraph/supervisor.ts`
- **Role**: Central orchestrator for all multi-agent workflows with built-in loop-protection
- **Key Features (2025-06 update)**:
  - **Three Workflow Types**:
    1. **Simple Workflow**: Intent → Tool Selection → Execution → Response
    2. **Multi-Agent Workflow**: Intent → Agent Selection → Delegation → Aggregation
    3. **ReAct Workflow**: Think → Act → Observe cycles with complex reasoning
  - **Intelligent Workflow Selection**: Based on query complexity and learning patterns
  - **Dynamic Agent Instantiation**: Creates and manages specialized agents on-demand
  - **State Management**: Maintains workflow state across execution steps
  - **Loop Protection**: Uses `completedSteps` array in workflow state to prevent agent recursion
  - **Resource Manager Integration**: Works with `WorkflowResourceManager` to enforce concurrency, time and step limits
  - **Performance Monitoring**: Comprehensive execution metrics and debugging

### Workflow Engine

- **Location**: `apps/agents/src/orchestration/langgraph/workflow-engine.ts`
- **Capabilities**:
  - Workflow graph execution with conditional edges
  - Concurrency & timeout enforcement
  - Error recovery and retry mechanisms
  - Performance monitoring and optimization
  - Parallel execution support

### State Manager

- **Location**: `apps/agents/src/orchestration/langgraph/state-manager.ts`
- **Features**:
  - Workflow state persistence and retrieval
  - Session management integration
  - Memory optimization with automatic cleanup
  - Debug information tracking

## 3. Intelligence Layer

The Intelligence Layer is the brain of the system, providing advanced analysis and decision-making capabilities that enable intelligent routing and tool selection.

### Intelligence Service

- **Location**: `apps/agents/src/intelligence/index.ts`
- **Role**: Central coordinator combining all intelligence capabilities
- **Key Methods**:
  - `analyzeIntent()`: Deep intent classification with context awareness
  - `selectTools()`: Dynamic tool selection with dependency optimization
  - `analyzeAndSelectTools()`: Combined analysis for workflow optimization

### Intent Analyzer

- **Location**: `apps/agents/src/intelligence/intent-analyzer.ts`
- **Supported Intent Types**:
  - `retrieve`: Document retrieval operations
  - `search`: Semantic search queries
  - `analyze`: Content analysis and insights
  - `query`: Database and structured queries
  - `summarize`: Content summarization
  - `compare`: Comparative analysis
  - `explain`: Explanatory responses
  - `troubleshoot`: Problem-solving scenarios
  - `recommend`: Recommendation generation
  - `complex`: Multi-step complex operations
  - `unknown`: Fallback with graceful handling

**Key Features**:

- Pattern-based classification with confidence scoring
- Entity-aware intent detection
- Contextual analysis with user preferences
- Multi-alternative intent suggestions
- Debug information for transparency

### Entity Extractor

- **Location**: `apps/agents/src/intelligence/entity-extractor.ts`
- **Supported Entity Types**:
  - `vulnerability`: CVE identifiers, CWE numbers, CVSS scores
  - `technology`: Operating systems, databases, security tools
  - `threat`: Malware types, attack patterns, security incidents
  - `organization`: Technology companies, security organizations
  - `person`: Security researchers, vendors
  - `date_range`: Temporal references and time periods
  - `document_type`: Reports, policies, guides, procedures
  - `keyword`: General search terms and topics
  - `topic`: Security domains and specialized areas

**Advanced Features**:

- Security-focused pattern recognition
- Confidence scoring with metadata
- Multi-pattern entity matching
- Context-aware extraction

### Tool Selector

- **Location**: `apps/agents/src/intelligence/tool-selector.ts`
- **Capabilities**:
  - Dynamic tool mapping based on intent and entities
  - Dependency-aware execution planning
  - Parallel execution optimization
  - Confidence-based tool filtering
  - Performance estimation and optimization

**Tool Selection Logic**:

- Intent-to-tool mapping with conditional logic
- Entity-based parameter building
- Dependency graph construction
- Parallel execution planning
- Confidence threshold filtering

## 4. Specialized Agents

### AskAI Agent V2

- **Location**: `apps/agents/src/agents/ask-ai-v2/index.ts`
- **Evolution**: Next-generation agent using consolidated MCP tools
- **Features**:
  - Integration with @anter/mcp-tools package
  - Enhanced performance and reliability
  - Improved error handling and monitoring
  - Backward compatibility with V1 APIs

### Document Agent

- **Location**: `apps/agents/src/agents/document/index.ts`
- **Specialization**: Advanced document processing and analysis
- **Core Capabilities**:
  - **Content Analysis**: Deep document content understanding
  - **Classification**: 10+ document type categorization
  - **Metadata Extraction**: Comprehensive document metadata
  - **Security Assessment**: Security-focused document evaluation
  - **Bulk Processing**: Efficient multi-document handling

**Services Integration**:

- `DocumentAnalyzerService`: Advanced analysis capabilities
- `DocumentProcessor`: Content processing and extraction
- `DocumentSanitizerService`: Content cleaning and validation

### Search Agent

- **Location**: `apps/agents/src/agents/search/index.ts`
- **Specialization**: Intelligent search and information retrieval
- **Core Capabilities**:
  - **Semantic Search**: Context-aware document search
  - **Vector Operations**: Advanced similarity computations
  - **Query Expansion**: Intelligent query enhancement
  - **Result Ranking**: Multi-factor relevance scoring
  - **Context Awareness**: User preferences and history integration

**Services Integration**:

- `SemanticSearchService`: Core search functionality
- `VectorOperationsService`: Vector similarity and indexing
- `QueryExpansionService`: Query enhancement and optimization
- `SearchRankingService`: Advanced result ranking

### Agent Registry

- **Location**: `apps/agents/src/agents/registry.ts`
- **Purpose**: Dynamic agent discovery and capability matching
- **Features**:
  - Automatic agent registration with metadata
  - Capability-based agent selection
  - Dynamic instantiation and lifecycle management
  - Integration with Intelligence Service for optimal routing

## 5. Prompt Architecture

The Prompt Architecture represents one of the most sophisticated aspects of the system, providing intelligent, context-aware prompt construction with domain expertise adaptation. This section requires significant future improvements and represents a key area for architectural evolution.

### Prompt Builder Service

- **Location**: `apps/agents/src/services/prompt-builder.service.ts`
- **Role**: Central service for intelligent prompt construction and context management
- **Architecture**: 750+ lines of sophisticated prompt engineering logic

### Domain Role Adaptation System

The system features an advanced domain role adaptation mechanism that dynamically adjusts the AI's expertise and communication style based on query context.

**Supported Domain Roles**:

1. **Marketing Domain**

   - **Keywords**: campaign, brand, marketing, analytics, conversion, social media
   - **Tone**: Creative, persuasive, metrics-driven, brand-focused
   - **Terminology**: ROI, CTR, conversion funnel, brand awareness, customer journey
   - **Structure**: Business impact → creative solutions → metrics → testing approaches

2. **Sales Domain**

   - **Keywords**: sales, revenue, pipeline, CRM, closing, negotiation
   - **Tone**: Results-oriented, persuasive, relationship-focused, goal-driven
   - **Terminology**: Pipeline velocity, close rate, deal size, qualified leads
   - **Structure**: Revenue impact → actionable tactics → success metrics → relationship building

3. **HR & Benefits Compliance**

   - **Keywords**: employee, benefits, policy, compliance, workplace culture
   - **Tone**: Professional, empathetic, policy-focused, people-centered
   - **Terminology**: Talent acquisition, employee engagement, compliance framework
   - **Structure**: Policy reference → legal compliance → supportive guidance → process steps

4. **IT & Security Domain**

   - **Keywords**: security, infrastructure, vulnerability, incident, monitoring
   - **Tone**: Technical, precise, reliability-focused, security-first
   - **Terminology**: SLA, threat landscape, security controls, incident response
   - **Structure**: Technical specs → monitoring → reliability/security → best practices

5. **Finance Domain**

   - **Keywords**: budget, cost, revenue, financial, accounting, investment
   - **Tone**: Analytical, detail-oriented, compliance-focused, cost-conscious
   - **Terminology**: ROI, CAPEX, OPEX, budget variance, financial controls
   - **Structure**: Financial impact → cost-benefit → accounting standards → controls

6. **Legal & Compliance**

   - **Keywords**: contract, legal, regulation, privacy, audit, compliance
   - **Tone**: Precise, cautious, regulation-focused, risk-averse
   - **Terminology**: Regulatory compliance, contractual obligations, due diligence
   - **Structure**: Regulation reference → compliance requirements → risk mitigation → legal review

7. **Virtual Assistant (Fallback)**
   - **Keywords**: general queries, basic support, routing
   - **Tone**: Professional, helpful, neutral, adaptive
   - **Structure**: Clear information → appropriate routing → professional tone

### Context Management System

**RAG-Enhanced Prompt Construction**:

```typescript
buildRAGPrompt(
  userInput: string,
  relevantDocs: RankedDocument[],
  conversationHistory: MemoryEntry[] = [],
  maxTokens: number = 16000
): string
```

**Key Features**:

- **Token Management**: Intelligent token allocation (60% context, 40% instruction)
- **Document Context**: Smart document truncation with readability preservation
- **Conversation History**: Multi-turn conversation awareness
- **Role Identification**: Automatic domain expertise selection
- **Dynamic Adaptation**: Context-aware role switching

### Conversation History Management

**Features**:

- Multi-turn conversation tracking
- Context preservation across sessions
- Memory optimization with automatic cleanup
- Role consistency across conversation turns
- Smart truncation for long conversations

### Future Improvement Areas

The Prompt Architecture section represents significant opportunities for enhancement:

1. **Advanced Role Models**: More sophisticated domain expertise models
2. **Context Window Optimization**: Better token utilization strategies
3. **Multi-Modal Prompts**: Integration with image and document understanding
4. **Personalization**: User-specific prompt adaptation
5. **Performance Optimization**: Reduced prompt processing latency
6. **A/B Testing Framework**: Systematic prompt optimization
7. **Template Management**: Dynamic prompt template system
8. **Quality Metrics**: Prompt effectiveness measurement

## 6. MCP Integration Layer

The MCP (Model Context Protocol) Integration Layer provides a unified, high-performance interface to all system tools and services through the consolidated @anter/mcp-tools package.

### MCP Connection Manager

- **Location**: `apps/agents/src/integration/mcp-server/connection-manager.ts`
- **Role**: Sophisticated session pooling and connection lifecycle management
- **Key Features**:
  - **Session Pooling**: Efficient reuse with configurable pool sizes per organization
  - **Automatic Cleanup**: TTL-based session expiration and resource management
  - **Health Monitoring**: Continuous connection health checks and metrics
  - **Retry Logic**: Configurable retry mechanisms with exponential backoff
  - **Performance Metrics**: Comprehensive monitoring and analytics

**Configuration Options**:

- `maxSessionsPerOrg`: Default 5 sessions per organization
- `sessionTTL`: 30-minute default session lifetime
- `cleanupInterval`: 5-minute cleanup cycle
- `healthCheckInterval`: 2-minute health monitoring

### Agent MCP Bridge

- **Location**: `apps/agents/src/integration/mcp-server/mcp-bridge.ts`
- **Purpose**: Low-level MCP protocol handling and tool execution
- **Features**:
  - JSON-RPC message handling
  - Session validation and management
  - Tool call routing and execution
  - Error handling and recovery
  - Performance monitoring

### Session Management

- **Features**:
  - Organization-based session isolation
  - User context preservation
  - Automatic session lifecycle management
  - Security validation and authentication
  - Session state tracking and metrics

## 7. Tool & Service Layer

### MCP Tool Registry

- **Location**: `packages/mcp-tools/src/core/enhanced-tool-registry.ts`
- **Role**: Centralized tool registration and execution management
- **Features**:
  - Dynamic tool registration with metadata
  - Execution timing and performance tracking
  - Advanced filtering and capability matching
  - Tool lifecycle management
  - Comprehensive logging and monitoring

### Database Tools

- **Location**: `packages/mcp-tools/src/tools/database/`
- **Available Tools**:
  - `QueryDatabaseTool`: Advanced SQL query execution with safety features
  - `GetAllDocumentsTool`: Optimized document retrieval with caching
  - Future tools: Data analysis, reporting, backup tools

### Document Tools

- **Location**: `packages/mcp-tools/src/tools/`
- **Comprehensive Suite**:
  - Document processing and analysis
  - Content extraction and sanitization
  - Metadata enrichment and classification
  - Bulk processing capabilities

### Consolidated Services

- **Package**: `@anter/mcp-tools`
- **Status**: 46/46 tests passing, production-ready
- **Services**:
  - `DocumentAnalyzerService`: Advanced document analysis
  - `DocumentProcessor`: Content processing and extraction
  - `SemanticSearchService`: Intelligent search capabilities
  - `VectorOperationsService`: Vector similarity and indexing
  - `EmbeddingService`: OpenAI embeddings management

## 8. Workflow Execution Patterns

### Simple Workflow

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Intelligence
    participant Tools
    participant Response

    User->>Supervisor: Query Request
    Supervisor->>Intelligence: Analyze Intent
    Intelligence-->>Supervisor: Intent + Entities
    Supervisor->>Intelligence: Select Tools
    Intelligence-->>Supervisor: Tool Selection
    Supervisor->>Tools: Execute Tools
    Tools-->>Supervisor: Tool Results
    Supervisor->>Response: Generate Response
    Response-->>User: Final Answer
```

### Multi-Agent Workflow

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Intelligence
    participant DocAgent
    participant SearchAgent
    participant Aggregator

    User->>Supervisor: Complex Query
    Supervisor->>Intelligence: Analyze Intent
    Intelligence-->>Supervisor: Intent + Agent Selection
    Supervisor->>DocAgent: Delegate Document Tasks
    Supervisor->>SearchAgent: Delegate Search Tasks
    DocAgent-->>Supervisor: Document Results
    SearchAgent-->>Supervisor: Search Results
    Supervisor->>Aggregator: Combine Results
    Aggregator-->>User: Unified Response
```

### ReAct Workflow

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant ReAct
    participant Tools
    participant Decision

    User->>Supervisor: Complex Problem
    Supervisor->>ReAct: Initialize ReAct Loop
    loop Think-Act-Observe
        ReAct->>ReAct: Think (Reason)
        ReAct->>Tools: Act (Execute)
        Tools-->>ReAct: Observe (Results)
        ReAct->>Decision: Should Continue?
        Decision-->>ReAct: Yes/No
    end
    ReAct-->>User: Final Solution
```

## 9. Performance & Monitoring

### Performance Targets (Achieved)

- **Simple Queries**: < 3 seconds (maintained)
- **Intelligence Analysis**: < 100ms (achieved)
- **Specialized Agent Processing**: < 300ms (achieved)
- **Document Analysis**: < 500ms (baseline established)
- **Search Operations**: < 400ms (achieved)
- **Multi-Agent Coordination**: < 1 second (Phase 2 target)

### Quality Metrics

- **Test Coverage**: 241/241 tests passing ✅
- **MCP Tools**: 46/46 tests passing ✅
- **Agent Classification Accuracy**: 95%
- **Search Ranking Quality**: 90%
- **Intent Analysis Confidence**: 85%+ average

### Monitoring Capabilities

- Comprehensive execution metrics
- Performance tracking and optimization
- Error rates and failure analysis
- Session utilization and health
- Tool execution timing and success rates

## 10. Deployment Architecture

### Development Environment

```bash
# Enable enhanced multi-agent features
export ENABLE_LANGGRAPH=true
export ENABLE_INTELLIGENCE_LAYER=true
export ENABLE_SPECIALIZED_AGENTS=true

# Start development server
npm run dev:multi-agent
```

### Production Configuration

- **Containerized Deployment**: Docker support for all components
- **Horizontal Scaling**: Stateless design enables easy scaling
- **Health Checks**: Comprehensive health monitoring endpoints
- **Graceful Shutdown**: Proper cleanup and session management
- **Environment Configuration**: Flexible configuration management

## 11. Future Roadmap

### Phase 3: Advanced Multi-Agent Coordination

- Inter-agent communication protocols
- Advanced delegation patterns
- Agent-to-agent learning and optimization
- Distributed workflow execution

### Phase 4: Agent-to-Agent (A2A) Integration

- External agent discovery and integration
- Cross-system agent communication
- Agent capability marketplace
- Federated agent networks

### Phase 5: Advanced Intelligence

- Machine learning-enhanced intent analysis
- Personalized agent selection
- Predictive tool selection
- Continuous learning and optimization

## Conclusion

The Anter AI Multi-Agent System represents a mature, production-ready architecture that successfully combines intelligent orchestration, specialized domain expertise, and robust infrastructure. The system's modular design, comprehensive testing, and performance optimization make it suitable for enterprise deployment while providing a solid foundation for future enhancements and capabilities.

**Last Updated:** 2025-07-01
