# AI Agent System Architecture Diagram

## System Architecture Overview

```mermaid
graph TB
    %% Client Layer
    subgraph "Client Applications"
        A1[Microsoft Teams Bot]
        A2[Slack App]
        A3[Web Application]
        A4[Mobile App]
    end

    %% API Gateway Layer
    subgraph "API Gateway Layer"
        B1[Fastify Server]
        B2[Authentication Middleware]
        B3[Rate Limiting]
        B4[Request Validation]
        B5[Response Formatter / SSE]
    end

    %% Agent Layer
    subgraph "Agent Processing Layer"
        C1[AskAIAgent]
        C2[Unified RAG Executor]
        C3[Response Strategy Factory]
        C4[Single-Shot Delivery Adapter]
        C5[Streaming Delivery Adapter]
        C6[Conversation Memory]
        C7[ChatGenerationService]
        C8[StreamingChatGenerationService]
        C9[UnifiedResponderImpl - Central Confidence Gate]
        C10[Langfuse Prompt Adapter]
    end

    %% MCP Tools Layer
    subgraph "MCP Tools & Services"
        D2[Semantic Search Service]
        D3[Embedding Service]
        D4[Content Hash Service]
    end

    %% Data Layer
    subgraph "Data & Storage Layer"
        E1[Redis-backed Memory Store]
        E2[Redis Vector Index]
        E5[Log Storage]
    end

    %% External Services
    subgraph "External Services"
        F1[OpenAI LLM]
        F4[Langfuse Observability]
    end

    %% Connections
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5

    B5 --> C1
    C1 --> C2
    C1 --> C3
    C1 --> C6
    C3 --> C4
    C3 --> C5

    C2 --> D2
    D2 --> E2

    C6 --> E1

    C4 --> C9
    C5 --> C9

    C9 --> C7
    C9 --> C8

    C7 --> F1
    C8 --> F1

    C7 --> C10
    C8 --> C10
    C1 --> F4

    %% Styling
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef agent fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef mcp fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef external fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class A1,A2,A3,A4 client
    class B1,B2,B3,B4,B5 api
    class C1,C2,C3,C4,C5,C6,C7,C8,C9,C10 agent
    class D2,D3,D4 mcp
    class E1,E2,E5 data
    class F1,F4 external
```

## Data Flow Diagram (Runtime)

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant API as API Gateway
    participant Agent as AskAIAgent
    participant RAG as Unified RAG Executor
    participant MCP as MCP Tools (Semantic Search)
    participant Memory as Conversation Memory
    participant LLM as OpenAI

    Client->>API: POST /agent/invoke or /agent/stream
    API->>API: Validate API Key & Rate Limit
    API->>Agent: Dispatch request

    Agent->>RAG: Execute Unified RAG Pipeline
    RAG->>Memory: Load last 10 entries (if sessionId)
    RAG->>Memory: Store user input (if sessionId)
    Note over RAG, MCP: Documents are pre-embedded and indexed in Redis
    RAG->>MCP: Execute Semantic Search (topK)
    RAG->>Agent: Return Unified Context (avg score, enriched text)

    Agent->>Agent: Central Confidence Gate - UnifiedResponderImpl

    alt Low-confidence (avgScore < 0.6 or no results)
        Agent-->>API: Fallback text (no LLM call)
        API-->>Client: JSON response / SSE chunk
    else Single-Shot
        Agent->>LLM: ChatGenerationService - Langfuse prompt
        LLM-->>Agent: Response
        Agent-->>API: JSON response
        API-->>Client: 200 OK
    else Streaming
        Agent->>LLM: StreamingChatGenerationService - Langfuse prompt
        loop Stream Chunks
            Agent-->>API: SSE {type:"content", content}
            API-->>Client: data: {chunk}
        end
        Agent-->>API: SSE {type:"complete"}
        API-->>Client: data: {complete}
    end
```

## RAG Pipeline Flow

```mermaid
flowchart TD
    A[User Input] --> B[Load Conversation History optional]
    B --> C[Store User Input optional]
    C --> E[Execute Semantic Search]
    E --> F[Build Unified Context]
    F --> G[Central Confidence Gate]
    G -->|Low confidence| H[Fallback Text]
    G -->|Proceed| I{Response Mode}
    I -->|SingleShot| J[ChatGenerationService OpenAI]
    I -->|Streaming| K[StreamingChatGenerationService OpenAI]
    J --> L[Store Assistant Response]
    K --> L
    L --> M[End]
```

## OpenAI LLM Integration Flow

```mermaid
flowchart TD
    A[Unified RAG Context] --> B[UnifiedResponderImpl]
    B --> C{Low-confidence?}
    C -->|Yes| D[Return Fallback]
    C -->|No| E{Mode}
    E -->|Single-Shot| F[ChatGenerationService]
    E -->|Streaming| G[StreamingChatGenerationService]

    F --> H[OpenAI - gpt-4o-mini]
    G --> I[OpenAI - gpt-4o-mini - streaming]

    H --> J[Response]
    I --> K[Content Chunks]

    J --> L[Agent Result]
    K --> M[SSE Stream]
```

## Response Strategy Architecture

```mermaid
graph TB
    subgraph "ResponseStrategyFactory"
        RSF[ResponseStrategyFactory]
        RSF --> SS[SingleShotDeliveryAdapter]
        RSF --> ST[StreamingDeliveryAdapter]
    end

    subgraph "Core"
        CORE[UnifiedResponderImpl - Confidence Gate]
    end

    subgraph "LLM Services"
        CGS[ChatGenerationService]
        SCGS[StreamingChatGenerationService]
    end

    subgraph "Utilities & Observability"
        TE[TokenEstimator]
        SEH[StreamingErrorHandler]
        SPT[StreamingPerformanceTracker]
        LPA[Langfuse Prompt Adapter]
        LF[Langfuse Spans]
    end

    subgraph "Types"
        URT[unified-rag-types.ts]
        STT[streaming-types.ts]
        RT[rag-types.ts]
    end

    SS --> CORE
    ST --> CORE
    CORE --> CGS
    CORE --> SCGS

    CGS --> LPA
    SCGS --> LPA

    CGS --> TE
    SCGS --> SEH
    SCGS --> SPT

    CGS --> LF
    SCGS --> LF

    CGS --> RT
    SCGS --> STT
    SS --> URT
    ST --> URT

    %% Styling
    classDef factory fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef core fill:#fff3e0,stroke:#f57f17,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef util fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef types fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class RSF,SS,ST factory
    class CORE core
    class CGS,SCGS service
    class TE,SEH,SPT,LPA,LF util
    class URT,STT,RT types
```

## Streaming Events

- stream_started
- content
- complete
- error

Note: a `confidence` chunk type exists in `streaming-types.ts` but is not emitted; confidence is handled upstream by the central gate.
