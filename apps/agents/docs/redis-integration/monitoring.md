# Monitoring & Observability – Redis Vector Index

## 1. Metrics

Key Prometheus metrics (exported via `/metrics` endpoint):

| Metric                      | Type      | Labels                      | Description                           |
| --------------------------- | --------- | --------------------------- | ------------------------------------- |
| `search_duration_seconds`   | Histogram | `org`, `strategy`, `status` | End-to-end search latency             |
| `search_index_hit_rate`     | Gauge     | `org`                       | % of searches served from Redis index |
| `redis_operations_total`    | Counter   | `op`, `status`              | Redis calls grouped by command        |
| `embedding_api_calls_total` | Counter   | `org`, `batch`              | OpenAI embedding requests             |
| `circuit_breaker_state`     | Gauge     | `component`                 | 0=closed,1=half-open,2=open           |
| `fallback_rate`             | Gauge     | `strategy`                  | share of requests handled by fallback |

## 2. Dashboards

1. **Executive** – SLA indicators (success-rate, P95 latency, cost saving).
2. **Ops** – Redis health, memory, key-space, slow-logs, hit-rate.
3. **Developer** – embedding latency, batch efficiency, pipeline ratio.

## 3. Alerts

| Alert                   | Condition                                    | Severity |
| ----------------------- | -------------------------------------------- | -------- |
| `RedisConnectionFailed` | `redis_connection_status == failed` for 2m   | critical |
| `HighSearchLatency`     | `search_latency_p95 > 5` for 10m             | critical |
| `LowIndexHitRate`       | `search_index_hit_rate < 0.85` for 15m       | warning  |
| `CircuitBreakerOpen`    | `circuit_breaker_state == 2` for 5m          | warning  |
| `RedisMemoryHigh`       | `redis_memory_usage_bytes / maxmemory > 0.8` | warning  |

## 4. Troubleshooting Runbooks

### 4.1 High Search Latency

1. **Verify Redis health**
   ```bash
   docker exec askinfosec-redis redis-cli ping
   docker exec askinfosec-redis redis-cli info latency | head
   ```
2. **Check circuit breaker** – is it _open_?
   ```bash
   curl -s http://localhost:8000/internal/metrics | grep circuit_breaker_state
   ```
3. **Look for large embedding batches** in logs (`batch size > 200`).
4. **Inspect slowlog**:
   ```bash
   docker exec askinfosec-redis redis-cli slowlog get 20
   ```
5. **Fallback in effect?** High fallback_rate indicates index misses.
6. **Mitigation** – scale Redis CPU/memory or shard docs across orgs.

### 4.2 Redis Out-of-Memory

1. Confirm memory usage:
   ```bash
   docker exec askinfosec-redis redis-cli info memory | grep used_memory_human
   ```
2. Review eviction policy (`maxmemory-policy`).
3. Increase `maxmemory` or enable compression.
4. Flush old org indexes:
   ```bash
   docker exec askinfosec-redis redis-cli del $(redis-cli keys "embed:oldorg:*")
   ```

### 4.3 Circuit Breaker Open

1. Inspect error causes in application logs.
2. Verify OpenAI API connectivity.
3. After root cause fixed, circuit resets automatically after timeout (default 60 s).

---

For detailed architecture see `architecture.md`. For rollout tasks refer to `implementation-plan.md`.
