# Redis Integration for Search Agent

## Overview

This document provides step-by-step instructions to spin up Redis **and** Redis Insight locally (or via Docker Compose) and explains how they connect to the AskInfoSec Search Agent. Follow these steps before implementing the code changes described in the accompanying architecture and implementation-plan documents.

## Table of Contents

1. Docker setup
2. Starting the stack
3. Environment variables
4. Quick sanity checks
5. Cleanup

---

## 1. Docker setup

Create (or update) `docker-compose.dev.yml` with the following services:

```yaml
services:
  redis:
    image: redis:7.2-alpine
    container_name: askinfosec-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: askinfosec-redis-insight
    ports:
      - '8001:8001'
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
```

Place the tuned Redis config at `redis/redis.conf` (see `architecture.md`). The default config is perfectly fine for development – the custom file is only required for production hardening.

## 2. Starting the stack

```bash
# Bring up Redis + Redis Insight in detached mode
$ docker-compose -f docker-compose.dev.yml up -d redis redis-insight

# Follow the container logs (optional)
$ docker logs -f askinfosec-redis
```

Once the containers are healthy, open Redis Insight at `http://localhost:8001` (mapped to container port 5540) and connect to `redis:6379`.

### Option B – standalone Redis stack (API runs outside Docker)

If you prefer to start the API directly on your host machine (e.g., `pnpm dev`), you can keep Redis running via a dedicated compose file that contains **only** Redis and Redis Insight:

```bash
# 1. Spin up Redis services only (detached)
$ docker-compose -f docker-compose.redis.yml up -d

# 2. Inspect logs (optional)
$ docker logs -f askinfosec-redis

# 3. Verify connectivity
$ redis-cli -h localhost -p 6379 ping   # -> PONG
```

When finished:

```bash
# Stop containers (data volume persists)
$ docker-compose -f docker-compose.redis.yml down
```

Your API process picks up the same `REDIS_URL` environment variable (`redis://localhost:6379`). If you need to expose Redis on a different host/port, adjust the compose file and the env var accordingly.

## 3. Environment variables

In every service that needs Redis (API, agents, CLI, etc.) set:

```env
REDIS_URL=redis://redis:6379
SKIP_REDIS=false          # must be **false** to enable real client
REDIS_INDEX_ENABLED=true  # feature-flag for vector index
```

Optional tuning knobs are documented in `implementation-plan.md`.

## 4. Quick sanity checks

```bash
# Ping redis
$ docker exec askinfosec-redis redis-cli ping
PONG

# Write / read test key
$ docker exec askinfosec-redis redis-cli set test:ping "hello" EX 60
$ docker exec askinfosec-redis redis-cli get test:ping
hello
```

When you start the AskInfoSec API (`docker-compose up api`) you should see the startup log:  
`Redis connected successfully at redis://redis:6379`.

## 5. Cleanup

```bash
# Stop & remove containers
$ docker-compose -f docker-compose.dev.yml down

# Remove persisted volume
$ docker volume rm askinfosec-redis_data
```

---

Next steps: read `architecture.md` for the high-level design and `implementation-plan.md` for the step-by-step coding tasks.
