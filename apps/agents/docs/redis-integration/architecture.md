# Redis-Based Vector Index – Architecture

## 1. Problem Statement

Large document sets (>100) cause repeated in-memory indexing and embedding, resulting in 30-60 s latency and frequent timeouts. The goal is to introduce **persistent Redis-backed vector storage** with intelligent fall-backs while preserving tenant isolation.

## 2. High-Level Components

```mermaid
graph TD
    subgraph "Agent Layer"
        SA["SearchAgentFactory"]
        SST["semantic_search tool"]
        RDT["retrieve_documents tool"]
    end
    subgraph "Index Layer"
        IEI["IEmbeddingIndex"]
        RIX["RedisEmbeddingIndex"]
        IMX["InMemoryIndex"]
    end
    subgraph "Infrastructure"
        REDIS[(Redis 7.2)]
        RI[(Redis Insight)]
        RS[(RediSearch*)]
    end

    SA --> SST
    SA --> RDT
    SST --> IEI
    IEI -->|primary| RIX
    IEI -->|fallback| IMX
    RIX --> REDIS
    REDIS -->|monitor| RI
    RIX --> RS
```

_RediSearch is auto-detected and used when present._

### Key Interfaces

```typescript
interface IEmbeddingIndex {
  hasDocument(orgId: string, docId: string): Promise<boolean>;
  getDocumentHash(orgId: string, docId: string): Promise<string | null>;
  setDocumentEmbedding(
    orgId: string,
    docId: string,
    embedding: number[],
    hash: string,
    meta?: any
  ): Promise<void>;
  searchSimilar(
    orgId: string,
    queryEmbedding: number[],
    topK: number,
    threshold?: number
  ): Promise<SearchResult[]>;
  batchSetEmbeddings(orgId: string, docs: BatchEmbeddingDocument[]): Promise<void>;
  getIndexedDocumentIds(orgId: string): Promise<string[]>;
  getIndexStats(orgId: string): Promise<IndexStats>;
}
```

### Redis Key Schema

| Key                            | Type                 | Purpose                         |
| ------------------------------ | -------------------- | ------------------------------- |
| `embed:{org}:docs`             | **SET**              | all docIds indexed for org      |
| `embed:{org}:doc:{docId}:meta` | **HASH**             | hash, timestamp, metadata       |
| `embed:{org}:doc:{docId}:vec`  | **STRING / BINARY**  | raw Float32Array buffer         |
| `embed:{org}:index`            | **HASH**             | stats (hit-rate, lastUpdate …)  |
| `embed_search_{org}`           | **RediSearch index** | native vector search (optional) |

### Intelligent Workflow

1. **Try semantic search** on Redis index.
2. If score < _threshold_ or no hits:  
   a. fetch missing docs (only those not already indexed).  
   b. embed in batches (OpenAI).  
   c. store vectors + metadata atomically (`MULTI`).  
   d. re-run search.
3. Fallback chain: `redis_vector → redis_simple → in_memory → text_search`.

### Performance Targets

| Metric                    | Baseline | Target  |
| ------------------------- | -------- | ------- |
| First search latency      | 30-60 s  | 2-5 s   |
| Cached search latency P95 | 10-30 s  | <500 ms |
| Index hit-rate            | 0%       | ≥90%    |
| Timeout rate              | 15-25%   | <2%     |

---

See `implementation-plan.md` for the concrete work items.
