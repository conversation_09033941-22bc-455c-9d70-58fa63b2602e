/**
 * Simple test script for the intelligence layer
 */

import { IntelligenceService } from './src/intelligence/index.js';

async function testIntelligence() {
  console.log('🧠 Testing Intelligence Layer...\n');

  const intelligenceService = new IntelligenceService({
    debugMode: true,
  });

  // Test cases
  const testCases = [
    'Show me all security documents',
    'Search for CVE-2024-1234 vulnerabilities',
    'Analyze Windows security configurations',
    'First check the firewall logs, then analyze patterns, and recommend improvements',
  ];

  for (const testInput of testCases) {
    console.log(`📝 Input: "${testInput}"`);

    try {
      const result = await intelligenceService.analyzeAndSelectTools(testInput, {
        sessionId: 'test-session',
        organizationId: 'test-org',
      });

      console.log(
        `✅ Intent: ${result.intentAnalysis.intent.type} (confidence: ${result.intentAnalysis.confidence.toFixed(2)})`
      );
      console.log(
        `🔧 Tools: ${result.toolSelection.selectedTools.map(t => t.toolName).join(', ')}`
      );
      console.log(`🎯 Reasoning: ${result.toolSelection.reasoning}`);
      console.log(`⏱️  Processing time: ${result.intentAnalysis.processingTime}ms\n`);
    } catch (error) {
      console.error(`❌ Error: ${error.message}\n`);
    }
  }

  console.log('🎉 Intelligence Layer test completed!');
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testIntelligence().catch(console.error);
}

export { testIntelligence };
