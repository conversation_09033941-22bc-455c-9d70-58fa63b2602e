const { ChatOpenAI } = require('@langchain/openai');
const { tool } = require('@langchain/core/tools');
const { z } = require('zod');
const { Annotation } = require('@langchain/langgraph');
const { createReactAgent } = require('@langchain/langgraph/prebuilt');

console.log('=== Testing exact SearchAgentFactory scenario ===');

// Replicate the exact SupervisorStateAnnotation from idiomatic-supervisor.ts
const SupervisorStateAnnotation = Annotation.Root({
  messages: Annotation({
    reducer: (existing, newMessages) => [...existing, ...newMessages],
    default: () => [],
  }),
  sessionId: Annotation({
    reducer: (existing, newValue) => newValue || existing,
    default: () => 'default-session',
  }),
  organizationId: Annotation({
    reducer: (existing, newValue) => newValue || existing,
    default: () => 'default-org',
  }),
  userId: Annotation({
    reducer: (existing, newValue) => (newValue !== undefined ? newValue : existing),
    default: () => undefined,
  }),
  documents: Annotation({
    reducer: (existing, newValue) => {
      if (Array.isArray(newValue)) {
        return newValue.length > 0 ? newValue : existing;
      }
      return newValue !== undefined ? newValue : existing;
    },
    default: () => [],
  }),
  searchResults: Annotation({
    reducer: (existing, newValue) => {
      if (Array.isArray(newValue)) {
        return newValue.length > 0 ? newValue : existing;
      }
      return newValue !== undefined ? newValue : existing;
    },
    default: () => [],
  }),
  analysis: Annotation({
    reducer: (existing, newValue) => {
      return newValue || existing;
    },
    default: () => null,
  }),
  metadata: Annotation({
    reducer: (existing, newValue) => ({ ...existing, ...newValue }),
    default: () => ({}),
  }),
});

console.log('1. State schema created');

// Test different ways of creating the LLM
console.log('2. Testing LLM creation and ensureBindTools...');

function ensureBindTools(llm) {
  if (typeof llm.bindTools !== 'function') {
    llm.bindTools = () => llm;
  }
  return llm;
}

const llmRaw = new ChatOpenAI({ model: 'gpt-4o', temperature: 0.1 });
console.log('   Raw LLM bindTools type:', typeof llmRaw.bindTools);

const llm = ensureBindTools(llmRaw);
console.log('   After ensureBindTools bindTools type:', typeof llm.bindTools);
console.log('   Same instance?', llm === llmRaw);

// Create tools that match the SearchAgent complexity
console.log('3. Creating complex tools like SearchAgent...');

const retrieveDocumentsTool = tool(
  async args => {
    console.log(`[MockSearchAgent] Retrieving documents for org: ${args.organizationId}`);
    return {
      success: true,
      documents: [{ id: 1, title: 'Test Doc' }],
      count: 1,
      message: 'Successfully retrieved documents',
    };
  },
  {
    name: 'retrieve_documents',
    description: 'Retrieve all documents from the organization knowledge base',
    schema: z.object({
      organizationId: z.string().describe('Organization ID to retrieve documents for'),
      userId: z.string().nullable().describe('Optional user ID for access control'),
    }),
  }
);

const semanticSearchTool = tool(
  async args => {
    console.log(`[MockSearchAgent] Performing semantic search for query: "${args.query}"`);
    return {
      success: true,
      searchResults: [{ content: 'Mock result', score: 0.9 }],
      count: 1,
      message: 'Found relevant results',
    };
  },
  {
    name: 'semantic_search',
    description: 'Perform semantic search on indexed documents',
    schema: z.object({
      query: z.string().describe('Search query to find relevant information'),
      organizationId: z.string().describe('Organization ID for document scope'),
      documents: z.array(z.object({}).passthrough()).describe('Documents to search through'),
      topK: z.number().nullable().describe('Maximum number of results to return'),
    }),
  }
);

const tools = [retrieveDocumentsTool, semanticSearchTool];
console.log('   Tools created successfully');

// Test the exact createReactAgent call from SearchAgentFactory
console.log('4. Testing exact createReactAgent call from SearchAgentFactory...');
try {
  const searchAgent = createReactAgent({
    llm,
    tools,
    name: 'search_expert',
    prompt: `You are a document search expert specializing in information retrieval and semantic search.

**Your Responsibilities:**
1. **Document Retrieval**: Retrieve all documents from the organization's knowledge base
2. **Document Indexing**: Index documents for efficient semantic search
3. **Semantic Search**: Perform intelligent search to find relevant information
4. **Result Ranking**: Return search results with relevance scores

**Available Tools:**
- retrieve_documents: Get all documents for an organization
- semantic_search: Perform semantic search on indexed documents

**Context:**
Current organization ID: {{organizationId}}

**Instructions:**
- Always start by retrieving documents if they're not available
- Index documents before performing search operations
- Use semantic search to find the most relevant information
- Provide clear feedback about search results and their relevance
- Handle errors gracefully and provide informative messages

**Important:** You must use the tools to complete your tasks. Do not attempt to generate fake data or responses.`,
    stateSchema: SupervisorStateAnnotation,
  });

  console.log('   SUCCESS: SearchAgent created successfully');
  console.log('   Agent type:', typeof searchAgent);
  console.log('   Agent constructor:', searchAgent.constructor.name);

  // Try to run the agent to see if the error occurs during execution
  console.log('5. Testing agent execution...');
  try {
    const result = await searchAgent.invoke({
      messages: [{ role: 'human', content: 'Get documents for organization test-org' }],
    });
    console.log('   SUCCESS: Agent execution completed');
  } catch (execError) {
    console.log('   FAILED: Agent execution failed:', execError.message);
    console.log('   Error stack:', execError.stack);
  }
} catch (error) {
  console.log('   FAILED: SearchAgent creation failed:', error.message);
  console.log('   Error stack:', error.stack);
}
