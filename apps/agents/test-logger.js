// Simple test to verify logger functionality
const { getLogger } = require('./dist/apps/agents/src/observability/global-logger');

const logger = getLogger();

console.log('Testing logger with LOG_LEVEL:', process.env.LOG_LEVEL || 'info');

logger.debug('This is a debug message');
logger.info('This is an info message');
logger.warn('This is a warn message');
logger.error(new Error('Test error'), 'This is an error message');
