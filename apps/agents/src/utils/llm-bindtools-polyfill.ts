export function ensureBindTools<T extends Record<string, any>>(llm: T): T {
  if (typeof (llm as any).bindTools !== 'function') {
    /*
     * Some versions of @langchain/openai Chat models (e.g. <=0.5.x) do not yet
     * expose the `bindTools` helper required by LangGraph’s `createReactAgent`.
     * We poly-fill it here so the rest of the agent code can rely on the method
     * existing.  The fallback simply returns the original instance unchanged.
     */
    (llm as any).bindTools = function (this: T, _tools: unknown): T {
      return this;
    };
  }
  return llm;
}
