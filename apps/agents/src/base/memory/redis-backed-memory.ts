import { AgentMemory, MemoryEntry } from '../types';
import * as crypto from 'crypto';
import { z } from 'zod';
import { getLogger } from '../../observability/global-logger';
import { ILogger } from '@/integration/mcp-server/mcp-bridge-enhanced';

/**
 * Redis-backed implementation of AgentMemory
 * Uses in-memory storage for performance with Redis persistence
 * Falls back to in-memory only if Redis is unavailable
 */
export class RedisBackedMemory implements AgentMemory {
  private entries: MemoryEntry[] = [];
  private maxEntries: number;
  private redisClient?: any;
  private redisKeyPrefix: string;
  private redisTtlSeconds: number;
  // @ts-ignore
  private logger?: ILogger;

  constructor(
    maxEntries: number = 100,
    redisClient?: any,
    options: {
      redisKeyPrefix?: string;
      redisTtlSeconds?: number;
      logger?: ILogger;
    } = {}
  ) {
    this.maxEntries = maxEntries;
    this.redisClient = redisClient;
    this.redisKeyPrefix = options.redisKeyPrefix || 'conversation_memory:';
    this.redisTtlSeconds = options.redisTtlSeconds || 86400; // 24 hours
    this.logger = options.logger || getLogger();

    // Note: We don't load all sessions on initialization anymore
    // Sessions are loaded on-demand via loadSession() method
  }

  async add(entry: MemoryEntry): Promise<void> {
    this.entries.push(entry);

    // Maintain max entries limit
    if (this.entries.length > this.maxEntries) {
      this.entries.shift(); // Remove oldest entry
    }

    // Save to Redis if available
    const sessionId = entry.metadata?.sessionId;
    const organizationId: string | undefined = entry.metadata?.organizationId;
    if (sessionId && this.redisClient) {
      // Load existing session data from Redis
      const existingEntries = organizationId
        ? await this.loadSession(sessionId, organizationId)
        : await this.loadSession(sessionId);

      // Combine existing entries with new entry
      const allEntries = [...existingEntries, entry];

      // Maintain max entries limit for the complete session
      const finalEntries = allEntries.slice(-this.maxEntries);

      // Save the complete session back to Redis
      if (organizationId) {
        await this.saveSession(sessionId, finalEntries, organizationId);
      } else {
        await this.saveSession(sessionId, finalEntries);
      }

      // Update in-memory entries to match what's in Redis
      this.entries = finalEntries;
    }
  }

  async get(count?: number): Promise<MemoryEntry[]> {
    if (!count || count >= this.entries.length) {
      return [...this.entries];
    }

    // Return most recent entries
    return this.entries.slice(-count);
  }

  async search(query: string, limit: number = 10): Promise<MemoryEntry[]> {
    const lowerQuery = query.toLowerCase();

    const results = this.entries.filter(entry => {
      const content = JSON.stringify(entry.content).toLowerCase();
      return content.includes(lowerQuery);
    });

    // Return most recent matches
    return results.slice(-limit);
  }

  async clear(): Promise<void> {
    this.entries = [];
    // Note: We don't save to Redis on clear since we don't know which session to clear
    // Individual sessions should be cleared via clearSession() method
  }

  async consolidate(): Promise<void> {
    // Simple consolidation: remove duplicate consecutive entries
    const consolidated: MemoryEntry[] = [];

    for (let i = 0; i < this.entries.length; i++) {
      const current = this.entries[i];
      const previous = consolidated[consolidated.length - 1];

      // Skip if identical to previous entry
      if (
        previous &&
        previous.type === current.type &&
        JSON.stringify(previous.content) === JSON.stringify(current.content)
      ) {
        continue;
      }

      consolidated.push(current);
    }

    this.entries = consolidated;

    // Save consolidated entries to Redis if we have a session
    if (this.entries.length > 0) {
      const sessionId = this.entries[0].metadata?.sessionId;
      if (sessionId && this.redisClient) {
        await this.saveSession(sessionId, this.entries);
      }
    }
  }

  /**
   * Get current memory size
   */
  size(): number {
    return this.entries.length;
  }

  /**
   * Get all entries (for debugging)
   */
  getAllEntries(): MemoryEntry[] {
    return [...this.entries];
  }

  /**
   * Load conversation memory from Redis
   */
  // private async loadFromRedis(): Promise<void> {
  //   if (!this.redisClient) {
  //     return;
  //   }

  //   try {
  //     // Get all session keys
  //     const pattern = `${this.redisKeyPrefix}*`;
  //     const keys = await this.redisClient.keys(pattern);

  //     if (keys.length === 0) {
  //       this.logger?.debug('No conversation memory found in Redis');
  //       return;
  //     }

  //     // Load all sessions
  //     const allEntries: MemoryEntry[] = [];
  //     for (const key of keys) {
  //       try {
  //         const data = await this.redisClient.get(key);
  //         if (data) {
  //           const sessionEntries: MemoryEntry[] = JSON.parse(data);
  //           allEntries.push(...sessionEntries);
  //         }
  //       } catch (error) {
  //         this.logger?.warn('Failed to load session from Redis', { key, error: error });
  //       }
  //     }

  //     // Sort by timestamp and take the most recent entries
  //     allEntries.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  //     this.entries = allEntries.slice(-this.maxEntries);

  //     this.logger?.info('Loaded conversation memory from Redis', {
  //       totalEntries: allEntries.length,
  //       loadedEntries: this.entries.length,
  //       sessions: keys.length,
  //     });
  //   } catch (error) {
  //     this.logger?.error('Failed to load conversation memory from Redis', { error: error });
  //   }
  // }

  /**
   * Save conversation memory to Redis
   */
  // private async saveToRedis(): Promise<void> {
  //   if (!this.redisClient || this.entries.length === 0) {
  //     return;
  //   }

  //   try {
  //     // Group entries by session ID
  //     const sessions = new Map<string, MemoryEntry[]>();

  //     for (const entry of this.entries) {
  //       const sessionId = entry.metadata?.sessionId;
  //       if (sessionId) {
  //         if (!sessions.has(sessionId)) {
  //           sessions.set(sessionId, []);
  //         }
  //         sessions.get(sessionId)!.push(entry);
  //       }
  //     }

  //     // Save each session to Redis
  //     const pipeline = this.redisClient.pipeline();

  //     for (const [sessionId, entries] of sessions) {
  //       const key = `${this.redisKeyPrefix}${sessionId}`;
  //       pipeline.setex(key, this.redisTtlSeconds, JSON.stringify(entries));
  //     }

  //     await pipeline.exec();

  //     this.logger?.debug('Saved conversation memory to Redis', {
  //       sessions: sessions.size,
  //       totalEntries: this.entries.length,
  //     });
  //   } catch (error) {
  //     this.logger?.error('Failed to save conversation memory to Redis', { error: error });
  //   }
  // }

  /**
   * Load specific session from Redis
   */
  async loadSession(sessionId: string, organizationId?: string): Promise<MemoryEntry[]> {
    if (!this.redisClient) {
      return [];
    }
    if (!organizationId) {
      throw new Error('organizationId is required for memory isolation');
    }
    const data = await this.redisClient.get(
      `${this.redisKeyPrefix}org-${organizationId}:${sessionId}`
    );
    if (data) {
      const MemoryEntrySchema = z.object({
        id: z.string().default(() => crypto.randomUUID()),
        content: z.any(),
        type: z.enum(['user', 'assistant', 'system', 'tool']),
        timestamp: z.string().transform(str => new Date(str)),
        metadata: z.record(z.string(), z.any()).optional(),
        embedding: z.array(z.number()).optional(),
      });
      const ArraySchema = z.array(MemoryEntrySchema);
      const raw = safeJsonParse(data);
      const parseResult = ArraySchema.safeParse(raw);
      if (!parseResult.success) {
        throw new Error('Invalid memory data: ' + parseResult.error.message);
      }
      const validated = parseResult.data;
      const entries: MemoryEntry[] = validated.map(v => ({
        id: v.id,
        content: v.content,
        type: v.type,
        timestamp: v.timestamp,
        metadata: v.metadata,
        embedding: v.embedding,
      }));
      return entries;
    }
    return [];
  }

  /**
   * Save specific session to Redis
   */
  async saveSession(
    sessionId: string,
    entries: MemoryEntry[],
    organizationId?: string
  ): Promise<void> {
    if (!this.redisClient) {
      return;
    }
    if (!organizationId) {
      throw new Error('organizationId is required for memory isolation');
    }
    const key = `${this.redisKeyPrefix}org-${organizationId}:${sessionId}`;
    await this.redisClient.setex(key, this.redisTtlSeconds, JSON.stringify(entries));
  }
}

function safeJsonParse(data: string): unknown {
  // Strict safe parse with minimal exposure
  let parsed: unknown;
  try {
    // nosemgrep: owasp-top10.a08.untrusted-data-deserialization - Immediately validated with Zod schema below
    parsed = JSON.parse(data);
  } catch {
    throw new Error('Invalid JSON data');
  }
  // Only allow objects or arrays; primitives are rejected
  if (parsed === null || typeof parsed !== 'object') {
    throw new Error('Invalid JSON structure');
  }
  return parsed;
}
