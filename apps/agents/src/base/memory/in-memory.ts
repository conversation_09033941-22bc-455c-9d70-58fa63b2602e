import { AgentMemory, MemoryEntry } from '../types';

/**
 * In-memory implementation of AgentMemory
 * Suitable for development and testing
 */
export class InMemoryAgentMemory implements AgentMemory {
  private entries: MemoryEntry[] = [];
  private maxEntries: number;

  constructor(maxEntries: number = 100) {
    this.maxEntries = maxEntries;
  }

  async add(entry: MemoryEntry): Promise<void> {
    this.entries.push(entry);

    // Maintain max entries limit
    if (this.entries.length > this.maxEntries) {
      this.entries.shift(); // Remove oldest entry
    }
  }

  async get(count?: number): Promise<MemoryEntry[]> {
    if (!count || count >= this.entries.length) {
      return [...this.entries];
    }

    // Return most recent entries
    return this.entries.slice(-count);
  }

  async search(query: string, limit: number = 10): Promise<MemoryEntry[]> {
    const lowerQuery = query.toLowerCase();

    const results = this.entries.filter(entry => {
      const content = JSON.stringify(entry.content).toLowerCase();
      return content.includes(lowerQuery);
    });

    // Return most recent matches
    return results.slice(-limit);
  }

  async clear(): Promise<void> {
    this.entries = [];
  }

  async consolidate(): Promise<void> {
    // Simple consolidation: remove duplicate consecutive entries
    const consolidated: MemoryEntry[] = [];

    for (let i = 0; i < this.entries.length; i++) {
      const current = this.entries[i];
      const previous = consolidated[consolidated.length - 1];

      // Skip if identical to previous entry
      if (
        previous &&
        previous.type === current.type &&
        JSON.stringify(previous.content) === JSON.stringify(current.content)
      ) {
        continue;
      }

      consolidated.push(current);
    }

    this.entries = consolidated;
  }

  /**
   * Get current memory size
   */
  size(): number {
    return this.entries.length;
  }

  /**
   * Get all entries (for debugging)
   */
  getAllEntries(): MemoryEntry[] {
    return [...this.entries];
  }
}
