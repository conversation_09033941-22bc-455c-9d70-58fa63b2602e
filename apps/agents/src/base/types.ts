import { z } from 'zod';

// Agent archetypes for different use cases
export enum AgentArchetype {
  REACTIVE = 'reactive', // Simple request-response
  DELIBERATIVE = 'deliberative', // Planning before execution
  HYBRID = 'hybrid', // Combines reactive + deliberative
  SWARM = 'swarm', // Multi-agent collaboration
  ADVERSARIAL = 'adversarial', // Red team/blue team scenarios
}

// Agent capabilities
export enum AgentCapability {
  CHAT = 'chat',
  REASONING = 'reasoning',
  PLANNING = 'planning',
  TOOL_USE = 'tool-use',
  MEMORY = 'memory',
  STREAMING = 'streaming',
  MULTI_MODAL = 'multi-modal',
  COLLABORATION = 'collaboration',
  DATABASE_ACCESS = 'database-access',
  LLM_INTEGRATION = 'llm-integration',
}

// Base schema for agent input
export const AgentInputSchema = z.object({
  input: z.any(),
  sessionId: z.string().optional(),
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  traceId: z.string().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export type AgentInput = z.infer<typeof AgentInputSchema>;

// Agent context for execution
export interface AgentContext {
  sessionId: string;
  organizationId?: string;
  userId?: string;
  requestId: string;
  timestamp: Date;
  memory?: AgentMemory;
  tools?: AgentTool[];
  securityContext?: SecurityContext;
  performanceConfig?: PerformanceConfig;
}

// Agent result
export interface AgentResult {
  output: StandardAgentOutput;
  metadata?: {
    tokensUsed?: number;
    latencyMs?: number;
    toolsInvoked?: string[];
    cost?: number;
    warnings?: string[];
    sources?: any;
  };
  sessionUpdate?: Record<string, any>;
}

// Unified output structure: separates user-facing response from metadata & metrics
export interface StandardAgentOutput {
  /** Primary AI-generated textual answer */
  response: string;
  /** Additional non-numeric contextual information */
  metadata: {
    context_doc_ids?: string[];
    session_id?: string;
    conversation_entries?: number;
    // Diagnostics from semantic search / retrieval
    semantic_search_results?: any;
    semantic_search_synced?: boolean;
    semantic_search_average_score?: number;
    [key: string]: any; // Allow future extensibility
  };
  /** Operational numbers useful for performance monitoring */
  metrics: {
    tool_used?: string;
    execution_time_ms?: number;
    embedding_calls?: number;
    rows?: number;
    [key: string]: any; // Additional numeric metrics in future
  };
  /** Allow legacy flat fields for backward compatibility */
  [key: string]: any;
}

// Streaming chunk for real-time responses
export interface AgentChunk {
  type: 'content' | 'tool_call' | 'metadata' | 'error' | 'complete';
  content: any;
  metadata?: Record<string, any>;
}

// Agent configuration
export interface AgentConfig {
  name: string;
  version: string;
  description?: string;
  archetype: AgentArchetype;
  capabilities: AgentCapability[];
  modelConfig?: ModelConfig;
  memoryConfig?: MemoryConfig;
  securityConfig?: SecurityConfig;
  observabilityConfig?: ObservabilityConfig;
}

// Model configuration
export interface ModelConfig {
  provider: 'openai' | 'anthropic' | 'custom';
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
}

// Memory configuration
export interface MemoryConfig {
  type: 'in-memory' | 'redis' | 'vector' | 'hierarchical';
  maxMessages?: number;
  ttl?: number;
  vectorStore?: {
    provider: 'pgvector' | 'pinecone' | 'weaviate';
    dimension: number;
    similarityThreshold: number;
  };
}

// Security configuration
export interface SecurityConfig {
  inputSanitization: boolean;
  outputFiltering: boolean;
  promptInjectionDetection: boolean;
  piiDetection: boolean;
  rateLimiting?: {
    maxRequests: number;
    windowMs: number;
  };
  allowedTools?: string[];
  blockedPatterns?: string[];
}

// Observability configuration
export interface ObservabilityConfig {
  telemetry: boolean;
  metrics: boolean;
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    includePrompts: boolean;
    includeResponses: boolean;
  };
  tracing?: {
    provider: 'opentelemetry' | 'datadog' | 'newrelic';
    endpoint: string;
  };
}

// Performance configuration
export interface PerformanceConfig {
  timeout: number;
  maxRetries: number;
  caching: boolean;
  streaming: boolean;
  batchSize?: number;
}

// Agent memory interface
export interface AgentMemory {
  add(message: MemoryEntry): Promise<void>;
  get(count?: number): Promise<MemoryEntry[]>;
  search(query: string, limit?: number): Promise<MemoryEntry[]>;
  clear(): Promise<void>;
  consolidate(): Promise<void>;
}

// Memory entry
export interface MemoryEntry {
  id: string;
  timestamp: Date;
  type: 'user' | 'assistant' | 'system' | 'tool';
  content: any;
  metadata?: Record<string, any>;
  embedding?: number[];
}

// Agent tool interface
export interface AgentTool {
  name: string;
  description: string;
  parameters: z.ZodObject<any>;
  execute: (params: any) => Promise<any>;
}

// Security context
export interface SecurityContext {
  organizationId: string;
  userId: string;
  permissions: string[];
  ipAddress?: string;
  userAgent?: string;
}

// Health status
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  checks: {
    memory: boolean;
    dependencies: boolean;
    model: boolean;
  };
  details?: Record<string, any>;
}

// Agent metrics
export interface AgentMetrics {
  invocations: number;
  errors: number;
  avgLatency: number;
  p95Latency: number;
  p99Latency: number;
  tokensUsed: number;
  cost: number;
  lastInvocation?: Date;
}

// Agent dependencies for dependency injection
export interface AgentDependencies {
  logger?: any;
  telemetry?: any;
  redis?: any;
  database?: any;
  mcpClient?: any;
  securityFramework?: any;
}
