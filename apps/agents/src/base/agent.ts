import {
  AgentArchetype,
  Agent<PERSON><PERSON>bility,
  AgentInput,
  AgentResult,
  AgentChunk,
  AgentConfig,
  HealthStatus,
  AgentMetrics,
} from './types';
import { ILogger } from '../integration/mcp-server/mcp-bridge-enhanced';
import { getLogger, getLoggerWithTraceId } from '../observability/global-logger';

/**
 * Base agent interface with production-grade features
 */
export interface BaseAgent {
  readonly name: string;
  readonly version: string;
  readonly archetype: AgentArchetype;
  readonly capabilities: AgentCapability[];

  /**
   * Core execution method
   */
  invoke(input: AgentInput): Promise<AgentResult>;

  /**
   * Streaming support for real-time responses
   */
  stream(input: AgentInput): AsyncGenerator<AgentChunk>;

  /**
   * Lifecycle management
   */
  initialize(config: AgentConfig): Promise<void>;
  destroy(): Promise<void>;

  /**
   * Health and metrics
   */
  healthCheck(): Promise<HealthStatus>;
  getMetrics(): AgentMetrics;
}

/**
 * Abstract base agent implementation
 */
export abstract class AbstractAgent implements BaseAgent {
  protected logger: ILogger;
  protected config: AgentConfig;
  protected initialized: boolean = false;
  protected metrics: AgentMetrics = {
    invocations: 0,
    errors: 0,
    avgLatency: 0,
    p95Latency: 0,
    p99Latency: 0,
    tokensUsed: 0,
    cost: 0,
  };

  /**
   * Creates a new agent instance.
   *
   * @param config - Partial or complete {@link AgentConfig}. The object is
   *                 stored and later merged with configuration supplied to
   *                 {@link initialize}.
   */
  constructor(config: AgentConfig) {
    this.config = config;
    this.logger = getLogger();
  }

  get name(): string {
    return this.config.name;
  }

  get version(): string {
    return this.config.version;
  }

  get archetype(): AgentArchetype {
    return this.config.archetype;
  }

  get capabilities(): AgentCapability[] {
    return this.config.capabilities;
  }

  /**
   * Initialises the agent instance and calls the subclass hook
   * {@link onInitialize}. This method **must** be invoked exactly once before
   * `invoke`/`stream` are used.
   *
   * @param config - Finalised configuration object which may extend the one
   *                 provided in the constructor.
   * @throws `Error` if the agent has already been initialised.
   */
  async initialize(config: AgentConfig): Promise<void> {
    if (this.initialized) {
      throw new Error(`Agent ${this.name} is already initialized`);
    }

    this.config = { ...this.config, ...config };
    await this.onInitialize();
    this.initialized = true;
  }

  /**
   * Gracefully disposes resources by calling the subclass hook
   * {@link onDestroy}. Multiple calls are safe and will be ignored after the
   * first destroy.
   */
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    await this.onDestroy();
    this.initialized = false;
  }

  /**
   * Executes a single request/response cycle.
   *
   * @param input - Input payload as defined by the concrete agent implementation.
   * @returns {@link AgentResult} produced by the subclass.
   * @throws `Error` if the agent has not been initialised or if the subclass
   *         throws during processing.
   */
  async invoke(input: AgentInput): Promise<AgentResult> {
    if (!this.initialized) {
      throw new Error(`Agent ${this.name} is not initialized`);
    }

    const startTime = Date.now();
    const logger = input.traceId ? getLoggerWithTraceId(input.traceId) : this.logger;

    try {
      this.metrics.invocations++;
      logger.info(`Starting agent invocation`, {
        agent: this.name,
        sessionId: input.sessionId,
        organizationId: input.organizationId,
        userId: input.userId,
      });

      const result = await this.doInvoke(input);

      // Update metrics
      const latency = Date.now() - startTime;
      this.updateLatencyMetrics(latency);

      if (result.metadata?.tokensUsed) {
        this.metrics.tokensUsed += result.metadata.tokensUsed;
      }

      if (result.metadata?.cost) {
        this.metrics.cost += result.metadata.cost;
      }

      this.metrics.lastInvocation = new Date();

      logger.info(`Agent invocation completed successfully`, {
        agent: this.name,
        latencyMs: latency,
        tokensUsed: result.metadata?.tokensUsed,
        cost: result.metadata?.cost,
      });

      return result;
    } catch (error) {
      this.metrics.errors++;
      logger.error(error, `Agent invocation failed`, {
        agent: this.name,
        latencyMs: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Streams incremental chunks back to the caller. The default implementation
   * first verifies that the agent supports the `STREAMING` capability and then
   * delegates to {@link doStream}.
   *
   * @param input - Same structure as used in {@link invoke}.
   * @yields A series of {@link AgentChunk} objects.
   * @throws `Error` if the agent is not initialised or does not support
   *         streaming.
   */
  async *stream(input: AgentInput): AsyncGenerator<AgentChunk> {
    if (!this.initialized) {
      throw new Error(`Agent ${this.name} is not initialized`);
    }

    if (!this.capabilities.includes(AgentCapability.STREAMING)) {
      throw new Error(`Agent ${this.name} does not support streaming`);
    }

    const startTime = Date.now();

    try {
      this.metrics.invocations++;

      yield* this.doStream(input);

      // Update metrics
      const latency = Date.now() - startTime;
      this.updateLatencyMetrics(latency);
      this.metrics.lastInvocation = new Date();
    } catch (error) {
      this.metrics.errors++;
      yield {
        type: 'error',
        content: error,
      };
    }
  }

  /**
   * Performs an aggregated health assessment composed of memory, dependency,
   * and model checks.
   *
   * @returns {HealthStatus} Object summarising component status as well as
   *          operational metrics.
   */
  async healthCheck(): Promise<HealthStatus> {
    const checks = await this.performHealthChecks();

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (!checks.memory || !checks.dependencies || !checks.model) {
      status = checks.memory && checks.dependencies ? 'degraded' : 'unhealthy';
    }

    return {
      status,
      timestamp: new Date(),
      checks,
      details: {
        initialized: this.initialized,
        metrics: this.metrics,
      },
    };
  }

  /**
   * Returns a snapshot of runtime metrics collected since process start.
   */
  getMetrics(): AgentMetrics {
    return { ...this.metrics };
  }

  /**
   * Default implementation of streaming by converting a single `invoke` result
   * into two chunks: `content` and optional `metadata`.
   *
   * Subclasses may override this for true token-level streaming.
   *
   * @param input - Request payload provided by the caller.
   */
  protected async *doStream(input: AgentInput): AsyncGenerator<AgentChunk> {
    // Default implementation: convert invoke to stream
    const result = await this.doInvoke(input);
    yield {
      type: 'content',
      content: result.output,
    };

    if (result.metadata) {
      yield {
        type: 'metadata',
        content: result.metadata,
      };
    }
  }

  /**
   * Carry out subsystem health checks. The base implementation simply reports
   * all components healthy; subclasses should override to perform real checks.
   *
   * @returns Object with boolean flags for the different subsystems.
   */
  protected async performHealthChecks(): Promise<{
    memory: boolean;
    dependencies: boolean;
    model: boolean;
  }> {
    return {
      memory: true,
      dependencies: true,
      model: true,
    };
  }

  /**
   * Updates moving-average latency statistics.
   *
   * @param latency - Measured latency in milliseconds for the last invocation.
   */
  private updateLatencyMetrics(latency: number): void {
    const alpha = 0.1; // Smoothing factor

    if (this.metrics.invocations === 1) {
      this.metrics.avgLatency = latency;
      this.metrics.p95Latency = latency;
      this.metrics.p99Latency = latency;
    } else {
      // Exponential moving average
      this.metrics.avgLatency = alpha * latency + (1 - alpha) * this.metrics.avgLatency;

      // Simple approximation for percentiles
      // In production, use a proper percentile algorithm
      if (latency > this.metrics.p95Latency) {
        this.metrics.p95Latency = latency;
      }

      if (latency > this.metrics.p99Latency) {
        this.metrics.p99Latency = latency;
      }
    }
  }

  /**
   * Abstract methods to be implemented by subclasses
   */
  /**
   * Get a logger with traceId context for structured logging
   */
  protected getLoggerWithTraceId(traceId?: string): ILogger {
    return traceId ? getLoggerWithTraceId(traceId) : this.logger;
  }

  protected abstract onInitialize(): Promise<void>;
  protected abstract onDestroy(): Promise<void>;
  protected abstract doInvoke(input: AgentInput): Promise<AgentResult>;
}
