import { BaseAgent } from './agent';
import { AgentConfig, AgentDependencies } from './types';

/**
 * Agent registry entry
 */
interface AgentRegistryEntry {
  type: string;
  factory: AgentFactoryFunction;
  metadata?: {
    description?: string;
    author?: string;
    tags?: string[];
  };
}

/**
 * Factory function type
 */
type AgentFactoryFunction = (
  config: AgentConfig,
  dependencies: AgentDependencies
) => Promise<BaseAgent>;

/**
 * Agent factory with dependency injection and plugin system
 */
export class AgentFactory {
  private static registry = new Map<string, AgentRegistryEntry>();
  private static defaultDependencies: AgentDependencies = {};

  /**
   * Registers a new agent type with its factory function in the global registry.
   *
   * Registration is *idempotent*—attempting to register the same `type` more
   * than once will be silently ignored. This design choice allows tests to call
   * the registration helper repeatedly without the need for manual cleanup.
   *
   * @param type - Unique identifier used to resolve the agent later via
   *               {@link AgentFactory.create | `create`}.
   * @param factory - Async factory that receives the {@link AgentConfig} and a
   *                  fully-merged {@link AgentDependencies} object and returns
   *                  an instantiated agent.
   * @param metadata - _(optional)_ Descriptive information such as
   *                   `description`, `author`, and `tags` for documentation or
   *                   runtime discovery.
   *
   * @example
   * ```ts
   * AgentFactory.register('echo', async (cfg) => createEchoAgent(cfg), {
   *   description: 'Echoes user input',
   *   author: 'AskInfoSec',
   *   tags: ['testing']
   * });
   * ```
   */
  static register(
    type: string,
    factory: AgentFactoryFunction,
    metadata?: AgentRegistryEntry['metadata']
  ): void {
    // Make registration idempotent – if already registered, skip re-registration
    if (this.registry.has(type)) {
      return; // Silently ignore duplicate registrations to support repeated calls in tests
    }

    this.registry.set(type, {
      type,
      factory,
      metadata,
    });
  }

  /**
   * Removes an agent type from the registry.
   *
   * @param type - The identifier previously used in {@link AgentFactory.register}.
   * @returns `true` if the type existed and has been removed, otherwise `false`.
   */
  static unregister(type: string): boolean {
    return this.registry.delete(type);
  }

  /**
   * Returns the list of all currently registered agent identifiers.
   *
   * @returns Array of registration keys.
   */
  static getRegisteredTypes(): string[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Retrieves the optional metadata associated with a particular agent type.
   *
   * @param type - Agent identifier.
   * @returns Metadata object or `undefined` if the type is unknown.
   */
  static getMetadata(type: string): AgentRegistryEntry['metadata'] | undefined {
    return this.registry.get(type)?.metadata;
  }

  /**
   * Defines default dependency instances injected into *all* subsequently
   * created agents unless explicitly overridden.
   *
   * @param dependencies - Concrete implementations to use as defaults.
   * @example
   * ```ts
   * AgentFactory.setDefaultDependencies({ logger, cache });
   * ```
   */
  static setDefaultDependencies(dependencies: AgentDependencies): void {
    this.defaultDependencies = { ...dependencies };
  }

  /**
   * Instantiates and initialises a single agent.
   *
   * The method performs the following steps:
   * 1. Looks up the factory registered for `type`.
   * 2. Merges `dependencies` with previously configured defaults.
   * 3. Applies fall-back capability configuration for minimal specs.
   * 4. Validates the final {@link AgentConfig}.
   * 5. Invokes the factory and runs the agent's `initialize` lifecycle hook.
   *
   * @typeParam T - Concrete subclass of {@link BaseAgent} expected by the caller.
   * @param type - Agent identifier previously registered via {@link register}.
   * @param config - Configuration object adhering to {@link AgentConfig}.
   * @param dependencies - _(optional)_ Overrides for the default dependency set.
   * @returns Fully initialised agent instance.
   * @throws `Error` when the requested `type` is unknown or the config fails validation.
   */
  static async create<T extends BaseAgent>(
    type: string,
    config: AgentConfig,
    dependencies?: Partial<AgentDependencies>
  ): Promise<T> {
    const entry = this.registry.get(type);

    if (!entry) {
      throw new Error(
        `Agent type '${type}' is not registered. ` +
          `Available types: ${this.getRegisteredTypes().join(', ')}`
      );
    }

    // Merge dependencies
    const mergedDependencies: AgentDependencies = {
      ...this.defaultDependencies,
      ...dependencies,
    };

    // Auto-inject default capability when none provided to support minimal test agents
    if (!config.capabilities || config.capabilities.length === 0) {
      config.capabilities = ['chat' as any];
    }

    // Validate configuration (after applying defaults)
    this.validateConfig(config);

    // Create agent instance
    const agent = await entry.factory(config, mergedDependencies);

    // Initialize agent
    await agent.initialize(config);

    return agent as T;
  }

  /**
   * Convenience helper to create multiple agents in parallel.
   *
   * @typeParam T - Common agent base type.
   * @param specs - Array of objects each describing the agent `type`,
   *                `config`, and optional `dependencies`.
   * @returns Promise resolving to an array of agents preserving the order of `specs`.
   */
  static async createBatch<T extends BaseAgent>(
    specs: Array<{
      type: string;
      config: AgentConfig;
      dependencies?: Partial<AgentDependencies>;
    }>
  ): Promise<T[]> {
    const promises = specs.map(spec => this.create<T>(spec.type, spec.config, spec.dependencies));

    return Promise.all(promises);
  }

  /**
   * Ensures that the supplied {@link AgentConfig} conforms to mandatory fields
   * and sane limits.
   *
   * @param config - Configuration candidate.
   * @throws `Error` if any required property is missing or invalid.
   * @internal This helper is only used within the factory.
   */
  private static validateConfig(config: AgentConfig): void {
    if (!config.name) {
      throw new Error('Agent name is required');
    }

    if (!config.version) {
      throw new Error('Agent version is required');
    }

    if (!config.archetype) {
      throw new Error('Agent archetype is required');
    }

    if (!config.capabilities || config.capabilities.length === 0) {
      throw new Error('Agent must have at least one capability');
    }

    // Validate model config if provided
    if (config.modelConfig) {
      if (!config.modelConfig.provider) {
        throw new Error('Model provider is required');
      }

      if (!config.modelConfig.model) {
        throw new Error('Model name is required');
      }
    }

    // Validate security config if provided
    if (config.securityConfig?.rateLimiting) {
      if (config.securityConfig.rateLimiting.maxRequests <= 0) {
        throw new Error('Rate limit max requests must be positive');
      }

      if (config.securityConfig.rateLimiting.windowMs <= 0) {
        throw new Error('Rate limit window must be positive');
      }
    }
  }
}

/**
 * Fluent builder that simplifies incremental construction of {@link AgentConfig}
 * objects while keeping type-safety.
 *
 * @example
 * ```ts
 * const agent = await agent('ask_ai')
 *   .name('AskAI')
 *   .version('1.0.0')
 *   .capabilities('chat')
 *   .withDependencies({ logger })
 *   .build();
 * ```
 */
export class AgentBuilder {
  private config: Partial<AgentConfig> = {};
  private dependencies: Partial<AgentDependencies> = {};

  constructor(private type: string) {}

  name(name: string): this {
    this.config.name = name;
    return this;
  }

  version(version: string): this {
    this.config.version = version;
    return this;
  }

  description(description: string): this {
    this.config.description = description;
    return this;
  }

  archetype(archetype: AgentConfig['archetype']): this {
    this.config.archetype = archetype;
    return this;
  }

  capabilities(...capabilities: AgentConfig['capabilities']): this {
    this.config.capabilities = capabilities;
    return this;
  }

  modelConfig(modelConfig: AgentConfig['modelConfig']): this {
    this.config.modelConfig = modelConfig;
    return this;
  }

  memoryConfig(memoryConfig: AgentConfig['memoryConfig']): this {
    this.config.memoryConfig = memoryConfig;
    return this;
  }

  securityConfig(securityConfig: AgentConfig['securityConfig']): this {
    this.config.securityConfig = securityConfig;
    return this;
  }

  observabilityConfig(observabilityConfig: AgentConfig['observabilityConfig']): this {
    this.config.observabilityConfig = observabilityConfig;
    return this;
  }

  withDependencies(dependencies: Partial<AgentDependencies>): this {
    this.dependencies = { ...this.dependencies, ...dependencies };
    return this;
  }

  /**
   * Finalises configuration and delegates instantiation to
   * {@link AgentFactory.create}.
   *
   * @typeParam T - Expected concrete agent type.
   * @returns A fully initialised agent instance.
   */
  async build<T extends BaseAgent>(): Promise<T> {
    return AgentFactory.create<T>(this.type, this.config as AgentConfig, this.dependencies);
  }
}

/**
 * Helper function to create an agent builder
 */
export function agent(type: string): AgentBuilder {
  return new AgentBuilder(type);
}
