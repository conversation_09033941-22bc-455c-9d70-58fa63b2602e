export interface ChatOutput {
  response: string;
  tool_used?: string;
  rows?: number;
  executed_query?: string;
  execution_time_ms?: number;
  warning?: string;
  context_doc_ids?: string[];
  embedding_calls?: number;
  session_id?: string;
  conversation_entries?: number;
}

export interface ProcessedDocument {
  id: string;
  name: string;
  content: string;
  documentType?: string;
  fileType?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

export interface DocumentCache {
  documents: ProcessedDocument[];
  timestamp: Date;
  organizationId: string;
}

export interface RankedDocument extends ProcessedDocument {
  similarityScore: number;
  rank: number;
}

export interface EmbeddingCache {
  embeddings: Map<string, number[]>; // documentId -> embedding vector
  timestamp: Date;
  organizationId: string;
}

export interface AskAIOutput {
  response: string;
  tool_used?: string;
  rows?: number;
  executed_query?: string;
  execution_time_ms?: number;
  warning?: string;
  context_doc_ids?: string[];
  embedding_calls?: number;
  session_id?: string;
  conversation_entries?: number;
}
