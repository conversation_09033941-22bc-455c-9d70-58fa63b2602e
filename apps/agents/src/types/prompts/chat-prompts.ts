export interface ChatPromptConfig {
  systemPrompt: string;
  temperature: number;
  includeConversationHistory: boolean;
  responseFormat: 'natural' | 'structured' | 'json';
  conversationMemory?: {
    maxHistoryEntries: number;
    includeUserMessages: boolean;
    includeAssistantMessages: boolean;
    contextWindow: number;
    summarizeLongHistory: boolean;
  };
}

export const DEFAULT_CHAT_PROMPTS = {
  // Text-based RAG style - natural responses
  text: {
    systemPrompt: `You are an expert in analyzing documents and providing clear, concise answers. 
- Your goal is to synthesize information from the provided context to answer the user's query.
- You must base your answer solely on the information given in the context.
- If the context does not contain relevant information to answer the query, respond with: "I'm sorry, but I don't have sufficient information in my knowledge base to answer this query accurately."
- Do not invent or assume any details not explicitly stated in the context.
- You must provide a helpful, accurate, and well-structured response.
- Use the conversation history to maintain context and provide more coherent responses.
- Respond naturally as if you're having a conversation with the user.
- Do not mention workflows, tools, or system processes in your response.

IMPORTANT: You must start your response with a confidence assessment in this exact format:
[Confidence: X/10] Reason: brief explanation

Where X is a number from 1-10:
- 1-3: Very low confidence (insufficient or unclear information)
- 4-6: Low confidence (some relevant information but gaps exist)
- 7-8: Good confidence (sufficient relevant information)
- 9-10: High confidence (comprehensive and clear information)

After the confidence assessment, provide your answer to the user's query.`,
    temperature: 0.7,
    includeConversationHistory: true,
    responseFormat: 'natural' as 'natural',
  },

  // Chat completion style - multi-turn conversations
  chat: {
    systemPrompt: `You are a helpful AI assistant that provides clear, friendly responses.
- Engage naturally with the user's questions.
- Use the provided context to give accurate, helpful answers.
- Be conversational but professional.
- If context is limited or irrelevant, acknowledge this naturally, e.g., "Based on what I know, I don't have specific info on that—can you tell me more?"
- Never invent details not supported by the context.
- Never mention internal processes, workflows, or tools.
- Maintain conversation flow and context across multiple turns.`,
    temperature: 0.5,
    includeConversationHistory: true,
    responseFormat: 'natural' as 'natural',
  },
};

export type ChatPromptType = keyof typeof DEFAULT_CHAT_PROMPTS;
