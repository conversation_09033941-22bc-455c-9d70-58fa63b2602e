# Unified Prompting System

This system provides consistent prompting across both traditional RAG and multi-agent LangGraph workflows, eliminating the "workflow complete" messages and ensuring natural responses.

## Features

- **Unified Prompts**: Same high-quality prompts across traditional and multi-agent systems
- **Configurable Styles**: Choose from different prompt types for different use cases
- **Langfuse Ready**: Prepared for centralized prompt management
- **Natural Responses**: Eliminates workflow-centric messages
- **A/B Testing**: Easy to compare different prompt variations

## Available Prompt Types

### 1. Traditional (`traditional`)

- **Use Case**: General document analysis and Q&A
- **Style**: Natural, conversational responses
- **Temperature**: 0.7 (balanced creativity)
- **History**: Includes conversation history
- **Best For**: Most general use cases, matching traditional RAG behavior

### 2. Analytical (`analytical`)

- **Use Case**: Structured analysis and insights
- **Style**: Organized, evidence-based responses
- **Temperature**: 0.1 (focused and precise)
- **History**: No conversation history (focused on current query)
- **Best For**: Business analysis, research, structured reports

### 3. Conversational (`conversational`)

- **Use Case**: Friendly, interactive conversations
- **Style**: Casual, helpful, and engaging
- **Temperature**: 0.5 (moderate creativity)
- **History**: Includes conversation history
- **Best For**: Customer support, casual Q&A, interactive sessions

## Usage Examples

### Basic Usage in Supervisors

```typescript
// Traditional RAG style (default)
const supervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
  chatPromptType: 'traditional',
});

// Analytical style for structured responses
const analyticalSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
  chatPromptType: 'analytical',
});

// Conversational style for friendly interactions
const conversationalSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
  chatPromptType: 'conversational',
});
```

### Direct Chat Agent Usage

```typescript
// Create with specific prompt type
const chatAgent = new ChatAgentFactory(langfuseClient, 'traditional');

// Or customize the prompt
const customChatAgent = new ChatAgentFactory(langfuseClient, 'conversational');
customChatAgent.setPromptConfig({
  systemPrompt: `You are a cybersecurity expert assistant...`,
  temperature: 0.3,
  includeConversationHistory: true,
});
```

### Future: Langfuse Integration

```typescript
// Load prompts from Langfuse (coming soon)
const chatAgent = new ChatAgentFactory(langfuseClient, 'traditional');
await chatAgent.loadPromptFromLangfuse('chat-agent-v2', 1);
```

## Configuration Options

```typescript
interface ChatPromptConfig {
  systemPrompt: string; // The main system instruction
  temperature: number; // LLM temperature (0-1)
  includeConversationHistory?: boolean; // Whether to include chat history
  responseFormat?: 'natural' | 'structured'; // Response style hint
}
```

## Benefits

1. **Consistent Quality**: Same prompt engineering across all systems
2. **No Workflow Messages**: Eliminates "workflow complete" and similar system messages
3. **Easy Experimentation**: Switch between prompt types with a single parameter
4. **Future-Proof**: Ready for Langfuse prompt management integration
5. **Performance Tuning**: Different temperatures for different use cases

## Traditional RAG Integration

The traditional RAG system (`traditional-rag.ts`) now also uses the unified prompting system:

```typescript
// Traditional RAG now uses the same prompts as multi-agent
const answer = await callChatAgent(context, input, history, 'traditional');

// Test different prompt types
const variations = await testPromptVariations(context, input, history);
console.log(variations.traditional.answer);
console.log(variations.analytical.answer);
console.log(variations.conversational.answer);

// Get current prompt config for debugging
const currentConfig = getCurrentPromptConfig('traditional');
console.log('Using prompt:', currentConfig.systemPrompt);
```

### Benefits of Unification

- **Consistent Quality**: Both systems produce the same high-quality responses
- **Easy Comparison**: Direct A/B testing between traditional and multi-agent approaches
- **Centralized Management**: All prompts managed in one place
- **No More "Workflow Complete"**: Multi-agent responses are now natural

## Comparison with Traditional RAG

| Aspect           | Traditional RAG (Before) | Multi-Agent (Before)    | Both Systems (After)   |
| ---------------- | ------------------------ | ----------------------- | ---------------------- |
| Response Quality | High                     | Low (workflow messages) | High (unified prompts) |
| Consistency      | Good                     | Poor                    | Excellent              |
| Customization    | Hard-coded               | Hard-coded              | Configurable           |
| A/B Testing      | Difficult                | Difficult               | Easy                   |
| Maintenance      | Manual                   | Manual                  | Centralized            |
| Prompt Source    | Hard-coded in function   | Hard-coded in function  | Unified config file    |

## Next Steps

1. **Test Different Prompt Types**: Try all three types with your use cases
2. **Custom Prompts**: Create domain-specific prompts using `setPromptConfig()`
3. **Langfuse Integration**: Prepare for centralized prompt management
4. **Performance Monitoring**: Use Langfuse to track prompt performance
5. **A/B Testing**: Compare different prompt variations in production
