import { WorkflowInput, WorkflowResult } from '../types';
import { StandardAgentOutput } from '../../../base/types';
import { BaseMessage, HumanMessage, SystemMessage, AIMessage } from '@langchain/core/messages';
import { createSupervisor } from '@langchain/langgraph-supervisor';
import { Annotation } from '@langchain/langgraph';
import { MemorySaver, InMemoryStore } from '@langchain/langgraph';
import { Runnable } from '@langchain/core/runnables';
import { ChatOpenAI } from '@langchain/openai';

// Import agent factories
import { SearchAgentFactory } from './search-agent';
import { AnalysisAgentFactory } from './analysis-agent';
import { ChatAgentFactory } from './chat-agent';

// Import services
import { MCPConnectionManager } from '../../../integration/mcp-server/connection-manager';

// Langfuse observability
import { getLangfuseClient } from '../../../observability/langfuse';
// Langfuse ↔ LangChain integration handler
import { CallbackHandler as LangfuseCallbackHandler } from 'langfuse-langchain';
import { ensureBindTools } from '../../../utils/llm-bindtools-polyfill';
import { ILogger } from '../../../integration/mcp-server/mcp-bridge-enhanced';
import { getLogger } from '../../../observability/global-logger';
// EnrichedSearchResult import removed - now using simplified fields

/**
 * Enhanced state schema extending MessagesAnnotation with domain-specific data
 */
export const SupervisorStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (existing, newMessages) => [...existing, ...newMessages],
    default: () => [],
  }),
  sessionId: Annotation<string>({
    reducer: (existing, newValue) => newValue || existing,
    default: () => '',
  }),
  organizationId: Annotation<string>({
    reducer: (existing, newValue) => newValue || existing,
    default: () => '',
  }),
  userId: Annotation<string | undefined>({
    reducer: (existing, newValue) => newValue || existing,
    default: () => undefined,
  }),
  documents: Annotation<any[]>({
    reducer: (existing, newValue) => {
      if (Array.isArray(newValue)) return newValue;
      return existing;
    },
    default: () => [],
  }),
  // Simplified search results as separate fields instead of nested object
  searchEnrichedContext: Annotation<string | undefined>({
    reducer: (existing, newValue) => {
      const logger = getLogger();
      logger.debug('[SupervisorState:searchEnrichedContext] Reducer called:', {
        existingType: typeof existing,
        newValueType: typeof newValue,
        newValueLength: typeof newValue === 'string' ? newValue.length : 0,
      });
      const result = newValue || existing;
      logger.debug('[SupervisorState:searchEnrichedContext] Final result:', {
        hasValue: !!result,
        length: typeof result === 'string' ? result.length : 0,
      });
      return result;
    },
  }),
  searchRawResults: Annotation<any[] | undefined>({
    reducer: (existing, newValue) => {
      const logger = getLogger();
      logger.debug('[SupervisorState:searchRawResults] Reducer called:', {
        existingType: typeof existing,
        existingLength: Array.isArray(existing) ? existing.length : 0,
        newValueType: typeof newValue,
        newValueLength: Array.isArray(newValue) ? newValue.length : 0,
      });
      const result = newValue || existing;
      logger.debug('[SupervisorState:searchRawResults] Final result:', {
        hasValue: !!result,
        length: Array.isArray(result) ? result.length : 0,
      });
      return result;
    },
  }),
  searchMetadata: Annotation<
    { documentCount: number; averageScore: number; relevanceQuality: string } | undefined
  >({
    reducer: (existing, newValue) => {
      const logger = getLogger();
      logger.debug('[SupervisorState:searchMetadata] Reducer called:', {
        existingType: typeof existing,
        newValueType: typeof newValue,
        newValueKeys: newValue && typeof newValue === 'object' ? Object.keys(newValue) : [],
      });
      const result = newValue || existing;
      logger.debug('[SupervisorState:searchMetadata] Final result:', {
        hasValue: !!result,
        documentCount: result?.documentCount || 0,
      });
      return result;
    },
  }),
  analysis: Annotation<any>({
    reducer: (existing, newValue) => {
      // Handle the case where newValue is the complete tool return object
      if (newValue && typeof newValue === 'object') {
        // If it has analysis property, extract it
        if (newValue.analysis) {
          return newValue.analysis;
        }
        // If it looks like an AnalysisResult directly
        if (newValue.searchResultCount !== undefined || newValue.summary || newValue.insights) {
          return newValue;
        }
      }
      return newValue || existing;
    },
    default: () => null,
  }),
  completedTools: Annotation<string[]>({
    reducer: (existing, newValue) => {
      const logger = getLogger();
      logger.debug('[SupervisorState:completedTools] Reducer called:', {
        existingLength: existing?.length || 0,
        newValueType: typeof newValue,
        newValueLength: Array.isArray(newValue) ? newValue.length : 0,
        newValue: newValue,
      });

      if (Array.isArray(newValue)) {
        const result = [...new Set([...(existing || []), ...newValue])];
        logger.debug('[SupervisorState:completedTools] Final result:', result);
        return result;
      }
      return existing || [];
    },
    default: () => [],
  }),
  metadata: Annotation<any>({
    reducer: (existing, newValue) => ({ ...existing, ...newValue }),
    default: () => ({}),
  }),
  traceId: Annotation<string | undefined>({
    reducer: (existing, newValue) => newValue || existing,
    default: () => undefined,
  }),
  supervisorSpanId: Annotation<string | undefined>({
    reducer: (existing, newValue) => newValue || existing,
    default: () => undefined,
  }),
});

type SupervisorState = typeof SupervisorStateAnnotation.State;

/** Add a helper type for the compiled graph */
type CompiledWorkflow = Runnable<Partial<SupervisorState>, unknown> & {
  getState: (options: any) => Promise<any>;
};

/**
 * Configuration for the idiomatic supervisor
 */
export interface IdiomaticSupervisorConfig {
  maxExecutionTime?: number;
  enableDebug?: boolean;
  fallbackEnabled?: boolean;
  outputMode?: 'full_history' | 'last_message';
  enablePersistence?: boolean;
  maxConversationLength?: number;
  enableResourceLimits?: boolean;
  chatPromptType?: 'text' | 'chat';
}

/**
 * Production-grade LangGraph supervisor using idiomatic createSupervisor pattern
 * This implementation follows the official LangGraph supervisor architecture
 */
export class IdiomaticLangGraphSupervisor {
  /** compiled LangGraph runtime */
  private workflow: CompiledWorkflow | null = null;
  private config: Required<IdiomaticSupervisorConfig>;
  private connectionManager: MCPConnectionManager;
  private langfuseClient: any;
  private logger!: ILogger;

  // Agent factories
  private searchAgentFactory: SearchAgentFactory;
  private analysisAgentFactory: AnalysisAgentFactory;
  private chatAgentFactory: ChatAgentFactory;

  // Memory and persistence (currently in-memory; will switch to RedisSaver once package is GA)
  private checkpointer: MemorySaver;
  private store: InMemoryStore;
  private redisClient: any | null = null;
  private activeThreads: Set<string> = new Set();

  /**
   * A promise that represents completion of async constructor work
   * (loading prompts from Langfuse and building the LangGraph workflow).
   * All public async methods that depend on the workflow will await this before proceeding.
   */
  private initializationPromise: Promise<void>;

  constructor(connectionManager: MCPConnectionManager, config: IdiomaticSupervisorConfig = {}) {
    this.connectionManager = connectionManager;
    this.langfuseClient = getLangfuseClient();
    this.logger = getLogger();
    this.config = {
      maxExecutionTime: 30000,
      enableDebug: process.env.NODE_ENV === 'development',
      fallbackEnabled: true,
      outputMode: 'last_message',
      enablePersistence: true,
      maxConversationLength: 100,
      enableResourceLimits: true,
      chatPromptType: 'text',
      ...config,
    };

    // Initialize memory and persistence (temporary: always in-memory)
    // TODO: switch to RedisSaver once @langchain/langgraph-checkpoint-redis is published.
    this.checkpointer = new MemorySaver();
    this.store = new InMemoryStore();

    // Initialize agent factories
    this.searchAgentFactory = new SearchAgentFactory(this.connectionManager, this.langfuseClient);
    this.analysisAgentFactory = new AnalysisAgentFactory(this.langfuseClient);
    this.chatAgentFactory = new ChatAgentFactory(this.langfuseClient, this.config.chatPromptType);

    // Capture logger locally for use inside asynchronous initialization closure.
    const logger = this.logger;

    // Kick-off asynchronous initialization.  We cannot "await" inside the
    // constructor, so we create a promise that callers can wait on.
    this.initializationPromise = (async () => {
      try {
        // await this.initializePrompts();
        await Promise.resolve(); // Just do nothing
      } catch (err) {
        // initializePrompts() already handles and logs errors internally, but we
        // guard here to avoid an unhandled promise rejection.
        logger.warn(
          `[IdiomaticSupervisor] initializePrompts encountered an error: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      } finally {
        // Always attempt to build the workflow – even if prompt loading failed we
        // still want a functional supervisor (it will fall back to default prompts).
        this.initializeWorkflow();
      }
    })();
  }

  /**
   * Ensures that asynchronous constructor work is finished.
   * Must be awaited before any operation that depends on the compiled workflow.
   */
  private async ensureInitialized(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  /**
   * Initialize prompts from Langfuse
   */
  // private async initializePrompts(): Promise<void> {
  //   try {
  //     // Load chat agent prompt from Langfuse using the prompt type directly
  //     const chatPromptName = this.config.chatPromptType;

  //     // Use try-catch to handle potential getPrompt errors gracefully
  //     try {
  //       await this.chatAgentFactory.loadPromptFromLangfuse(chatPromptName);

  //       if (this.config.enableDebug) {
  //         this.logger.info(`[IdiomaticSupervisor] Loaded chat agent prompt: ${chatPromptName}`);
  //       }
  //     } catch (promptError) {
  //       this.logger.warn(
  //         `[IdiomaticSupervisor] Failed to load chat agent prompt ${chatPromptName}: ${promptError instanceof Error ? promptError.message : String(promptError)}`
  //       );
  //       // Continue with default prompts - this is non-blocking
  //     }
  //   } catch (error) {
  //     this.logger.warn(
  //       `[IdiomaticSupervisor] Failed to initialize prompts from Langfuse: ${error}`
  //     );
  //     // Continue with default prompts - this is non-blocking
  //   }
  // }

  /**
   * Initialize the LangGraph workflow using createSupervisor
   */
  private initializeWorkflow(): void {
    try {
      // Create the LLM instance with state inspection tools
      const llmAgentSupervisor = ensureBindTools(
        new ChatOpenAI({ model: 'gpt-4o-mini', temperature: 0.1 })
      );
      // Create specialized agents using factories
      const baseSearchAgent = this.searchAgentFactory.createAgent(SupervisorStateAnnotation);
      const baseAnalysisAgent = this.analysisAgentFactory.createAgent(SupervisorStateAnnotation);
      const baseChatAgent = this.chatAgentFactory.createAgent(SupervisorStateAnnotation);

      // Create the supervisor workflow
      const supervisorWorkflow = createSupervisor({
        supervisorName: 'idiomatic_supervisor',
        includeAgentName: 'inline',
        agents: [baseSearchAgent, baseAnalysisAgent, baseChatAgent],
        llm: llmAgentSupervisor,
        stateSchema: SupervisorStateAnnotation,
        outputMode: this.config.outputMode,
        addHandoffBackMessages: false, // Disable handoff messages to prevent workflow completion messages
        prompt: `You are a team supervisor managing a workflow of three specialized AI experts. Your goal is to efficiently process user requests by retrieving information, analyzing it, and generating a response.

**Available Experts and Workflow:**

1.  **\`search_expert\`**: Your primary information gatherer.
    *   **Responsibility**: Performs an intelligent, multi-step search. It first checks a persistent Redis cache for existing indexed documents. If necessary, it will fetch, index, and search new documents from the knowledge base.
    *   **When to call**: Always call this expert **first** to gather context for the user's query.
    *   **State Update**: This expert will populate the \`searchResults\` field with an EnrichedSearchResult object containing enrichedContext, rawResults, and metadata.

2.  **\`analysis_expert\`**: The data synthesizer.
    *   **Responsibility**: Analyzes the search results provided by \`search_expert\` to extract key insights, themes, and patterns.
    *   **When to call**: Always call this expert **after** \`search_expert\` has returned search results AND the \`searchResults\` field is populated. This is a mandatory step before generating a response.
    *   **State Requirement**: This expert requires \`searchResults\` to be populated with valid data from \`search_expert\`.
    *   **State Update**: This expert will populate the \`analysis\` field with comprehensive analysis data.

3.  **\`response_expert\`**: The final communicator.
    *   **Responsibility**: Generates a comprehensive, user-facing response using the context from \`search_expert\` and \`analysis_expert\`.
    *   **When to call**: Call this expert **last**, once both \`searchResults\` and \`analysis\` fields are populated.
    *   **State Requirements**: This expert requires both \`searchResults\` and \`analysis\` to be populated.

**Your Decision-Making Process:**

You must follow this sequence strictly. Do not call experts out of order or repeat calls to the same expert. ALWAYS check the state before making decisions.

1.  **Start with Search**: Check if \`searchResults\` is undefined, null, or empty. If so, delegate to \`search_expert\`.
2.  **Proceed to Analysis**: Check if \`searchResults\` is populated BUT \`analysis\` is undefined, null, or empty. If so, delegate to \`analysis_expert\`.
3.  **Generate Response**: Check if both \`searchResults\` and \`analysis\` are populated. If so, delegate to \`response_expert\`.
4.  **Finish**: Once \`response_expert\` has run, the task is complete. Terminate the workflow.

**STATE VALIDATION RULES:**
- Before calling \`analysis_expert\`, VERIFY that \`searchResults\` contains valid data with enrichedContext, rawResults, and metadata properties.
- Before calling \`response_expert\`, VERIFY that both \`searchResults\` and \`analysis\` contain valid data.
- If state validation fails, log the issue and retry the previous step.

**IMPORTANT**: You are a silent supervisor. Do not add any completion messages or workflow status updates. Simply route to the appropriate expert and let their responses be the final output. The response_expert's output should be the final user-facing response.

Inspect the state carefully at each step to choose the correct expert.`,
      });

      // Compile with memory and store for conversation persistence
      const compileOptions: any = {};

      if (this.config.enablePersistence) {
        compileOptions.checkpointer = this.checkpointer;
        compileOptions.store = this.store;
      }

      // Add interrupts for debugging if enabled
      if (this.config.enableDebug) {
        compileOptions.interruptBefore = [];
        compileOptions.interruptAfter = [];
      }

      this.workflow = supervisorWorkflow.compile(compileOptions) as unknown as CompiledWorkflow;

      if (this.config.enableDebug) {
        this.logger.info(
          '[IdiomaticSupervisor] Workflow initialized successfully using createSupervisor'
        );
        this.logger.info(
          `[IdiomaticSupervisor] Agents configured: ${JSON.stringify(
            ['search_expert', 'analysis_expert', 'response_expert'],
            null,
            2
          )}`
        );
        this.logger.info(`[IdiomaticSupervisor] Output mode: ${this.config.outputMode}`);
      }
    } catch (error) {
      this.logger.error(error, '[IdiomaticSupervisor] Failed to initialize workflow');
      throw new Error(`Failed to initialize idiomatic LangGraph workflow: ${error}`);
    }
  }

  /**
   * Executes the compiled LangGraph workflow.
   *
   * Workflow life-cycle in detail:
   *
   * 1. `createSupervisor(...)` builds an in-memory graph specification that contains
   *    the root router (named "supervisor") plus three child nodes that are the
   *    specialized expert Runnables returned by the agent factories
   *    (`searchAgent`, `analysisAgent`, `chatAgent`).
   *
   * 2. `supervisorWorkflow.compile(...)` turns that specification into **one**
   *    concrete `Runnable`.  We store that instance in `this.workflow`.
   *    The resulting object implements the LangChain `Runnable` interface, so it
   *    exposes `.invoke()`, `.stream()`, and—via our intersection type—`.getState()`.
   *
   * 3. Calling `this.workflow.invoke(initialState, { configurable: { thread_id } })`
   *    kicks off the entire execution:
   *      a. The router prompt inspects the state and decides which expert to call.
   *      b. The chosen expert runs, mutating/augmenting the state (e.g. adding
   *         `searchResults`, `analysis`, or response messages).
   *      c. Control returns to the router which re-evaluates the updated state
   *         and selects the next expert.  This loop continues until the router
   *         decides the conversation is complete (after `response_expert`).
   *
   * 4. The fully aggregated state is returned by `.invoke()`.  Here we extract the
   *    last `AIMessage` as the user-visible answer, compute execution metrics, and
   *    wrap everything in our `WorkflowResult` domain object.
   *
   * Implementation notes:
   * • We pass a unique `thread_id` so each conversation has isolated memory.
   * • Persistence is handled by `MemorySaver` + `InMemoryStore` when enabled.
   * • The router prompt includes a guard: each expert may only be invoked once.
   */
  async executeWorkflow(input: WorkflowInput, parentSpan?: any): Promise<WorkflowResult> {
    // Wait for constructor async work to finish
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    const startTime = Date.now();
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const threadId = input.sessionId || `thread_${workflowId}`;

    // Create Langfuse span for supervisor execution if parentSpan provided
    const langfuseClient = this.langfuseClient;
    let supervisorSpan: any = null;
    let supervisorSpanId: string | undefined;

    if (langfuseClient && parentSpan) {
      try {
        supervisorSpan = langfuseClient.span({
          traceId: input.traceId,
          parentObservationId: parentSpan.id,
          name: 'idiomatic_supervisor',
          input: input.input, // Add the supervisor input
          metadata: {
            workflowId,
            threadId,
            organizationId: input.organizationId,
            supervisorType: 'idiomatic',
          },
        });
        supervisorSpanId = supervisorSpan.id;
        this.logger.info(
          `[IdiomaticSupervisor] Langfuse supervisor span created | workflowId=${workflowId}`
        );
      } catch (error) {
        this.logger.error(error, '[IdiomaticSupervisor] Failed to create supervisor span');
      }
    }

    // Create a Langfuse handler to propagate tracing to child runs
    const langfuseHandler = supervisorSpan
      ? new LangfuseCallbackHandler({ root: supervisorSpan })
      : undefined;

    // Resource management
    if (this.config.enableResourceLimits) {
      this.activeThreads.add(threadId);

      // Cleanup old threads if too many active
      if (this.activeThreads.size > 50) {
        const oldThreads = Array.from(this.activeThreads).slice(0, 10);
        oldThreads.forEach(thread => this.activeThreads.delete(thread));
      }
    }

    try {
      if (this.config.enableDebug) {
        this.logger.info(
          `[IdiomaticSupervisor] Starting workflow ${workflowId} with thread ${threadId}`
        );
        this.logger.info(`[IdiomaticSupervisor] Active threads: ${this.activeThreads.size}`);
      }

      // Check conversation length limits
      if (this.config.enablePersistence && this.config.maxConversationLength > 0) {
        await this.enforceConversationLimits(threadId);
      }

      // Create initial state
      const initialState: Partial<SupervisorState> = {
        messages: [
          new HumanMessage(
            typeof input.input === 'string' ? input.input : JSON.stringify(input.input)
          ),
          new SystemMessage(`Context: organizationId=${input.organizationId || 'default-org'}`),
        ] as BaseMessage[],
        sessionId: input.sessionId || 'default-session',
        organizationId: input.organizationId || 'default-org',
        userId: input.userId,
        documents: [],
        searchEnrichedContext: undefined,
        searchRawResults: undefined,
        searchMetadata: undefined,
        analysis: null,
        metadata: {
          workflowId,
          startTime,
        },
        traceId: input.traceId,
        supervisorSpanId,
      };

      // Execute the workflow with thread-based memory
      if (this.config.enableDebug) {
        this.logger.info('[IdiomaticSupervisor] Starting workflow with initial state:', {
          initialStateKeys: Object.keys(initialState),
          hasSearchEnrichedContext: !!initialState.searchEnrichedContext,
          hasSearchRawResults: !!initialState.searchRawResults,
          hasSearchMetadata: !!initialState.searchMetadata,
          hasAnalysis: !!initialState.analysis,
          messagesLength: initialState.messages?.length || 0,
          completedToolsValue: initialState.completedTools,
        });
      }

      const result: any = await this.workflow.invoke(initialState, {
        configurable: { thread_id: threadId },
        callbacks: langfuseHandler ? [langfuseHandler] : [],
      });

      if (this.config.enableDebug) {
        this.logger.info('[IdiomaticSupervisor] Workflow execution completed:', {
          resultKeys: Object.keys(result || {}),
          hasMessages: !!result?.messages,
          messagesLength: result?.messages?.length || 0,
          hasValues: !!result?.values,
          valuesKeys: result?.values ? Object.keys(result.values) : [],
        });
      }

      if (this.config.enableDebug) {
        this.logger.info('[IdiomaticSupervisor] Workflow completed with result:', {
          resultKeys: Object.keys(result),
          hasSearchResults: !!result.searchResults,
          hasAnalysis: !!result.analysis,
          messagesLength: result.messages?.length || 0,
        });
      }

      this.logger.debug(
        `[IdiomaticSupervisor] Result of [this.workflow.invoke], (result): ${JSON.stringify(result, null, 2).substring(0, 100)}`
      );
      // After the main invocation, parse the tool outputs and update state
      if (result.messages && Array.isArray(result.messages)) {
        const toolOutputs = result.messages
          .filter((msg: any) => msg.role === 'tool' && msg.content)
          .map((msg: any) => {
            try {
              return JSON.parse(msg.content);
            } catch (e) {
              return null;
            }
          })
          .filter((item: any) => item !== null);

        if (toolOutputs.length > 0) {
          // Merge tool outputs back into the final state
          const finalState = toolOutputs.reduce(
            (acc: object, current: object) => ({ ...acc, ...current }),
            result
          );
          const formatted = this._formatResponse(finalState, workflowId, startTime);

          // End supervisor span and attach metrics
          if (supervisorSpan) {
            try {
              supervisorSpan.end({
                output: formatted.output.response.substring(0, 1000),
                metadata: {
                  execution_time_ms: formatted.executionTime,
                  documents: formatted.output.metrics?.rows ?? 0,
                  tools_used: formatted.toolsUsed,
                },
              });
            } catch (_) {
              // ignore
            }
          }

          // Push Langfuse score metrics
          if (langfuseClient && input.traceId) {
            try {
              langfuseClient.score({
                traceId: input.traceId,
                name: 'langgraph_workflow.latency_ms',
                value: formatted.executionTime,
              });
              langfuseClient.score({
                traceId: input.traceId,
                name: 'langgraph_workflow.document_count',
                value: formatted.output.metrics?.rows ?? 0,
              });
            } catch (_) {
              // ignore
            }
          }

          return formatted;
        }
      }

      const formatted = this._formatResponse(result, workflowId, startTime);

      // End supervisor span and attach metrics for normal path
      if (supervisorSpan) {
        try {
          supervisorSpan.end({
            output: formatted.output.response.substring(0, 1000),
            metadata: {
              execution_time_ms: formatted.executionTime,
              documents: formatted.output.metrics?.rows ?? 0,
              tools_used: formatted.toolsUsed,
            },
          });
        } catch (_) {
          // ignore
        }
      }

      if (langfuseClient && input.traceId) {
        try {
          langfuseClient.score({
            traceId: input.traceId,
            name: 'langgraph_workflow.latency_ms',
            value: formatted.executionTime,
          });
          langfuseClient.score({
            traceId: input.traceId,
            name: 'langgraph_workflow.document_count',
            value: formatted.output.metrics?.rows ?? 0,
          });
        } catch (_) {
          // ignore
        }
      }

      return formatted;
    } catch (error) {
      // Update supervisor span with error
      if (supervisorSpan) {
        try {
          supervisorSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              success: false,
            },
          });
          supervisorSpan.end();
        } catch (spanError) {
          this.logger.error(
            spanError,
            '[IdiomaticSupervisor] Failed to update supervisor span with error'
          );
        }
      }

      if (this.config.enableDebug) {
        this.logger.error(error, `[IdiomaticSupervisor] Workflow ${workflowId} failed`);
      }
      if (this.config.fallbackEnabled) {
        return this.createFallbackResult(input, error as Error);
      }
      throw error;
    } finally {
      // Cleanup resources
      if (this.config.enableResourceLimits) {
        this.activeThreads.delete(threadId);
      }
    }
  }

  /**
   * Extract tools used from message history
   */
  private extractToolsUsed(messages: BaseMessage[]): string[] {
    const toolsUsed: string[] = [];

    for (const message of messages) {
      if (message.constructor.name === 'ToolMessage') {
        const toolMessage = message as any;
        if (toolMessage.name && !toolsUsed.includes(toolMessage.name)) {
          toolsUsed.push(toolMessage.name);
        }
      }
    }

    return toolsUsed;
  }

  /**
   * Create fallback result when workflow fails
   */
  private createFallbackResult(_input: WorkflowInput, error: Error): WorkflowResult {
    return {
      output: {
        response: `I apologize, but I encountered an error processing your request: ${error.message}. Please try again or contact support if the issue persists.`,
        metadata: {},
        metrics: {},
      },
      success: false,
      executionTime: 0,
      toolsUsed: [],
      metadata: {
        tokensUsed: 0,
        latencyMs: 0,
        toolsInvoked: [],
        warnings: [error.message],
      },
      warning: `Fallback response due to error: ${error.message}`,
    };
  }

  /**
   * Get workflow metrics
   */
  getMetrics() {
    return {
      workflowInitialized: !!this.workflow,
      debugEnabled: this.config.enableDebug,
      fallbackEnabled: this.config.fallbackEnabled,
      maxExecutionTime: this.config.maxExecutionTime,
      outputMode: this.config.outputMode,
      persistenceEnabled: this.config.enablePersistence,
      activeThreads: this.activeThreads.size,
      maxConversationLength: this.config.maxConversationLength,
      resourceLimitsEnabled: this.config.enableResourceLimits,
    };
  }

  /**
   * Shutdown the supervisor
   */
  async shutdown(): Promise<void> {
    if (this.config.enableDebug) {
      this.logger.info('[IdiomaticSupervisor] Shutting down');
    }
    // Gracefully close Redis connection if used
    if (this.redisClient) {
      try {
        await this.redisClient.quit();
      } catch {
        // ignore errors during shutdown
      }
    }
    this.workflow = null;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics: any;
  }> {
    const metrics = this.getMetrics();
    const status = this.workflow ? 'healthy' : 'unhealthy';
    return {
      status,
      metrics,
    };
  }

  /**
   * Get conversation history for a thread
   */
  async getConversationHistory(threadId: string): Promise<BaseMessage[]> {
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    try {
      const state = await this.workflow.getState({ configurable: { thread_id: threadId } });
      return state.values.messages || [];
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[IdiomaticSupervisor] Failed to get conversation history for thread ${threadId}`
        );
      }
      return [];
    }
  }

  /**
   * Clear conversation history for a thread
   */
  async clearConversationHistory(threadId: string): Promise<void> {
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    try {
      // Remove from active threads
      this.activeThreads.delete(threadId);

      // Clear from checkpointer if persistence is enabled
      if (this.config.enablePersistence && this.checkpointer) {
        // Note: MemorySaver doesn't have a direct clear method
        // In production, you might use a database-backed checkpointer with clear functionality
        if (this.config.enableDebug) {
          this.logger.info(
            `[IdiomaticSupervisor] Cleared conversation history for thread ${threadId}`
          );
        }
      }
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[IdiomaticSupervisor] Failed to clear conversation history for thread ${threadId}`
        );
      }
    }
  }

  /**
   * Enforce conversation length limits
   */
  private async enforceConversationLimits(threadId: string): Promise<void> {
    await this.ensureInitialized();

    try {
      if (!this.config.enablePersistence || !this.workflow) {
        return;
      }

      const state = await this.workflow.getState({ configurable: { thread_id: threadId } });
      const messages = state.values.messages || [];

      if (messages.length > this.config.maxConversationLength) {
        if (this.config.enableDebug) {
          this.logger.info(
            `[IdiomaticSupervisor] Conversation length (${messages.length}) exceeds limit (${this.config.maxConversationLength}) for thread ${threadId}`
          );
        }

        // In a production system, you might truncate old messages or archive them
        // For now, we just log the situation
      }
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[IdiomaticSupervisor] Failed to enforce conversation limits for thread ${threadId}`
        );
      }
    }
  }

  /**
   * Get active thread count
   */
  getActiveThreadCount(): number {
    return this.activeThreads.size;
  }

  /**
   * Get all active thread IDs
   */
  getActiveThreadIds(): string[] {
    return Array.from(this.activeThreads);
  }

  /**
   * Check if a thread is active
   */
  isThreadActive(threadId: string): boolean {
    return this.activeThreads.has(threadId);
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    activeThreads: number;
    checkpointerType: string;
    storeType: string;
    persistenceEnabled: boolean;
  } {
    return {
      activeThreads: this.activeThreads.size,
      checkpointerType: this.checkpointer.constructor.name,
      storeType: this.store.constructor.name,
      persistenceEnabled: this.config.enablePersistence,
    };
  }

  /**
   * Extract data from tool messages in the conversation
   */
  private extractDataFromMessages(messages: BaseMessage[]): {
    documents: any[];
    searchResults: any[];
    analysis: any | null;
  } {
    const data = {
      documents: [] as any[],
      searchResults: [] as any[],
      analysis: null as any,
    };

    try {
      for (const message of messages) {
        if (message.constructor.name === 'AIMessage') {
          const aiMessage = message as any;
          // Look for agent responses that mention data
          const content = aiMessage.content?.toString() || '';

          // Extract documents from search results that list specific documents
          // Pattern: "1. **Document Name**: description [Relevance Score: 0.xx]"
          const documentListMatches = content.match(
            /\d+\.\s+\*\*([^*]+)\*\*:[^[]*\[Relevance Score: ([\d.]+)\]/gi
          );
          if (documentListMatches && documentListMatches.length > 0) {
            data.documents = documentListMatches.map((match: string, i: number) => {
              const titleMatch = match.match(/\*\*([^*]+)\*\*/);
              const scoreMatch = match.match(/\[Relevance Score: ([\d.]+)\]/);
              return {
                id: `doc_${i}`,
                title: titleMatch?.[1] || `Document ${i + 1}`,
                relevanceScore: scoreMatch ? parseFloat(scoreMatch[1]) : 0,
                source: 'search_results',
                placeholder: false,
              };
            });

            // Also count these as search results
            data.searchResults = [...data.documents];
          }

          // Fallback: count numbered items (1., 2., 3.) in search responses
          if (data.documents.length === 0) {
            const numberedItems = content.match(/^\d+\.\s+/gm);
            if (numberedItems && numberedItems.length > 0) {
              const count = numberedItems.length;
              data.documents = Array(count)
                .fill(null)
                .map((_, i) => ({ id: `doc_${i}`, title: `Document ${i + 1}`, placeholder: true }));
              data.searchResults = [...data.documents];
            }
          }

          // Look for analysis completion indicators
          if (content.match(/analysis.*completed/i) || content.match(/generated.*insights/i)) {
            data.analysis = {
              completed: true,
              extractedFrom: 'message_content',
              placeholder: true,
            };
          }
        }
      }
    } catch (error) {
      this.logger.error(error, '[IdiomaticSupervisor] Error extracting data from messages');
    }

    return data;
  }

  /**
   * Format the final response from the workflow result.
   * This method is called after the workflow execution to extract the AI message content.
   */
  // @ts-ignore
  private _formatResponse(result: any, workflowId: string, startTime: number): WorkflowResult {
    // Retrieve the persisted workflow state (ensures we get any state mutations
    // performed inside child agents via `state: { ... }` payloads)
    const persistedState = result;

    // DEBUG: Log the exact structure of persistedState to diagnose the issue
    if (this.config.enableDebug) {
      this.logger.debug(
        `[IdiomaticSupervisor] persistedState keys: ${Object.keys(persistedState || {})}`
      );
      if (persistedState?.values) {
        this.logger.debug(
          `[IdiomaticSupervisor] persistedState.values keys: ${Object.keys(persistedState.values)}`
        );
        this.logger.debug(
          `[IdiomaticSupervisor] persistedState.values.documents: ${persistedState.values.documents}`
        );
        this.logger.debug(
          `[IdiomaticSupervisor] persistedState.values.searchResults: ${persistedState.values.searchResults}`
        );
        this.logger.debug(
          `[IdiomaticSupervisor]  persistedState.values.analysis: ${persistedState.values.analysis}`
        );
      }
    }

    // `createSupervisor` keeps the domain data inside the `values` property.
    // Fallback to direct access when `values` is not present (future-proofing).
    const stateValues: any = (persistedState && persistedState.values) || persistedState || {};

    // Extract and enhance state data from agent messages
    const extractedData = this.extractDataFromMessages(stateValues.messages || []);

    // Merge extracted data into stateValues for accurate metrics
    const enhancedStateValues = {
      ...stateValues,
      documents:
        extractedData.documents.length > 0 ? extractedData.documents : stateValues.documents || [],
      searchResults:
        extractedData.searchResults.length > 0
          ? extractedData.searchResults
          : stateValues.searchResults || [],
      analysis: extractedData.analysis || stateValues.analysis,
    };

    // DEBUG: Log the final stateValues we're using for metrics
    if (this.config.enableDebug) {
      this.logger.info(`[IdiomaticSupervisor] Final stateValues:`);
      this.logger.info(
        `[IdiomaticSupervisor] enhancedStateValues.documents: ${JSON.stringify(
          enhancedStateValues.documents,
          null,
          2
        )}`
      );
      this.logger.debug(
        `[IdiomaticSupervisor] stateValues.searchResults: ${JSON.stringify(
          enhancedStateValues.searchResults,
          null,
          2
        )}`
      );
      this.logger.debug(
        `[IdiomaticSupervisor] enhancedStateValues.analysis: ${JSON.stringify(
          enhancedStateValues.analysis,
          null,
          2
        )}`
      );
    }

    // Extract textual AI response from the response_expert (chat agent), not supervisor messages
    let responseText = 'Response generated successfully.';
    const messagesArr: any[] = stateValues.messages || [];

    // Look for the response from response_expert first (the actual chat response)
    for (let i = messagesArr.length - 1; i >= 0; i--) {
      const msg = messagesArr[i];
      if (msg instanceof AIMessage && msg.content) {
        const content = msg.content.toString();

        if (this.config.enableDebug) {
          this.logger.debug(
            `[IdiomaticSupervisor] Processing AI message ${i}: ${content.substring(0, 100)}...`
          );
        }

        // Skip supervisor completion messages
        if (
          content.includes('workflow is complete') ||
          content.includes('task is complete') ||
          content.includes('workflow complete') ||
          content.toLowerCase().includes('if you have any further questions')
        ) {
          if (this.config.enableDebug) {
            this.logger.debug(`[IdiomaticSupervisor] Skipping supervisor completion message`);
          }
          continue;
        }
        // This should be the actual response from the response_expert
        responseText = content;
        if (this.config.enableDebug) {
          this.logger.debug(
            `[IdiomaticSupervisor] Selected response: ${content.substring(0, 100)}...`
          );
        }
        break;
      }
    }

    const output: StandardAgentOutput = {
      response: responseText,
      metadata: {
        session_id: (stateValues as any).sessionId || undefined,
        context_doc_ids: (enhancedStateValues.documents || []).map((d: any) => d.id),
        conversation_entries: messagesArr.length,
        semantic_search_results: enhancedStateValues.searchResults,
        semantic_search_synced: false,
        semantic_search_average_score: undefined,
      },
      metrics: {
        execution_time_ms: Date.now() - startTime,
        rows: (enhancedStateValues.documents || []).length,
        tool_used: 'langgraph_workflow',
      },
    };

    return {
      output,
      executionTime: output.metrics.execution_time_ms || 0,
      toolsUsed: this.extractToolsUsed(stateValues.messages || []),
      success: true,
    } as WorkflowResult;
  }
}
