import { tool } from '@langchain/core/tools';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { ChatOpenAI } from '@langchain/openai';
import { AIMessage } from '@langchain/core/messages';
import { ensureBindTools } from '../../../utils/llm-bindtools-polyfill';
import { z } from 'zod';
import { SupervisorState } from '../types';
import { SupervisorStateAnnotation } from './idiomatic-supervisor';
import { getLogger } from '../../../observability/global-logger';
import { EnrichedSearchResult } from '../helpers/semantic-search.executor';

export interface AnalysisResult {
  summary: string;
  insights: string[];
  themes: string[];
  relevanceScore: number;
  recommendations: string[];
  searchResultCount: number;
  analysisTimestamp: number;
  analysisType: any;
  query: any;
  enrichedMetadata: {
    documentCount: number;
    averageScore: number;
    relevanceQuality: string;
  };
}

/**
 * SearchResult interface (locally defined for type safety)
 */
interface SearchResult {
  document: any;
  score: number;
  content: string;
}

// Type guard removed - now using simplified fields directly

// Relevance quality thresholds - no magic numbers
const HIGH_RELEVANCE_THRESHOLD = 0.8;
const MODERATE_RELEVANCE_THRESHOLD = 0.6;

/**
 * Analysis Agent - Specialized for document analysis and insight generation
 *
 * This agent processes EnrichedSearchResult objects from the search agent to generate
 * comprehensive analysis including summaries, insights, themes, and recommendations.
 *
 * Responsibilities:
 * 1. Analyze EnrichedSearchResult objects from the search agent
 * 2. Extract key insights and patterns from enriched context and raw results
 * 3. Generate comprehensive analysis summaries using metadata-aware processing
 * 4. Provide structured analysis data with relevance scoring and recommendations
 *
 * Key Features:
 * - Type-safe handling of EnrichedSearchResult interface
 * - Fallback logic for state vs args searchResults
 * - Comprehensive error handling and validation
 * - Named threshold constants for consistent scoring
 * - Enhanced observability with detailed logging
 */
export class AnalysisAgentFactory {
  private langfuseClient: any;

  constructor(langfuseClient: any) {
    this.langfuseClient = langfuseClient;
  }

  /**
   * Create the analysis agent with specialized tools
   */
  createAgent(stateSchema: typeof SupervisorStateAnnotation) {
    const tools = this.createAnalysisTools();
    const agentLlm = ensureBindTools(new ChatOpenAI({ model: 'gpt-4o-mini', temperature: 0.1 }));

    return createReactAgent({
      llm: agentLlm,
      tools,
      name: 'analysis_expert',
      prompt: `You are a document analysis expert. Your sole purpose is to use the analyze_documents tool.

You have one tool at your disposal: 'analyze_documents'.

Your one and only task is to call the 'analyze_documents' tool. The tool will automatically read searchResults from the current state. Do not attempt to do anything else.`,
      stateSchema,
    });
  }

  /**
   * Create specialized tools for document analysis operations
   */
  private createAnalysisTools() {
    const analyzeDocumentsTool = tool(
      async (args: any, state: Partial<SupervisorState>) => {
        const langfuseClient = this.langfuseClient;
        let toolSpan: any = null;
        const logger = getLogger();

        // Get simplified search fields from state
        const searchEnrichedContext = state.searchEnrichedContext;
        const searchRawResults = state.searchRawResults;
        const searchMetadata = state.searchMetadata;

        // Add detailed debugging
        logger.debug('[analysis-expert] State debugging:', {
          hasSearchEnrichedContext: !!searchEnrichedContext,
          hasSearchRawResults: !!searchRawResults,
          hasSearchMetadata: !!searchMetadata,
          searchEnrichedContextType: typeof searchEnrichedContext,
          searchRawResultsType: typeof searchRawResults,
          searchMetadataType: typeof searchMetadata,
          searchRawResultsLength: Array.isArray(searchRawResults) ? searchRawResults.length : 0,
          fullState: Object.keys(state),
        });

        logger.debug('[analysis-expert] Args debugging:', {
          hasSearchResults: false, // No longer using args for search results
          fullArgs: Object.keys(args),
        });

        // Reconstruct EnrichedSearchResult from simplified fields
        let searchResults: EnrichedSearchResult | undefined;
        let dataSource = 'none';

        if (searchEnrichedContext && searchRawResults && searchMetadata) {
          searchResults = {
            enrichedContext: searchEnrichedContext,
            rawResults: searchRawResults,
            metadata: searchMetadata,
          };
          dataSource = 'simplified-state-fields';
          logger.info(
            '[analysis-expert] Successfully reconstructed EnrichedSearchResult from simplified fields:',
            {
              enrichedContextLength: searchEnrichedContext.length,
              rawResultsCount: searchRawResults.length,
              metadata: searchMetadata,
            }
          );
        }

        if (!searchResults) {
          logger.error(
            '[analysis-expert] No valid EnrichedSearchResult found - missing simplified search fields',
            JSON.stringify(
              {
                hasSearchEnrichedContext: !!searchEnrichedContext,
                hasSearchRawResults: !!searchRawResults,
                hasSearchMetadata: !!searchMetadata,
                searchEnrichedContextType: typeof searchEnrichedContext,
                searchRawResultsType: typeof searchRawResults,
                searchMetadataType: typeof searchMetadata,
                stateKeys: Object.keys(state),
                hasOrganizationId: !!state.organizationId,
                hasUserId: !!state.userId,
                hasMessages: !!state.messages,
                messagesLength: state.messages?.length || 0,
              },
              null,
              2
            )
          );

          // Log additional debugging information
          logger.error(
            {
              stateKeys: Object.keys(state),
              completedTools: state.completedTools,
              searchFieldsPresent: {
                searchEnrichedContext: !!searchEnrichedContext,
                searchRawResults: !!searchRawResults,
                searchMetadata: !!searchMetadata,
              },
            },
            '[analysis-expert] Additional state debugging'
          );

          return {
            success: false,
            error:
              'No valid EnrichedSearchResult found in state - expected structured object with enrichedContext, rawResults, and metadata',
            analysis: null,
            message: 'Document analysis failed - no valid search results available',
          };
        }

        // Destructure the validated EnrichedSearchResult
        // @ts-ignore
        const { enrichedContext, rawResults, metadata } = searchResults;

        if (langfuseClient && state.traceId && state.supervisorSpanId) {
          toolSpan = langfuseClient.span({
            name: 'analysis-agent-analyze_documents',
            traceId: state.traceId,
            parentObservationId: state.supervisorSpanId,
            input: {
              ...args,
              searchResultsSource: dataSource,
            },
            metadata: {
              searchResultsCount: rawResults.length,
              enrichedResultMetadata: metadata,
              searchResultsSource: dataSource,
              relevanceQuality: metadata.relevanceQuality,
            },
          });
        }

        try {
          logger.debug(
            `[analysis-expert] Starting analysis | searchResultsCount=${rawResults.length} | source=${dataSource}`
          );
          logger.debug(`[analysis-expert] Data received: rawResults `, {
            searchResultsCount: rawResults.length,
            rawResults: rawResults,
            metadata: metadata,
            source: dataSource,
          });

          // Calculate the relevance score from the metadata
          const relevanceScore = this.calculateRelevanceScore(metadata);
          const analysis = {
            searchResultCount: rawResults.length,
            analysisTimestamp: Date.now(),
            analysisType: args.analysisType || 'general',
            query: args.query || 'No specific query',
            summary: this.generateAnalysisSummary(rawResults),
            insights: this.generateInsights(rawResults),
            themes: this.extractThemes(rawResults),
            relevanceScore: relevanceScore,
            recommendations: this.generateRecommendations(rawResults),
            // Include metadata from the enriched search result
            enrichedMetadata: metadata,
          };

          logger.info(
            `[analysis-expert:analyze_documents] Analysis complete | averageScore/relevanceScore=${analysis.relevanceScore}`
          );
          logger.debug(
            `[analysis-expert:analyze_documents] Analysis complete | insights=${analysis.insights}`
          );

          const completedTools = (state.completedTools || []).concat('analyze_documents');

          const toolResult = {
            analysis, // This will update state.analysis
            completedTools,
            messages: [
              new AIMessage(
                `Analysis completed successfully. Generated ${analysis.insights.length} insights and identified ${analysis.themes.length} key themes.`
              ),
            ],
            metadata: {
              relevanceScore: analysis.relevanceScore,
            },
          };

          // For observability, log the detailed results
          if (toolSpan) {
            toolSpan.end({
              output: {
                success: true,
                message: `Analysis completed successfully. Generated ${analysis.insights.length} insights and identified ${analysis.themes.length} key themes.`,
                insightsCount: analysis.insights.length,
                themesCount: analysis.themes.length,
                relevanceScore: analysis.relevanceScore,
              },
            });
          }
          return toolResult;
        } catch (error) {
          if (toolSpan) {
            toolSpan.end({
              level: 'ERROR',
              statusMessage: error instanceof Error ? error.message : String(error),
            });
          }
          logger.error(error, '[AnalysisAgent] Document analysis failed');
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            analysis: null,
            message: 'Document analysis failed',
          };
        }
      },
      {
        name: 'analyze_documents',
        description: 'Analyze search results from state to generate comprehensive insights',
        schema: z
          .object({
            query: z.string().optional().nullable().describe('Original user query for context'),
            analysisType: z
              .string()
              .optional()
              .nullable()
              .describe('Type of analysis to perform (general, security, technical, etc.)'),
            // Removed searchResults from schema to enforce state usage
          })
          .strict(),
      }
    );

    return [analyzeDocumentsTool];
  }

  /**
   * Generate analysis summary using EnrichedSearchResult structure
   */
  private generateAnalysisSummary(rawResults: SearchResult[]): string {
    const logger = getLogger();
    try {
      if (!Array.isArray(rawResults)) {
        logger.warn('[analysis-expert:generateAnalysisSummary] rawResults is not an array', {
          rawResults,
        });
        return 'Error: Invalid search results format for analysis summary';
      }

      const resultCount = rawResults.length;
      let summary = `Analyzed ${resultCount} relevant passages through semantic search`;

      if (resultCount > 0) {
        const avgScore = rawResults.reduce((sum, r) => sum + (r.score || 0), 0) / resultCount;
        if (avgScore > HIGH_RELEVANCE_THRESHOLD) {
          summary += '. High relevance matches indicate strong alignment with the query';
        } else if (avgScore > MODERATE_RELEVANCE_THRESHOLD) {
          summary += '. Moderate relevance matches provide useful context';
        } else {
          summary += '. Lower relevance matches may provide background information';
        }
      } else {
        summary = 'No relevant search results were found to analyze.';
      }
      return summary;
    } catch (error) {
      logger.error(
        error,
        '[analysis-expert:generateAnalysisSummary] Error generating analysis summary'
      );
      return 'Error generating analysis summary';
    }
  }

  /**
   * Generate insights from search results using EnrichedSearchResult structure
   */
  private generateInsights(rawResults: SearchResult[]): string[] {
    const logger = getLogger();
    try {
      if (!Array.isArray(rawResults)) {
        logger.warn('[analysis-expert:generateInsights] rawResults is not an array', {
          rawResults,
        });
        return ['Error: Invalid search results format for insights generation'];
      }

      const insights: string[] = [];
      if (rawResults.length > 0) {
        insights.push(`Found ${rawResults.length} relevant passages`);
        const avgScore = rawResults.reduce((sum, r) => sum + (r.score || 0), 0) / rawResults.length;

        if (avgScore > HIGH_RELEVANCE_THRESHOLD) {
          insights.push('High confidence matches found - query well-covered by knowledge base');
        } else if (avgScore > MODERATE_RELEVANCE_THRESHOLD) {
          insights.push('Moderate confidence matches - partial coverage available');
        } else {
          insights.push('Lower confidence matches - limited coverage in knowledge base');
        }

        const uniqueSources = new Set(
          rawResults.map(r => r.document?.id || r.document?.source || 'unknown').filter(Boolean)
        );
        if (uniqueSources.size > 1) {
          insights.push(`Information sourced from ${uniqueSources.size} different documents`);
        }
      } else {
        insights.push('No relevant search results found');
      }
      return insights;
    } catch (error) {
      logger.error(error, '[analysis-expert:generateInsights] Error generating insights');
      return ['Error generating insights'];
    }
  }

  /**
   * Extract themes from search results using EnrichedSearchResult structure
   */
  private extractThemes(rawResults: SearchResult[]): string[] {
    const logger = getLogger();
    try {
      if (!Array.isArray(rawResults)) {
        logger.warn('[analysis-expert:extractThemes] rawResults is not an array', {
          rawResults,
        });
        return ['Error: Invalid search results format for theme extraction'];
      }

      const contentPieces: string[] = [];
      rawResults.forEach(result => {
        if (result.content) contentPieces.push(result.content);
      });
      return this.extractThemesFromContent(contentPieces, 5);
    } catch (error) {
      logger.error(error, '[analysis-expert:extractThemes] Error extracting themes');
      return ['Error extracting themes'];
    }
  }

  /**
   * Extract themes from content pieces
   */
  private extractThemesFromContent(content: string[], maxThemes: number): string[] {
    const keywords = new Map<string, number>();
    content.forEach(piece => {
      const words = piece
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 3);
      words.forEach(word => {
        keywords.set(word, (keywords.get(word) || 0) + 1);
      });
    });
    return Array.from(keywords.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxThemes)
      .map(([word]) => word);
  }

  /**
   * Calculate overall relevance score using metadata from EnrichedSearchResult
   */
  private calculateRelevanceScore(metadata: EnrichedSearchResult['metadata']): number {
    const logger = getLogger();
    try {
      // Use the pre-calculated average score from metadata
      const relevanceScore = metadata.averageScore;

      // Add assertion for data validation
      if (relevanceScore < 0 || relevanceScore > 1) {
        logger.warn(
          '[analysis-expert:calculateRelevanceScore] Invalid relevance score outside 0-1 range',
          {
            relevanceScore,
            metadata,
          }
        );
      }

      return Math.max(0, Math.min(1, relevanceScore)); // Clamp to valid range
    } catch (error) {
      logger.error(
        error,
        '[analysis-expert:calculateRelevanceScore] Error calculating relevance score'
      );
      return 0;
    }
  }

  /**
   * Generate recommendations based on analysis using EnrichedSearchResult structure
   */
  private generateRecommendations(rawResults: SearchResult[]): string[] {
    const logger = getLogger();
    try {
      if (!Array.isArray(rawResults)) {
        logger.warn('[analysis-expert:generateRecommendations] rawResults is not an array', {
          rawResults,
        });
        return ['Error: Invalid search results format for recommendations generation'];
      }

      const recommendations: string[] = [];
      if (rawResults.length === 0) {
        recommendations.push(
          'The search did not return any relevant results. The query may be too specific, or the knowledge base may lack coverage on this topic.'
        );
      } else {
        const avgScore = rawResults.reduce((sum, r) => sum + (r.score || 0), 0) / rawResults.length;
        if (avgScore < MODERATE_RELEVANCE_THRESHOLD / 2) {
          // Use threshold constant
          recommendations.push(
            'Low relevance scores suggest the query may not be well-covered by available documents. Consider broadening the search terms.'
          );
        }

        // Additional recommendations based on result count
        if (rawResults.length < 2) {
          recommendations.push(
            'Limited search results found. Consider using alternative keywords or expanding the search scope.'
          );
        }
      }
      return recommendations;
    } catch (error) {
      logger.error(
        error,
        '[analysis-expert:generateRecommendations] Error generating recommendations'
      );
      return ['Error generating recommendations'];
    }
  }

  /**
   * Generate detailed summary using EnrichedSearchResult structure
   */
  // @ts-ignore - Method currently unused but maintained for compatibility
  private async generateDetailedSummary(
    rawResults: SearchResult[],
    focusArea?: string,
    length: 'brief' | 'detailed' | 'comprehensive' = 'detailed'
  ): Promise<string> {
    const logger = getLogger();
    try {
      let summary = `Analysis Summary (${length}):\n\n`;
      summary += `Relevant Passages Found: ${rawResults.length}\n\n`;
      if (focusArea) {
        summary += `Focus Area: ${focusArea}\n\n`;
      }
      if (rawResults.length > 0) {
        summary += 'Key Findings:\n';
        rawResults
          .slice(0, length === 'brief' ? 2 : length === 'detailed' ? 3 : 5)
          .forEach((result, i) => {
            summary += `${i + 1}. ${result.content.substring(0, 200)}...\n`;
          });
      }
      return summary;
    } catch (error) {
      logger.error(
        error,
        '[analysis-expert:generateDetailedSummary] Error generating detailed summary'
      );
      return 'Error generating detailed summary';
    }
  }
}
