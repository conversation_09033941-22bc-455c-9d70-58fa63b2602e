import { ChatOpenAI } from '@langchain/openai';
import { ensureBindTools } from '../../../utils/llm-bindtools-polyfill';
import { tool } from '@langchain/core/tools';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';
import { SupervisorState } from '../types';
import { SupervisorStateAnnotation } from './idiomatic-supervisor';
import {
  DEFAULT_CHAT_PROMPTS,
  ChatPromptType,
  ChatPromptConfig,
} from '../../../types/prompts/chat-prompts';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { getChatPromptConfig } from '../../../integration/langfuse/prompt-manager';
import { getLogger } from '../../../observability/global-logger';
import { EnrichedSearchResult } from '../helpers/semantic-search.executor';
import { AnalysisResult } from './analysis-agent';

// Type guard for analysis results validation

function isAnalysisResult(data: any): data is AnalysisResult {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.summary === 'string' &&
    Array.isArray(data.insights) &&
    Array.isArray(data.themes) &&
    typeof data.relevanceScore === 'number' &&
    Array.isArray(data.recommendations) &&
    typeof data.enrichedMetadata === 'object' &&
    typeof data.enrichedMetadata.documentCount === 'number' &&
    typeof data.enrichedMetadata.averageScore === 'number' &&
    typeof data.enrichedMetadata.relevanceQuality === 'string'
  );
}

/**
 * ChatAgentFactory (response_expert)
 *
 * Builds the response generation expert that synthesizes the final user-facing answer
 * from validated EnrichedSearchResult and AnalysisResult data using an enhanced
 * context building system.
 *
 * Key Features:
 * - Strict type safety with EnrichedSearchResult and AnalysisResult validation
 * - State-based data access (uses state.searchResults and state.analysis)
 * - Enhanced context building utilizing all analysis components
 * - Comprehensive error handling and fallback mechanisms
 * - Rich observability with detailed logging and Langfuse integration
 *
 * Data Flow:
 * 1. Extracts and validates searchResults from state using type guards
 * 2. Extracts and validates analysis from state using type guards
 * 3. Builds enriched context using all available data components
 * 4. Generates natural language response with confidence assessment
 *
 * @since v2.0.0 - Refactored for EnrichedSearchResult compliance
 */
export class ChatAgentFactory {
  private langfuseClient: any;
  private promptConfig: ChatPromptConfig;

  constructor(langfuseClient: any, promptType: ChatPromptType = 'text') {
    this.langfuseClient = langfuseClient;
    this.promptConfig = DEFAULT_CHAT_PROMPTS[promptType];
  }

  /**
   * Set custom prompt configuration
   */
  setPromptConfig(config: Partial<ChatPromptConfig>) {
    this.promptConfig = { ...this.promptConfig, ...config };
  }

  /**
   * Load prompt from Langfuse (future integration)
   */
  // @ts-ignore
  async loadPromptFromLangfuse(promptName: string, version?: number): Promise<void> {
    try {
      const cfg = await getChatPromptConfig({ promptName });
      if (cfg) {
        this.setPromptConfig(cfg);
      }
    } catch (err) {
      console.warn(
        `[ChatAgentFactory] Failed to obtain prompt configuration via PromptManager: ${err instanceof Error ? err.message : String(err)}. Falling back to default.`
      );
    }
  }

  /**
   * Create the response_expert agent runnable
   */
  createAgent(stateSchema: typeof SupervisorStateAnnotation) {
    const agentLlm = ensureBindTools(
      new ChatOpenAI({
        model: 'gpt-4o-mini',
        temperature: this.promptConfig.temperature,
      })
    );

    const generateResponseTool = tool(
      async (args: any, state: Partial<SupervisorState>) => {
        const langfuseClient = this.langfuseClient;
        const logger = getLogger();
        let toolSpan: any = null;

        // Get search results and analysis from state using simplified fields
        const searchEnrichedContext = state.searchEnrichedContext;
        const searchRawResults = state.searchRawResults;
        const searchMetadata = state.searchMetadata;

        // Reconstruct EnrichedSearchResult from simplified fields
        const searchResults: EnrichedSearchResult | undefined =
          searchEnrichedContext && searchRawResults && searchMetadata
            ? {
                enrichedContext: searchEnrichedContext,
                rawResults: searchRawResults,
                metadata: searchMetadata,
              }
            : undefined;

        const analysis: AnalysisResult | undefined = isAnalysisResult(state.analysis)
          ? state.analysis
          : undefined;

        // Enhanced logging with data source tracking
        try {
          const hasSearchResults = searchResults ? 'present' : 'null';
          const hasAnalysis = analysis ? 'present' : 'null';
          const searchResultsSource = searchResults ? 'simplified-state-fields' : 'none';
          const analysisSource = analysis ? 'state.analysis' : 'none';

          logger.debug(
            `[ChatAgent] generate_response invoked | ` +
              `searchResults=${hasSearchResults} (from: ${searchResultsSource}) | ` +
              `analysis=${hasAnalysis} (from: ${analysisSource}) | ` +
              `query="${args.query}"`
          );

          if (searchResults) {
            logger.debug(
              `[ChatAgent] searchResults metadata: documentCount=${searchResults.metadata.documentCount}, ` +
                `averageScore=${searchResults.metadata.averageScore}, ` +
                `relevanceQuality=${searchResults.metadata.relevanceQuality}`
            );
          }

          if (analysis) {
            logger.debug(
              `[ChatAgent] analysis data: summary.length=${analysis.summary.length}, ` +
                `insights.count=${analysis.insights.length}, ` +
                `themes.count=${analysis.themes.length}, ` +
                `relevanceScore=${analysis.relevanceScore}, ` +
                `recommendations.count=${analysis.recommendations.length}`
            );
          }
        } catch (error) {
          logger.warn(
            `[ChatAgent] Logging failed: ${error instanceof Error ? error.message : String(error)}`
          );
        }

        if (langfuseClient && state.traceId && state.supervisorSpanId) {
          toolSpan = langfuseClient.span({
            name: 'chat-agent-generate_response',
            traceId: state.traceId,
            parentObservationId: state.supervisorSpanId,
            input: { query: args.query },
            metadata: {
              hasSearchResults: !!searchResults,
              hasAnalysis: !!analysis,
              searchResultsCount: searchResults?.rawResults?.length || 0,
              documentCount: searchResults?.metadata?.documentCount || 0,
              relevanceQuality: searchResults?.metadata?.relevanceQuality || 'unknown',
            },
          });
        }

        // NEW: Add retry loop for confidence prefix compliance (up to 2 retries)
        let responseContent = '';
        let retryCount = 0;
        const MAX_RETRIES = 2;
        let context = '';

        while (retryCount <= MAX_RETRIES) {
          try {
            // Build context from validated search results and analysis
            context = this.buildEnhancedContext(searchResults, analysis, state);

            // Create the prompt template with the unified system prompt
            const messages: Array<[string, string]> = [['system', this.promptConfig.systemPrompt]];

            // Add conversation history if enabled
            if (this.promptConfig.includeConversationHistory && state.messages) {
              // Extract conversation history from state
              const conversationHistory = this.extractConversationHistory(state.messages);
              messages.push(...conversationHistory);
            }

            // Add the current context and query
            messages.push(['human', `Context:\n${context}\n\nQuery: ${args.query}`]);

            // NEW: If retrying, reinforce the prompt with stricter instructions
            if (retryCount > 0) {
              messages.unshift([
                'system',
                'IMPORTANT: You MUST start your response with "[Confidence: X/10] Reason: brief explanation." Follow all instructions precisely.',
              ]);
            }

            const promptTemplate = ChatPromptTemplate.fromMessages(messages);
            const responseChain = promptTemplate.pipe(agentLlm);

            const response = await responseChain.invoke({
              context,
              query: args.query,
            });

            // Preserve content formatting by carefully extracting the response
            if (typeof response.content === 'string') {
              responseContent = response.content;
            } else if (response.content && typeof response.content === 'object') {
              // Handle LangChain response objects that might have nested content
              const content = response.content as any; // Type assertion for complex content
              if (content.text && typeof content.text === 'string') {
                responseContent = content.text;
              } else if (content.content && typeof content.content === 'string') {
                responseContent = content.content;
              } else if (content.message && typeof content.message === 'string') {
                responseContent = content.message;
              } else {
                // For other objects, try to preserve structure with proper formatting
                try {
                  responseContent = JSON.stringify(content, null, 2);
                } catch (error) {
                  logger.warn('Failed to stringify response content, using fallback:', {
                    error: error instanceof Error ? error.message : String(error),
                  });
                  responseContent = String(content);
                }
              }
            } else {
              // Fallback for other types
              responseContent = String(response.content || '');
            }

            // NEW: Parse for confidence score
            const confidenceMatch = responseContent.match(
              /^\s*\[Confidence: (\d+)\/10\] Reason: (.*?)(?:\n|$)/s
            );
            if (confidenceMatch) {
              const score = parseInt(confidenceMatch[1], 10);
              const reason = confidenceMatch[2].trim();

              // Validate confidence score is within expected range
              if (isNaN(score) || score < 1 || score > 10) {
                logger.warn(`Invalid confidence score: ${score} - treating as low confidence`);
                responseContent =
                  "I'm sorry, but I don't have sufficient confidence in the available information to provide an accurate response. Could you rephrase or provide more details?";
                break;
              }

              logger.info(`Confidence assessment: ${score}/10 - Reason: ${reason}`);

              if (score < 7) {
                // Override with fallback if confidence is low
                responseContent =
                  "I'm sorry, but I don't have sufficient confidence in the available information to provide an accurate response. Could you rephrase or provide more details?";
              } else {
                // Strip the confidence prefix from the final response
                const strippedResponse = responseContent
                  .replace(/^\s*\[Confidence: \d+\/10\] Reason: .*?(?:\n|$)/s, '')
                  .trim();

                // Ensure we have a non-empty response after stripping
                if (!strippedResponse) {
                  logger.warn(
                    'Response became empty after stripping confidence prefix - using fallback'
                  );
                  responseContent =
                    "I'm sorry, but I encountered an issue processing the response. Please try rephrasing your query.";
                } else {
                  responseContent = strippedResponse;
                }
              }

              // If we got a valid prefix, break the retry loop
              break;
            } else {
              // Missing prefix: log and retry
              logger.warn(
                `[chat-agent:generate_response] Response missing confidence prefix on attempt ${retryCount + 1} - retrying... Actual Response: ${JSON.stringify(responseContent)}`
              );
              retryCount++;
            }
          } catch (error) {
            retryCount++; // Increment retry count on error to prevent infinite loop
            if (retryCount > MAX_RETRIES) {
              if (toolSpan) {
                toolSpan.end({
                  level: 'ERROR',
                  statusMessage: error instanceof Error ? error.message : String(error),
                });
              }
              throw error;
            }
            // Log error but continue retrying
            logger.warn(
              `Error on attempt ${retryCount}, retrying: ${error instanceof Error ? error.message : String(error)}`
            );
          }
        }

        // NEW: If max retries exceeded without valid prefix, use fallback
        if (retryCount > MAX_RETRIES) {
          logger.error(
            `Failed to get compliant response after ${MAX_RETRIES} retries - using fallback`,
            JSON.stringify({
              retryCount,
              MAX_RETRIES,
              responseContent,
            })
          );
          responseContent =
            "I'm sorry, but I encountered an issue generating a response. Please try rephrasing your query.";
        }

        // Enhanced error handling for missing data
        if (!searchResults && !analysis) {
          logger.warn(
            '[ChatAgent] No search results or analysis available - providing limited response'
          );
          responseContent =
            "I don't have sufficient information to provide a comprehensive answer to your query. Please try rephrasing your question or check that the knowledge base contains relevant information.";
        }

        const toolResult = {
          messages: [['ai', responseContent]],
          completedTools: (state.completedTools || []).concat('generate_response'),
        };

        if (toolSpan) {
          toolSpan.end({
            output: {
              content: responseContent,
              hasSearchResults: !!searchResults,
              hasAnalysis: !!analysis,
              contextLength: context?.length || 0,
            },
          });
        }

        return toolResult;
      },
      {
        name: 'generate_response',
        description:
          'Generate a natural user-facing response based on search results and analysis from state',
        schema: z
          .object({
            query: z.string().describe('Original user query'),
          })
          .strict(),
        returnDirect: false,
      }
    );

    return createReactAgent({
      llm: agentLlm,
      tools: [generateResponseTool],
      name: 'response_expert',
      prompt: `You are a response generation expert. Your task is to use the generate_response tool to craft natural, user-facing answers.

When activated, you MUST call the 'generate_response' tool. The tool will automatically access the current workflow state which contains:
- searchResults: EnrichedSearchResult with enriched context and metadata from semantic search
- analysis: Comprehensive analysis including summary, insights, themes, and recommendations
- messages: The conversation history including the original user query

Extract the user's original query from the first human message in state.messages.

ALWAYS call 'generate_response' with only the query argument:
• query: The original user question (extracted from state.messages)

Example tool call (must be valid JSON):
\`\`\`json
{
  \"query\": \"Extracted user question from state.messages\"
}
\`\`\`

Never answer directly - always use the tool. The tool will handle all context building and response generation using validated state data.`,
      stateSchema,
    });
  }

  /**
   * Build enhanced context from validated EnrichedSearchResult and AnalysisResult
   */
  private buildEnhancedContext(
    searchResults: EnrichedSearchResult | undefined,
    analysis: AnalysisResult | undefined,
    // @ts-ignore
    state?: Partial<SupervisorState>
  ): string {
    const logger = getLogger();
    let context = '';

    // Primary: Use enriched context from search results
    if (searchResults?.enrichedContext) {
      context = searchResults.enrichedContext;

      // Add metadata context for transparency
      const { documentCount, averageScore, relevanceQuality } = searchResults.metadata;
      context += `\n\n[Search Context: Found ${documentCount} relevant documents with ${relevanceQuality.toLowerCase()} relevance (average score: ${averageScore.toFixed(2)})]`;

      logger.debug(
        `[ChatAgent] Using enriched context from search results (${searchResults.enrichedContext.length} chars)`
      );
    }
    // Fallback: Build context from raw search results
    else if (searchResults?.rawResults && searchResults.rawResults.length > 0) {
      context += 'Relevant Information:\n';
      searchResults.rawResults.forEach((result, index) => {
        const content = result.content || '';
        const score = result.score || 0;
        context += `${index + 1}. [Score: ${score.toFixed(2)}] ${content}\n`;
      });

      logger.debug(
        `[ChatAgent] Built context from ${searchResults.rawResults.length} raw search results`
      );
    }

    // Add comprehensive analysis information if available
    if (analysis) {
      context += '\n\n--- Analysis Summary ---\n';
      context += analysis.summary;

      if (analysis.insights.length > 0) {
        context += '\n\n--- Key Insights ---\n';
        analysis.insights.forEach((insight, index) => {
          context += `${index + 1}. ${insight}\n`;
        });
      }

      if (analysis.themes.length > 0) {
        context += '\n\n--- Main Themes ---\n';
        // @ts-ignore
        analysis.themes.forEach((theme, index) => {
          context += `• ${theme}\n`;
        });
      }

      if (analysis.recommendations.length > 0) {
        context += '\n\n--- Recommendations ---\n';
        analysis.recommendations.forEach((rec, index) => {
          context += `${index + 1}. ${rec}\n`;
        });
      }

      // Add relevance assessment
      context += `\n\n[Analysis Confidence: ${analysis.relevanceScore}/10]`;

      logger.debug(
        `[ChatAgent] Added comprehensive analysis to context (relevance: ${analysis.relevanceScore}/10)`
      );
    }

    // Final fallback
    if (!context.trim()) {
      context = 'No specific context available for this query.';
      logger.warn('[ChatAgent] No context available - using fallback message');
    }

    return context;
  }

  /**
   * Build context from search results and analysis (legacy method - kept for compatibility)
   * @deprecated Use buildEnhancedContext instead
   */
  // @ts-ignore
  private buildContext(searchResults: any[], analysis: any, state?: any): string {
    let context = '';

    // Check if we have enriched context from semantic search
    if (state?.enrichedContext) {
      // Use the enriched context that includes retrieval metadata
      context = state.enrichedContext;
    } else if (searchResults && searchResults.length > 0) {
      // Fallback to traditional context building
      context += 'Relevant Information:\n';
      searchResults.forEach((result, index) => {
        const content = result.content || result.pageContent || result.text || '';
        context += `${index + 1}. ${content}\n`;
      });
    }

    if (analysis && analysis.summary) {
      context += `\nAnalysis Summary: ${analysis.summary}\n`;
    }

    return context || 'No specific context available.';
  }

  /**
   * Extract conversation history from state messages
   */
  private extractConversationHistory(messages: any[]): Array<[string, string]> {
    const history: Array<[string, string]> = [];

    // Take the last 6 messages for context (adjustable)
    const recentMessages = messages.slice(-6);

    for (const msg of recentMessages) {
      if (msg instanceof HumanMessage) {
        const content =
          typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content as any);
        history.push(['human', content]);
      } else if (msg instanceof AIMessage) {
        const content =
          typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content as any);
        history.push(['ai', content]);
      }
    }

    return history;
  }
}
