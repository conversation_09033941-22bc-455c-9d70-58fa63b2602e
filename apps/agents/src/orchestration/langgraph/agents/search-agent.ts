import { tool } from '@langchain/core/tools';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { ChatOpenAI } from '@langchain/openai';
import { AIMessage } from '@langchain/core/messages';
import { ensureBindTools } from '../../../utils/llm-bindtools-polyfill';
import {
  EmbeddingService,
  SemanticSearchServiceEnhanced,
  DocumentAnalyzerService,
} from '@anter/mcp-tools';
import {
  executeSemanticSearchEnriched,
  EnrichedSearchResult,
} from '../helpers/semantic-search.executor';
import { z } from 'zod';
import { DocumentServiceV2 } from '../../../services/document.service.v2';
import { MCPConnectionManager } from '../../../integration/mcp-server/connection-manager';
import { SupervisorStateAnnotation } from './idiomatic-supervisor';
import { getLogger } from '../../../observability/global-logger';

/**
 * Search Agent - Specialized for document retrieval and semantic search
 *
 * Responsibilities:
 * 1. Retrieve documents from organization knowledge base
 * 2. Index documents for semantic search
 * 3. Perform semantic search with user queries
 * 4. Return relevant search results with scores
 */
export class SearchAgentFactory {
  private embeddingService: EmbeddingService;
  private searchService: SemanticSearchServiceEnhanced;
  private documentAnalyzer: DocumentAnalyzerService;
  private documentService: DocumentServiceV2;
  private connectionManager: MCPConnectionManager;
  private langfuseClient: any;

  constructor(connectionManager: MCPConnectionManager, langfuseClient: any) {
    this.connectionManager = connectionManager;
    this.langfuseClient = langfuseClient;

    this.embeddingService = new EmbeddingService();
    this.searchService = new SemanticSearchServiceEnhanced(this.embeddingService);
    this.documentAnalyzer = new DocumentAnalyzerService();
    this.documentService = new DocumentServiceV2(connectionManager, this.documentAnalyzer);
  }

  /**
   * Create the search agent with specialized tools
   */
  createAgent(stateSchema: typeof SupervisorStateAnnotation) {
    const tools = this.createSearchTools();
    const agentLlm = ensureBindTools(new ChatOpenAI({ model: 'gpt-4o-mini', temperature: 0.1 }));

    return createReactAgent({
      llm: agentLlm,
      tools,
      name: 'search_expert',
      // namespace removed to keep state keys unprefixed
      prompt: `You are a document search expert. Your sole purpose is to use the semantic_search tool to find information.

You have one tool at your disposal: 'semantic_search'.

When you receive a query, your only task is to call the 'semantic_search' tool with the user's query and the correct organizationId. Do not attempt to do anything else.
      `,
      stateSchema,
    });
  }

  /**
   * Create specialized tools for document search operations
   */
  private createSearchTools() {
    const semanticSearchTool = tool(
      async (args: any) => {
        const langfuseClient = this.langfuseClient;
        let toolSpan: any = null;
        const logger = getLogger();

        // For createReactAgent, we need to get the state from args or use a different approach
        // The organizationId should be passed as an argument to the tool
        logger.info('[search-agent:semantic_search] TOOL CALLED - Args debugging:', {
          argsReceived: args,
          hasQuery: !!args.query,
          hasOrganizationId: !!args.organizationId,
        });

        // Note: In createReactAgent, we don't have direct access to state for tracing
        // This will be handled at the agent level
        if (langfuseClient && args.traceId && args.supervisorSpanId) {
          toolSpan = langfuseClient.span({
            name: 'search-agent-semantic_search',
            traceId: args.traceId,
            parentObservationId: args.supervisorSpanId,
            input: args,
            metadata: {
              organizationId: args.organizationId,
              executor: 'langgraph-search-agent',
            },
          });
        }
        try {
          const topK = args.topK || 2; // Reduced from 3 to 2 to limit token usage

          // Use enriched semantic search for better confidence assessment
          const enrichedResult: EnrichedSearchResult = await executeSemanticSearchEnriched({
            query: args.query,
            organizationId: args.organizationId,
            topK,
            searchService: this.searchService,
            redisClient: this.connectionManager.getRedisClient(),
            logger,
            executor: 'langgraph-search-agent',
          });

          // Extract traditional format for compatibility
          const finalResults = enrichedResult.rawResults;
          const averageScore = enrichedResult.metadata.averageScore;
          const relevanceQuality = enrichedResult.metadata.relevanceQuality;

          const relevanceMessage = `${relevanceQuality} relevance matches found`;

          // In createReactAgent, we return the state update directly
          // The agent will handle merging with existing state
          const completedTools = ['semantic_search'];

          // Log the search results
          logger.info('[search-agent:semantic_search] Search completed successfully:', {
            resultsCount: finalResults.length,
            averageScore,
            relevanceQuality,
          });
          logger.debug(
            `[search-agent:semantic_search] 'enrichedResult' is assigned as 'searchResults' as the tool result:`,
            {
              enrichedContext: JSON.parse(
                JSON.stringify(enrichedResult.enrichedContext.substring(0, 200))
              ),
              rawResultsCount: enrichedResult.rawResults.length,
              metadata: enrichedResult.metadata,
            }
          );

          // For observability, log the detailed results
          if (toolSpan) {
            toolSpan.end({
              output: {
                success: true,
                count: finalResults.length,
                message: `Found ${finalResults.length} relevant results. ${relevanceMessage}`,
                averageScore,
                relevanceQuality,
                executor: 'langgraph-search-agent',
              },
            });
          }
          // Return the enriched result as simplified fields for state update
          const searchEnrichedContext = enrichedResult.enrichedContext;
          const searchRawResults = enrichedResult.rawResults;
          const searchMetadata = {
            documentCount: enrichedResult.rawResults.length,
            averageScore: enrichedResult.metadata.averageScore,
            relevanceQuality: enrichedResult.metadata.relevanceQuality,
          };

          logger.info(
            `[search-agent:semantic_search] RETURNING STATE UPDATE with simplified search fields:`,
            {
              hasEnrichedContext: !!searchEnrichedContext,
              enrichedContextLength: searchEnrichedContext?.length || 0,
              rawResultsCount: searchRawResults.length,
              metadata: searchMetadata,
              completedToolsCount: completedTools.length,
            }
          );

          // Return the state update object with simplified fields for better state management
          const stateUpdate = {
            searchEnrichedContext,
            searchRawResults,
            searchMetadata,
            completedTools,
            messages: [
              new AIMessage(
                `Search completed successfully. Found ${searchRawResults.length} relevant documents with ${searchMetadata.relevanceQuality.toLowerCase()} relevance.`
              ),
            ],
          };

          logger.info('[search-agent:semantic_search] Final state update object:', {
            updateKeys: Object.keys(stateUpdate),
            hasSearchEnrichedContext: !!stateUpdate.searchEnrichedContext,
            hasSearchRawResults: !!stateUpdate.searchRawResults,
            hasSearchMetadata: !!stateUpdate.searchMetadata,
            messagesCount: stateUpdate.messages.length,
          });

          // Return the state update object directly - the createSupervisor framework
          // should handle merging this into the state via the reducers
          return stateUpdate;
        } catch (error) {
          if (toolSpan) {
            toolSpan.end({
              level: 'ERROR',
              statusMessage: error instanceof Error ? error.message : String(error),
              output: { executor: 'langgraph-search-agent' },
            });
          }
          logger.error(
            `[SearchAgent] Semantic search failed: ${error instanceof Error ? error.message : String(error)}`,
            JSON.stringify(
              {
                executor: 'langgraph-search-agent',
              },
              null,
              2
            )
          );
          const completedTools = ['semantic_search'];

          // Return state update with empty search results for error case
          return {
            searchResults: {
              enrichedContext: '',
              rawResults: [],
              metadata: { documentCount: 0, averageScore: 0, relevanceQuality: 'none' },
            },
            completedTools,
          };
        }
      },
      {
        name: 'semantic_search',
        description: 'Perform semantic search on indexed documents to find relevant information',
        schema: z
          .object({
            query: z.string().describe('Search query to find relevant information'),
            organizationId: z.string().describe('Organization ID for document scope'),
            documents: z
              .array(z.record(z.string(), z.any()))
              .optional()
              .nullable()
              .describe(
                'Optional pre-fetched documents to search through. If omitted, the tool will load from the knowledge base automatically.'
              ),
            userId: z
              .string()
              .nullable()
              .optional()
              .describe('Optional user ID for access control'),
            topK: z
              .number()
              .nullable()
              .optional()
              .describe('Maximum number of results to return (default: 2)'),
          })
          .strict(),
      }
    );

    return [semanticSearchTool];
  }

  getSearchService(): SemanticSearchServiceEnhanced {
    return this.searchService;
  }

  getDocumentService(): DocumentServiceV2 {
    return this.documentService;
  }
}
