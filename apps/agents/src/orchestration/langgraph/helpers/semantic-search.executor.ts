import { EmbeddingIndexFactory, SemanticSearchServiceEnhanced } from '@anter/mcp-tools';
import { DocumentServiceV2 } from '../../../services/document.service.v2';
import { MCPConnectionManager } from '../../../integration/mcp-server/connection-manager';
import { SupervisorState } from '../types';
import { getLogger } from '../../../observability/global-logger';
import { QueryExpansionService } from './query-expansion.service';

/**
 * Options for the executeSemanticSearch helper.
 */
export interface ExecuteSemanticSearchOptions {
  query: string;
  organizationId: string;
  userId?: string | null;
  topK?: number;
  searchService: SemanticSearchServiceEnhanced;
  documentService: DocumentServiceV2;
  connectionManager: MCPConnectionManager;
  langfuseClient?: any;
  state: Partial<SupervisorState>;
  toolSpan?: any;
  executor?: string;
  useQueryExpansion?: boolean;
}

/**
 * Result returned by executeSemanticSearch.
 */
export interface ExecuteSemanticSearchResult {
  finalResults: any[];
  syncPerformed: boolean;
  averageScore: number;
  detailedScores: Array<{
    id: string;
    rawSimilarity: number;
    rerankScore?: number;
    finalScore: number;
  }>;
}

/**
 * Options for the sync logic helper.
 */
export interface SyncIndexOptions {
  organizationId: string;
  userId?: string | null;
  topK: number;
  initialResults: any[];
  searchService: SemanticSearchServiceEnhanced;
  documentService: DocumentServiceV2;
  connectionManager: MCPConnectionManager;
  langfuseClient?: any;
  state: Partial<SupervisorState>;
  toolSpan?: any;
  executor?: string;
}

/**
 * Result returned by syncIndexIfNeeded.
 */
export interface SyncIndexResult {
  syncPerformed: boolean;
  finalResults: any[];
}

interface SearchResult {
  document: any;
  score: number;
  content: string;
}

export interface EnrichedSearchResult {
  enrichedContext: string;
  rawResults: SearchResult[];
  metadata: {
    documentCount: number;
    averageScore: number;
    relevanceQuality: string;
  };
}

/**
 * Detect document type to adjust scoring thresholds appropriately
 */
function detectDocumentType(content: string): 'narrative' | 'structured' | 'mixed' {
  const structuredIndicators = [
    /\$\d+/g, // Dollar amounts
    /\d+%/g, // Percentages
    /[A-Z]{3,}/g, // Acronyms (3+ caps)
    /\b\d+\b/g, // Numbers
    /:\s*\d/g, // Colon followed by number
  ];

  const totalWords = content.split(/\s+/).length;
  const structuredMatches = structuredIndicators.reduce((count, pattern) => {
    return count + (content.match(pattern) || []).length;
  }, 0);

  const structuredRatio = structuredMatches / totalWords;

  if (structuredRatio > 0.15) return 'structured';
  if (structuredRatio > 0.08) return 'mixed';
  return 'narrative';
}

/**
 * Calculate lexical similarity boost for exact term matches
 */
function calculateLexicalBoost(query: string, content: string): number {
  const queryTerms = query
    .toLowerCase()
    .split(/\s+/)
    .filter(term => term.length > 2);
  const contentLower = content.toLowerCase();

  let matches = 0;
  let partialMatches = 0;

  queryTerms.forEach(term => {
    const exactMatches = (contentLower.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
    const partialMatchesFound =
      (contentLower.match(new RegExp(term, 'g')) || []).length - exactMatches;

    matches += exactMatches;
    partialMatches += partialMatchesFound;
  });

  // Boost calculation: exact matches count more than partial
  const boost = matches * 0.1 + partialMatches * 0.05;
  return Math.min(boost, 0.3); // Cap boost at 0.3
}

/**
 * Get relevance quality based on document type and score
 */
function getRelevanceQuality(averageScore: number, documentType: string): string {
  // Lower thresholds for structured documents
  const thresholds =
    documentType === 'structured'
      ? { excellent: 0.75, good: 0.55, fair: 0.35, poor: 0.2 }
      : documentType === 'mixed'
        ? { excellent: 0.8, good: 0.6, fair: 0.4, poor: 0.25 }
        : { excellent: 0.85, good: 0.7, fair: 0.5, poor: 0.3 };

  if (averageScore >= thresholds.excellent) return 'Excellent';
  if (averageScore >= thresholds.good) return 'Good';
  if (averageScore >= thresholds.fair) return 'Fair';
  if (averageScore >= thresholds.poor) return 'Poor';
  return 'Very Poor';
}

function buildEnrichedContext(
  results: SearchResult[],
  averageScore: number,
  query?: string
): EnrichedSearchResult {
  if (!results || results.length === 0) {
    return {
      enrichedContext:
        'RETRIEVAL CONTEXT:\n- No relevant documents found from semantic search\n- Confidence should be low for this query\n\nCONTEXT: No relevant information available.',
      rawResults: [],
      metadata: {
        documentCount: 0,
        averageScore: 0,
        relevanceQuality: 'No Match',
      },
    };
  }

  // Analyze document types and calculate boosts
  const allContent = results.map(r => r.content).join('\n');
  const documentType = detectDocumentType(allContent);

  // Calculate lexical boost if query provided
  let lexicalBoost = 0;
  if (query) {
    lexicalBoost = calculateLexicalBoost(query, allContent);
  }

  // Adjust average score with lexical boost
  const boostedScore = Math.min(averageScore + lexicalBoost, 1.0);

  // Determine relevance quality using document-type-aware thresholds
  const relevanceQuality = getRelevanceQuality(boostedScore, documentType);

  // Build enriched context with enhanced metadata
  let enrichedContext = `RETRIEVAL CONTEXT:\n`;
  enrichedContext += `- Retrieved ${results.length} documents from semantic search\n`;
  enrichedContext += `- Document type: ${documentType} (affects scoring thresholds)\n`;
  enrichedContext += `- Average semantic similarity: ${averageScore.toFixed(3)} (0=no match, 1=perfect match)\n`;

  if (lexicalBoost > 0) {
    enrichedContext += `- Lexical boost applied: +${lexicalBoost.toFixed(3)} for term matches\n`;
    enrichedContext += `- Boosted score: ${boostedScore.toFixed(3)}\n`;
  }

  enrichedContext += `- Relevance quality: ${relevanceQuality}\n`;
  enrichedContext += `- Individual scores: ${results.map(r => r.score.toFixed(3)).join(', ')}\n`;
  enrichedContext += `\nBased on ${documentType} document analysis and retrieval quality, confidence should be ${relevanceQuality.toLowerCase()}.\n\n`;
  enrichedContext += `CONTEXT:\n${results.map(r => r.content).join('\n\n')}`;

  return {
    enrichedContext,
    rawResults: results,
    metadata: {
      documentCount: results.length,
      averageScore: boostedScore, // Use boosted score in metadata
      relevanceQuality,
    },
  };
}

/**
 * Reusable function to sync embedding index if search results are insufficient.
 * This handles the logic of checking for missing documents between DB and Redis index,
 * and re-indexing them if needed.
 */
export async function syncIndexIfNeeded(
  opts: SyncIndexOptions,
  finalQuery: string
): Promise<SyncIndexResult> {
  const {
    organizationId,
    userId,
    topK,
    initialResults,
    searchService,
    documentService,
    connectionManager,
    langfuseClient,
    state,
    toolSpan,
    executor = 'unknown',
  } = opts;

  const logger = getLogger();
  let finalResults = initialResults;
  let syncPerformed = false;

  const avgScoreInitial =
    initialResults.length > 0
      ? initialResults.reduce((sum, r) => sum + (r.score || 0), 0) / initialResults.length
      : 0;

  // Log sync condition evaluation
  logger.info(`[SemanticSearch] Sync condition evaluation`, {
    executor,
    organizationId,
    initialResultsLength: initialResults.length,
    topK,
    avgScoreInitial,
    shouldSync: initialResults.length < topK || avgScoreInitial < 0.6,
    tags: ['semantic-search', 'redis-sync', 'condition-evaluation'],
  });

  // Decide whether we need to sync the index (few results or low average score)
  if (initialResults.length < topK || avgScoreInitial < 0.6) {
    logger.info(`[SemanticSearch] Sync condition met - starting sync process`, {
      executor,
      organizationId,
      initialResultsLength: initialResults.length,
      topK,
      avgScoreInitial,
      hasConnectionManager: !!connectionManager,
      tags: ['semantic-search', 'redis-sync', 'sync-condition-met'],
    });

    // Obtain Redis connection (may be undefined if Redis disabled)
    const redisClient = connectionManager.getRedisClient();

    // Ensure we have an embedding index (prefers Redis when available)
    const embeddingIndex = await EmbeddingIndexFactory.createIndex({
      organizationId,
      redisClient,
    });

    // --- index sync phase ----------------------------------------------------
    let syncSpan: any = null;
    if (langfuseClient && state.traceId && state.supervisorSpanId) {
      syncSpan = langfuseClient.span({
        name: 'search.redis_sync_missing',
        traceId: state.traceId,
        parentObservationId: toolSpan?.id || state.supervisorSpanId,
        metadata: { organizationId },
        tags: ['semantic-search', 'redis-sync'],
      });
    }

    // Collect IDs from DB and from the current Redis index
    const allIds = await documentService.listDocumentIds(organizationId, userId || undefined);
    logger.info(`[SemanticSearch] DB document IDs fetched`, {
      executor,
      count: allIds.length,
      ids: JSON.stringify(allIds),
      tags: ['semantic-search', 'redis-sync', 'db-document-ids-fetched'],
    });

    const indexedIds = await embeddingIndex.getIndexedDocumentIds();
    logger.info(`[SemanticSearch] IDs already indexed in Redis`, {
      organizationId,
      executor,
      count: indexedIds.length,
      ids: JSON.stringify(indexedIds),
      tags: ['semantic-search', 'redis-sync', 'redis-document-ids-indexed'],
    });

    // Determine which IDs are missing
    const indexedBaseIds = new Set(
      indexedIds.map(id => {
        // Handle different ID formats:
        // 1. If ID contains ::, split and take first part (chunk format from document processor)
        // 2. If ID contains :, split and take last part (new format with org:doc)
        // 3. Otherwise, use the ID as-is
        if (id.includes('::')) {
          return id.split('::')[0]; // Remove chunk suffix (e.g., ::1)
        } else if (id.includes(':')) {
          const parts = id.split(':');
          return parts[parts.length - 1]; // Take the last part as document ID
        } else {
          return id;
        }
      })
    );

    // For each database document ID, check if any chunk exists in Redis
    const missingIds = allIds.filter(dbId => {
      // Check if any chunk of this document exists in Redis
      const hasAnyChunk = Array.from(indexedBaseIds).some(indexedId => indexedId === dbId);
      return !hasAnyChunk;
    });

    // Debug logging to help identify issues
    logger.info(`[SemanticSearch] Sync analysis`, {
      executor,
      organizationId,
      dbDocumentIds: allIds,
      redisChunkIds: indexedIds,
      normalizedRedisIds: Array.from(indexedBaseIds),
      missingDocumentIds: missingIds,
      tags: ['semantic-search', 'redis-sync', 'debug-analysis'],
    });

    // If missing, fetch and (re)index the documents
    if (missingIds.length > 0) {
      logger.info(`[SemanticSearch] Syncing missing documents to Redis`, {
        executor,
        count: missingIds.length,
        tags: ['semantic-search', 'redis-sync', 'missing-document-ids-found'],
      });

      const missingDocs = await documentService.fetchDocuments(
        organizationId,
        userId || undefined,
        missingIds
      );

      logger.info(`[SemanticSearch] Downloaded missing documents`, {
        executor,
        count: missingDocs.length,
        tags: ['semantic-search', 'redis-sync', 'missing-document-ids-found'],
      });

      // Debug: Check document content before indexing
      if (missingDocs.length > 0) {
        const docContentSummary = missingDocs.map(doc => ({
          id: doc.id,
          name: doc.name,
          contentLength: doc.content?.length || 0,
          hasContent: !!doc.content?.trim(),
          contentPreview: doc.content?.substring(0, 100) || 'NO_CONTENT',
        }));

        logger.info(`[SemanticSearch] Document content analysis before indexing`, {
          executor,
          organizationId,
          documents: docContentSummary,
          tags: ['semantic-search', 'redis-sync', 'content-analysis'],
        });
      }

      if (missingDocs.length > 0) {
        // Use indexDocuments instead of indexDocumentsIncremental for missing documents
        // because the content hash service doesn't know about documents already in Redis
        // and might incorrectly skip indexing new documents
        await searchService.indexDocuments(missingDocs, organizationId, {
          useRedis: true,
          redisClient,
          logger, // Pass the logger to enable debug logging
        });
        syncPerformed = true;
        logger.info(`[SemanticSearch] Document sync to Redis complete`, {
          executor,
          count: missingDocs.length,
          tags: ['semantic-search', 'redis-sync', 'missing-document-ids-found'],
        });

        // Verify that documents were actually synced to Redis
        try {
          const expectedChunkIds = missingDocs.map(doc => doc.id);

          // Get the embedding index to check what's actually in Redis
          const embeddingIndex = await searchService['getEmbeddingIndex'](organizationId, {
            useRedis: true,
            redisClient,
          });

          // Get all indexed document IDs from Redis
          const verificationResult = await embeddingIndex.getIndexedDocumentIds();

          // Debug: Show the actual Redis keys being checked
          const keyPrefix = `embeddings:${organizationId}`;
          const expectedKeys = expectedChunkIds.map(id => `${keyPrefix}:${id}`);

          logger.info(`[SemanticSearch] Redis verification details`, {
            executor,
            organizationId,
            keyPrefix,
            expectedKeys,
            expectedChunkIds,
            foundInRedis: verificationResult,
            tags: ['semantic-search', 'redis-sync', 'verification-details'],
          });

          // Check if all expected chunk IDs are now in Redis
          const missingInRedis = expectedChunkIds.filter(
            expectedId => !verificationResult.includes(expectedId)
          );

          if (missingInRedis.length > 0) {
            logger.error(
              new Error(`Redis sync verification failed - documents not found in Redis`),
              `[SemanticSearch] Redis sync verification failed - documents not found in Redis`,
              {
                executor,
                organizationId,
                expectedChunkIds,
                foundInRedis: verificationResult,
                missingInRedis,
                tags: ['semantic-search', 'redis-sync', 'verification-failed'],
              }
            );
          } else {
            logger.info(
              `[SemanticSearch] Redis sync verification successful - all documents found in Redis`,
              {
                executor,
                organizationId,
                expectedChunkIds,
                foundInRedis: verificationResult,
                tags: ['semantic-search', 'redis-sync', 'verification-success'],
              }
            );
          }
        } catch (verificationError) {
          logger.error(
            verificationError,
            `[SemanticSearch] Redis sync verification failed - could not check Redis`,
            {
              executor,
              organizationId,
              tags: ['semantic-search', 'redis-sync', 'verification-error'],
            }
          );
        }
      }
    } else {
      logger.info(`[SemanticSearch] No missing IDs found, no sync needed.`, {
        organizationId,
        executor,
        tags: ['semantic-search', 'redis-sync', 'missing-document-ids-not-found'],
      });
    }

    if (syncSpan) {
      syncSpan.end({
        output: { missingIdsCount: missingIds.length, synced: syncPerformed, executor },
      });
    }

    // Re-run search after potential sync
    if (syncPerformed) {
      finalResults = await searchService.search(finalQuery, {
        organizationId,
        topK,
        useRedis: true,
        redisClient,
      });
    }
  }

  return { syncPerformed, finalResults };
}

/**
 * Enhanced semantic search with enriched context containing retrieval metadata
 */
export async function executeSemanticSearchEnriched(opts: {
  query: string;
  organizationId: string;
  userId?: string | null;
  topK?: number;
  searchService: SemanticSearchServiceEnhanced;
  documentService?: DocumentServiceV2;
  connectionManager?: MCPConnectionManager;
  redisClient?: any;
  logger?: any;
  useExpansion?: boolean;
  langfuseClient?: any;
  state?: Partial<SupervisorState>;
  toolSpan?: any;
  executor?: string;
}): Promise<EnrichedSearchResult> {
  const {
    query,
    organizationId,
    userId,
    topK = 2,
    searchService,
    documentService,
    connectionManager,
    redisClient,
    logger,
    useExpansion = false,
    langfuseClient,
    state,
    toolSpan,
    executor = 'unknown',
  } = opts;

  let queries = [query];
  if (useExpansion) {
    const expander = new QueryExpansionService();
    const expanded = await expander.expandQuery(query);
    queries = [query, ...expanded];
    logger?.info('Query expanded', { original: query, expanded, executor });
  }

  let finalQuery = queries[0]; // Use the first (original or expanded) query for sync logic

  // Perform initial search for the primary query
  const initialResults = await searchService.search(finalQuery, {
    organizationId,
    topK,
    useRedis: !!redisClient,
    redisClient,
    executor,
    logger,
  });

  let allResults = [...initialResults];

  // Perform sync logic if we have the required dependencies
  if (documentService && connectionManager) {
    const { syncPerformed, finalResults } = await syncIndexIfNeeded(
      {
        organizationId,
        userId,
        topK,
        initialResults,
        searchService,
        documentService,
        connectionManager,
        langfuseClient,
        state: state || {},
        toolSpan,
        executor,
      },
      finalQuery
    );

    // Update allResults with synced results
    allResults = [...finalResults];

    // If sync was performed and we have additional expanded queries, search with them too
    if (syncPerformed && queries.length > 1) {
      for (const q of queries.slice(1)) {
        const expandedResults = await searchService.search(q, {
          organizationId,
          topK,
          useRedis: !!redisClient,
          redisClient,
          executor,
          logger,
        });
        allResults = [...allResults, ...expandedResults];
      }
    } else if (!syncPerformed && queries.length > 1) {
      // If no sync was needed, search with remaining expanded queries
      for (const q of queries.slice(1)) {
        const expandedResults = await searchService.search(q, {
          organizationId,
          topK,
          useRedis: !!redisClient,
          redisClient,
          executor,
          logger,
        });
        allResults = [...allResults, ...expandedResults];
      }
    }
  } else {
    // If no sync dependencies available, just perform expanded searches without sync
    if (queries.length > 1) {
      for (const q of queries.slice(1)) {
        const expandedResults = await searchService.search(q, {
          organizationId,
          topK,
          useRedis: !!redisClient,
          redisClient,
          executor,
          logger,
        });
        allResults = [...allResults, ...expandedResults];
      }
    }
  }

  // Deduplicate and sort by score (simple implementation)
  const uniqueResults = Array.from(new Map(allResults.map(r => [r.documentId, r])).values());
  uniqueResults.sort((a, b) => (b.score || 0) - (a.score || 0));
  const finalResults = uniqueResults.slice(0, topK);

  // Proceed with averageScore calculation and enriched context as before
  const averageScore =
    finalResults.length > 0
      ? finalResults.reduce((sum: number, result: any) => sum + (result.score || 0), 0) /
        finalResults.length
      : 0;

  const searchResults: SearchResult[] = finalResults.map((result: any) => ({
    document: { id: result.documentId, content: result.content },
    score: result.score || 0,
    content: result.content || 'No content available',
  }));

  const enrichedResult = buildEnrichedContext(searchResults, averageScore, query);

  logger &&
    logger.info('[semantic-search.executor] Enhanced semantic search completed', {
      organizationId,
      documentCount: enrichedResult.metadata.documentCount,
      averageScore: enrichedResult.metadata.averageScore,
      relevanceQuality: enrichedResult.metadata.relevanceQuality,
      executor,
    });

  return enrichedResult;
}
