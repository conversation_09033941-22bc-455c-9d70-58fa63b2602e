import { ChatOpenAI } from '@langchain/openai';
import { ChatPromptTemplate } from '@langchain/core/prompts';

export class QueryExpansionService {
  private llm: ChatOpenAI;

  constructor() {
    this.llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0.0, // Low temperature for consistent expansions
    });
  }

  async expandQuery(originalQuery: string, numExpansions: number = 3): Promise<string[]> {
    try {
      const prompt = ChatPromptTemplate.fromTemplate(`
        Given the original query: "{query}"
        Generate {num} expanded versions that capture synonyms, related terms, and rephrasings to improve search relevance.
        Focus on healthcare and benefits context if applicable.
        Return ONLY a valid JSON array of strings, without any markdown formatting or explanation.
        
        Example output format:
        ["expanded query 1", "expanded query 2", "expanded query 3"]
      `);

      const chain = prompt.pipe(this.llm);
      const response = await chain.invoke({ query: originalQuery, num: numExpansions });

      let content = response.content as string;

      // Clean up markdown code blocks if present
      content = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim();

      // Validate that we have a JSON array
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed)) {
        const validQueries = parsed.filter(item => typeof item === 'string' && item.length > 0);
        return validQueries.length > 0 ? validQueries : [originalQuery];
      } else {
        console.warn('Query expansion response is not an array, using rule-based fallback');
        return this.generateRuleBasedExpansions(originalQuery, numExpansions);
      }
    } catch (error) {
      console.error('Query expansion failed:', error);
      console.error('Using rule-based fallback expansion');
      return this.generateRuleBasedExpansions(originalQuery, numExpansions);
    }
  }

  private generateRuleBasedExpansions(originalQuery: string, numExpansions: number): string[] {
    const expansions = [originalQuery];
    const lowerQuery = originalQuery.toLowerCase();

    // Insurance/benefits specific expansions (enhanced)
    if (
      lowerQuery.includes('benefit') ||
      lowerQuery.includes('coverage') ||
      lowerQuery.includes('plan')
    ) {
      expansions.push(
        originalQuery.replace(/benefit/gi, 'coverage'),
        originalQuery.replace(/coverage/gi, 'benefit'),
        originalQuery.replace(/our/gi, 'the'), // "our summary" -> "the summary"
        originalQuery + ' plan details',
        originalQuery + ' information',
        originalQuery + ' document',
        'plan benefits and coverage details',
        'health plan summary document'
      );

      // Specific insurance terminology
      if (lowerQuery.includes('summary of benefit')) {
        expansions.push(
          'Summary of Benefits and Coverage',
          'SBC document',
          'plan summary information',
          'benefits overview'
        );
      }
    }

    // Medical/healthcare terms expansion (enhanced)
    if (lowerQuery.includes('medical') || lowerQuery.includes('health')) {
      expansions.push(
        originalQuery.replace(/medical/gi, 'healthcare'),
        originalQuery.replace(/health/gi, 'medical'),
        originalQuery + ' services',
        originalQuery + ' plan',
        'healthcare benefits',
        'medical coverage'
      );
    }

    // Summary/document expansions (enhanced)
    if (lowerQuery.includes('summary') || lowerQuery.includes('document')) {
      expansions.push(
        originalQuery.replace(/summary/gi, 'overview'),
        originalQuery.replace(/document/gi, 'file'),
        originalQuery.replace(/summary/gi, 'details'),
        originalQuery + ' details',
        originalQuery + ' specifications'
      );
    }

    // Deductible and plan-specific expansions
    if (
      lowerQuery.includes('deductible') ||
      lowerQuery.includes('apollo') ||
      lowerQuery.includes('ppo')
    ) {
      expansions.push(
        originalQuery + ' plan information',
        originalQuery + ' coverage details',
        'plan specifications and benefits'
      );
    }

    // "What is" question pattern optimization
    if (lowerQuery.startsWith('what is')) {
      const topic = originalQuery.substring(7).trim(); // Remove "what is"
      expansions.push(
        topic,
        topic + ' information',
        topic + ' details',
        'information about ' + topic,
        'details about ' + topic
      );
    }

    // Remove duplicates and limit to requested number
    const uniqueExpansions = [...new Set(expansions)];
    return uniqueExpansions.slice(0, Math.max(1, numExpansions + 2)); // Allow more expansions
  }
}
