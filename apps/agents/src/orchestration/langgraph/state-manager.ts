/**
 * State manager for LangGraph workflows
 * Handles state persistence, transitions, and conflict resolution
 */

import { WorkflowState, WorkflowMetadata, NodeUpdate } from './types';
import { BaseMessage } from '@langchain/core/messages';
import { getLogger } from '../../observability/global-logger';

/**
 * Interface for LangGraph supervisor state that includes custom fields
 */
export interface SupervisorStateUpdate {
  messages?: BaseMessage[];
  completedTools?: string[];
  searchEnrichedContext?: string;
  searchRawResults?: any[];
  searchMetadata?: { documentCount: number; averageScore: number; relevanceQuality: string };
  analysis?: any;
  [key: string]: any;
}

/**
 * `StateManager` persists `WorkflowState` instances in-memory and keeps a
 * versioned history for each workflow. It also offers convenience helpers for
 * lifecycle operations such as cleanup and activity checks.
 */
export class StateManager {
  private states: Map<string, WorkflowState> = new Map();
  private stateHistory: Map<string, WorkflowState[]> = new Map();

  /**
   * Creates and stores a brand-new `WorkflowState` using the provided details.
   *
   * @param sessionId - Conversation/session identifier shared across workflows.
   * @param userInput - Original user query.
   * @param organizationId - Tenant/organization the request belongs to.
   * @param userId - _(optional)_ Authenticated user identifier.
   * @param metadata - _(optional)_ Additional metadata merged into the default block.
   * @returns The newly created state object.
   */
  createState(
    sessionId: string,
    userInput: string,
    organizationId: string,
    userId?: string,
    metadata?: Partial<WorkflowMetadata>
  ): WorkflowState {
    const workflowId = this.generateWorkflowId();

    const state: WorkflowState = {
      sessionId,
      userInput,
      organizationId,
      userId,
      metadata: {
        startTime: Date.now(),
        workflowId,
        version: '0.0.1',
        ...metadata,
      },
    };

    this.states.set(workflowId, state);
    this.stateHistory.set(workflowId, [state]);

    return state;
  }

  /**
   * Retrieves the latest state snapshot for the given workflow.
   *
   * @param workflowId - Unique workflow identifier returned from `createState`.
   * @returns Current state or `undefined` if the workflow is unknown.
   */
  getState(workflowId: string): WorkflowState | undefined {
    return this.states.get(workflowId);
  }

  /**
   * Applies a partial update to the current workflow state and stores a history
   * entry.
   *
   * @param workflowId - Target workflow.
   * @param update - Partial state update originating from a workflow node.
   * @returns The merged state.
   * @throws `Error` if the workflow does not exist.
   */
  updateState(workflowId: string, update: NodeUpdate): WorkflowState {
    const currentState = this.states.get(workflowId);
    if (!currentState) {
      throw new Error(`Workflow state not found: ${workflowId}`);
    }

    // Deep merge the update into current state
    const newState: WorkflowState = this.mergeState(currentState, update);

    // Store the new state
    this.states.set(workflowId, newState);

    // Add to history
    const history = this.stateHistory.get(workflowId) || [];
    history.push(newState);
    this.stateHistory.set(workflowId, history);

    return newState;
  }

  /**
   * Returns the chronological history of states for a workflow.
   *
   * @param workflowId - Workflow identifier.
   */
  getStateHistory(workflowId: string): WorkflowState[] {
    return this.stateHistory.get(workflowId) || [];
  }

  /**
   * Removes states older than `maxAgeMs` to free memory.
   *
   * @param maxAgeMs - Age threshold in milliseconds. Defaults to **1 hour**.
   */
  cleanupOldStates(maxAgeMs: number = 3600000): void {
    const now = Date.now();
    const workflowIdsToDelete: string[] = [];

    this.states.forEach((state, workflowId) => {
      if (now - state.metadata.startTime > maxAgeMs) {
        workflowIdsToDelete.push(workflowId);
      }
    });

    workflowIdsToDelete.forEach(workflowId => {
      this.states.delete(workflowId);
      this.stateHistory.delete(workflowId);
    });
  }

  /**
   * Determines whether a workflow is considered *active*.
   *
   * A workflow is active if it has no response yet **or** was started within the
   * last five minutes.
   *
   * @param workflowId - Workflow identifier.
   */
  isWorkflowActive(workflowId: string): boolean {
    const state = this.states.get(workflowId);
    if (!state) return false;

    // Workflow is considered active if it doesn't have a response yet
    // or if it was started less than 5 minutes ago
    const fiveMinutesAgo = Date.now() - 300000;
    return !state.response || state.metadata.startTime > fiveMinutesAgo;
  }

  /**
   * Generate a unique workflow ID
   */
  private generateWorkflowId(): string {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Deep merge state updates
   */
  private mergeState(current: WorkflowState, update: NodeUpdate): WorkflowState {
    const merged = { ...current };

    // Handle special cases for arrays and objects
    Object.keys(update).forEach(key => {
      const updateValue = update[key as keyof WorkflowState];

      if (updateValue === undefined) {
        return;
      }

      if (key === 'metadata' && current.metadata && updateValue) {
        // Special handling for metadata - merge instead of replace
        merged.metadata = { ...current.metadata, ...(updateValue as any) };
      } else if (key === 'errors' && Array.isArray(current.errors) && Array.isArray(updateValue)) {
        // Append errors instead of replacing
        merged.errors = [...current.errors, ...(updateValue as Error[])];
      } else if (
        key === 'warnings' &&
        Array.isArray(current.warnings) &&
        Array.isArray(updateValue)
      ) {
        // Append warnings instead of replacing
        merged.warnings = [...current.warnings, ...(updateValue as string[])];
      } else {
        // Default: replace the value
        (merged as any)[key] = updateValue;
      }
    });

    return merged;
  }

  /**
   * Serialises the current state and history into JSON for debugging.
   *
   * @param workflowId - Workflow identifier.
   * @returns Pretty-printed JSON string.
   */
  exportState(workflowId: string): string {
    const state = this.states.get(workflowId);
    const history = this.stateHistory.get(workflowId);

    return JSON.stringify(
      {
        currentState: state,
        history: history,
      },
      null,
      2
    );
  }

  /**
   * Lists workflow IDs that meet the *active* criteria.
   */
  getActiveWorkflowIds(): string[] {
    return Array.from(this.states.keys()).filter(id => this.isWorkflowActive(id));
  }

  /**
   * Test-helper that clears **all** stored states and history.
   */
  clearAll(): void {
    this.states.clear();
    this.stateHistory.clear();
  }

  /**
   * Custom state merger for LangGraph supervisor states
   * Extracts custom fields from agent return values and merges them properly
   */
  mergeSupervisorState(
    currentState: SupervisorStateUpdate,
    agentResult: SupervisorStateUpdate
  ): SupervisorStateUpdate {
    const logger = getLogger();

    logger.debug('[StateManager] Merging supervisor state:', {
      currentStateKeys: Object.keys(currentState),
      agentResultKeys: Object.keys(agentResult),
      hasSearchFields: !!(
        agentResult.searchEnrichedContext ||
        agentResult.searchRawResults ||
        agentResult.searchMetadata
      ),
    });

    // Start with current state
    const merged: SupervisorStateUpdate = { ...currentState };

    // Merge messages (append new messages)
    if (agentResult.messages && Array.isArray(agentResult.messages)) {
      merged.messages = [...(currentState.messages || []), ...agentResult.messages];
      logger.debug('[StateManager] Merged messages:', {
        previousCount: currentState.messages?.length || 0,
        newCount: agentResult.messages.length,
        totalCount: merged.messages.length,
      });
    }

    // Merge completedTools (append new tools)
    if (agentResult.completedTools && Array.isArray(agentResult.completedTools)) {
      merged.completedTools = [
        ...(currentState.completedTools || []),
        ...agentResult.completedTools,
      ];
      logger.debug('[StateManager] Merged completedTools:', {
        previousCount: currentState.completedTools?.length || 0,
        newCount: agentResult.completedTools.length,
        totalCount: merged.completedTools.length,
      });
    }

    // Merge search fields (replace with new values if present)
    if (agentResult.searchEnrichedContext !== undefined) {
      merged.searchEnrichedContext = agentResult.searchEnrichedContext;
      logger.debug('[StateManager] Updated searchEnrichedContext:', {
        hasValue: !!merged.searchEnrichedContext,
        length: merged.searchEnrichedContext?.length || 0,
      });
    }

    if (agentResult.searchRawResults !== undefined) {
      merged.searchRawResults = agentResult.searchRawResults;
      logger.debug('[StateManager] Updated searchRawResults:', {
        hasValue: !!merged.searchRawResults,
        length: merged.searchRawResults?.length || 0,
      });
    }

    if (agentResult.searchMetadata !== undefined) {
      merged.searchMetadata = agentResult.searchMetadata;
      logger.debug('[StateManager] Updated searchMetadata:', {
        hasValue: !!merged.searchMetadata,
        documentCount: merged.searchMetadata?.documentCount || 0,
      });
    }

    // Merge analysis (replace with new value if present)
    if (agentResult.analysis !== undefined) {
      merged.analysis = agentResult.analysis;
      logger.debug('[StateManager] Updated analysis:', {
        hasValue: !!merged.analysis,
      });
    }

    // Merge any other custom fields
    Object.keys(agentResult).forEach(key => {
      if (
        ![
          'messages',
          'completedTools',
          'searchEnrichedContext',
          'searchRawResults',
          'searchMetadata',
          'analysis',
        ].includes(key)
      ) {
        (merged as any)[key] = agentResult[key as keyof SupervisorStateUpdate];
      }
    });

    logger.debug('[StateManager] Final merged state keys:', Object.keys(merged));

    return merged;
  }

  /**
   * Extract search results from agent messages when direct state updates fail
   * This is a fallback mechanism for when LangGraph doesn't process custom fields
   */
  extractSearchResultsFromMessages(messages: BaseMessage[]): {
    searchEnrichedContext?: string;
    searchRawResults?: any[];
    searchMetadata?: { documentCount: number; averageScore: number; relevanceQuality: string };
  } {
    const logger = getLogger();
    const extracted: any = {};

    for (const message of messages) {
      // Check if message content contains search results
      if (message.content && typeof message.content === 'string') {
        try {
          // Try to parse message content as JSON
          const contentObj = JSON.parse(message.content);
          if (contentObj.searchEnrichedContext) {
            extracted.searchEnrichedContext = contentObj.searchEnrichedContext;
          }
          if (contentObj.searchRawResults) {
            extracted.searchRawResults = contentObj.searchRawResults;
          }
          if (contentObj.searchMetadata) {
            extracted.searchMetadata = contentObj.searchMetadata;
          }
        } catch (e) {
          // Not JSON, skip
        }
      }

      // Check additional_kwargs for tool calls
      if (message.additional_kwargs?.tool_calls) {
        for (const toolCall of message.additional_kwargs.tool_calls) {
          if (toolCall.function?.name === 'semantic_search' && toolCall.function?.arguments) {
            try {
              const toolArgs = JSON.parse(toolCall.function.arguments);
              if (toolArgs.searchEnrichedContext) {
                extracted.searchEnrichedContext = toolArgs.searchEnrichedContext;
              }
              if (toolArgs.searchRawResults) {
                extracted.searchRawResults = toolArgs.searchRawResults;
              }
              if (toolArgs.searchMetadata) {
                extracted.searchMetadata = toolArgs.searchMetadata;
              }
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }
    }

    if (Object.keys(extracted).length > 0) {
      logger.info('[StateManager] Extracted search results from messages:', {
        hasSearchEnrichedContext: !!extracted.searchEnrichedContext,
        hasSearchRawResults: !!extracted.searchRawResults,
        hasSearchMetadata: !!extracted.searchMetadata,
      });
    }

    return extracted;
  }
}
