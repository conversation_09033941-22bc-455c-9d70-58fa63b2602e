/**
 * LangGraph workflow types for the multi-agent RAG system
 */

import { AgentInput, AgentResult } from '../../base/types';
import {
  Intent as IntelligenceIntent,
  IntentAnalysisResult,
  ToolSelectionResult,
  ToolSelection as IntelligenceToolSelection,
} from '../../intelligence/types';
import { BaseMessage } from '@langchain/core/messages';
import { Command } from '@langchain/langgraph';

/**
 * Intent classification types (extended from intelligence layer)
 */
export interface Intent extends IntelligenceIntent {}

/**
 * Tool selection types (extended from intelligence layer)
 */
export interface ToolSelection extends IntelligenceToolSelection {}

/**
 * Workflow metadata types
 */
export interface WorkflowMetadata {
  startTime: number;
  workflowId: string;
  version: string;
  debugMode?: boolean;
  compatibilityMode?: boolean;
}

/**
 * Main workflow state definition
 * This represents the state that flows through all nodes in the LangGraph workflow
 */
export interface WorkflowState {
  // Core identification
  sessionId: string;
  userInput: string;
  organizationId: string;
  userId?: string;

  // Workflow processing state
  intent?: Intent;
  intentAnalysisResult?: IntentAnalysisResult;
  selectedTools?: ToolSelection[];
  toolSelectionResult?: ToolSelectionResult;
  results?: Record<string, any>;
  response?: string;
  selectedAgents?: string[];
  agentResults?: Record<string, any>;

  // Error handling
  errors?: Error[];
  warnings?: string[];

  // Metadata
  metadata: WorkflowMetadata;

  // ReAct workflow fields
  thoughts?: string[];
  stepCount?: number;
}

/**
 * Workflow input type (extends base AgentInput for compatibility)
 */
export interface WorkflowInput extends AgentInput {
  // Langfuse observability
  traceId?: string;

  workflowHints?: {
    maintainCompatibility?: boolean;
    legacyResponseFormat?: boolean;
    preferredWorkflow?: 'simple' | 'complex' | 'multi-agent';
    debugMode?: boolean;
  };
}

/**
 * Workflow result type
 */
export interface WorkflowResult extends AgentResult {
  // Enhanced metadata for workflow execution
  workflowId?: string;
  workflowType?: string;
  toolsUsed?: string[];
  executionTime: number;

  // Indicates whether the workflow executed successfully (`true`) or fell back / failed (`false`).
  success?: boolean;

  // Optional enhanced data
  reasoning?: string[];
  debugInfo?: {
    workflowSteps: WorkflowStep[];
    toolSelectionReasoning?: string;
    performanceMetrics?: Record<string, number>;
  };

  // MCP compliance fields
  mcpCompliant?: boolean;

  // Memory and context
  contextDocIds?: string[];
  embeddingCalls?: number;
  documentCount?: number;
  conversationEntries?: number;
  warning?: string;
}

/**
 * Workflow step information for debugging
 */
export interface WorkflowStep {
  step: string;
  duration: number;
  input: any;
  output: any;
  error?: string;
}

/**
 * Node update type for LangGraph state transitions
 */
export type NodeUpdate = Partial<WorkflowState>;

/**
 * Node function type
 */
export type WorkflowNode = (state: WorkflowState) => Promise<NodeUpdate>;

/**
 * Edge function type for conditional routing
 */
export type EdgeFunction = (state: WorkflowState) => string;

/**
 * Workflow configuration
 */
export interface WorkflowConfig {
  maxExecutionTime?: number;
  enableDebug?: boolean;
  fallbackEnabled?: boolean;
  parallelExecution?: boolean;
}

/**
 * Enhanced state schema for LangGraph supervisor architecture
 * Follows the MessagesState pattern with additional context
 */
export interface SupervisorState {
  /** Message history for the conversation */
  messages: BaseMessage[];
  /** Session identifier for conversation tracking */
  sessionId: string;
  /** Organization context for document scoping */
  organizationId: string;
  /** User identifier */
  userId?: string;
  /** Retrieved documents for context */
  documents?: any[];
  /** Simplified search results - enriched context string */
  searchEnrichedContext?: string;
  /** Simplified search results - raw results array */
  searchRawResults?: any[];
  /** Simplified search results - metadata object */
  searchMetadata?: { documentCount: number; averageScore: number; relevanceQuality: string };
  /** Analysis results from document analysis */
  analysis?: any;
  /** Completed steps in the workflow */
  completedTools?: string[];
  /** Metadata for tracking and debugging */
  metadata?: Record<string, any>;
  /** Langfuse trace ID */
  traceId?: string;
  /** Langfuse parent span ID for tool calls */
  supervisorSpanId?: string;
}

/**
 * Tool function signature for handoff tools
 */
export type HandoffTool = () => Command;

/**
 * Agent node function signature for LangGraph
 */
export type AgentNode = (state: SupervisorState) => Promise<Partial<SupervisorState>>;
