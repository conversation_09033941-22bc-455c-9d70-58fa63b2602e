/**
 * Production-grade LangGraph workflow engine with proper resource management
 */

import { WorkflowResourceManager, WorkflowResource } from './workflow-resource-manager';

export interface WorkflowConfig {
  enableDebug?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  resourceManager?: WorkflowResourceManager;
}

export interface WorkflowState {
  [key: string]: any;
}

export interface WorkflowNode {
  name: string;
  execute: (state: WorkflowState, abortSignal?: AbortSignal) => Promise<Partial<WorkflowState>>;
}

export interface WorkflowEdge {
  from: string;
  to: string;
  condition?: (state: WorkflowState) => boolean;
}

/**
 * Production-grade workflow engine with proper resource management and concurrency control
 */
export class WorkflowEngine {
  private nodes: Map<string, WorkflowNode> = new Map();
  private edges: Map<string, string[]> = new Map();
  private conditionalEdges: Map<
    string,
    Array<{ condition: (state: WorkflowState) => boolean; target: string }>
  > = new Map();
  private config: Required<WorkflowConfig>;
  private resourceManager: WorkflowResourceManager;

  constructor(config: WorkflowConfig = {}) {
    this.config = {
      enableDebug: false,
      maxRetries: 3,
      retryDelay: 1000,
      resourceManager: new WorkflowResourceManager(),
      ...config,
    };

    this.resourceManager = this.config.resourceManager;
  }

  /**
   * Adds a node to the workflow
   */
  addNode(node: WorkflowNode): void {
    this.nodes.set(node.name, node);

    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] Added node: ${node.name}`);
    }
  }

  /**
   * Adds an edge between two nodes
   */
  addEdge(from: string, to: string): void {
    if (!this.edges.has(from)) {
      this.edges.set(from, []);
    }

    this.edges.get(from)!.push(to);

    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] Added edge: ${from} -> ${to}`);
    }
  }

  /**
   * Adds a conditional edge
   */
  addConditionalEdge(
    from: string,
    condition: (state: WorkflowState) => boolean,
    target: string
  ): void {
    if (!this.conditionalEdges.has(from)) {
      this.conditionalEdges.set(from, []);
    }

    this.conditionalEdges.get(from)!.push({ condition, target });

    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] Added conditional edge: ${from} -> ${target}`);
    }
  }

  /**
   * Executes the workflow with proper resource management
   */
  async execute(initialState: WorkflowState, startNode: string = 'start'): Promise<WorkflowState> {
    const workflowId = this.generateWorkflowId();
    let resource: WorkflowResource | null = null;

    try {
      // Acquire workflow resource slot
      resource = await this.resourceManager.acquireWorkflowSlot(workflowId);

      if (this.config.enableDebug) {
        console.log(`[WorkflowEngine] Starting workflow ${workflowId} from node: ${startNode}`);
      }

      // Execute workflow with resource management
      const result = await this.executeWithResource(resource, initialState, startNode);

      if (this.config.enableDebug) {
        console.log(`[WorkflowEngine] Workflow ${workflowId} completed successfully`);
      }

      return result;
    } catch (error) {
      if (this.config.enableDebug) {
        console.error(`[WorkflowEngine] Workflow ${workflowId} failed:`, error);
      }

      // Mark resource as failed if we have it
      if (resource) {
        const activeResource = this.resourceManager['activeWorkflows'].get(workflowId);
        if (activeResource) {
          activeResource.status = 'failed';
        }
      }

      throw error;
    } finally {
      // Always clean up resources
      if (resource) {
        await resource.cleanup();
      }
    }
  }

  /**
   * Executes workflow with acquired resource
   */
  private async executeWithResource(
    resource: WorkflowResource,
    initialState: WorkflowState,
    startNode: string
  ): Promise<WorkflowState> {
    let currentState = { ...initialState };
    let currentNode: string | null = startNode;
    let retryCount = 0;

    // Validate start node exists
    if (!this.nodes.has(currentNode)) {
      throw new Error(`Start node '${currentNode}' not found in workflow`);
    }

    while (currentNode) {
      // Check if workflow should continue
      if (!this.resourceManager.incrementStepCount(resource.workflowId)) {
        throw new Error(`Workflow ${resource.workflowId} terminated due to resource limits`);
      }

      // Check abort signal
      if (resource.abortController.signal.aborted) {
        throw new Error(`Workflow ${resource.workflowId} was aborted`);
      }

      try {
        // Execute current node
        const nodeResult = await this.executeNode(
          currentNode,
          currentState,
          resource.abortController.signal
        );

        // Merge node result with current state
        currentState = { ...currentState, ...nodeResult };

        // Determine next node
        const nextNode = this.getNextNode(currentNode, currentState);

        // Reset retry count on successful execution
        retryCount = 0;

        if (this.config.enableDebug && nextNode) {
          console.log(`[WorkflowEngine] Moving to next node: ${nextNode}`);
        }

        // Update current node for next iteration (null will end the loop)
        currentNode = nextNode;
      } catch (error) {
        // Handle retries
        if (retryCount < this.config.maxRetries) {
          retryCount++;
          console.warn(
            `[WorkflowEngine] Node ${currentNode} failed, retrying (${retryCount}/${this.config.maxRetries}):`,
            error
          );

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
          continue;
        }

        // Max retries exceeded
        throw new Error(
          `Node ${currentNode} failed after ${this.config.maxRetries} retries: ${error}`
        );
      }
    }

    return currentState;
  }

  /**
   * Executes a single node with proper error handling
   */
  private async executeNode(
    nodeName: string,
    state: WorkflowState,
    abortSignal: AbortSignal
  ): Promise<Partial<WorkflowState>> {
    const node = this.nodes.get(nodeName);

    if (!node) {
      throw new Error(`Node '${nodeName}' not found`);
    }

    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] Executing node: ${nodeName}`);
    }

    // Execute node with abort signal
    const nodeResult = await node.execute(state, abortSignal);

    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] Node ${nodeName} completed with result:`, nodeResult);
    }

    return nodeResult;
  }

  /**
   * Determines the next node based on edges and conditions
   */
  private getNextNode(currentNode: string, state: WorkflowState): string | null {
    // Check conditional edges first
    const conditionalEdges = this.conditionalEdges.get(currentNode);
    if (conditionalEdges) {
      for (const { condition, target } of conditionalEdges) {
        try {
          if (condition(state)) {
            if (this.config.enableDebug) {
              console.log(`[WorkflowEngine] Conditional edge matched: ${currentNode} -> ${target}`);
            }
            return target;
          }
        } catch (error) {
          console.warn(`[WorkflowEngine] Error evaluating condition for ${currentNode}:`, error);
        }
      }
    }

    // Check regular edges
    const edges = this.edges.get(currentNode);
    if (edges && edges.length > 0) {
      // Return first edge (simple workflow)
      const nextNode = edges[0];
      if (this.config.enableDebug) {
        console.log(`[WorkflowEngine] Following edge: ${currentNode} -> ${nextNode}`);
      }
      return nextNode;
    }

    // No more nodes - workflow complete
    if (this.config.enableDebug) {
      console.log(`[WorkflowEngine] No more edges from ${currentNode} - workflow complete`);
    }
    return null;
  }

  /**
   * Gets workflow metrics from resource manager
   */
  getMetrics() {
    return this.resourceManager.getMetrics();
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    console.log('[WorkflowEngine] Shutting down...');
    await this.resourceManager.shutdown();
    console.log('[WorkflowEngine] Shutdown complete');
  }

  /**
   * Generates a unique workflow ID
   */
  private generateWorkflowId(): string {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
