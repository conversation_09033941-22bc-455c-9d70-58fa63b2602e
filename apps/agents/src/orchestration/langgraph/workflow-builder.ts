/**
 * Workflow builder helper for LangGraph v0.3.5
 * Provides type-safe workflow construction
 */

import { StateGraph, START, END, Annotation } from '@langchain/langgraph';
/**
 * Creates a fully typed *annotation schema* understood by LangGraph.
 * This helper exists because the current LangGraph version lacks generic type
 * inference for dynamic graph construction.
 *
 * @returns Root `Annotation` description used to instantiate a `StateGraph`.
 */
export function createWorkflowAnnotation() {
  return Annotation.Root({
    sessionId: Annotation<string>({
      reducer: (curr, next) => next || curr,
      default: () => '',
    }),
    userInput: Annotation<string>({
      reducer: (curr, next) => next || curr,
      default: () => '',
    }),
    organizationId: Annotation<string>({
      reducer: (curr, next) => next || curr,
      default: () => '',
    }),
    userId: Annotation<string | undefined>({
      reducer: (curr, next) => (next !== undefined ? next : curr),
      default: () => undefined,
    }),
    intent: Annotation<any>({
      reducer: (curr, next) => next || curr,
      default: () => undefined,
    }),
    selectedTools: Annotation<any[]>({
      reducer: (curr, next) => next || curr,
      default: () => [],
    }),
    results: Annotation<any>({
      reducer: (curr, next) => ({ ...curr, ...next }),
      default: () => ({}),
    }),
    response: Annotation<string | undefined>({
      reducer: (curr, next) => next || curr,
      default: () => undefined,
    }),
    errors: Annotation<Error[]>({
      reducer: (curr, next) => [...(curr || []), ...(next || [])],
      default: () => [],
    }),
    warnings: Annotation<string[]>({
      reducer: (curr, next) => [...(curr || []), ...(next || [])],
      default: () => [],
    }),
    metadata: Annotation<any>({
      reducer: (curr, next) => ({ ...curr, ...next }),
      default: () => ({
        startTime: Date.now(),
        workflowId: '',
        version: '0.0.1',
      }),
    }),
    thoughts: Annotation<string[]>({
      reducer: (curr, next) => [...(curr || []), ...(next || [])],
      default: () => [],
    }),
    stepCount: Annotation<number>({
      reducer: (_curr, next) => next,
      default: () => 0,
    }),
  });
}

/**
 * Dynamically builds a `StateGraph` by registering nodes and edges at runtime
 * while preserving strong typing for the shared `WorkflowStateType`.
 *
 * @param nodes - Mapping from node names to async handler functions.
 * @param edges - Directed edges connecting nodes; can reference `START` and `END`.
 * @returns Compiled `StateGraph` ready for execution.
 *
 * @example
 * ```ts
 * const workflow = buildWorkflowGraph({ hello: async s => ({ greeting: 'hi' }) }, [
 *   { from: START, to: 'hello' },
 *   { from: 'hello', to: END }
 * ]);
 * ```
 */
export function buildWorkflowGraph(
  nodes: Record<string, (state: WorkflowStateType) => Promise<Partial<WorkflowStateType>>>,
  edges: Array<{ from: string | typeof START; to: string | typeof END }>
): StateGraph<WorkflowStateType, any, any> {
  // Create a dynamic workflow that bypasses strict type checking
  const WorkflowAnnotation = createWorkflowAnnotation();
  const graph = new StateGraph(WorkflowAnnotation) as any;

  // Add nodes dynamically
  Object.entries(nodes).forEach(([name, handler]) => {
    graph.addNode(name, handler);
  });

  // Add edges dynamically
  edges.forEach(({ from, to }) => {
    graph.addEdge(from, to);
  });

  return graph;
}

/**
 * Type alias representing the complete workflow state shape used throughout the
 * LangGraph orchestration layer.
 */
export type WorkflowStateType = {
  sessionId: string;
  userInput: string;
  organizationId: string;
  userId?: string;
  intent?: any;
  selectedTools?: any[];
  results?: any;
  response?: string;
  errors?: Error[];
  warnings?: string[];
  metadata: any;
  selectedAgents?: string[];
  agentResults?: Record<string, any>;
  thoughts?: string[];
  stepCount?: number;
};
