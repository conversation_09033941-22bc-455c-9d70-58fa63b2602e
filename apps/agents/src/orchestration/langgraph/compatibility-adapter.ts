/**
 * Compatibility adapter for legacy request transformation
 * Bridges the gap between existing API formats and LangGraph workflows
 */

import { WorkflowInput } from './types';

interface LegacyRequest {
  prompt?: string;
  query?: string;
  message?: string;
  input?: string | { prompt?: string; query?: string; message?: string };
  text?: string;
  organizationId?: string;
  userId?: string;
  sessionId?: string;
  [key: string]: any;
}

/**
 * `CompatibilityAdapter` serves as a bridge between legacy API request/response
 * formats and the **LangGraph** workflow engine. It extracts the user prompt
 * from multiple historical field names, enriches metadata, and re-maps the
 * workflow result back to the legacy schema expected by earlier consumers.
 */
export class CompatibilityAdapter {
  /**
   * Transforms a *legacy* request object into a normalised {@link WorkflowInput}.
   *
   * @param legacyRequest - Object potentially containing various deprecated
   *                        input fields such as `prompt`, `query`, `message`, …
   * @returns Standardised workflow input used by the supervisor layer.
   */
  static transformRequest(legacyRequest: LegacyRequest): WorkflowInput {
    // Extract user input from various possible locations
    const userInput = this.extractUserInput(legacyRequest);

    // Extract metadata
    const organizationId = legacyRequest.organizationId || 'default';
    const userId = legacyRequest.userId;
    const sessionId = legacyRequest.sessionId || this.generateSessionId(organizationId, userId);

    // Create workflow input
    const workflowInput: WorkflowInput = {
      input: userInput,
      organizationId,
      userId,
      sessionId,
      workflowHints: {
        maintainCompatibility: true,
        debugMode: process.env.NODE_ENV === 'development',
      },
    };

    return workflowInput;
  }

  /**
   * Attempts to extract the user prompt from all known legacy properties.
   *
   * @param request - Incoming request payload.
   * @returns Resolved user input (can be empty string).
   */
  private static extractUserInput(request: LegacyRequest): string {
    // Direct string properties
    const directInput = request.prompt || request.query || request.message || request.text;
    if (directInput) {
      return directInput;
    }

    // Nested input object
    if (typeof request.input === 'string') {
      return request.input;
    }

    if (typeof request.input === 'object' && request.input !== null) {
      const nestedInput = request.input as any;
      return (
        nestedInput.prompt || nestedInput.query || nestedInput.message || nestedInput.text || ''
      );
    }

    // Fallback to empty string
    return '';
  }

  /**
   * Generates a unique session identifier when none is supplied by the client.
   *
   * @param organizationId - Organization scope for the session.
   * @param userId - Optional user identifier; *anonymous* is used when absent.
   */
  private static generateSessionId(organizationId: string, userId?: string): string {
    const userPart = userId || 'anonymous';
    return `session_${organizationId}_${userPart}_${Date.now()}`;
  }

  /**
   * Converts the final workflow result back into the legacy response layout.
   *
   * @param workflowResult - Rich result as returned by the supervisor.
   * @returns Simplified response for backwards compatibility.
   */
  static transformResponse(workflowResult: any): any {
    // For backward compatibility, return the same format as AskAIAgent
    if (workflowResult.output) {
      return workflowResult.output;
    }

    // Fallback to returning the entire result
    return workflowResult;
  }
}
