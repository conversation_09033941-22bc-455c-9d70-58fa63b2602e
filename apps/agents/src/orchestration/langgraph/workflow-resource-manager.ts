/**
 * Production-grade workflow resource manager
 * Handles proper lifecycle management, concurrency control, and resource cleanup
 */

import { EventEmitter } from 'events';
import { ILogger } from '../../integration/mcp-server/mcp-bridge-enhanced';
import { getLogger } from '../../observability/global-logger';

export interface WorkflowResourceConfig {
  maxConcurrentWorkflows: number;
  maxWorkflowDuration: number;
  maxStepsPerWorkflow: number;
  cleanupInterval: number;
  abortSignalMaxListeners: number;
}

export interface WorkflowResource {
  workflowId: string;
  abortController: AbortController;
  startTime: number;
  stepCount: number;
  status: 'running' | 'completed' | 'failed' | 'aborted';
  cleanup: () => Promise<void>;
}

/**
 * WorkflowResourceManager provides production-grade resource management for LangGraph workflows.
 *
 * Key features:
 * - Proper concurrency control with queue management
 * - Individual AbortController per workflow with proper cleanup
 * - Resource tracking and automatic cleanup
 * - Graceful degradation under high load
 * - Comprehensive monitoring and metrics
 */
export class WorkflowResourceManager {
  private config: WorkflowResourceConfig;
  private activeWorkflows: Map<string, WorkflowResource> = new Map();
  private workflowQueue: Array<{ resolve: Function; reject: Function; workflowId: string }> = [];
  private cleanupTimer: NodeJS.Timeout | null = null;
  private isShuttingDown = false;
  private logger: ILogger;

  constructor(config: Partial<WorkflowResourceConfig> = {}) {
    this.config = {
      maxConcurrentWorkflows: 10,
      maxWorkflowDuration: 30000, // 30 seconds
      maxStepsPerWorkflow: 15,
      cleanupInterval: 60000, // 1 minute
      abortSignalMaxListeners: 25,
      ...config,
    };
    this.logger = getLogger();
    this.startCleanupTimer();
  }

  /**
   * Acquires a workflow resource slot with proper concurrency control
   */
  async acquireWorkflowSlot(workflowId: string): Promise<WorkflowResource> {
    if (this.isShuttingDown) {
      throw new Error('WorkflowResourceManager is shutting down');
    }

    // Check if we're at capacity
    if (this.activeWorkflows.size >= this.config.maxConcurrentWorkflows) {
      this.logger.info(
        `[ResourceManager] At capacity (${this.activeWorkflows.size}/${this.config.maxConcurrentWorkflows}), queuing workflow ${workflowId}`
      );

      // Queue the workflow and wait for a slot
      await this.waitForSlot(workflowId);
    }

    // Create workflow resource
    const abortController = new AbortController();

    // Set max listeners for this specific AbortController
    if (abortController.signal && typeof abortController.signal.addEventListener === 'function') {
      try {
        // Set max listeners on the specific signal instance
        (abortController.signal as any).setMaxListeners?.(this.config.abortSignalMaxListeners);
      } catch (error) {
        // Fallback: use EventEmitter if signal extends EventEmitter
        if (abortController.signal instanceof EventEmitter) {
          abortController.signal.setMaxListeners(this.config.abortSignalMaxListeners);
        }
      }
    }

    const resource: WorkflowResource = {
      workflowId,
      abortController,
      startTime: Date.now(),
      stepCount: 0,
      status: 'running',
      cleanup: async () => {
        await this.releaseWorkflowSlot(workflowId);
      },
    };

    this.activeWorkflows.set(workflowId, resource);
    this.logger.info(
      `[ResourceManager] Slot allocated | workflowId=${workflowId} active=${this.activeWorkflows.size}/${this.config.maxConcurrentWorkflows}`
    );

    return resource;
  }

  /**
   * Releases a workflow resource slot and triggers queue processing
   */
  async releaseWorkflowSlot(workflowId: string): Promise<void> {
    const resource = this.activeWorkflows.get(workflowId);

    if (resource) {
      try {
        // Abort the controller to clean up listeners
        resource.abortController.abort();

        // Mark as completed
        resource.status = resource.status === 'running' ? 'completed' : resource.status;

        // Remove from active workflows
        this.activeWorkflows.delete(workflowId);

        this.logger.info(
          `[ResourceManager] Slot released | workflowId=${workflowId} active=${this.activeWorkflows.size}/${this.config.maxConcurrentWorkflows}`
        );

        // Process queue if there are waiting workflows
        this.processQueue();
      } catch (error) {
        this.logger.error(error, `[ResourceManager] Error releasing workflow ${workflowId}`);
      }
    }
  }

  /**
   * Increments step count and checks limits
   */
  incrementStepCount(workflowId: string): boolean {
    const resource = this.activeWorkflows.get(workflowId);

    if (!resource) {
      return false;
    }

    resource.stepCount++;

    // Check step limit
    if (resource.stepCount > this.config.maxStepsPerWorkflow) {
      this.logger.warn(
        `[ResourceManager] Workflow ${workflowId} exceeded max steps (${this.config.maxStepsPerWorkflow})`
      );
      this.abortWorkflow(workflowId, 'max_steps_exceeded');
      return false;
    }

    // Check time limit
    const elapsed = Date.now() - resource.startTime;
    if (elapsed > this.config.maxWorkflowDuration) {
      this.logger.warn(
        `[ResourceManager] Workflow ${workflowId} exceeded max duration (${this.config.maxWorkflowDuration}ms)`
      );
      this.abortWorkflow(workflowId, 'timeout');
      return false;
    }

    return true;
  }

  /**
   * Aborts a specific workflow
   */
  abortWorkflow(workflowId: string, reason: string): void {
    const resource = this.activeWorkflows.get(workflowId);

    if (resource && resource.status === 'running') {
      this.logger.info(`[ResourceManager] Aborting workflow ${workflowId} - reason: ${reason}`);

      resource.status = 'aborted';
      resource.abortController.abort();

      // Schedule cleanup
      setTimeout(() => {
        this.releaseWorkflowSlot(workflowId);
      }, 100);
    }
  }

  /**
   * Gets the AbortSignal for a workflow
   */
  getAbortSignal(workflowId: string): AbortSignal | null {
    const resource = this.activeWorkflows.get(workflowId);
    return resource?.abortController.signal || null;
  }

  /**
   * Gets current resource metrics
   */
  getMetrics() {
    const activeCount = this.activeWorkflows.size;
    const queuedCount = this.workflowQueue.length;

    const statusCounts = {
      running: 0,
      completed: 0,
      failed: 0,
      aborted: 0,
    };

    this.activeWorkflows.forEach(resource => {
      statusCounts[resource.status]++;
    });

    return {
      active: activeCount,
      queued: queuedCount,
      capacity: this.config.maxConcurrentWorkflows,
      utilization: (activeCount / this.config.maxConcurrentWorkflows) * 100,
      statusCounts,
    };
  }

  /**
   * Graceful shutdown with proper cleanup
   */
  async shutdown(): Promise<void> {
    this.logger.info('[ResourceManager] Starting graceful shutdown...');

    this.isShuttingDown = true;

    // Clear cleanup timer
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // Reject all queued workflows
    this.workflowQueue.forEach(({ reject, workflowId }) => {
      reject(new Error(`Workflow ${workflowId} rejected due to shutdown`));
    });
    this.workflowQueue.length = 0;

    // Abort all active workflows
    const abortPromises = Array.from(this.activeWorkflows.keys()).map(workflowId => {
      this.abortWorkflow(workflowId, 'shutdown');
      return this.waitForWorkflowCompletion(workflowId, 5000); // 5 second timeout
    });

    try {
      await Promise.all(abortPromises);
      this.logger.info('[ResourceManager] All workflows cleaned up successfully');
    } catch (error) {
      this.logger.error(error, '[ResourceManager] Error during shutdown cleanup');
    }

    // Final cleanup
    this.activeWorkflows.clear();

    this.logger.info('[ResourceManager] Shutdown complete');
  }

  /**
   * Waits for a workflow slot to become available
   */
  private async waitForSlot(workflowId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        // Remove from queue
        const index = this.workflowQueue.findIndex(item => item.workflowId === workflowId);
        if (index >= 0) {
          this.workflowQueue.splice(index, 1);
        }
        reject(new Error(`Workflow ${workflowId} timed out waiting for slot`));
      }, 30000); // 30 second queue timeout

      this.workflowQueue.push({
        resolve: () => {
          clearTimeout(timeout);
          resolve();
        },
        reject: (error: Error) => {
          clearTimeout(timeout);
          reject(error);
        },
        workflowId,
      });
    });
  }

  /**
   * Processes the workflow queue
   */
  private processQueue(): void {
    if (
      this.workflowQueue.length > 0 &&
      this.activeWorkflows.size < this.config.maxConcurrentWorkflows
    ) {
      const next = this.workflowQueue.shift();
      if (next) {
        next.resolve();
      }
    }
  }

  /**
   * Starts the cleanup timer for resource management
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Performs periodic cleanup of stale resources
   */
  private performCleanup(): void {
    const now = Date.now();
    const staleWorkflows: string[] = [];

    this.activeWorkflows.forEach((resource, workflowId) => {
      const elapsed = now - resource.startTime;

      // Clean up workflows that have exceeded their time limit
      if (elapsed > this.config.maxWorkflowDuration * 2) {
        // 2x the limit for cleanup
        staleWorkflows.push(workflowId);
      }
    });

    staleWorkflows.forEach(workflowId => {
      this.logger.warn(`[ResourceManager] Cleaning up stale workflow ${workflowId}`);
      this.abortWorkflow(workflowId, 'stale_cleanup');
    });

    // Log metrics periodically
    const metrics = this.getMetrics();
    if (metrics.active > 0 || metrics.queued > 0) {
      this.logger.info(
        `[ResourceManager] Metrics snapshot | active=${metrics.active} queued=${metrics.queued} utilization=${metrics.utilization.toFixed(1)}%`
      );
    }
  }

  /**
   * Waits for a specific workflow to complete
   */
  private async waitForWorkflowCompletion(workflowId: string, timeout: number): Promise<void> {
    return new Promise(resolve => {
      const checkInterval = setInterval(() => {
        if (!this.activeWorkflows.has(workflowId)) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);

      setTimeout(() => {
        clearInterval(checkInterval);
        resolve(); // Resolve anyway after timeout
      }, timeout);
    });
  }
}
