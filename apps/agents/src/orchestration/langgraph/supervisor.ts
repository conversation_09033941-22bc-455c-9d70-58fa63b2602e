import { WorkflowInput, WorkflowResult } from './types';
import { StandardAgentOutput } from '../../base/types';
import { StateGraph, START, END, MemorySaver, InMemoryStore } from '@langchain/langgraph';
import { BaseMessage, HumanMessage, SystemMessage, AIMessage } from '@langchain/core/messages';
import { Runnable } from '@langchain/core/runnables';

// Import agent factories from idiomatic supervisor
import { SupervisorStateAnnotation } from './agents/idiomatic-supervisor';
import { SearchAgentFactory } from './agents/search-agent';
import { AnalysisAgentFactory } from './agents/analysis-agent';
import { ChatAgentFactory } from './agents/chat-agent';

// Import services
import { MCPConnectionManager } from '../../integration/mcp-server/connection-manager';

// Langfuse observability
import { getLangfuseClient } from '../../observability/langfuse';
import { Callback<PERSON>andler as Langfuse<PERSON>allbackHandler } from 'langfuse-langchain';
import { ILogger } from '../../integration/mcp-server/mcp-bridge-enhanced';
import { getLogger, getLoggerWithTraceId } from '../../observability/global-logger';
import { StateManager } from './state-manager';

/**
 * Enhanced state schema using the same schema as idiomatic supervisor
 */
type SupervisorState = typeof SupervisorStateAnnotation.State;

/** Add a helper type for the compiled graph */
type CompiledWorkflow = Runnable<Partial<SupervisorState>, unknown> & {
  getState: (options: any) => Promise<any>;
};

/**
 * Configuration for the manual supervisor
 */
export interface ManualSupervisorConfig {
  maxExecutionTime?: number;
  enableDebug?: boolean;
  fallbackEnabled?: boolean;
  outputMode?: 'full_history' | 'last_message';
  enablePersistence?: boolean;
  maxConversationLength?: number;
  enableResourceLimits?: boolean;
  chatPromptType?: 'text' | 'chat';
}

/**
 * Manual LangGraph supervisor with deterministic routing
 * This implementation follows the same workflow as idiomatic supervisor:
 * search_expert → analysis_expert → response_expert → END
 * But uses manual routing logic instead of LLM-based routing
 */
export class ManualLangGraphSupervisor {
  /** compiled LangGraph runtime */
  private workflow: CompiledWorkflow | null = null;
  private config: Required<ManualSupervisorConfig>;
  private connectionManager: MCPConnectionManager;
  private langfuseClient: any;
  private logger: ILogger;

  // Agent factories (reused from idiomatic supervisor)
  private searchAgentFactory: SearchAgentFactory;
  private analysisAgentFactory: AnalysisAgentFactory;
  private chatAgentFactory: ChatAgentFactory;

  // Memory and persistence
  private checkpointer: MemorySaver;
  private store: InMemoryStore;
  private activeThreads: Set<string> = new Set();

  // Custom state manager for handling complex state merging
  private stateManager: StateManager;

  /**
   * A promise that represents completion of async constructor work
   */
  private initializationPromise: Promise<void>;

  constructor(connectionManager: MCPConnectionManager, config: ManualSupervisorConfig = {}) {
    this.connectionManager = connectionManager;
    this.langfuseClient = getLangfuseClient();
    this.logger = getLogger();
    this.config = {
      maxExecutionTime: 30000,
      enableDebug: process.env.NODE_ENV === 'development',
      fallbackEnabled: true,
      outputMode: 'last_message',
      enablePersistence: true,
      maxConversationLength: 20, // Reduced from 100 to 20 to limit token usage
      enableResourceLimits: true,
      chatPromptType: 'text',
      ...config,
    };

    // Initialize memory and persistence
    this.checkpointer = new MemorySaver();
    this.store = new InMemoryStore();

    // Initialize custom state manager
    this.stateManager = new StateManager();

    // Initialize agent factories (reusing from idiomatic supervisor)
    this.searchAgentFactory = new SearchAgentFactory(this.connectionManager, this.langfuseClient);
    this.analysisAgentFactory = new AnalysisAgentFactory(this.langfuseClient);
    this.chatAgentFactory = new ChatAgentFactory(this.langfuseClient, this.config.chatPromptType);

    // Capture logger locally for use inside asynchronous initialization closure
    const logger = this.logger;

    // Kick-off asynchronous initialization
    this.initializationPromise = (async () => {
      try {
        // await this.initializePrompts();
        await Promise.resolve(); // Just do nothing
      } catch (err) {
        logger.warn(
          `[ManualSupervisor] initializePrompts encountered an error: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      } finally {
        // Always attempt to build the workflow
        this.initializeWorkflow();
      }
    })();
  }

  /**
   * Ensures that asynchronous constructor work is finished
   */
  private async ensureInitialized(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  /**
   * Initialize prompts from Langfuse
   */
  // private async initializePrompts(): Promise<void> {
  //   try {
  //     // Load chat agent prompt from Langfuse using the prompt type directly
  //     const chatPromptName = this.config.chatPromptType;

  //     try {
  //       await this.chatAgentFactory.loadPromptFromLangfuse(chatPromptName);

  //       if (this.config.enableDebug) {
  //         this.logger.info(`[ManualSupervisor] Loaded chat agent prompt: ${chatPromptName}`);
  //       }
  //     } catch (promptError) {
  //       this.logger.warn(
  //         `[ManualSupervisor] Failed to load chat agent prompt ${chatPromptName}: ${
  //           promptError instanceof Error ? promptError.message : String(promptError)
  //         }`
  //       );
  //       // Continue with default prompts - this is non-blocking
  //     }
  //   } catch (error) {
  //     this.logger.warn(`[ManualSupervisor] Failed to initialize prompts from Langfuse: ${error}`);
  //     // Continue with default prompts - this is non-blocking
  //   }
  // }

  /**
   * Initialize the LangGraph workflow using manual StateGraph construction
   * This follows the same workflow as idiomatic supervisor:
   * search_expert → analysis_expert → response_expert → END
   */
  private initializeWorkflow(): void {
    try {
      // Create the StateGraph with the same annotation as idiomatic supervisor
      const graph = new StateGraph(SupervisorStateAnnotation) as any;

      // Create specialized agents using the same factories as idiomatic supervisor
      const searchAgent = this.searchAgentFactory.createAgent(SupervisorStateAnnotation);
      const analysisAgent = this.analysisAgentFactory.createAgent(SupervisorStateAnnotation);
      const chatAgent = this.chatAgentFactory.createAgent(SupervisorStateAnnotation);

      // Create node wrappers that invoke the agent runnables
      const searchExpertNode = async (
        state: SupervisorState
      ): Promise<Partial<SupervisorState>> => {
        if (this.config.enableDebug) {
          this.logger.info('[ManualSupervisor] Executing search_expert');
        }

        try {
          // Invoke the search agent runnable
          const result = await searchAgent.invoke(state);

          // Debug: Log what the search agent actually returns
          if (this.config.enableDebug) {
            this.logger.info('[ManualSupervisor] Search agent result debugging:', {
              resultKeys: Object.keys(result || {}),
              hasSearchEnrichedContext: !!(result as any)?.searchEnrichedContext,
              hasSearchRawResults: !!(result as any)?.searchRawResults,
              hasSearchMetadata: !!(result as any)?.searchMetadata,
              searchEnrichedContextType: typeof (result as any)?.searchEnrichedContext,
              searchRawResultsType: typeof (result as any)?.searchRawResults,
              searchMetadataType: typeof (result as any)?.searchMetadata,
              resultType: typeof result,
              messagesCount: result?.messages?.length || 0,
            });
          }

          // Ensure we track completion
          const updatedCompletedTools = [...(state.completedTools || []), 'search_expert'];

          // Extract search fields from the agent result
          const searchEnrichedContext = (result as any)?.searchEnrichedContext;
          const searchRawResults = (result as any)?.searchRawResults;
          const searchMetadata = (result as any)?.searchMetadata;

          // If the agent didn't return search fields directly, try to extract from messages
          let extractedSearchEnrichedContext = searchEnrichedContext;
          let extractedSearchRawResults = searchRawResults;
          let extractedSearchMetadata = searchMetadata;

          if (!searchEnrichedContext && !searchRawResults && !searchMetadata) {
            if (this.config.enableDebug) {
              this.logger.warn(
                '[ManualSupervisor] Search fields not found in agent result, attempting extraction from messages'
              );
            }

            const extracted = this.stateManager.extractSearchResultsFromMessages(
              result?.messages || []
            );
            extractedSearchEnrichedContext = extracted.searchEnrichedContext;
            extractedSearchRawResults = extracted.searchRawResults;
            extractedSearchMetadata = extracted.searchMetadata;

            if (
              extracted.searchEnrichedContext ||
              extracted.searchRawResults ||
              extracted.searchMetadata
            ) {
              if (this.config.enableDebug) {
                this.logger.info(
                  '[ManualSupervisor] Successfully extracted search fields from messages'
                );
              }
            }
          }

          // Return the merged state with proper typing
          const mergedState: any = {
            ...result,
            completedTools: updatedCompletedTools,
            searchEnrichedContext: extractedSearchEnrichedContext,
            searchRawResults: extractedSearchRawResults,
            searchMetadata: extractedSearchMetadata,
          };

          if (this.config.enableDebug) {
            this.logger.info('[ManualSupervisor] Final merged state:', {
              hasSearchEnrichedContext: !!mergedState.searchEnrichedContext,
              hasSearchRawResults: !!mergedState.searchRawResults,
              hasSearchMetadata: !!mergedState.searchMetadata,
              completedToolsCount: mergedState.completedTools?.length || 0,
              messagesCount: mergedState.messages?.length || 0,
            });
          }

          return mergedState;
        } catch (error) {
          this.logger.error(error, '[ManualSupervisor] Search expert failed');
          return {
            completedTools: [...(state.completedTools || []), 'search_expert_failed'],
            searchEnrichedContext: undefined,
            searchRawResults: undefined,
            searchMetadata: undefined,
          };
        }
      };

      const analysisExpertNode = async (
        state: SupervisorState
      ): Promise<Partial<SupervisorState>> => {
        if (this.config.enableDebug) {
          this.logger.info('[ManualSupervisor] Executing analysis_expert');
        }

        try {
          // Invoke the analysis agent runnable
          const result = await analysisAgent.invoke(state);

          // Ensure we track completion
          const updatedCompletedTools = [...(state.completedTools || []), 'analysis_expert'];

          // The agent will return state updates via tool calls
          return {
            ...result,
            completedTools: updatedCompletedTools,
          };
        } catch (error) {
          this.logger.error(error, '[ManualSupervisor] Analysis expert failed');
          return {
            completedTools: [...(state.completedTools || []), 'analysis_expert_failed'],
            analysis: null,
          };
        }
      };

      const responseExpertNode = async (
        state: SupervisorState
      ): Promise<Partial<SupervisorState>> => {
        if (this.config.enableDebug) {
          this.logger.info('[ManualSupervisor] Executing response_expert');
        }

        try {
          // Invoke the chat agent runnable
          const result = await chatAgent.invoke(state);

          // Ensure we track completion
          const updatedCompletedTools = [...(state.completedTools || []), 'response_expert'];

          // The agent will return state updates via tool calls
          return {
            ...result,
            completedTools: updatedCompletedTools,
          };
        } catch (error) {
          this.logger.error(error, '[ManualSupervisor] Response expert failed');
          return {
            completedTools: [...(state.completedTools || []), 'response_expert_failed'],
            messages: [
              ...state.messages,
              new AIMessage('I apologize, but I encountered an error generating the response.'),
            ],
          };
        }
      };

      // Manual routing logic based on state - this is the key difference from idiomatic supervisor
      const manualRouter = (state: SupervisorState): string => {
        const completedTools = state.completedTools || [];

        if (this.config.enableDebug) {
          this.logger.info(
            `[ManualSupervisor] Routing decision - searchEnrichedContext: ${(state as any).searchEnrichedContext ? 'present' : 'null'}, searchRawResults: ${(state as any).searchRawResults ? 'present' : 'null'}, analysis: ${(state as any).analysis ? 'present' : 'null'}, completedTools: ${completedTools.join(',')}`
          );
        }

        // Rule 1: If search_expert hasn't been executed yet, go to search_expert
        if (
          !completedTools.includes('search_expert') &&
          !completedTools.includes('semantic_search')
        ) {
          if (this.config.enableDebug) {
            this.logger.info('[ManualSupervisor] Routing to search_expert - not executed yet');
          }
          return 'search_expert';
        }

        // Rule 2: If search completed but analysis hasn't been executed yet, go to analysis_expert
        if (
          (completedTools.includes('search_expert') ||
            completedTools.includes('semantic_search')) &&
          !completedTools.includes('analysis_expert')
        ) {
          if (this.config.enableDebug) {
            this.logger.info(
              '[ManualSupervisor] Routing to analysis_expert - search completed, analysis pending'
            );
          }
          return 'analysis_expert';
        }

        // Rule 3: If both search and analysis completed, go to response_expert
        if (
          (completedTools.includes('search_expert') ||
            completedTools.includes('semantic_search')) &&
          completedTools.includes('analysis_expert') &&
          !completedTools.includes('response_expert')
        ) {
          if (this.config.enableDebug) {
            this.logger.info(
              '[ManualSupervisor] Routing to response_expert - search and analysis completed'
            );
          }
          return 'response_expert';
        }

        // Default: END the workflow
        if (this.config.enableDebug) {
          this.logger.info('[ManualSupervisor] Routing to END - workflow complete');
        }
        return END;
      };

      // Add nodes to the graph
      graph.addNode('search_expert', searchExpertNode);
      graph.addNode('analysis_expert', analysisExpertNode);
      graph.addNode('response_expert', responseExpertNode);

      // Add edges - manual routing with deterministic logic
      graph.addEdge(START, 'search_expert'); // Always start with search
      graph.addConditionalEdges('search_expert', manualRouter);
      graph.addConditionalEdges('analysis_expert', manualRouter);
      graph.addConditionalEdges('response_expert', manualRouter);

      // Compile with memory and store for conversation persistence
      const compileOptions: any = {};

      if (this.config.enablePersistence) {
        compileOptions.checkpointer = this.checkpointer;
        compileOptions.store = this.store;
      }

      // Add interrupts for debugging if enabled
      if (this.config.enableDebug) {
        compileOptions.interruptBefore = [];
        compileOptions.interruptAfter = [];
      }

      this.workflow = graph.compile(compileOptions) as unknown as CompiledWorkflow;

      if (this.config.enableDebug) {
        this.logger.info(
          '[ManualSupervisor] Workflow initialized successfully using manual routing'
        );
        this.logger.info(
          `[ManualSupervisor] Agents configured: ${JSON.stringify(['search_expert', 'analysis_expert', 'response_expert'], null, 2)}`
        );
        this.logger.info(`[ManualSupervisor] Output mode: ${this.config.outputMode}`);
      }
    } catch (error) {
      this.logger.error(error, '[ManualSupervisor] Failed to initialize workflow');
      throw new Error(`Failed to initialize manual LangGraph workflow: ${error}`);
    }
  }

  /**
   * Execute the compiled LangGraph workflow with manual routing
   *
   * This follows the same execution pattern as idiomatic supervisor but with
   * deterministic routing instead of LLM-based routing.
   */
  async executeWorkflow(input: WorkflowInput, parentSpan?: any): Promise<WorkflowResult> {
    // Wait for constructor async work to finish
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    const startTime = Date.now();
    const workflowId = `manual_workflow_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const threadId = input.sessionId || `thread_${workflowId}`;
    const logger = input.traceId ? getLoggerWithTraceId(input.traceId) : this.logger;

    logger.info(`Starting ManualLangGraphSupervisor workflow`, {
      workflowId,
      threadId,
      organizationId: input.organizationId,
      userId: input.userId,
    });

    // Create Langfuse span for supervisor execution if parentSpan provided
    const langfuseClient = this.langfuseClient;
    let supervisorSpan: any = null;
    let supervisorSpanId: string | undefined;

    if (langfuseClient && parentSpan) {
      try {
        supervisorSpan = langfuseClient.span({
          traceId: input.traceId,
          parentObservationId: parentSpan.id,
          name: 'manual_supervisor',
          input: input.input,
          metadata: {
            workflowId,
            threadId,
            organizationId: input.organizationId,
            supervisorType: 'manual',
          },
        });
        supervisorSpanId = supervisorSpan.id;
        logger.info(`Langfuse supervisor span created`, {
          workflowId,
          supervisorSpanId,
        });
      } catch (error) {
        logger.error(error, `Failed to create supervisor span`);
      }
    }

    // Create a Langfuse handler to propagate tracing to child runs
    const langfuseHandler = supervisorSpan
      ? new LangfuseCallbackHandler({ root: supervisorSpan })
      : undefined;

    // Resource management
    if (this.config.enableResourceLimits) {
      this.activeThreads.add(threadId);

      // Cleanup old threads if too many active
      if (this.activeThreads.size > 50) {
        const oldThreads = Array.from(this.activeThreads).slice(0, 10);
        oldThreads.forEach(thread => this.activeThreads.delete(thread));
      }
    }

    try {
      if (this.config.enableDebug) {
        logger.info(`Starting workflow execution`, {
          workflowId,
          threadId,
          activeThreads: this.activeThreads.size,
        });
      }

      // Check conversation length limits
      if (this.config.enablePersistence && this.config.maxConversationLength > 0) {
        await this.enforceConversationLimits(threadId);
      }

      // Create initial state with optimized message history
      const baseMessages = [
        new HumanMessage(
          typeof input.input === 'string' ? input.input : JSON.stringify(input.input as any)
        ),
        new SystemMessage(`Context: organizationId=${input.organizationId || 'default-org'}`),
      ] as BaseMessage[];

      // Add truncated conversation history if persistence is enabled
      let conversationHistory: BaseMessage[] = [];
      if (this.config.enablePersistence) {
        conversationHistory = await this.getTruncatedConversationHistory(threadId, 5); // Limit to last 5 messages
      }

      const initialState: Partial<SupervisorState> = {
        messages: [...conversationHistory, ...baseMessages],
        sessionId: input.sessionId || 'default-session',
        organizationId: input.organizationId || 'default-org',
        userId: input.userId,
        documents: [],
        searchEnrichedContext: undefined,
        searchRawResults: undefined,
        searchMetadata: undefined,
        analysis: null,
        metadata: {
          workflowId,
          startTime,
        },
        traceId: input.traceId,
        supervisorSpanId,
      };

      // Execute the workflow with thread-based memory
      const result: any = await this.workflow.invoke(initialState, {
        configurable: { thread_id: threadId },
        callbacks: langfuseHandler ? [langfuseHandler] : [],
      });

      logger.debug(`Workflow execution completed`, {
        workflowId,
        resultType: typeof result,
        hasMessages: result.messages && Array.isArray(result.messages),
        messageCount: result.messages?.length || 0,
      });

      // The workflow has completed, format the response directly
      // Note: State management should happen during workflow execution, not after completion

      const formatted = this._formatResponse(result, workflowId, startTime);

      // End supervisor span and attach metrics for normal path
      if (supervisorSpan) {
        try {
          supervisorSpan.end({
            output: formatted.output.response.substring(0, 1000),
            metadata: {
              execution_time_ms: formatted.executionTime,
              documents: formatted.output.metrics?.rows ?? 0,
              tools_used: formatted.toolsUsed,
              executor: 'manual-supervisor',
            },
          });
        } catch (_) {
          // ignore
        }
      }

      if (langfuseClient && input.traceId) {
        try {
          langfuseClient.score({
            traceId: input.traceId,
            name: 'manual_langgraph_workflow.latency_ms',
            value: formatted.executionTime,
          });
          langfuseClient.score({
            traceId: input.traceId,
            name: 'manual_langgraph_workflow.document_count',
            value: formatted.output.metrics?.rows ?? 0,
          });
        } catch (_) {
          // ignore
        }
      }

      logger.info(`Workflow execution completed successfully`, {
        workflowId,
        executionTimeMs: Date.now() - startTime,
        responseLength: formatted.output.response.length,
        toolsUsed: formatted.toolsUsed,
        executor: 'manual-supervisor',
      });

      return formatted;
    } catch (error) {
      // Update supervisor span with error
      if (supervisorSpan) {
        try {
          supervisorSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              success: false,
            },
          });
          supervisorSpan.end();
        } catch (spanError) {
          logger.error(spanError, `Failed to update supervisor span with error`);
        }
      }

      if (this.config.enableDebug) {
        logger.error(error, `Workflow execution failed`, {
          workflowId,
          executionTimeMs: Date.now() - startTime,
          executor: 'manual-supervisor',
        });
      }
      if (this.config.fallbackEnabled) {
        return this.createFallbackResult(input, error as Error);
      }
      throw error;
    } finally {
      // Cleanup resources
      if (this.config.enableResourceLimits) {
        this.activeThreads.delete(threadId);
      }
    }
  }

  /**
   * Extract tools used from message history
   */
  private extractToolsUsed(messages: BaseMessage[]): string[] {
    const toolsUsed: string[] = [];

    for (const message of messages) {
      if (message.constructor.name === 'ToolMessage') {
        const toolMessage = message as any;
        if (toolMessage.name && !toolsUsed.includes(toolMessage.name)) {
          toolsUsed.push(toolMessage.name);
        }
      }
    }

    return toolsUsed;
  }

  /**
   * Create fallback result when workflow fails
   */
  private createFallbackResult(_input: WorkflowInput, error: Error): WorkflowResult {
    return {
      output: {
        response: `I apologize, but I encountered an error processing your request: ${error.message}. Please try again or contact support if the issue persists.`,
        metadata: {},
        metrics: {},
      },
      success: false,
      executionTime: 0,
      toolsUsed: [],
      metadata: {
        tokensUsed: 0,
        latencyMs: 0,
        toolsInvoked: [],
        warnings: [error.message],
      },
      warning: `Fallback response due to error: ${error.message}`,
    };
  }

  /**
   * Get workflow metrics
   */
  getMetrics() {
    return {
      workflowInitialized: !!this.workflow,
      debugEnabled: this.config.enableDebug,
      fallbackEnabled: this.config.fallbackEnabled,
      maxExecutionTime: this.config.maxExecutionTime,
      outputMode: this.config.outputMode,
      persistenceEnabled: this.config.enablePersistence,
      activeThreads: this.activeThreads.size,
      maxConversationLength: this.config.maxConversationLength,
      resourceLimitsEnabled: this.config.enableResourceLimits,
    };
  }

  /**
   * Shutdown the supervisor
   */
  async shutdown(): Promise<void> {
    if (this.config.enableDebug) {
      this.logger.info('[ManualSupervisor] Shutting down');
    }
    this.workflow = null;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics: any;
  }> {
    const metrics = this.getMetrics();
    const status = this.workflow ? 'healthy' : 'unhealthy';
    return {
      status,
      metrics,
    };
  }

  /**
   * Get conversation history for a thread
   */
  async getConversationHistory(threadId: string): Promise<BaseMessage[]> {
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    try {
      const state = await this.workflow.getState({ configurable: { thread_id: threadId } });
      return state.values.messages || [];
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[ManualSupervisor] Failed to get conversation history for thread ${threadId}`
        );
      }
      return [];
    }
  }

  /**
   * Get truncated conversation history to reduce token usage
   */
  async getTruncatedConversationHistory(
    threadId: string,
    maxMessages: number = 5
  ): Promise<BaseMessage[]> {
    const fullHistory = await this.getConversationHistory(threadId);

    // Keep only the last N messages, excluding the current user message
    if (fullHistory.length <= maxMessages) {
      return fullHistory;
    }

    // Take the last maxMessages, but prioritize keeping system messages and user/assistant pairs
    const truncated = fullHistory.slice(-maxMessages);

    if (this.config.enableDebug) {
      this.logger.info(
        `[ManualSupervisor] Truncated conversation history from ${fullHistory.length} to ${truncated.length} messages for thread ${threadId}`
      );
    }

    return truncated;
  }

  /**
   * Clear conversation history for a thread
   */
  async clearConversationHistory(threadId: string): Promise<void> {
    await this.ensureInitialized();

    if (!this.workflow) {
      throw new Error('Workflow not initialized');
    }

    try {
      // Remove from active threads
      this.activeThreads.delete(threadId);

      // Clear from checkpointer if persistence is enabled
      if (this.config.enablePersistence && this.checkpointer) {
        if (this.config.enableDebug) {
          this.logger.info(
            `[ManualSupervisor] Cleared conversation history for thread ${threadId}`
          );
        }
      }
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[ManualSupervisor] Failed to clear conversation history for thread ${threadId}`
        );
      }
    }
  }

  /**
   * Enforce conversation length limits
   */
  private async enforceConversationLimits(threadId: string): Promise<void> {
    await this.ensureInitialized();

    try {
      if (!this.config.enablePersistence || !this.workflow) {
        return;
      }

      const state = await this.workflow.getState({ configurable: { thread_id: threadId } });
      const messages = state.values.messages || [];

      if (messages.length > this.config.maxConversationLength) {
        if (this.config.enableDebug) {
          this.logger.info(
            `[ManualSupervisor] Conversation length (${messages.length}) exceeds limit (${this.config.maxConversationLength}) for thread ${threadId}`
          );
        }
        // In a production system, you might truncate old messages or archive them
      }
    } catch (error) {
      if (this.config.enableDebug) {
        this.logger.error(
          error,
          `[ManualSupervisor] Failed to enforce conversation limits for thread ${threadId}`
        );
      }
    }
  }

  /**
   * Get active thread count
   */
  getActiveThreadCount(): number {
    return this.activeThreads.size;
  }

  /**
   * Get all active thread IDs
   */
  getActiveThreadIds(): string[] {
    return Array.from(this.activeThreads);
  }

  /**
   * Check if a thread is active
   */
  isThreadActive(threadId: string): boolean {
    return this.activeThreads.has(threadId);
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    activeThreads: number;
    checkpointerType: string;
    storeType: string;
    persistenceEnabled: boolean;
  } {
    return {
      activeThreads: this.activeThreads.size,
      checkpointerType: this.checkpointer.constructor.name,
      storeType: this.store.constructor.name,
      persistenceEnabled: this.config.enablePersistence,
    };
  }

  /**
   * Extract data from tool messages in the conversation
   */
  private extractDataFromMessages(messages: BaseMessage[]): {
    documents: any[];
    searchResults: any[];
    analysis: any | null;
  } {
    const data = {
      documents: [] as any[],
      searchResults: [] as any[],
      analysis: null as any,
    };

    try {
      for (const message of messages) {
        if (message.constructor.name === 'AIMessage') {
          const aiMessage = message as any;
          // Look for agent responses that mention data
          const content = aiMessage.content?.toString() || '';

          // Extract documents from search results that list specific documents
          const documentListMatches = content.match(
            /\d+\.\s+\*\*([^*]+)\*\*:[^[]*\[Relevance Score: ([\d.]+)\]/gi
          );
          if (documentListMatches && documentListMatches.length > 0) {
            data.documents = documentListMatches.map((match: string, i: number) => {
              const titleMatch = match.match(/\*\*([^*]+)\*\*/);
              const scoreMatch = match.match(/\[Relevance Score: ([\d.]+)\]/);
              return {
                id: `doc_${i}`,
                title: titleMatch?.[1] || `Document ${i + 1}`,
                relevanceScore: scoreMatch ? parseFloat(scoreMatch[1]) : 0,
                source: 'search_results',
                placeholder: false,
              };
            });

            // Also count these as search results
            data.searchResults = [...data.documents];
          }

          // Fallback: count numbered items (1., 2., 3.) in search responses
          if (data.documents.length === 0) {
            const numberedItems = content.match(/^\d+\.\s+/gm);
            if (numberedItems && numberedItems.length > 0) {
              const count = numberedItems.length;
              data.documents = Array(count)
                .fill(null)
                .map((_, i) => ({ id: `doc_${i}`, title: `Document ${i + 1}`, placeholder: true }));
              data.searchResults = [...data.documents];
            }
          }

          // Look for analysis completion indicators
          if (content.match(/analysis.*completed/i) || content.match(/generated.*insights/i)) {
            data.analysis = {
              completed: true,
              extractedFrom: 'message_content',
              placeholder: true,
            };
          }
        }
      }
    } catch (error) {
      this.logger.error(error, '[ManualSupervisor] Error extracting data from messages');
    }

    return data;
  }

  /**
   * Format the final response from the workflow result.
   * This method reuses the same logic as the idiomatic supervisor.
   */
  private _formatResponse(result: any, _workflowId: string, startTime: number): WorkflowResult {
    // Retrieve the persisted workflow state
    const persistedState = result;

    // DEBUG: Log the exact structure of persistedState
    if (this.config.enableDebug) {
      this.logger.debug(
        `[ManualSupervisor] persistedState keys: ${Object.keys(persistedState || {})}`
      );
      if (persistedState?.values) {
        this.logger.debug(
          `[ManualSupervisor] persistedState.values keys: ${Object.keys(persistedState.values)}`
        );
      }
    }

    // Extract state values
    const stateValues: any = (persistedState && persistedState.values) || persistedState || {};

    // Extract and enhance state data from agent messages
    const extractedData = this.extractDataFromMessages(stateValues.messages || []);

    // Merge extracted data into stateValues for accurate metrics
    const enhancedStateValues = {
      ...stateValues,
      documents:
        extractedData.documents.length > 0 ? extractedData.documents : stateValues.documents || [],
      searchResults:
        extractedData.searchResults.length > 0
          ? extractedData.searchResults
          : stateValues.searchResults || [],
      analysis: extractedData.analysis || stateValues.analysis,
    };

    // DEBUG: Log the final stateValues we're using for metrics
    if (this.config.enableDebug) {
      this.logger.debug(
        `[ManualSupervisor] Final enhancedStateValues.documents: ${JSON.stringify(enhancedStateValues.documents, null, 2)}`
      );
      this.logger.debug(
        `[ManualSupervisor] Final enhancedStateValues.searchResults: ${JSON.stringify(enhancedStateValues.searchResults, null, 2)}`
      );
      this.logger.debug(
        `[ManualSupervisor] Final enhancedStateValues.analysis: ${JSON.stringify(enhancedStateValues.analysis, null, 2)}`
      );
    }

    // Extract textual AI response from the response_expert (chat agent), not supervisor messages
    let responseText = 'Response generated successfully.';
    const messagesArr: any[] = stateValues.messages || [];

    // Look for the response from response_expert first (the actual chat response)
    for (let i = messagesArr.length - 1; i >= 0; i--) {
      const msg = messagesArr[i];
      if (msg instanceof AIMessage && msg.content) {
        // Preserve content formatting by carefully extracting the response
        let content: string;
        if (typeof msg.content === 'string') {
          content = msg.content;
        } else if (msg.content && typeof msg.content === 'object') {
          // Handle LangChain response objects that might have nested content
          const contentObj = msg.content as any;
          if (contentObj.text && typeof contentObj.text === 'string') {
            content = contentObj.text;
          } else if (contentObj.content && typeof contentObj.content === 'string') {
            content = contentObj.content;
          } else if (contentObj.message && typeof contentObj.message === 'string') {
            content = contentObj.message;
          } else {
            // For other objects, try to preserve structure with proper formatting
            try {
              content = JSON.stringify(contentObj, null, 2);
            } catch (error) {
              this.logger.warn('Failed to stringify message content, using fallback:', {
                error: error instanceof Error ? error.message : String(error),
              });
              content = String(contentObj);
            }
          }
        } else {
          // Fallback for other types
          content = String(msg.content || '');
        }

        if (this.config.enableDebug) {
          this.logger.debug(
            `[ManualSupervisor] Processing AI message ${i}: ${content.substring(0, 100)}...`
          );
        }

        // Skip supervisor completion messages
        if (
          content.includes('workflow is complete') ||
          content.includes('task is complete') ||
          content.includes('workflow complete') ||
          content.toLowerCase().includes('if you have any further questions')
        ) {
          if (this.config.enableDebug) {
            this.logger.debug(`[ManualSupervisor] Skipping supervisor completion message`);
          }
          continue;
        }
        // This should be the actual response from the response_expert
        responseText = content;
        if (this.config.enableDebug) {
          this.logger.debug(
            `[ManualSupervisor] Selected response: ${content.substring(0, 100)}...`
          );
        }
        break;
      }
    }

    const output: StandardAgentOutput = {
      response: responseText,
      metadata: {
        session_id: (stateValues as any).sessionId || undefined,
        context_doc_ids: (enhancedStateValues.documents || []).map((d: any) => d.id),
        conversation_entries: messagesArr.length,
        semantic_search_results: enhancedStateValues.searchResults,
        semantic_search_synced: false,
        semantic_search_average_score: undefined,
      },
      metrics: {
        execution_time_ms: Date.now() - startTime,
        rows: (enhancedStateValues.documents || []).length,
        tool_used: 'manual_langgraph_workflow',
      },
    };

    return {
      output,
      executionTime: output.metrics.execution_time_ms || 0,
      toolsUsed: this.extractToolsUsed(stateValues.messages || []),
      success: true,
    } as WorkflowResult;
  }
}

// Export for backward compatibility
export class LangGraphSupervisor extends ManualLangGraphSupervisor {
  constructor(connectionManager: MCPConnectionManager, config: ManualSupervisorConfig = {}) {
    super(connectionManager, config);
  }
}

// Legacy interface for backward compatibility
export interface EnhancedSupervisorConfig extends ManualSupervisorConfig {}
