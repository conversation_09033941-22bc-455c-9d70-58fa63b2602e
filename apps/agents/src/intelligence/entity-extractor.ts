/**
 * Entity extractor for identifying key entities in user queries
 * Supports both rule-based and pattern-based entity extraction
 */

import { Entity, EntityType } from './types';

interface EntityPattern {
  type: EntityType;
  patterns: RegExp[];
  confidence: number;
}

/**
 * Configuration options for {@link EntityExtractor}.
 *
 * All properties are optional; sensible defaults are applied when omitted.
 */
interface EntityExtractionConfig {
  enablePatternMatching: boolean;
  enableKeywordExtraction: boolean;
  enableMlModel: boolean;
  confidenceThreshold: number;
  maxEntities: number;
}

/**
 * `EntityExtractor` identifies domain-specific entities such as vulnerabilities,
 * technologies, organizations, dates, and more from free-form user input.
 * It supports a hybrid strategy consisting of:
 *
 * • **Pattern matching** using configurable regular expressions.
 * • **Keyword lookup** via an internal security-focused dictionary.
 * • **Date/time extraction** leveraging heuristics.
 * • **ML model fallback** (simulated) for additional recall.
 *
 * The extractor returns an array of normalised `Entity` objects sorted by
 * descending confidence.
 */
export class EntityExtractor {
  private patterns: EntityPattern[] = [];
  private securityKeywords: Map<string, EntityType> = new Map();
  private config: EntityExtractionConfig;

  /**
   * Creates a new `EntityExtractor`.
   *
   * @param config - Partial configuration overriding the default behaviour.
   *
   * @example
   * ```ts
   * const extractor = new EntityExtractor({ enableMlModel: true });
   * const entities = await extractor.extract('Show me CVE-2023-12345 in Windows');
   * ```
   */
  constructor(config?: Partial<EntityExtractionConfig>) {
    this.config = {
      enablePatternMatching: true,
      enableKeywordExtraction: true,
      enableMlModel: false,
      confidenceThreshold: 0.6,
      maxEntities: 10,
      ...config,
    };

    this.initializePatterns();
    this.initializeSecurityKeywords();
  }

  /**
   * Extracts entities from the supplied `input` string.
   *
   * The pipeline executes pattern matching, keyword lookup, date extraction, and
   * an (optional) ML step before de-duplicating and filtering entities.
   *
   * @param input - Raw user query.
   * @returns Promise resolving to an array of extracted {@link Entity} objects.
   * @throws Error Sub-calls may bubble up unexpected exceptions which are caught
   *         and logged; the method will then resolve to `[]` instead of throwing.
   */
  async extract(input: string): Promise<Entity[]> {
    const entities: Entity[] = [];
    const normalizedInput = input.toLowerCase();

    try {
      // Extract entities using patterns
      if (this.config.enablePatternMatching) {
        const patternEntities = this.extractWithPatterns(input, normalizedInput);
        entities.push(...patternEntities);
      }

      // Extract entities using security keyword matching
      if (this.config.enableKeywordExtraction) {
        const keywordEntities = this.extractWithKeywords(input, normalizedInput);
        entities.push(...keywordEntities);
      }

      // Extract date ranges and time references
      const dateEntities = this.extractDateReferences(input);
      entities.push(...dateEntities);

      // Extract entities using ML model if enabled
      if (this.config.enableMlModel) {
        const mlEntities = await this.extractWithML(input);
        entities.push(...mlEntities);
      }

      // Remove duplicates and apply confidence threshold
      const filteredEntities = this.filterAndDeduplicateEntities(entities);

      // Sort by confidence and limit results
      return filteredEntities
        .filter(entity => entity.confidence >= this.config.confidenceThreshold)
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, this.config.maxEntities);
    } catch (error) {
      console.error('Entity extraction failed:', error);
      return [];
    }
  }

  /**
   * Populates the `patterns` array with curated regexes for common security
   * entities (CVE, technologies, vendors, …).
   *
   * @private This helper mutates internal state during construction.
   */
  private initializePatterns(): void {
    this.patterns = [
      // Vulnerability identifiers (CVE, CWE)
      {
        type: 'vulnerability',
        patterns: [/CVE-\d{4}-\d{4,}/gi, /CWE-\d+/gi, /CVSS[:\s]+[\d.]+/gi],
        confidence: 0.95,
      },

      // Technology and tools
      {
        type: 'technology',
        patterns: [
          /\b(Windows|Linux|macOS|Ubuntu|CentOS|RHEL)\b/gi,
          /\b(Apache|Nginx|IIS|Tomcat)\b/gi,
          /\b(MySQL|PostgreSQL|Oracle|MongoDB|Redis)\b/gi,
          /\b(AWS|Azure|GCP|Docker|Kubernetes)\b/gi,
          /\b(Splunk|ELK|SIEM|IDS|IPS|WAF)\b/gi,
        ],
        confidence: 0.85,
      },

      // Organizations and vendors
      {
        type: 'organization',
        patterns: [
          /\b(Microsoft|Google|Apple|Amazon|Facebook|Meta)\b/gi,
          /\b(Cisco|IBM|Oracle|VMware|RedHat)\b/gi,
          /\b(NIST|SANS|OWASP|MITRE|CISA)\b/gi,
        ],
        confidence: 0.8,
      },

      // Document types
      {
        type: 'document_type',
        patterns: [
          /\b(policy|procedure|guideline|standard|framework)\b/gi,
          /\b(report|analysis|assessment|audit|review)\b/gi,
          /\b(manual|documentation|whitepaper|advisory)\b/gi,
        ],
        confidence: 0.75,
      },

      // Threat-related terms
      {
        type: 'threat',
        patterns: [
          /\b(malware|ransomware|phishing|spear.?phishing)\b/gi,
          /\b(APT|advanced.?persistent.?threat)\b/gi,
          /\b(DDoS|denial.?of.?service)\b/gi,
          /\b(social.?engineering|insider.?threat)\b/gi,
        ],
        confidence: 0.9,
      },
    ];
  }

  /**
   * Builds the `securityKeywords` dictionary mapping lowercase keywords to
   * entity types.
   *
   * @private Mutates internal `Map` for fast lookups.
   */
  private initializeSecurityKeywords(): void {
    this.securityKeywords = new Map([
      // Security controls
      ['firewall', 'technology'],
      ['antivirus', 'technology'],
      ['encryption', 'technology'],
      ['authentication', 'technology'],
      ['authorization', 'technology'],
      ['multi-factor', 'technology'],
      ['two-factor', 'technology'],

      // Security concepts
      ['confidentiality', 'topic'],
      ['integrity', 'topic'],
      ['availability', 'topic'],
      ['non-repudiation', 'topic'],
      ['zero-trust', 'topic'],
      ['defense-in-depth', 'topic'],

      // Compliance frameworks
      ['iso27001', 'topic'],
      ['sox', 'topic'],
      ['hipaa', 'topic'],
      ['gdpr', 'topic'],
      ['pci-dss', 'topic'],
      ['nist', 'organization'],

      // Security roles
      ['ciso', 'person'],
      ['security-analyst', 'person'],
      ['incident-responder', 'person'],
      ['penetration-tester', 'person'],

      // Attack types
      ['sql-injection', 'threat'],
      ['xss', 'threat'],
      ['csrf', 'threat'],
      ['buffer-overflow', 'threat'],
      ['privilege-escalation', 'threat'],
    ]);
  }

  /**
   * Pattern-based extraction stage.
   *
   * @param input - Original (case-preserving) user input.
   * @param _normalizedInput - Lower-cased variant (unused here but kept for API symmetry).
   * @returns Array of `Entity` instances created from regex matches.
   */
  private extractWithPatterns(input: string, _normalizedInput: string): Entity[] {
    const entities: Entity[] = [];

    for (const pattern of this.patterns) {
      for (const regex of pattern.patterns) {
        const matches = input.match(regex);
        if (matches) {
          for (const match of matches) {
            entities.push({
              type: pattern.type,
              value: match.trim(),
              confidence: pattern.confidence,
              metadata: {
                extractionMethod: 'pattern',
                originalText: match,
              },
            });
          }
        }
      }
    }

    return entities;
  }

  /**
   * Keyword-based extraction stage leveraging the internal dictionary.
   *
   * @param _input - Original user input (unused).
   * @param normalizedInput - Lower-cased user input.
   * @returns Array of keyword-derived `Entity` objects.
   */
  private extractWithKeywords(_input: string, normalizedInput: string): Entity[] {
    const entities: Entity[] = [];
    const words = normalizedInput.split(/\s+/);

    for (const word of words) {
      const cleanWord = word.replace(/[^\w-]/g, '');
      if (this.securityKeywords.has(cleanWord)) {
        const entityType = this.securityKeywords.get(cleanWord)!;
        entities.push({
          type: entityType,
          value: cleanWord,
          confidence: 0.7,
          metadata: {
            extractionMethod: 'keyword',
            originalText: word,
          },
        });
      }
    }

    return entities;
  }

  /**
   * Identifies absolute and relative date references.
   *
   * @param input - User query string.
   * @returns Array of date-related entities.
   */
  private extractDateReferences(input: string): Entity[] {
    const entities: Entity[] = [];
    const datePatterns = [
      // Absolute dates
      /\b\d{1,2}\/\d{1,2}\/\d{2,4}\b/g,
      /\b\d{4}-\d{1,2}-\d{1,2}\b/g,

      // Relative dates
      /\b(today|yesterday|tomorrow)\b/gi,
      /\b(last|past|previous)\s+(week|month|quarter|year)\b/gi,
      /\b(next|coming)\s+(week|month|quarter|year)\b/gi,
      /\b\d+\s+(days?|weeks?|months?|years?)\s+ago\b/gi,
    ];

    for (const pattern of datePatterns) {
      const matches = input.match(pattern);
      if (matches) {
        for (const match of matches) {
          entities.push({
            type: 'date_range',
            value: match.trim(),
            confidence: 0.85,
            metadata: {
              extractionMethod: 'date_pattern',
              originalText: match,
            },
          });
        }
      }
    }

    return entities;
  }

  /**
   * Removes duplicate entities keeping the highest-confidence occurrence of each
   * unique `(type, value)` pair.
   *
   * @param entities - Aggregate list of entities before deduplication.
   * @returns Deduplicated list of entities.
   */
  private filterAndDeduplicateEntities(entities: Entity[]): Entity[] {
    const entityMap = new Map<string, Entity>();

    for (const entity of entities) {
      const key = `${entity.type}:${entity.value.toLowerCase()}`;

      if (!entityMap.has(key) || entityMap.get(key)!.confidence < entity.confidence) {
        entityMap.set(key, entity);
      }
    }

    return Array.from(entityMap.values());
  }

  /**
   * Computes aggregate statistics useful for debugging or analytics.
   *
   * @param entities - Entities previously returned by {@link extract}.
   * @returns Object containing total count, average confidence, distribution, etc.
   */
  getExtractionStats(entities: Entity[]): Record<string, any> {
    const typeCount = new Map<EntityType, number>();
    let totalConfidence = 0;

    for (const entity of entities) {
      typeCount.set(entity.type, (typeCount.get(entity.type) || 0) + 1);
      totalConfidence += entity.confidence;
    }

    return {
      totalEntities: entities.length,
      averageConfidence: entities.length > 0 ? totalConfidence / entities.length : 0,
      typeDistribution: Object.fromEntries(typeCount),
      highConfidenceEntities: entities.filter(e => e.confidence >= 0.9).length,
    };
  }

  /**
   * Simulated ML-based extraction stage (placeholder).
   *
   * @param input - User input string.
   * @returns Promise producing additional entities inferred by the *simulated* model.
   */
  private async extractWithML(input: string): Promise<Entity[]> {
    // For demonstration, if input length > 20 we treat it as containing a "topic" entity.
    if (input.length < 20) return [];

    const topic: Entity = {
      type: 'topic',
      value: input.split(' ').slice(0, 3).join(' '),
      confidence: 0.7,
      metadata: { extractionMethod: 'ml_simulated' },
    };

    return [topic];
  }
}
