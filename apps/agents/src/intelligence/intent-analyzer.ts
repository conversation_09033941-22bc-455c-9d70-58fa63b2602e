/**
 * Intent analyzer for classifying user queries and determining appropriate workflows
 * Uses rule-based classification with support for future LLM integration
 */

import { Intent, IntentType, QueryComplexity, Context, IntentAnalysisResult } from './types';
import { EntityExtractor } from './entity-extractor';

interface IntentRule {
  type: IntentType;
  patterns: RegExp[];
  keywords: string[];
  weight: number;
  complexity: QueryComplexity;
}

interface IntentAnalysisConfig {
  enableEntityAnalysis: boolean;
  enableContextualAnalysis: boolean;
  confidenceThreshold: number;
  maxAlternatives: number;
  debugMode: boolean;
}

/**
 * `IntentAnalyzer` classifies a natural-language query into a predefined
 * `IntentType` and enriches the result with confidence scores, alternative
 * intents, and optional debug information.
 *
 * The analyser relies on:
 *
 * • **Rule-based pattern matching** – regex & keyword based scoring.
 * • **Entity correlation** – semantic boost using entities provided by
 *   {@link EntityExtractor}.
 * • **Contextual signals** – recent conversation history & user preferences.
 *
 * All thresholds and behavioural toggles can be customised through the
 * constructor.
 */
export class IntentAnalyzer {
  private entityExtractor: EntityExtractor;
  private intentRules: IntentRule[] = [];
  private config: IntentAnalysisConfig;

  /**
   * Creates a new `IntentAnalyzer`.
   *
   * @param config - Optional partial configuration overriding defaults.
   *
   * @example
   * ```ts
   * const analyzer = new IntentAnalyzer({ debugMode: true });
   * const result = await analyzer.analyze('Compare ISO27001 and NIST frameworks');
   * console.log(result.intent.type); // => 'compare'
   * ```
   */
  constructor(config?: Partial<IntentAnalysisConfig>) {
    this.config = {
      enableEntityAnalysis: true,
      enableContextualAnalysis: true,
      confidenceThreshold: 0.3, // Lower threshold for better matching
      maxAlternatives: 3,
      debugMode: process.env.NODE_ENV === 'development',
      ...config,
    };

    this.entityExtractor = new EntityExtractor();
    this.initializeIntentRules();
  }

  /**
   * Performs intent analysis for a single user query.
   *
   * @param input - Raw user input.
   * @param context - _(optional)_ Conversation context used for confidence
   *                  adjustments.
   * @returns An {@link IntentAnalysisResult} with primary intent, alternatives,
   *          confidence, and processing time.
   * @throws Never – internal errors are caught and logged, a low-confidence
   *         fallback intent is returned instead.
   */
  async analyze(input: string, context?: Context): Promise<IntentAnalysisResult> {
    const startTime = Date.now();

    try {
      // Extract entities if enabled
      const entities = this.config.enableEntityAnalysis
        ? await this.entityExtractor.extract(input)
        : [];

      // Classify intent using rules
      const intentCandidates = this.classifyIntent(input, entities);

      // Apply contextual analysis if enabled
      if (this.config.enableContextualAnalysis && context) {
        this.applyContextualAnalysis(intentCandidates, context);
      }

      // Sort by confidence and select best intent
      intentCandidates.sort((a, b) => b.confidence - a.confidence);

      const primaryIntent = intentCandidates[0] || this.createUnknownIntent(entities);
      const alternatives = intentCandidates.slice(1, this.config.maxAlternatives + 1);

      const processingTime = Date.now() - startTime;

      const result: IntentAnalysisResult = {
        intent: primaryIntent,
        confidence: primaryIntent.confidence,
        alternatives,
        processingTime,
      };

      if (this.config.debugMode) {
        result.debugInfo = {
          rawClassification: intentCandidates,
          entityExtractionDetails: this.entityExtractor.getExtractionStats(entities),
          contextFactors: context ? this.analyzeContextFactors(context) : null,
        };
      }

      return result;
    } catch (error) {
      console.error('Intent analysis failed:', error);

      return {
        intent: this.createUnknownIntent([]),
        confidence: 0.1,
        alternatives: [],
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Populates the internal rule set used for classification.
   *
   * @private Called once during construction.
   */
  private initializeIntentRules(): void {
    this.intentRules = [
      // Simple retrieval intents
      {
        type: 'retrieve',
        patterns: [
          /\b(get|show|display|list|find)\b.*\b(document|file|report)\b/i,
          /\b(what|where)\s+is\b/i,
          /\bshow\s+me\b/i,
        ],
        keywords: ['get', 'show', 'display', 'list', 'document', 'file', 'report'],
        weight: 1.0,
        complexity: 'simple',
      },

      // Search intents
      {
        type: 'search',
        patterns: [/\b(search|find|look\s+for|locate)\b/i, /\bhow\s+to\s+(find|search|locate)\b/i],
        keywords: ['search', 'find', 'look', 'locate'],
        weight: 1.0,
        complexity: 'simple',
      },

      // Analysis intents
      {
        type: 'analyze',
        patterns: [
          /\b(analyze|analysis|examine|review|assess)\b/i,
          /\b(what\s+does|how\s+does).*\b(work|function|operate)\b/i,
          /\btell\s+me\s+about\b/i,
        ],
        keywords: ['analyze', 'analysis', 'examine', 'review', 'assess'],
        weight: 1.2,
        complexity: 'moderate',
      },

      // Query intents
      {
        type: 'query',
        patterns: [
          /\b(how\s+many|count|number\s+of)\b/i,
          /\b(when|where|who|which)\b.*\b(was|were|is|are)\b/i,
          /\bdata\s+about\b/i,
        ],
        keywords: ['count', 'number', 'how many', 'when', 'where', 'who', 'which', 'data'],
        weight: 1.1,
        complexity: 'moderate',
      },

      // Summarization intents
      {
        type: 'summarize',
        patterns: [
          /\b(summarize|summary|overview|brief)\b/i,
          /\b(key\s+points|main\s+points)\b/i,
          /\bgive\s+me\s+a\s+(summary|overview)\b/i,
        ],
        keywords: ['summarize', 'summary', 'overview', 'brief', 'key points'],
        weight: 1.1,
        complexity: 'moderate',
      },

      // Comparison intents
      {
        type: 'compare',
        patterns: [
          /\b(compare|comparison|versus|vs|difference|differences)\b/i,
          /\b(better|worse|best|worst)\b/i,
          /\bwhich\s+is\s+(better|worse|best|more|less)\b/i,
        ],
        keywords: ['compare', 'comparison', 'versus', 'vs', 'difference', 'better', 'worse'],
        weight: 1.3,
        complexity: 'complex',
      },

      // Explanation intents
      {
        type: 'explain',
        patterns: [
          /\b(explain|explanation|why|how)\b/i,
          /\bwhat\s+is\b/i,
          /\bcan\s+you\s+(explain|tell)\b/i,
        ],
        keywords: ['explain', 'explanation', 'why', 'how', 'what is'],
        weight: 1.0,
        complexity: 'moderate',
      },

      // Troubleshooting intents
      {
        type: 'troubleshoot',
        patterns: [
          /\b(problem|issue|error|trouble|fix|solve)\b/i,
          /\b(not\s+working|broken|failing)\b/i,
          /\bhow\s+to\s+(fix|solve|resolve)\b/i,
        ],
        keywords: ['problem', 'issue', 'error', 'trouble', 'fix', 'solve', 'broken'],
        weight: 1.2,
        complexity: 'complex',
      },

      // Recommendation intents
      {
        type: 'recommend',
        patterns: [
          /\b(recommend|recommendation|suggest|advice)\b/i,
          /\b(should\s+I|what\s+should)\b/i,
          /\bbest\s+(practice|approach|way)\b/i,
        ],
        keywords: ['recommend', 'suggest', 'advice', 'should', 'best practice'],
        weight: 1.2,
        complexity: 'complex',
      },

      // Complex multi-step intents
      {
        type: 'complex',
        patterns: [
          /\b(and\s+then|after\s+that|next\s+step)\b/i,
          /\b(first|second|third|finally)\b/i,
          /\b(step\s+by\s+step|procedure|process)\b/i,
        ],
        keywords: ['step by step', 'procedure', 'process', 'first', 'then', 'finally'],
        weight: 1.5,
        complexity: 'multi_step',
      },
    ];
  }

  /**
   * Scores the input against each rule and returns a list of candidate intents.
   *
   * @param input - Raw user text.
   * @param entities - Entities extracted for additional signal.
   * @returns Array of candidate intents with preliminary confidence scores.
   */
  private classifyIntent(input: string, entities: any[]): Intent[] {
    const normalizedInput = input.toLowerCase();
    const intents: Intent[] = [];

    for (const rule of this.intentRules) {
      let score = 0;
      let matchCount = 0;

      // Check pattern matches
      for (const pattern of rule.patterns) {
        if (pattern.test(input)) {
          score += rule.weight * 0.4;
          matchCount++;
        }
      }

      // Check keyword matches
      for (const keyword of rule.keywords) {
        if (normalizedInput.includes(keyword.toLowerCase())) {
          score += rule.weight * 0.3;
          matchCount++;
        }
      }

      // Entity-based scoring boost
      const relevantEntities = this.getRelevantEntities(entities, rule.type);
      if (relevantEntities.length > 0) {
        score += relevantEntities.length * 0.2;
        matchCount++;
      }

      // Apply complexity boost for sophisticated queries
      if (this.isComplexQuery(input)) {
        if (rule.complexity === 'complex' || rule.complexity === 'multi_step') {
          score += 0.3;
        }
      }

      // Create intent if we have matches
      if (score > 0) {
        // Improve confidence calculation - normalize by available matches rather than total possible
        const maxPossibleScore = rule.weight * (matchCount > 0 ? 1.0 : 0.1);
        const confidence = Math.min(score / Math.max(maxPossibleScore, 0.1), 1.0);

        if (confidence >= this.config.confidenceThreshold) {
          intents.push({
            type: rule.type,
            entities: relevantEntities,
            confidence,
            complexity: rule.complexity,
            domain: this.determineDomain(entities),
          });
        }
      }
    }

    return intents;
  }

  /**
   * Tweaks intent confidence using conversational context signals.
   *
   * @param intents - Candidate intents to be modified in place.
   * @param context - Conversation context.
   */
  private applyContextualAnalysis(intents: Intent[], context: Context): void {
    if (!context.conversationHistory || context.conversationHistory.length === 0) {
      return;
    }

    // Boost confidence for similar recent intents
    const recentIntents = context.conversationHistory
      .slice(-3)
      .map(entry => entry.intent?.type)
      .filter(Boolean);

    for (const intent of intents) {
      if (recentIntents.includes(intent.type)) {
        intent.confidence = Math.min(intent.confidence * 1.1, 1.0);
      }
    }

    // Apply user preferences
    if (context.userPreferences?.preferredTools) {
      for (const _intent of intents) {
        // This would need tool-to-intent mapping
        // For now, just a placeholder for future enhancement
      }
    }
  }

  /**
   * Filters `entities` for those relevant to the `intentType`.
   *
   * @param entities - All entities extracted from the query.
   * @param intentType - Target intent category.
   * @returns Subset of entities influencing the score for the intent.
   */
  private getRelevantEntities(entities: any[], intentType: IntentType): any[] {
    const relevantTypes: Record<IntentType, string[]> = {
      retrieve: ['document_type', 'keyword', 'topic'],
      search: ['keyword', 'topic', 'technology', 'person'],
      analyze: ['technology', 'vulnerability', 'threat', 'document_type'],
      query: ['date_range', 'person', 'organization', 'technology'],
      summarize: ['document_type', 'topic', 'keyword'],
      compare: ['technology', 'vulnerability', 'organization'],
      explain: ['technology', 'threat', 'vulnerability', 'topic'],
      troubleshoot: ['technology', 'threat', 'vulnerability'],
      recommend: ['technology', 'topic'],
      complex: ['keyword', 'topic', 'technology'],
      unknown: [],
    };

    const relevant = relevantTypes[intentType] || [];
    return entities.filter(entity => relevant.includes(entity.type));
  }

  /**
   * Heuristic check to detect compound or multi-clause questions.
   *
   * @param input - User query.
   * @returns `true` if the query is deemed complex.
   */
  private isComplexQuery(input: string): boolean {
    const complexityMarkers = [
      /\b(and|or|but|however|although|because|since)\b/i,
      /\b(compare|contrast|analyze|evaluate)\b/i,
      /\b(step\s+by\s+step|procedure|process)\b/i,
      /\?.*\?/, // Multiple questions
      /[.!]\s*[A-Z]/, // Multiple sentences
    ];

    return complexityMarkers.some(marker => marker.test(input));
  }

  /**
   * Determines the primary domain (e.g. *security*, *business*) based on entity mix.
   *
   * @param entities - Entities extracted from the query.
   * @returns Most frequent domain or `undefined` if none identified.
   */
  private determineDomain(entities: any[]): string | undefined {
    const domainMapping: Record<string, string> = {
      vulnerability: 'security',
      threat: 'security',
      technology: 'infrastructure',
      organization: 'business',
      person: 'identity',
    };

    const domains = entities.map(entity => domainMapping[entity.type]).filter(Boolean);

    // Return most common domain
    const domainCount = domains.reduce(
      (acc, domain) => {
        acc[domain] = (acc[domain] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return Object.keys(domainCount).sort((a, b) => domainCount[b] - domainCount[a])[0];
  }

  /**
   * Generates a fallback *unknown* intent to ensure the analyser always returns
   * a valid result.
   *
   * @param entities - Entities to attach to the unknown intent.
   * @returns A low-confidence `unknown` intent.
   */
  private createUnknownIntent(entities: any[]): Intent {
    return {
      type: 'unknown',
      entities,
      confidence: 0.1,
      complexity: 'simple',
    };
  }

  /**
   * Extracts high-level context factors for debug purposes.
   *
   * @param context - Conversation context.
   * @returns Key metrics used in debug output.
   */
  private analyzeContextFactors(context: Context): Record<string, any> {
    return {
      hasConversationHistory:
        !!context.conversationHistory && context.conversationHistory.length > 0,
      recentIntentTypes:
        context.conversationHistory
          ?.slice(-3)
          .map(entry => entry.intent?.type)
          .filter(Boolean) || [],
      hasUserPreferences: !!context.userPreferences,
      hasPreviousResults: !!context.previousResults,
    };
  }
}
