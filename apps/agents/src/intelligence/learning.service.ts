export interface WorkflowStats {
  success: number;
  failure: number;
}

/**
 * In-memory service that tracks execution outcomes for named workflows and can
 * recommend the most successful one.
 *
 * The implementation is intentionally lightweight and *not* persisted across
 * process restarts; it serves as a placeholder for future ML-based learning.
 */
export class LearningService {
  private stats: Map<string, WorkflowStats> = new Map();

  /**
   * Records the outcome of a workflow execution.
   *
   * @param workflowType - Identifier of the workflow (e.g. `rag_pipeline`).
   * @param success - `true` if the execution completed successfully.
   */
  recordOutcome(workflowType: string, success: boolean): void {
    const entry = this.stats.get(workflowType) || { success: 0, failure: 0 };
    if (success) {
      entry.success += 1;
    } else {
      entry.failure += 1;
    }
    this.stats.set(workflowType, entry);
  }

  /**
   * Recommends the workflow with the highest success rate among those executed
   * at least **3** times.
   *
   * @returns The workflow identifier or `undefined` if no workflow meets the
   *          minimum sample size.
   */
  recommendWorkflow(): string | undefined {
    let best: string | undefined;
    let bestRate = 0;

    for (const [workflow, stat] of this.stats.entries()) {
      const total = stat.success + stat.failure;
      if (total < 3) continue; // Require minimum executions for confidence
      const rate = stat.success / total;
      if (rate > bestRate) {
        bestRate = rate;
        best = workflow;
      }
    }

    return best;
  }

  /**
   * Exposes raw success/failure counters for observability and debugging.
   *
   * @returns An object keyed by workflow type containing success/failure stats.
   */
  getStats(): Record<string, WorkflowStats> {
    return Object.fromEntries(this.stats);
  }
}
