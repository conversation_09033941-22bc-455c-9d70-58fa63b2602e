/**
 * Tool selector for dynamic tool selection based on intent analysis
 * Integrates with existing MCP tools package and optimizes execution plans
 */

import { Intent, ToolSelection, ExecutionPlan, ToolSelectionResult, Context } from './types';

interface ToolMapping {
  intentTypes: string[];
  toolName: string;
  priority: number;
  requiredEntities?: string[];
  optionalEntities?: string[];
  dependencies: string[];
  estimatedDuration: number;
  conditions?: (intent: Intent, context?: Context) => boolean;
}

interface ToolSelectionConfig {
  maxTools: number;
  parallelExecutionEnabled: boolean;
  enableOptimization: boolean;
  confidenceThreshold: number;
  debugMode: boolean;
}

/**
 * `ToolSelector` maps a classified {@link Intent} to one or more MCP tools and
 * generates an execution plan optimised for parallelism and dependency order.
 *
 * Configuration flags control optimisation level, parallel execution, maximum
 * number of tools, and debug behaviour.
 */
export class ToolSelector {
  private toolMappings: ToolMapping[];
  private availableTools: Set<string>;
  private config: ToolSelectionConfig;

  /**
   * Constructs a new `ToolSelector`.
   *
   * @param config - Partial configuration overriding defaults.
   *
   * @example
   * ```ts
   * const selector = new ToolSelector({ maxTools: 3 });
   * const selection = await selector.selectTools(intent);
   * ```
   */
  constructor(config?: Partial<ToolSelectionConfig>) {
    this.config = {
      maxTools: 5,
      parallelExecutionEnabled: true,
      enableOptimization: true,
      confidenceThreshold: 0.6,
      debugMode: process.env.NODE_ENV === 'development',
      ...config,
    };

    this.availableTools = new Set();
    this.toolMappings = [];
    this.initializeToolMappings();
    this.discoverAvailableTools();
  }

  /**
   * Main entry point that selects appropriate tools and builds an executable
   * plan for the provided intent.
   *
   * @param intent - Result of intent analysis step.
   * @param context - _(optional)_ Additional context such as organization ID.
   * @returns Structured {@link ToolSelectionResult} containing chosen tools,
   *          execution plan, confidence, reasoning and debug info.
   * @throws Any unexpected error is caught internally; the method returns a
   *         fallback selection instead of throwing.
   */
  async selectTools(intent: Intent, context?: Context): Promise<ToolSelectionResult> {
    try {
      // Find relevant tool mappings
      const relevantMappings = this.findRelevantMappings(intent, context);

      // Score and rank tools
      const scoredTools = this.scoreTools(relevantMappings, intent, context);

      // Filter by confidence threshold
      const viableTools = scoredTools.filter(
        tool => tool.confidence >= this.config.confidenceThreshold
      );

      // Limit number of tools
      const selectedTools = viableTools.slice(0, this.config.maxTools);

      // Create execution plan
      const executionPlan = this.createExecutionPlan(selectedTools);

      // Calculate total estimated time
      const totalEstimatedTime = this.calculateTotalTime(executionPlan);

      // Generate reasoning
      const reasoning = this.generateReasoning(intent, selectedTools, executionPlan);

      const result: ToolSelectionResult = {
        selectedTools,
        executionPlan,
        totalEstimatedTime,
        confidence: this.calculateOverallConfidence(selectedTools),
        reasoning,
      };

      if (this.config.debugMode) {
        result.debugInfo = {
          availableTools: Array.from(this.availableTools),
          selectionCriteria: {
            intentType: intent.type,
            entityCount: intent.entities.length,
            complexity: intent.complexity,
            relevantMappingsCount: relevantMappings.length,
          },
          optimizationSteps: this.getOptimizationSteps(executionPlan),
        };
      }

      return result;
    } catch (error) {
      console.error('Tool selection failed:', error);

      // Fallback to basic tool selection
      return this.createFallbackSelection(intent);
    }
  }

  /**
   * Populates static `toolMappings` with intent-to-tool rules.
   *
   * @private Executed once from the constructor.
   */
  private initializeToolMappings(): void {
    this.toolMappings = [
      // Document retrieval tools
      {
        intentTypes: ['retrieve', 'search', 'analyze', 'summarize'],
        toolName: 'get-all-documents-tool',
        priority: 1,
        dependencies: [],
        estimatedDuration: 2000, // 2 seconds
        conditions: intent => {
          // Always include for document-related queries
          return (
            intent.entities.some(e => e.type === 'document_type') ||
            intent.type === 'retrieve' ||
            intent.entities.length === 0
          ); // Default fallback
        },
      },

      // Database query tools
      {
        intentTypes: ['query', 'analyze', 'search'],
        toolName: 'query-database-tool',
        priority: 2,
        requiredEntities: ['date_range', 'person', 'organization'],
        dependencies: [],
        estimatedDuration: 1500,
        conditions: intent => {
          // Use for structured data queries
          return (
            intent.entities.some(e =>
              ['date_range', 'person', 'organization', 'technology'].includes(e.type)
            ) || intent.type === 'query'
          );
        },
      },

      // Search and filtering tools (placeholder for future tools)
      {
        intentTypes: ['search', 'find'],
        toolName: 'semantic-search-tool',
        priority: 3,
        optionalEntities: ['keyword', 'topic', 'technology'],
        dependencies: [],
        estimatedDuration: 1000,
        conditions: intent => {
          // Use for semantic search when specific keywords present
          return (
            intent.entities.some(e =>
              ['keyword', 'topic', 'technology', 'threat'].includes(e.type)
            ) && intent.type === 'search'
          );
        },
      },

      // Analysis tools (placeholder for future tools)
      {
        intentTypes: ['analyze', 'compare', 'explain'],
        toolName: 'content-analyzer-tool',
        priority: 4,
        requiredEntities: ['technology', 'vulnerability', 'threat'],
        dependencies: ['get-all-documents-tool'],
        estimatedDuration: 3000,
        conditions: intent => {
          // Use for technical analysis
          return (
            intent.entities.some(e => ['technology', 'vulnerability', 'threat'].includes(e.type)) &&
            ['analyze', 'compare', 'explain'].includes(intent.type)
          );
        },
      },

      // Troubleshooting tools (placeholder for future tools)
      {
        intentTypes: ['troubleshoot', 'recommend'],
        toolName: 'troubleshooting-tool',
        priority: 5,
        requiredEntities: ['technology', 'threat'],
        dependencies: ['get-all-documents-tool', 'content-analyzer-tool'],
        estimatedDuration: 4000,
        conditions: intent => {
          // Use for problem-solving queries
          return (
            intent.type === 'troubleshoot' ||
            (intent.type === 'recommend' && intent.entities.some(e => e.type === 'threat'))
          );
        },
      },
    ];
  }

  /**
   * Builds the internal cache of MCP tools available at runtime.
   *
   * @private Side-effect: mutates `availableTools` set.
   */
  private discoverAvailableTools(): void {
    // Add known tools from @anter/mcp-tools package
    const knownTools = [
      'get-all-documents-tool',
      'query-database-tool',
      // Future tools will be discovered dynamically
    ];

    knownTools.forEach(tool => this.availableTools.add(tool));
  }

  /**
   * Filters `toolMappings` relevant to the current intent and environment.
   *
   * @param intent - Classified intent.
   * @param context - Optional execution context.
   * @returns Array of matching tool mappings.
   */
  private findRelevantMappings(intent: Intent, context?: Context): ToolMapping[] {
    return this.toolMappings.filter(mapping => {
      // Check if intent type matches
      if (!mapping.intentTypes.includes(intent.type)) {
        return false;
      }

      // Check if tool is available
      if (!this.availableTools.has(mapping.toolName)) {
        return false;
      }

      // Check required entities
      if (mapping.requiredEntities && mapping.requiredEntities.length > 0) {
        const hasRequiredEntities = mapping.requiredEntities.some(entityType =>
          intent.entities.some(entity => entity.type === entityType)
        );
        if (!hasRequiredEntities) {
          return false;
        }
      }

      // Check conditions
      if (mapping.conditions && !mapping.conditions(intent, context)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Assigns a confidence score to each candidate tool.
   *
   * @param mappings - Tool mappings returned by {@link findRelevantMappings}.
   * @param intent - Current intent.
   * @param context - Optional context.
   * @returns Array of scored tool selections.
   */
  private scoreTools(mappings: ToolMapping[], intent: Intent, context?: Context): ToolSelection[] {
    return mappings.map(mapping => {
      let score = 0;

      // Base score from priority (inverted - lower priority number = higher score)
      score += (10 - mapping.priority) * 0.2;

      // Entity relevance boost
      const relevantEntities = intent.entities.filter(
        entity =>
          mapping.requiredEntities?.includes(entity.type) ||
          mapping.optionalEntities?.includes(entity.type)
      );
      score += relevantEntities.length * 0.3;

      // Intent type exact match boost
      if (mapping.intentTypes.includes(intent.type)) {
        score += 0.4;
      }

      // Complexity appropriateness
      if (intent.complexity === 'complex' || intent.complexity === 'multi_step') {
        // Prefer tools with dependencies for complex queries
        score += mapping.dependencies.length * 0.1;
      } else {
        // Prefer simpler tools for simple queries
        score += mapping.dependencies.length === 0 ? 0.2 : 0;
      }

      // Contextual boost
      if (context?.userPreferences?.preferredTools?.includes(mapping.toolName)) {
        score += 0.3;
      }

      // Normalize confidence (cap at 1.0)
      const confidence = Math.min(score, 1.0);

      return {
        toolName: mapping.toolName,
        priority: mapping.priority,
        parameters: this.buildParameters(mapping, intent, context),
        dependencies: mapping.dependencies,
        confidence,
        reason: this.generateToolReason(mapping, intent, confidence),
        estimatedDuration: mapping.estimatedDuration,
      };
    });
  }

  /**
   * Create an optimized execution plan
   */
  private createExecutionPlan(tools: ToolSelection[]): ExecutionPlan {
    if (!this.config.enableOptimization) {
      // Simple sequential execution
      return {
        steps: tools.map(tool => ({
          parallel: false,
          tools: [tool],
        })),
        estimatedTime: tools.reduce((sum, tool) => sum + (tool.estimatedDuration || 0), 0),
        fallbackOptions: [],
        parallelGroups: [],
      };
    }

    // Build dependency graph
    const dependencyGraph = this.buildDependencyGraph(tools);

    // Create parallel execution groups
    const parallelGroups = this.identifyParallelGroups(dependencyGraph);

    // Create execution steps
    const steps = this.createExecutionSteps(parallelGroups);

    // Calculate optimized time
    const estimatedTime = this.calculateOptimizedTime(parallelGroups);

    // Identify fallback options
    const fallbackOptions = this.identifyFallbackOptions(tools);

    return {
      steps,
      estimatedTime,
      fallbackOptions,
      parallelGroups,
    };
  }

  /**
   * Build tool parameters based on intent and context
   */
  private buildParameters(
    mapping: ToolMapping,
    intent: Intent,
    context?: Context
  ): Record<string, any> {
    const parameters: Record<string, any> = {};

    // Always include organization context
    if (context?.organizationId) {
      parameters.organizationId = context.organizationId;
    }

    if (context?.userId) {
      parameters.userId = context.userId;
    }

    // Add entity-based parameters
    for (const entity of intent.entities) {
      switch (entity.type) {
        case 'date_range':
          parameters.dateRange = entity.value;
          break;
        case 'keyword':
          parameters.keywords = parameters.keywords
            ? [...parameters.keywords, entity.value]
            : [entity.value];
          break;
        case 'technology':
          parameters.technology = entity.value;
          break;
        case 'person':
          parameters.person = entity.value;
          break;
        case 'organization':
          parameters.targetOrganization = entity.value;
          break;
      }
    }

    // Tool-specific parameter building
    switch (mapping.toolName) {
      case 'get-all-documents-tool':
        // No additional parameters needed
        break;
      case 'query-database-tool':
        if (parameters.keywords) {
          parameters.searchQuery = parameters.keywords.join(' ');
        }
        break;
      case 'semantic-search-tool':
        parameters.query = intent.entities
          .filter(e => e.type === 'keyword' || e.type === 'topic')
          .map(e => e.value)
          .join(' ');
        break;
    }

    return parameters;
  }

  /**
   * Build dependency graph for tools
   */
  private buildDependencyGraph(tools: ToolSelection[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();

    for (const tool of tools) {
      graph.set(
        tool.toolName,
        tool.dependencies.filter(dep => tools.some(t => t.toolName === dep))
      );
    }

    return graph;
  }

  /**
   * Identify tools that can be executed in parallel
   */
  private identifyParallelGroups(dependencyGraph: Map<string, string[]>): ToolSelection[][] {
    // For now, implement simple parallel grouping
    // Future: implement topological sorting for complex dependencies

    const groups: ToolSelection[][] = [];

    // Find tools with no dependencies first
    const independentTools = Array.from(dependencyGraph.entries())
      .filter(([_, deps]) => deps.length === 0)
      .map(([toolName, _]) => toolName);

    if (independentTools.length > 1 && this.config.parallelExecutionEnabled) {
      // Group independent tools for parallel execution
      groups.push(
        independentTools.map(toolName => ({
          toolName,
          priority: 1,
          parameters: {},
          dependencies: [],
          confidence: 1.0,
          reason: '',
        }))
      );
    } else {
      // Sequential execution
      independentTools.forEach(toolName => {
        groups.push([
          { toolName, priority: 1, parameters: {}, dependencies: [], confidence: 1.0, reason: '' },
        ]);
      });
    }

    return groups;
  }

  /**
   * Create execution steps from parallel groups
   */
  private createExecutionSteps(parallelGroups: ToolSelection[][]): any[] {
    return parallelGroups.map(group => ({
      parallel: group.length > 1,
      tools: group,
    }));
  }

  /**
   * Calculate optimized execution time
   */
  private calculateOptimizedTime(parallelGroups: ToolSelection[][]): number {
    return parallelGroups.reduce((totalTime, group) => {
      if (group.length > 1) {
        // Parallel execution - use max time in group
        const maxTime = Math.max(...group.map(tool => tool.estimatedDuration || 0));
        return totalTime + maxTime;
      } else {
        // Sequential execution
        return totalTime + (group[0]?.estimatedDuration || 0);
      }
    }, 0);
  }

  /**
   * Calculate total execution time
   */
  private calculateTotalTime(plan: ExecutionPlan): number {
    return plan.estimatedTime;
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(tools: ToolSelection[]): number {
    if (tools.length === 0) return 0;

    const totalConfidence = tools.reduce((sum, tool) => sum + tool.confidence, 0);
    return totalConfidence / tools.length;
  }

  /**
   * Generate reasoning for tool selection
   */
  private generateReasoning(intent: Intent, tools: ToolSelection[], plan: ExecutionPlan): string {
    const reasonParts = [];

    reasonParts.push(`Selected ${tools.length} tools for ${intent.type} intent`);

    if (intent.entities.length > 0) {
      const entityTypes = [...new Set(intent.entities.map(e => e.type))];
      reasonParts.push(`with entities: ${entityTypes.join(', ')}`);
    }

    if (plan.steps.some(step => step.parallel)) {
      reasonParts.push('optimized for parallel execution');
    }

    if (intent.complexity !== 'simple') {
      reasonParts.push(`handling ${intent.complexity} complexity`);
    }

    return reasonParts.join(', ');
  }

  /**
   * Generate reason for individual tool selection
   */
  private generateToolReason(mapping: ToolMapping, intent: Intent, confidence: number): string {
    const reasons = [];

    if (mapping.intentTypes.includes(intent.type)) {
      reasons.push(`matches ${intent.type} intent`);
    }

    const relevantEntities = intent.entities.filter(
      e => mapping.requiredEntities?.includes(e.type) || mapping.optionalEntities?.includes(e.type)
    );

    if (relevantEntities.length > 0) {
      reasons.push(`relevant for ${relevantEntities.map(e => e.type).join(', ')}`);
    }

    if (confidence > 0.8) {
      reasons.push('high confidence match');
    }

    return reasons.join(', ');
  }

  /**
   * Identify fallback options
   */
  private identifyFallbackOptions(tools: ToolSelection[]): ToolSelection[] {
    // Return basic document retrieval as fallback
    return tools.filter(tool => tool.toolName === 'get-all-documents-tool');
  }

  /**
   * Create fallback selection for error cases
   */
  private createFallbackSelection(_intent: Intent): ToolSelectionResult {
    const fallbackTool: ToolSelection = {
      toolName: 'get-all-documents-tool',
      priority: 1,
      parameters: {},
      dependencies: [],
      confidence: 0.5,
      reason: 'fallback selection due to error',
      estimatedDuration: 2000,
    };

    return {
      selectedTools: [fallbackTool],
      executionPlan: {
        steps: [{ parallel: false, tools: [fallbackTool] }],
        estimatedTime: 2000,
        fallbackOptions: [],
        parallelGroups: [],
      },
      totalEstimatedTime: 2000,
      confidence: 0.5,
      reasoning: 'Fallback to basic document retrieval due to selection error',
    };
  }

  /**
   * Get optimization steps for debugging
   */
  private getOptimizationSteps(plan: ExecutionPlan): Record<string, any> {
    return {
      totalSteps: plan.steps.length,
      parallelSteps: plan.steps.filter(step => step.parallel).length,
      sequentialSteps: plan.steps.filter(step => !step.parallel).length,
      estimatedTimeReduction: plan.parallelGroups.length > 0 ? '30-50%' : '0%',
    };
  }
}
