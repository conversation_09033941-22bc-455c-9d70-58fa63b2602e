/**
 * Types for the intelligence layer - intent analysis and tool selection
 */

export interface Intent {
  type: IntentType;
  entities: Entity[];
  confidence: number;
  complexity: QueryComplexity;
  domain?: string;
  subIntents?: Intent[];
}

export type IntentType =
  | 'retrieve' // Simple document retrieval
  | 'search' // Semantic search
  | 'analyze' // Document analysis
  | 'query' // Database query
  | 'summarize' // Content summarization
  | 'compare' // Comparative analysis
  | 'explain' // Explanatory response
  | 'troubleshoot' // Problem-solving
  | 'recommend' // Recommendations
  | 'complex' // Multi-step complex query
  | 'unknown'; // Unrecognized intent

export type QueryComplexity = 'simple' | 'moderate' | 'complex' | 'multi_step';

export interface Entity {
  type: EntityType;
  value: string;
  confidence: number;
  metadata?: Record<string, any>;
}

export type EntityType =
  | 'person'
  | 'organization'
  | 'technology'
  | 'vulnerability'
  | 'threat'
  | 'document_type'
  | 'date_range'
  | 'keyword'
  | 'topic';

export interface Context {
  sessionId: string;
  userId?: string;
  organizationId: string;
  conversationHistory?: ConversationEntry[];
  userPreferences?: UserPreferences;
  previousResults?: Record<string, any>;
}

export interface ConversationEntry {
  timestamp: Date;
  userInput: string;
  agentResponse: string;
  intent?: Intent;
  toolsUsed?: string[];
}

export interface UserPreferences {
  preferredTools?: string[];
  responseStyle?: 'concise' | 'detailed' | 'technical';
  domains?: string[];
}

export interface ToolSelection {
  toolName: string;
  priority: number;
  parameters: Record<string, any>;
  dependencies: string[];
  confidence: number;
  reason: string;
  estimatedDuration?: number;
}

export interface ExecutionPlan {
  steps: ExecutionStep[];
  estimatedTime: number;
  fallbackOptions: ToolSelection[];
  parallelGroups: ToolSelection[][];
}

export interface ExecutionStep {
  parallel: boolean;
  tools: ToolSelection[];
  condition?: (state: any) => boolean;
}

export interface IntentAnalysisResult {
  intent: Intent;
  confidence: number;
  alternatives: Intent[];
  processingTime: number;
  debugInfo?: {
    rawClassification: any;
    entityExtractionDetails: any;
    contextFactors: any;
  };
}

export interface ToolSelectionResult {
  selectedTools: ToolSelection[];
  executionPlan: ExecutionPlan;
  totalEstimatedTime: number;
  confidence: number;
  reasoning: string;
  debugInfo?: {
    availableTools: string[];
    selectionCriteria: any;
    optimizationSteps: any;
  };
}
