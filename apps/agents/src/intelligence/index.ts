/**
 * Intelligence layer exports
 * Provides intent analysis and dynamic tool selection capabilities
 */

export * from './types';
export * from './entity-extractor';
export * from './intent-analyzer';
export * from './tool-selector';

// Convenience class that combines all intelligence capabilities
import { IntentAnalyzer } from './intent-analyzer';
import { ToolSelector } from './tool-selector';
import { Intent, Context, IntentAnalysisResult, ToolSelectionResult } from './types';
import { LearningService } from './learning.service';

interface IntelligenceServiceConfig {
  intentAnalysis?: any;
  toolSelection?: any;
  debugMode?: boolean;
}

export class IntelligenceService {
  private intentAnalyzer: IntentAnalyzer;
  private toolSelector: ToolSelector;
  private debugMode: boolean;
  private learningService: LearningService;

  constructor(config?: IntelligenceServiceConfig) {
    this.debugMode = config?.debugMode ?? process.env.NODE_ENV === 'development';

    this.intentAnalyzer = new IntentAnalyzer({
      debugMode: this.debugMode,
      ...config?.intentAnalysis,
    });

    this.toolSelector = new ToolSelector({
      debugMode: this.debugMode,
      ...config?.toolSelection,
    });

    this.learningService = new LearningService();
  }

  /**
   * Analyze user input and select appropriate tools
   */
  async analyzeAndSelectTools(
    userInput: string,
    context?: Context
  ): Promise<{
    intentAnalysis: IntentAnalysisResult;
    toolSelection: ToolSelectionResult;
  }> {
    try {
      // Analyze intent first
      const intentAnalysis = await this.intentAnalyzer.analyze(userInput, context);

      // Select tools based on intent
      const toolSelection = await this.toolSelector.selectTools(intentAnalysis.intent, context);

      if (this.debugMode) {
        console.log('[IntelligenceService] Intent Analysis:', {
          intent: intentAnalysis.intent.type,
          confidence: intentAnalysis.confidence,
          entities: intentAnalysis.intent.entities.length,
          complexity: intentAnalysis.intent.complexity,
        });

        console.log('[IntelligenceService] Tool Selection:', {
          toolCount: toolSelection.selectedTools.length,
          tools: toolSelection.selectedTools.map(t => t.toolName),
          confidence: toolSelection.confidence,
          reasoning: toolSelection.reasoning,
        });
      }

      return {
        intentAnalysis,
        toolSelection,
      };
    } catch (error) {
      console.error('[IntelligenceService] Analysis failed:', error);
      throw error;
    }
  }

  /**
   * Analyze intent only
   */
  async analyzeIntent(userInput: string, context?: Context): Promise<IntentAnalysisResult> {
    return this.intentAnalyzer.analyze(userInput, context);
  }

  /**
   * Select tools for a given intent
   */
  async selectTools(intent: Intent, context?: Context): Promise<ToolSelectionResult> {
    return this.toolSelector.selectTools(intent, context);
  }

  /** Record outcome for adaptive learning */
  recordWorkflowOutcome(workflowType: string, success: boolean): void {
    this.learningService.recordOutcome(workflowType, success);
  }

  /** Recommend a workflow based on historical performance */
  recommendWorkflow(): string | undefined {
    return this.learningService.recommendWorkflow();
  }

  /**
   * Backwards compatibility – previous API name used in legacy tests
   * Delegates to analyzeAndSelectTools
   */
  async analyzeAndSelect(
    userInput: string,
    context?: Context
  ): Promise<{
    intentAnalysis: IntentAnalysisResult;
    toolSelection: ToolSelectionResult;
  }> {
    return this.analyzeAndSelectTools(userInput, context);
  }
}
