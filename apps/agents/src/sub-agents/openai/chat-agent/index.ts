/**
 * LangChain-based OpenAI Chat Agent Implementation
 *
 * Replaces the problematic @openai/agents package with LangChain's
 * mature, CommonJS-compatible OpenAI integration.
 *
 * @deprecated This agent is deprecated. Consider using the newer LangGraph-based agents
 * or the enhanced chat agents in the orchestration module for better performance
 * and features. This will be removed in a future version.
 */

import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage } from '@langchain/core/messages';

export interface ChatAgentConfig {
  apiKey?: string;
  model: string;
  temperature: number;
  maxTokens: number;
  instructions: string;
  organizationId?: string;
}

export interface ChatAgentContext {
  userId?: string;
  organizationId?: string;
  metadata?: Record<string, any>;
}

export interface ChatResponse {
  response: string;
  metadata?: Record<string, any>;
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  message: string;
}

const DEFAULT_CONFIG: Required<ChatAgentConfig> = {
  apiKey: '',
  model: 'gpt-4o-mini',
  instructions:
    'You are a response generation expert specializing in creating helpful, accurate, and well-structured responses.',
  temperature: 0.7,
  maxTokens: 1000,
  organizationId: '',
};

/**
 * LangChain-based OpenAI Chat Agent
 *
 * @deprecated This agent is deprecated. Consider using the newer LangGraph-based agents
 * or the enhanced chat agents in the orchestration module for better performance
 * and features. This will be removed in a future version.
 */
export class OpenAIChatAgent {
  private config: ChatAgentConfig;
  private chatModel: ChatOpenAI | null = null;
  private initialized = false;
  private deprecationWarningShown = false;

  constructor(config: ChatAgentConfig) {
    this.config = config;
    this.showDeprecationWarning();
  }

  private showDeprecationWarning(): void {
    if (!this.deprecationWarningShown) {
      console.warn(
        '⚠️  DEPRECATION WARNING: OpenAIChatAgent is deprecated. ' +
          'Consider using the newer LangGraph-based agents or the enhanced chat agents ' +
          'in the orchestration module for better performance and features. ' +
          'This will be removed in a future version.'
      );
      this.deprecationWarningShown = true;
    }
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      const apiKey = this.config.apiKey || process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error('OpenAI API key is required');
      }

      this.chatModel = new ChatOpenAI({
        apiKey,
        model: this.config.model,
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
        ...(this.config.organizationId && {
          organization: this.config.organizationId,
        }),
        // @ts-ignore
        prompt: this.config.instructions,
      });

      // Test connection with simple message format
      const testMessage = new HumanMessage('test');
      await this.chatModel.invoke([testMessage]);
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize OpenAI Chat Agent:', error);
      throw error;
    }
  }

  async chat(prompt: string, context?: ChatAgentContext): Promise<string> {
    try {
      if (!prompt || typeof prompt !== 'string') {
        throw new Error('Invalid prompt: must be a non-empty string');
      }

      await this.initialize();

      if (!this.chatModel) {
        throw new Error('Chat model not initialized');
      }

      // Create messages using simple string constructor (recommended by docs)
      const systemMessage = new SystemMessage(
        this.config.instructions || DEFAULT_CONFIG.instructions
      );
      const humanMessage = new HumanMessage(prompt);

      const messages = [systemMessage, humanMessage];

      if (context?.metadata) {
        const contextStr = `Context: ${JSON.stringify(context.metadata)}`;
        const contextMessage = new SystemMessage(contextStr);
        messages.splice(1, 0, contextMessage);
      }

      const response = await this.chatModel.invoke(messages);
      return response.content as string;
    } catch (error) {
      console.error('Chat agent error:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      await this.initialize();

      if (!this.chatModel) {
        throw new Error('Chat model not available');
      }

      const testMessage = new HumanMessage('Respond with "OK" if working.');
      await this.chatModel.invoke([testMessage]);
      return { status: 'healthy', message: `Chat agent operational (${this.config.model})` };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        message: `Chat agent health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async invoke(input: any): Promise<any> {
    if (typeof input === 'string') {
      return this.chat(input);
    }

    if (input?.prompt || input?.query) {
      return this.chat(input.prompt || input.query, input.context);
    }

    throw new Error('Invalid input format for OpenAI Chat Agent');
  }
}

/**
 * Factory function to create a new OpenAI Chat Agent
 *
 * @deprecated This factory function is deprecated. Consider using the newer LangGraph-based agents
 * or the enhanced chat agents in the orchestration module for better performance
 * and features. This will be removed in a future version.
 */
export function createChatAgent(config?: Partial<ChatAgentConfig>): OpenAIChatAgent {
  // Show deprecation warning once per process
  if (!(globalThis as any).__openaiChatAgentDeprecationWarningShown) {
    console.warn(
      '⚠️  DEPRECATION WARNING: createChatAgent factory is deprecated. ' +
        'Consider using the newer LangGraph-based agents or the enhanced chat agents ' +
        'in the orchestration module for better performance and features. ' +
        'This will be removed in a future version.'
    );
    (globalThis as any).__openaiChatAgentDeprecationWarningShown = true;
  }

  const instructions = `You are a response generation expert specializing in creating helpful, accurate, and well-structured responses.

**Your Responsibilities:**
1. **Context Building**: Synthesize information from search results and analysis
2. **Response Generation**: Create comprehensive, helpful responses to user queries
3. **Information Synthesis**: Combine multiple sources into coherent answers
4. **Quality Assurance**: Ensure responses are accurate, relevant, and well-formatted
5. **User Experience**: Provide clear, actionable information that addresses user needs

**Instructions:**
- Always use available search results and analysis to inform your responses
- Provide comprehensive answers that directly address the user's query
- Include relevant details and context from the knowledge base
- Structure responses clearly with appropriate formatting
- Acknowledge limitations when information is incomplete
- Cite sources when specific information is referenced

**Important:** Base your responses on the actual search results and analysis provided. Do not generate information not supported by the available context.`;

  const envConfig: ChatAgentConfig = {
    model: process.env.OPENAI_CHAT_MODEL || DEFAULT_CONFIG.model,
    instructions: instructions || DEFAULT_CONFIG.instructions,
    temperature: process.env.OPENAI_TEMPERATURE
      ? parseFloat(process.env.OPENAI_TEMPERATURE)
      : DEFAULT_CONFIG.temperature,
    maxTokens: process.env.OPENAI_MAX_TOKENS
      ? parseInt(process.env.OPENAI_MAX_TOKENS)
      : DEFAULT_CONFIG.maxTokens,
  };

  return new OpenAIChatAgent({ ...envConfig, ...config });
}
