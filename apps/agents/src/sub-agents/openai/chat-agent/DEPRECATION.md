# OpenAIChatAgent Deprecation Notice

## Overview

The `OpenAIChatAgent` and `createChatAgent` factory function have been **deprecated** and will be removed in a future version.

## Why is it deprecated?

The `OpenAIChatAgent` was an early implementation that has been superseded by more advanced agent architectures:

1. **LangGraph-based agents** - Provide better orchestration, state management, and multi-agent workflows
2. **Enhanced chat agents** - Offer improved performance, better error handling, and more features
3. **Orchestration module** - Provides superior agent coordination and workflow management

## Migration Path

### Current Usage

```typescript
import { OpenAIChatAgent, createChatAgent } from '@anter/agents';

// Direct instantiation
const agent = new OpenAIChatAgent({
  model: 'gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 1000,
  instructions: 'Your instructions here',
});

// Factory function
const agent = createChatAgent({
  model: 'gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 1000,
});
```

### Recommended Migration

#### Option 1: Use LangGraph-based Agents (Recommended)

```typescript
import { LangGraphSupervisor } from '@anter/agents';

const supervisor = new LangGraphSupervisor({
  // Configuration for modern agent architecture
});
```

#### Option 2: Use Enhanced Chat Agents

```typescript
import { EnhancedChatAgent } from '@anter/agents';

const agent = new EnhancedChatAgent({
  // Enhanced configuration options
});
```

#### Option 3: Use Orchestration Module

```typescript
import { AgentOrchestrator } from '@anter/agents';

const orchestrator = new AgentOrchestrator({
  // Orchestration configuration
});
```

## Timeline

- **Current**: Deprecated with warnings
- **Next Major Version**: Will be removed
- **Migration Period**: Until next major version release

## What happens if I don't migrate?

1. **Warnings**: You'll see deprecation warnings in the console
2. **Functionality**: The agent will continue to work normally
3. **Future**: The code will be removed in the next major version

## Need Help?

- Check the main documentation for the new agent architectures
- Review the orchestration module documentation
- Open an issue if you need migration assistance

## Breaking Changes

There are no breaking changes in the current version. The deprecation is purely informational to prepare for future removal.
