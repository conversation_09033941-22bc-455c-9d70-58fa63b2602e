# OpenAI Chat Agent

A conversational AI agent using LangChain's ChatOpenAI integration for reliable, production-ready OpenAI chat functionality.

## Overview

The OpenAI Chat Agent provides advanced conversational AI capabilities through LangChain's mature ChatOpenAI integration. It features proper message handling with SystemMessage and HumanMessage types, configurable instructions, and robust error handling without ES module complications.

## Features

- **LangChain Integration**: Uses `@langchain/openai` and `@langchain/core` for stable OpenAI integration
- **CommonJS Compatible**: No ES module issues, works reliably in all Node.js environments
- **Message Types**: Proper SystemMessage and HumanMessage handling following LangChain best practices
- **Configurable Instructions**: Environment-based customization and system prompts
- **Error Handling**: Comprehensive error handling with detailed logging
- **Production Ready**: Clean architecture with proper initialization and health checks

## Implementation

### LangChain Architecture

The agent uses LangChain's ChatOpenAI model with proper message types:

```typescript
import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage } from '@langchain/core/messages';

export class OpenAIChatAgent {
  private chatModel: ChatOpenAI | null = null;
  private config: ChatAgentConfig;
  private initialized = false;

  async initialize(): Promise<void> {
    this.chatModel = new ChatOpenAI({
      apiKey: this.config.apiKey || process.env.OPENAI_API_KEY,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
    });

    // Test connection with simple message format
    const testMessage = new HumanMessage('test');
    await this.chatModel.invoke([testMessage]);
    this.initialized = true;
  }

  async chat(prompt: string, context?: ChatAgentContext): Promise<string> {
    await this.initialize();

    // Create messages using LangChain's recommended format
    const systemMessage = new SystemMessage(this.config.instructions);
    const humanMessage = new HumanMessage(prompt);

    const messages = [systemMessage, humanMessage];

    const response = await this.chatModel.invoke(messages);
    return response.content as string;
  }
}
```

### Message Handling

LangChain messages use simple string constructors (recommended approach):

```typescript
// Correct LangChain message format
const systemMessage = new SystemMessage('Your instructions here');
const humanMessage = new HumanMessage('User input here');

// LangChain automatically handles internal message structure
const messages = [systemMessage, humanMessage];
const response = await chatModel.invoke(messages);
```

## Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key                    # Required
OPENAI_CHAT_MODEL=gpt-4o-mini                        # Optional (default: gpt-4o-mini)
OPENAI_CHAT_INSTRUCTIONS=custom_instructions          # Optional (uses default InfoSec instructions)
OPENAI_TEMPERATURE=0.7                               # Optional (default: 0.7)
OPENAI_MAX_TOKENS=1000                               # Optional (default: 1000)
```

### Default Configuration

```typescript
const DEFAULT_CONFIG: Required<ChatAgentConfig> = {
  apiKey: '',
  model: 'gpt-4o-mini',
  instructions: `You are a helpful AI assistant specialized in information security and cybersecurity. 
Provide accurate, concise, and professional responses. Focus on practical security advice, 
vulnerability explanations, and best practices. When unsure, acknowledge limitations.`,
  temperature: 0.7,
  maxTokens: 1000,
  organizationId: '',
};
```

## Usage

### Basic Usage

```typescript
import { createChatAgent } from '@anter/agents';

// Create with default configuration
const chatAgent = createChatAgent();

// Use the agent
const response = await chatAgent.chat('What is a zero-day vulnerability?');
console.log(response);
```

### Custom Configuration

```typescript
import { OpenAIChatAgent } from '@anter/agents';

// Create with custom configuration
const chatAgent = new OpenAIChatAgent({
  model: 'gpt-4o-mini',
  instructions: 'You are a cybersecurity expert specializing in threat analysis.',
  temperature: 0.7,
  maxTokens: 1500,
});

const response = await chatAgent.chat('Analyze this security incident...');
```

### Integration with Ask-AI Agent

The OpenAI chat agent is integrated into the ask-ai agent workflow:

```typescript
// In AskAI Agent - Sequential execution
export class AskAIAgent extends AbstractAgent {
  private chatAgent: OpenAIChatAgent;

  async invoke(input: AgentInput, context: AgentContext): Promise<AgentResult> {
    const userInput = this.extractUserInput(input);

    // Step 1: Process with LangChain OpenAI chat agent
    const chatResponse = await this.chatAgent.chat(userInput, context);

    // Step 2: Execute default database query
    const dbResult = await this.executeDefaultQuery(context);

    // Step 3: Combine results
    return this.combineResults(chatResponse, dbResult);
  }
}
```

## API Reference

### ChatAgentConfig Interface

```typescript
export interface ChatAgentConfig {
  apiKey?: string; // OpenAI API key (defaults to env var)
  model: string; // OpenAI model (e.g., 'gpt-4o-mini')
  temperature: number; // Sampling temperature (0-2)
  maxTokens: number; // Maximum tokens in response
  instructions: string; // System instructions/prompt
  organizationId?: string; // OpenAI organization ID
}
```

### ChatAgentContext Interface

```typescript
export interface ChatAgentContext {
  userId?: string; // User identifier
  organizationId?: string; // Organization identifier
  metadata?: Record<string, any>; // Additional context
}
```

## Methods

### chat(prompt, context?)

Processes a user prompt and returns an AI response.

**Parameters:**

- `prompt: string` - The user input prompt
- `context?: ChatAgentContext` - Optional context information

**Returns:** `Promise<string>` - The AI response

**Example:**

```typescript
const response = await chatAgent.chat('What are the OWASP Top 10 vulnerabilities?', {
  userId: 'user-123',
  organizationId: 'org-456',
});
```

### healthCheck()

Performs a health check on the chat agent.

**Returns:** `Promise<{ status: string; message: string }>` - Health status

**Example:**

```typescript
const health = await chatAgent.healthCheck();
console.log(health.status); // 'healthy' or 'unhealthy'
```

### initialize()

Initializes the LangChain ChatOpenAI model and tests the connection.

**Returns:** `Promise<void>`

**Example:**

```typescript
await chatAgent.initialize();
```

## Error Handling

The agent includes comprehensive error handling:

```typescript
try {
  const response = await chatAgent.chat('Your prompt here');
  console.log(response);
} catch (error) {
  console.error('Chat agent error:', error);
  // Error details are logged automatically
}
```

Common error scenarios:

- **Missing API Key**: Throws error if OPENAI_API_KEY is not set
- **Invalid Model**: Throws error if specified model is not available
- **Network Issues**: Handles connection failures gracefully
- **Rate Limiting**: OpenAI rate limits are handled by LangChain

## Dependencies

### Core Dependencies

```json
{
  "@langchain/core": "^0.3.59",
  "@langchain/openai": "^0.5.13",
  "langchain": "0.3.28"
}
```

### Advantages of LangChain Integration

1. **Stability**: Mature, well-tested integration with extensive community support
2. **CommonJS Compatible**: No ES module complications or dynamic import issues
3. **Rich Ecosystem**: Access to LangChain's extensive toolkit and integrations
4. **Proper Types**: Full TypeScript support with proper message types
5. **Error Handling**: Built-in retry logic and error handling
6. **Documentation**: Comprehensive documentation and examples

## Migration from OpenAI Agents SDK

This implementation replaces the previous `@openai/agents` integration with several advantages:

### Before (OpenAI Agents SDK)

```typescript
// Had ES module compatibility issues
const openaiAgents = await import('@openai/agents');
const Agent = openaiAgents.Agent;
const run = openaiAgents.run;

// Complex dynamic imports and fallback logic
```

### After (LangChain)

```typescript
// Simple, reliable imports
import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage } from '@langchain/core/messages';

// Direct usage without complications
const chatModel = new ChatOpenAI({ ... });
const response = await chatModel.invoke([...messages]);
```

### Benefits of Migration

1. **Reliability**: No more ES module import errors
2. **Simplicity**: Cleaner, more straightforward implementation
3. **Performance**: Better error handling and connection management
4. **Maintainability**: Standard LangChain patterns and practices
5. **Community**: Large, active community and extensive documentation

## Testing

```typescript
import { OpenAIChatAgent } from '@anter/agents';

describe('OpenAIChatAgent', () => {
  let agent: OpenAIChatAgent;

  beforeEach(() => {
    agent = new OpenAIChatAgent({
      model: 'gpt-4o-mini',
      instructions: 'Test instructions',
      temperature: 0.7,
      maxTokens: 100,
    });
  });

  test('should respond to basic prompts', async () => {
    const response = await agent.chat('Hello');
    expect(typeof response).toBe('string');
    expect(response.length).toBeGreaterThan(0);
  });

  test('should pass health check', async () => {
    const health = await agent.healthCheck();
    expect(health.status).toBe('healthy');
  });
});
```

## Troubleshooting

### Common Issues

1. **API Key Not Set**

   ```
   Error: OpenAI API key is required
   ```

   Solution: Set `OPENAI_API_KEY` environment variable

2. **Model Not Available**

   ```
   Error: The model 'gpt-xyz' does not exist
   ```

   Solution: Use a valid OpenAI model name (e.g., 'gpt-4o-mini')

3. **Network Connection Issues**
   ```
   Error: Connection failed
   ```
   Solution: Check internet connection and OpenAI service status

### Debug Logging

Enable debug logging to troubleshoot issues:

```typescript
const chatAgent = new OpenAIChatAgent({
  model: 'gpt-4o-mini',
  // ... other config
});

// Errors are automatically logged to console
```

## License

ISC
