// Base exports
export { BaseAgent, AbstractAgent } from './base/agent';
export { AgentFactory, AgentBuilder, agent } from './base/agent-factory';

// Type exports
export {
  // Enums
  AgentArchetype,
  AgentCapability,

  // Core types
  AgentInput,
  AgentContext,
  AgentResult,
  AgentChunk,
  AgentConfig,

  // Configuration types
  ModelConfig,
  MemoryConfig,
  SecurityConfig,
  ObservabilityConfig,
  PerformanceConfig,

  // Support types
  AgentMemory,
  MemoryEntry,
  AgentTool,
  SecurityContext,
  HealthStatus,
  AgentMetrics,
  AgentDependencies,

  // Schemas
  AgentInputSchema,
} from './base/types';

// Memory exports
export { InMemoryAgentMemory } from './base/memory';

// Agent exports
export { EchoAgent, createEchoAgent, defaultEchoConfig } from './agents/echo';
export { registerBuiltInAgents, initializeAgentSystem } from './agents/registry';

// Sub-agent exports
// @deprecated These OpenAI chat agent exports are deprecated. Consider using the newer LangGraph-based agents
// or the enhanced chat agents in the orchestration module for better performance and features.
export {
  OpenAIChatAgent,
  createChatAgent,
  type ChatAgentConfig,
  type ChatAgentContext,
} from './sub-agents/openai/chat-agent';

// Observability exports
export {
  AgentLogger,
  ConsoleAgentLogger,
  createAgentLogger,
  LogLevel,
  LogEntry,
} from './observability/logging';

export { MetricsCollector, globalMetricsCollector, trackMetrics } from './observability/metrics';

// MCP Integration exports
export {
  MCPConnectionManager,
  type ConnectionPoolConfig,
  type IConnectionManager,
} from './integration/mcp-server/connection-manager';
