interface Logger {
  debug(message: string, data?: any): void;
  info(message: string, data?: any): void;
  warn(message: string, data?: any): void;
  error(error: any, message: string, data?: any): void;
}

export interface AgentMetricsConfig {
  enabled: boolean;
  collection_interval: number;
  retention_period: number;
  export_to_prometheus: boolean;
  include_detailed_traces: boolean;
}

export interface AgentPerformanceMetrics {
  agent_id: string;
  total_invocations: number;
  successful_invocations: number;
  failed_invocations: number;
  average_latency_ms: number;
  p95_latency_ms: number;
  p99_latency_ms: number;
  last_invocation: Date | null;
  error_rate: number;
}

export interface MCPBridgeMetrics {
  total_sessions: number;
  active_sessions: number;
  session_creation_rate: number;
  tool_call_count: number;
  tool_call_success_rate: number;
  average_tool_execution_time: number;
  connection_pool_utilization: number;
}

export interface AskAISpecificMetrics {
  total_queries: number;
  query_success_rate: number;
  average_rows_returned: number;
  query_validation_failures: number;
  sql_injection_attempts_blocked: number;
  average_query_execution_time: number;
  popular_query_patterns: Array<{
    pattern: string;
    frequency: number;
  }>;
}

export interface AgentSystemMetrics {
  timestamp: Date;
  system: {
    memory_usage_mb: number;
    cpu_usage_percent: number;
    active_agents: number;
    total_memory_allocated: number;
  };
  agents: Record<string, AgentPerformanceMetrics>;
  mcp_bridge: MCPBridgeMetrics;
  ask_ai: AskAISpecificMetrics;
}

/**
 * Agent metrics collection and monitoring system
 * Provides comprehensive observability for the agent system including:
 * - Individual agent performance tracking
 * - MCP bridge monitoring
 * - AskAI specific metrics
 * - System resource usage
 */
export class AgentMetricsCollector {
  private config: AgentMetricsConfig;
  private metrics: AgentSystemMetrics;
  private collectionTimer?: NodeJS.Timeout;
  private metricsHistory: AgentSystemMetrics[] = [];

  constructor(
    private logger: Logger,
    config: Partial<AgentMetricsConfig> = {}
  ) {
    this.config = {
      enabled: config.enabled ?? true,
      collection_interval: config.collection_interval ?? 60000, // 1 minute
      retention_period: config.retention_period ?? 24 * 60 * 60 * 1000, // 24 hours
      export_to_prometheus: config.export_to_prometheus ?? false,
      include_detailed_traces: config.include_detailed_traces ?? false,
    };

    this.metrics = this.initializeMetrics();

    if (this.config.enabled) {
      this.startCollection();
    }
  }

  /**
   * Record agent invocation
   */
  recordAgentInvocation(
    agentId: string,
    success: boolean,
    latencyMs: number,
    errorType?: string
  ): void {
    if (!this.config.enabled) return;

    const agentMetrics = this.getOrCreateAgentMetrics(agentId);

    agentMetrics.total_invocations++;
    agentMetrics.last_invocation = new Date();

    if (success) {
      agentMetrics.successful_invocations++;
    } else {
      agentMetrics.failed_invocations++;
    }

    // Update latency metrics using exponential moving average
    const alpha = 0.1;
    if (agentMetrics.average_latency_ms === 0) {
      agentMetrics.average_latency_ms = latencyMs;
    } else {
      agentMetrics.average_latency_ms =
        agentMetrics.average_latency_ms * (1 - alpha) + latencyMs * alpha;
    }

    // Update error rate
    agentMetrics.error_rate = agentMetrics.failed_invocations / agentMetrics.total_invocations;

    // Approximate percentile tracking (simplified)
    if (latencyMs > agentMetrics.p95_latency_ms) {
      agentMetrics.p95_latency_ms = latencyMs;
    }
    if (latencyMs > agentMetrics.p99_latency_ms) {
      agentMetrics.p99_latency_ms = latencyMs;
    }

    this.logger.debug(`Recorded agent invocation: ${agentId}`, {
      success,
      latencyMs,
      errorType,
    });
  }

  /**
   * Record MCP bridge metrics
   */
  recordMCPBridgeMetrics(metrics: Partial<MCPBridgeMetrics>): void {
    if (!this.config.enabled) return;

    this.metrics.mcp_bridge = {
      ...this.metrics.mcp_bridge,
      ...metrics,
    };
  }

  /**
   * Record AskAI specific metrics
   */
  recordAskAIMetrics(metrics: Partial<AskAISpecificMetrics>): void {
    if (!this.config.enabled) return;

    this.metrics.ask_ai = {
      ...this.metrics.ask_ai,
      ...metrics,
    };
  }

  /**
   * Record SQL query pattern for analysis
   */
  recordQueryPattern(query: string): void {
    if (!this.config.enabled) return;

    // Extract pattern from query (simplified pattern matching)
    const pattern = this.extractQueryPattern(query);

    const existing = this.metrics.ask_ai.popular_query_patterns.find(p => p.pattern === pattern);

    if (existing) {
      existing.frequency++;
    } else {
      this.metrics.ask_ai.popular_query_patterns.push({
        pattern,
        frequency: 1,
      });
    }

    // Keep only top 10 patterns
    this.metrics.ask_ai.popular_query_patterns.sort((a, b) => b.frequency - a.frequency);
    this.metrics.ask_ai.popular_query_patterns = this.metrics.ask_ai.popular_query_patterns.slice(
      0,
      10
    );
  }

  /**
   * Get current metrics snapshot
   */
  getCurrentMetrics(): AgentSystemMetrics {
    return JSON.parse(JSON.stringify(this.metrics));
  }

  /**
   * Get metrics for specific agent
   */
  getAgentMetrics(agentId: string): AgentPerformanceMetrics | null {
    return this.metrics.agents[agentId] || null;
  }

  /**
   * Get metrics history within time range
   */
  getMetricsHistory(startTime: Date, endTime: Date = new Date()): AgentSystemMetrics[] {
    return this.metricsHistory.filter(m => m.timestamp >= startTime && m.timestamp <= endTime);
  }

  /**
   * Get system health summary
   */
  getHealthSummary(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    agents: Record<string, 'healthy' | 'degraded' | 'unhealthy'>;
    mcp_bridge: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
  } {
    const issues: string[] = [];
    const agentHealth: Record<string, 'healthy' | 'degraded' | 'unhealthy'> = {};

    // Check agent health
    for (const [agentId, metrics] of Object.entries(this.metrics.agents)) {
      if (metrics.error_rate > 0.1) {
        agentHealth[agentId] = 'unhealthy';
        issues.push(
          `Agent ${agentId} has high error rate: ${(metrics.error_rate * 100).toFixed(1)}%`
        );
      } else if (metrics.error_rate > 0.05 || metrics.average_latency_ms > 5000) {
        agentHealth[agentId] = 'degraded';
        issues.push(`Agent ${agentId} showing degraded performance`);
      } else {
        agentHealth[agentId] = 'healthy';
      }
    }

    // Check MCP bridge health
    let mcpHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (this.metrics.mcp_bridge.tool_call_success_rate < 0.9) {
      mcpHealth = 'unhealthy';
      issues.push(
        `MCP bridge has low success rate: ${(this.metrics.mcp_bridge.tool_call_success_rate * 100).toFixed(1)}%`
      );
    } else if (this.metrics.mcp_bridge.tool_call_success_rate < 0.95) {
      mcpHealth = 'degraded';
      issues.push('MCP bridge showing degraded performance');
    }

    // Overall system status
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const unhealthyAgents = Object.values(agentHealth).filter(h => h === 'unhealthy').length;
    const degradedAgents = Object.values(agentHealth).filter(h => h === 'degraded').length;

    if (unhealthyAgents > 0 || mcpHealth === 'unhealthy') {
      status = 'unhealthy';
    } else if (degradedAgents > 0 || mcpHealth === 'degraded') {
      status = 'degraded';
    }

    return {
      status,
      agents: agentHealth,
      mcp_bridge: mcpHealth,
      issues,
    };
  }

  /**
   * Export metrics to Prometheus format (if enabled)
   */
  exportPrometheusMetrics(): string {
    if (!this.config.export_to_prometheus) {
      return '';
    }

    const lines: string[] = [];

    // Agent metrics
    for (const [agentId, metrics] of Object.entries(this.metrics.agents)) {
      lines.push(`agent_invocations_total{agent="${agentId}"} ${metrics.total_invocations}`);
      lines.push(
        `agent_invocations_successful{agent="${agentId}"} ${metrics.successful_invocations}`
      );
      lines.push(`agent_invocations_failed{agent="${agentId}"} ${metrics.failed_invocations}`);
      lines.push(`agent_latency_ms{agent="${agentId}"} ${metrics.average_latency_ms}`);
      lines.push(`agent_error_rate{agent="${agentId}"} ${metrics.error_rate}`);
    }

    // MCP bridge metrics
    lines.push(`mcp_sessions_total ${this.metrics.mcp_bridge.total_sessions}`);
    lines.push(`mcp_sessions_active ${this.metrics.mcp_bridge.active_sessions}`);
    lines.push(`mcp_tool_calls_total ${this.metrics.mcp_bridge.tool_call_count}`);
    lines.push(`mcp_tool_call_success_rate ${this.metrics.mcp_bridge.tool_call_success_rate}`);

    // AskAI metrics
    lines.push(`ask_ai_queries_total ${this.metrics.ask_ai.total_queries}`);
    lines.push(`ask_ai_query_success_rate ${this.metrics.ask_ai.query_success_rate}`);
    lines.push(`ask_ai_avg_rows_returned ${this.metrics.ask_ai.average_rows_returned}`);
    lines.push(`ask_ai_validation_failures ${this.metrics.ask_ai.query_validation_failures}`);

    return lines.join('\n');
  }

  /**
   * Shutdown metrics collection
   */
  async shutdown(): Promise<void> {
    if (this.collectionTimer) {
      clearInterval(this.collectionTimer);
    }

    // Save final metrics snapshot
    this.saveMetricsSnapshot();

    this.logger.info('Agent metrics collector shutdown complete');
  }

  // Private methods

  private initializeMetrics(): AgentSystemMetrics {
    return {
      timestamp: new Date(),
      system: {
        memory_usage_mb: 0,
        cpu_usage_percent: 0,
        active_agents: 0,
        total_memory_allocated: 0,
      },
      agents: {},
      mcp_bridge: {
        total_sessions: 0,
        active_sessions: 0,
        session_creation_rate: 0,
        tool_call_count: 0,
        tool_call_success_rate: 1.0,
        average_tool_execution_time: 0,
        connection_pool_utilization: 0,
      },
      ask_ai: {
        total_queries: 0,
        query_success_rate: 1.0,
        average_rows_returned: 0,
        query_validation_failures: 0,
        sql_injection_attempts_blocked: 0,
        average_query_execution_time: 0,
        popular_query_patterns: [],
      },
    };
  }

  private getOrCreateAgentMetrics(agentId: string): AgentPerformanceMetrics {
    if (!this.metrics.agents[agentId]) {
      this.metrics.agents[agentId] = {
        agent_id: agentId,
        total_invocations: 0,
        successful_invocations: 0,
        failed_invocations: 0,
        average_latency_ms: 0,
        p95_latency_ms: 0,
        p99_latency_ms: 0,
        last_invocation: null,
        error_rate: 0,
      };
    }
    return this.metrics.agents[agentId];
  }

  private startCollection(): void {
    this.collectionTimer = setInterval(() => {
      this.collectSystemMetrics();
      this.saveMetricsSnapshot();
      this.cleanupOldMetrics();
    }, this.config.collection_interval);

    this.logger.info('Agent metrics collection started', {
      interval: this.config.collection_interval,
      retention: this.config.retention_period,
    });
  }

  private collectSystemMetrics(): void {
    const memUsage = process.memoryUsage();

    this.metrics.system = {
      memory_usage_mb: Math.round(memUsage.rss / 1024 / 1024),
      cpu_usage_percent: process.cpuUsage().user / 1000000, // Simplified CPU calculation
      active_agents: Object.keys(this.metrics.agents).length,
      total_memory_allocated: Math.round(memUsage.heapTotal / 1024 / 1024),
    };

    this.metrics.timestamp = new Date();
  }

  private saveMetricsSnapshot(): void {
    const snapshot = JSON.parse(JSON.stringify(this.metrics)) as AgentSystemMetrics;
    this.metricsHistory.push(snapshot);
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - this.config.retention_period);
    this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > cutoffTime);
  }

  private extractQueryPattern(query: string): string {
    // Simplified pattern extraction - replace literals with placeholders
    return query
      .replace(/\d+/g, 'N')
      .replace(/'[^']*'/g, "'STR'")
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 100); // Limit pattern length
  }
}
