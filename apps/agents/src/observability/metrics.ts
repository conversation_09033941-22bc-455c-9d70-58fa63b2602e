import { AgentMetrics } from '../base/types';

/**
 * Metrics collector for agent performance tracking
 */
export class MetricsCollector {
  private metrics: Map<string, AgentMetrics> = new Map();
  private latencyHistogram: Map<string, number[]> = new Map();

  /**
   * Record a successful invocation
   */
  recordInvocation(
    agentName: string,
    latencyMs: number,
    tokensUsed: number = 0,
    cost: number = 0
  ): void {
    const metrics = this.getOrCreateMetrics(agentName);
    const histogram = this.getOrCreateHistogram(agentName);

    metrics.invocations++;
    metrics.tokensUsed += tokensUsed;
    metrics.cost += cost;
    metrics.lastInvocation = new Date();

    // Update latency metrics
    histogram.push(latencyMs);
    this.updateLatencyMetrics(metrics, histogram);
  }

  /**
   * Record an error
   */
  recordError(agentName: string): void {
    const metrics = this.getOrCreateMetrics(agentName);
    metrics.errors++;
  }

  /**
   * Get metrics for an agent
   */
  getMetrics(agentName: string): AgentMetrics {
    return { ...this.getOrCreateMetrics(agentName) };
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): Map<string, AgentMetrics> {
    const result = new Map<string, AgentMetrics>();

    for (const [name, metrics] of this.metrics) {
      result.set(name, { ...metrics });
    }

    return result;
  }

  /**
   * Reset metrics for an agent
   */
  resetMetrics(agentName: string): void {
    this.metrics.delete(agentName);
    this.latencyHistogram.delete(agentName);
  }

  /**
   * Reset all metrics
   */
  resetAllMetrics(): void {
    this.metrics.clear();
    this.latencyHistogram.clear();
  }

  private getOrCreateMetrics(name: string): AgentMetrics {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, {
        invocations: 0,
        errors: 0,
        avgLatency: 0,
        p95Latency: 0,
        p99Latency: 0,
        tokensUsed: 0,
        cost: 0,
      });
    }
    return this.metrics.get(name)!;
  }

  private getOrCreateHistogram(name: string): number[] {
    if (!this.latencyHistogram.has(name)) {
      this.latencyHistogram.set(name, []);
    }
    return this.latencyHistogram.get(name)!;
  }

  private updateLatencyMetrics(metrics: AgentMetrics, histogram: number[]): void {
    // Keep only last 1000 samples for memory efficiency
    if (histogram.length > 1000) {
      histogram.splice(0, histogram.length - 1000);
    }

    // Calculate average
    const sum = histogram.reduce((a, b) => a + b, 0);
    metrics.avgLatency = sum / histogram.length;

    // Calculate percentiles
    const sorted = [...histogram].sort((a, b) => a - b);
    metrics.p95Latency = this.calculatePercentile(sorted, 0.95);
    metrics.p99Latency = this.calculatePercentile(sorted, 0.99);
  }

  private calculatePercentile(sorted: number[], percentile: number): number {
    const index = Math.ceil(sorted.length * percentile) - 1;
    return sorted[Math.max(0, index)] || 0;
  }
}

/**
 * Global metrics collector instance
 */
export const globalMetricsCollector = new MetricsCollector();

/**
 * Metrics decorator for automatic tracking
 */
export function trackMetrics(_target: any, _propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    const startTime = Date.now();
    // Use 'this' context if available, else fallback
    const agentName =
      this && typeof this === 'object' && 'name' in this ? (this as any).name : 'unknown';

    try {
      const result = await originalMethod.apply(this, args);
      const latency = Date.now() - startTime;

      // Extract metrics from result if available
      const tokensUsed = result?.metadata?.tokensUsed || 0;
      const cost = result?.metadata?.cost || 0;

      globalMetricsCollector.recordInvocation(agentName, latency, tokensUsed, cost);

      return result;
    } catch (error) {
      globalMetricsCollector.recordError(agentName);
      throw error;
    }
  };

  return descriptor;
}
