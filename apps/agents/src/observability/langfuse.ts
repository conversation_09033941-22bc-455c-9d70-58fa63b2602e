import { Langfuse } from 'langfuse';
import { getLogger } from './global-logger';

/**
 * Singleton Langfuse client for observability across the agents system.
 * Configured via environment variables:
 * - LANGFUSE_PUBLIC_KEY
 * - LANGFUSE_SECRET_KEY
 * - LANGFUSE_BASEURL (optional, defaults to cloud.langfuse.com)
 * - LANGFUSE_SAMPLE_RATE (optional, 0-1, defaults to 1)
 * - LANGFUSE_TRACING_ENABLED (optional, defaults to true if keys are set)
 */
class LangfuseClient {
  private static instance: Langfuse | null = null;
  private static initialized = false;
  private static logger: any;

  /**
   * Get the singleton Langfuse client instance
   */
  static getInstance(): Langfuse | null {
    if (!this.initialized) {
      this.initialize();
    }
    return this.instance;
  }

  /**
   * Initialize the Langfuse client with environment configuration
   */
  private static initialize(): void {
    this.logger = getLogger();
    const publicKey = process.env.LANGFUSE_PUBLIC_KEY;
    const secretKey = process.env.LANGFUSE_SECRET_KEY;
    const baseUrl = process.env.LANGFUSE_BASEURL || 'https://cloud.langfuse.com';
    const sampleRate = parseFloat(process.env.LANGFUSE_SAMPLE_RATE || '1');
    const tracingEnabled = process.env.LANGFUSE_TRACING_ENABLED !== 'false';

    // Only initialize if keys are provided and tracing is enabled
    if (!publicKey || !secretKey || !tracingEnabled) {
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('[Langfuse] Observability disabled - missing keys or disabled via env');
      }
      return;
    }

    try {
      this.instance = new Langfuse({
        publicKey,
        secretKey,
        baseUrl,
        sampleRate: Math.max(0, Math.min(1, sampleRate)), // Clamp between 0-1
        enabled: tracingEnabled,
        requestTimeout: 10000, // 10 second timeout
        flushAt: 10, // Batch size
        flushInterval: 5000, // 5 second flush interval
      });

      if (process.env.NODE_ENV === 'development') {
        this.logger.info('[Langfuse] Observability enabled', {
          baseUrl,
          sampleRate,
          tracingEnabled,
        });
      }
    } catch (error) {
      this.logger.error('[Langfuse] Failed to initialize client:', error);
      this.instance = null;
    }

    this.initialized = true;
  }

  /**
   * Shutdown the Langfuse client and flush all pending events
   */
  static async shutdown(): Promise<void> {
    if (this.instance) {
      try {
        await this.instance.shutdownAsync();
        if (process.env.NODE_ENV === 'development') {
          this.logger.info('[Langfuse] Client shutdown completed');
        }
      } catch (error) {
        this.logger.error('[Langfuse] Error during shutdown:', error);
      }
    }
  }

  /**
   * Flush all pending events (useful for testing or short-lived processes)
   */
  static async flush(): Promise<void> {
    if (this.instance) {
      try {
        await this.instance.flushAsync();
      } catch (error) {
        this.logger.error('[Langfuse] Error during flush:', error);
      }
    }
  }

  /**
   * Check if Langfuse is enabled and configured
   */
  static isEnabled(): boolean {
    return this.getInstance() !== null;
  }
}

// Export the singleton instance getter
export const getLangfuseClient = () => LangfuseClient.getInstance();
export const shutdownLangfuse = () => LangfuseClient.shutdown();
export const flushLangfuse = () => LangfuseClient.flush();
export const isLangfuseEnabled = () => LangfuseClient.isEnabled();

// Default export for convenience
export default getLangfuseClient;
