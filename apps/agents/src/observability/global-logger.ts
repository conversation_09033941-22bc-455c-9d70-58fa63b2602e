import { ILogger } from '../integration/mcp-server/mcp-bridge-enhanced';

// Fallback console-based logger that matches the ILogger contract
declare global {
  // eslint-disable-next-line vars-on-top, no-var
  var __ASKINFOSEC_GLOBAL_LOGGER__: ILogger | undefined;
}

// Log level enum for comparison
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

// Parse LOG_LEVEL environment variable
function getLogLevel(): LogLevel {
  const level = process.env.LOG_LEVEL?.toLowerCase() || 'info';
  switch (level) {
    case 'error':
      return LogLevel.ERROR;
    case 'warn':
      return LogLevel.WARN;
    case 'info':
      return LogLevel.INFO;
    case 'debug':
      return LogLevel.DEBUG;
    default:
      return LogLevel.INFO;
  }
}

const currentLogLevel = getLogLevel();

const consoleLogger: ILogger = {
  debug: (message: string, meta?: Record<string, any>) => {
    if (currentLogLevel >= LogLevel.DEBUG) {
      const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
      console.debug(message + metaStr);
    }
  },
  info: (message: string, meta?: Record<string, any>) => {
    if (currentLogLevel >= LogLevel.INFO) {
      const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
      console.info(message + metaStr);
    }
  },
  warn: (message: string, meta?: Record<string, any>) => {
    if (currentLogLevel >= LogLevel.WARN) {
      const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
      console.warn(message + metaStr);
    }
  },
  error: (error: any, message: string, meta?: Record<string, any>) => {
    if (currentLogLevel >= LogLevel.ERROR) {
      const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
      console.error(message + metaStr, error);
    }
  },
};

const globalObj = globalThis as typeof globalThis & {
  __ASKINFOSEC_GLOBAL_LOGGER__?: ILogger;
};

function getCurrent(): ILogger {
  return globalObj.__ASKINFOSEC_GLOBAL_LOGGER__ || consoleLogger;
}

/**
 * Register the application-wide logger (call once during bootstrap).
 */
export function setGlobalLogger(logger: ILogger) {
  globalObj.__ASKINFOSEC_GLOBAL_LOGGER__ = logger;
}

/**
 * Retrieve the application-wide logger from anywhere in the codebase.
 */
export function getLogger(): ILogger {
  return getCurrent();
}

/**
 * Create a logger with traceId context that automatically includes traceId in all log calls.
 */
export function getLoggerWithTraceId(traceId: string): ILogger {
  const baseLogger = getCurrent();

  return {
    debug: (message: string, meta?: Record<string, any>) => {
      baseLogger.debug(message, { ...(meta || {}), traceId });
    },
    info: (message: string, meta?: Record<string, any>) => {
      baseLogger.info(message, { ...(meta || {}), traceId });
    },
    warn: (message: string, meta?: Record<string, any>) => {
      baseLogger.warn(message, { ...(meta || {}), traceId });
    },
    error: (error: any, message: string, meta?: Record<string, any>) => {
      baseLogger.error(error, message, { ...(meta || {}), traceId });
    },
  };
}
