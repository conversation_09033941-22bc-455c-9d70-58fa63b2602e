import { ObservabilityConfig } from '../base/types';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  agentName: string;
  message: string;
  metadata?: Record<string, any>;
  error?: Error;
}

/**
 * Agent logger interface
 */
export interface AgentLogger {
  debug(message: string, metadata?: Record<string, any>): void;
  info(message: string, metadata?: Record<string, any>): void;
  warn(message: string, metadata?: Record<string, any>): void;
  error(message: string, error?: Error, metadata?: Record<string, any>): void;
}

/**
 * Simple console logger implementation
 */
export class ConsoleAgentLogger implements AgentLogger {
  private agentName: string;
  private config: ObservabilityConfig['logging'];
  private logLevels: LogLevel[] = ['debug', 'info', 'warn', 'error'];

  constructor(agentName: string, config: ObservabilityConfig['logging']) {
    this.agentName = agentName;
    this.config = config;
  }

  debug(message: string, metadata?: Record<string, any>): void {
    this.log('debug', message, metadata);
  }

  info(message: string, metadata?: Record<string, any>): void {
    this.log('info', message, metadata);
  }

  warn(message: string, metadata?: Record<string, any>): void {
    this.log('warn', message, metadata);
  }

  error(message: string, error?: Error, metadata?: Record<string, any>): void {
    this.log('error', message, { ...metadata, error: error?.stack });
  }

  private log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    // Check if this log level should be logged
    const currentLevelIndex = this.logLevels.indexOf(this.config.level);
    const messageLevelIndex = this.logLevels.indexOf(level);

    if (messageLevelIndex < currentLevelIndex) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      agentName: this.agentName,
      message,
      metadata,
    };

    // Format and output log
    const formatted = this.formatLogEntry(entry);

    switch (level) {
      case 'debug':
        console.debug(formatted);
        break;
      case 'info':
        console.info(formatted);
        break;
      case 'warn':
        console.warn(formatted);
        break;
      case 'error':
        console.error(formatted);
        break;
    }
  }

  private formatLogEntry(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = entry.level.toUpperCase().padEnd(5);
    const prefix = `[${timestamp}] ${level} [${entry.agentName}]`;

    let message = `${prefix} ${entry.message}`;

    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      // Filter sensitive data based on config
      const filtered = this.filterSensitiveData(entry.metadata);
      message += ` ${JSON.stringify(filtered)}`;
    }

    return message;
  }

  private filterSensitiveData(data: Record<string, any>): Record<string, any> {
    const filtered = { ...data };

    // Remove prompts and responses if configured
    if (!this.config.includePrompts && 'prompt' in filtered) {
      filtered.prompt = '[REDACTED]';
    }

    if (!this.config.includeResponses && 'response' in filtered) {
      filtered.response = '[REDACTED]';
    }

    // Remove common sensitive fields
    const sensitiveFields = ['password', 'token', 'apiKey', 'secret'];
    for (const field of sensitiveFields) {
      if (field in filtered) {
        filtered[field] = '[REDACTED]';
      }
    }

    return filtered;
  }
}

/**
 * Create a logger for an agent
 */
export function createAgentLogger(
  agentName: string,
  config: ObservabilityConfig['logging']
): AgentLogger {
  // In production, this could return different logger implementations
  // based on configuration (e.g., Winston, Bunyan, etc.)
  return new ConsoleAgentLogger(agentName, config);
}
