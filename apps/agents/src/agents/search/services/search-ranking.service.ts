/**
 * Search Ranking Service
 * Provides advanced ranking and scoring for search results
 */
export interface RankingOptions {
  query: string;
  intent?: string;
  entities?: any[];
  userPreferences?: Record<string, any>;
  boostRecent?: boolean;
}

export interface RankedResult {
  id: string;
  title: string;
  content?: string;
  snippet?: string;
  score: number;
  relevanceScore: number;
  metadata: Record<string, any>;
  highlights?: Array<{
    field: string;
    text: string;
    startIndex: number;
    endIndex: number;
  }>;
  explanation?: {
    scoringFactors: Array<{
      factor: string;
      score: number;
      weight: number;
      description: string;
    }>;
    matchedTerms: string[];
    semanticSimilarity?: number;
  };
}

/**
 * Service responsible for calculating an overall relevance score for raw
 * search results returned by the semantic or vector engines. A weighted set of
 * factors—such as original relevance, entity matching, intent alignment and
 * document recency—is applied to every result before returning a sorted list.
 *
 * The weighting model is intentionally conservative (scores are capped at 1)
 * and can be tuned in the future without changing the external API.
 *
 * @public
 */
export class SearchRankingService {
  /**
   * Rank and score a list of raw search `results` using a multi-factor
   * heuristic. The list is processed in parallel and finally sorted in
   * descending order by the aggregated `score` property.
   *
   * @param results Array of unranked result objects coming from the search
   *                backend. Each object should at least carry an `id` and a
   *                base `score`.
   * @param options Additional signals such as the original `query`, detected
   *                `intent`, extracted `entities` or `userPreferences` that may
   *                influence scoring.
   * @returns       An array of {@link RankedResult} items sorted from most to
   *                least relevant.
   * @example
   * ```ts
   * const ranked = await rankingService.rankResults(raw, { query: 'zero trust' });
   * console.log(ranked[0].score); // 0.93
   * ```
   */
  async rankResults(results: any[], options: RankingOptions): Promise<RankedResult[]> {
    console.log('[SearchRankingService] Ranking', results.length, 'results');

    const rankedResults = await Promise.all(
      results.map(result => this.scoreResult(result, options))
    );

    // Sort by final score
    return rankedResults.sort((a, b) => b.score - a.score);
  }

  private async scoreResult(result: any, options: RankingOptions): Promise<RankedResult> {
    const scoringFactors: Array<{
      factor: string;
      score: number;
      weight: number;
      description: string;
    }> = [];

    let finalScore = 0;

    // Factor 1: Base relevance score (from search)
    const baseScore = result.score || 0;
    const baseWeight = 0.4;
    scoringFactors.push({
      factor: 'base_relevance',
      score: baseScore,
      weight: baseWeight,
      description: 'Original search relevance score',
    });
    finalScore += baseScore * baseWeight;

    // Factor 2: Title matching
    const titleScore = this.calculateTitleScore(result.title || '', options.query);
    const titleWeight = 0.2;
    scoringFactors.push({
      factor: 'title_match',
      score: titleScore,
      weight: titleWeight,
      description: 'Query terms found in document title',
    });
    finalScore += titleScore * titleWeight;

    // Factor 3: Content relevance
    const contentScore = this.calculateContentScore(result.content || '', options.query);
    const contentWeight = 0.15;
    scoringFactors.push({
      factor: 'content_relevance',
      score: contentScore,
      weight: contentWeight,
      description: 'Query terms density in content',
    });
    finalScore += contentScore * contentWeight;

    // Factor 4: Intent alignment
    const intentScore = this.calculateIntentScore(result, options.intent);
    const intentWeight = 0.1;
    scoringFactors.push({
      factor: 'intent_alignment',
      score: intentScore,
      weight: intentWeight,
      description: 'Alignment with user intent',
    });
    finalScore += intentScore * intentWeight;

    // Factor 5: Entity matching
    const entityScore = this.calculateEntityScore(result, options.entities || []);
    const entityWeight = 0.1;
    scoringFactors.push({
      factor: 'entity_match',
      score: entityScore,
      weight: entityWeight,
      description: 'Matches with extracted entities',
    });
    finalScore += entityScore * entityWeight;

    // Factor 6: Recency boost
    if (options.boostRecent) {
      const recencyScore = this.calculateRecencyScore(result.metadata);
      const recencyWeight = 0.05;
      scoringFactors.push({
        factor: 'recency_boost',
        score: recencyScore,
        weight: recencyWeight,
        description: 'Boost for recent documents',
      });
      finalScore += recencyScore * recencyWeight;
    }

    // Generate highlights
    const highlights = this.generateHighlights(result, options.query);

    // Get matched terms
    const matchedTerms = this.getMatchedTerms(result, options.query);

    return {
      id: result.id,
      title: result.title,
      content: result.content,
      snippet: this.generateSnippet(result.content || '', options.query),
      score: Math.min(finalScore, 1.0), // Cap at 1.0
      relevanceScore: baseScore,
      metadata: result.metadata || {},
      highlights,
      explanation: {
        scoringFactors,
        matchedTerms,
        semanticSimilarity: result.semanticSimilarity,
      },
    };
  }

  private calculateTitleScore(title: string, query: string): number {
    if (!title || !query) return 0;

    const titleLower = title.toLowerCase();
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(t => t.length > 0);

    let matchingTerms = 0;
    let exactMatches = 0;

    queryTerms.forEach(term => {
      if (titleLower.includes(term)) {
        matchingTerms++;

        // Check for exact word boundary match
        const regex = new RegExp(`\\b${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
        if (regex.test(title)) {
          exactMatches++;
        }
      }
    });

    const matchRatio = matchingTerms / queryTerms.length;
    const exactRatio = exactMatches / queryTerms.length;

    // Bonus for exact matches
    return Math.min(matchRatio * 0.7 + exactRatio * 0.3, 1.0);
  }

  private calculateContentScore(content: string, query: string): number {
    if (!content || !query) return 0;

    const contentLower = content.toLowerCase();
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(t => t.length > 0);
    const contentWords = contentLower.split(/\s+/).length;

    if (contentWords === 0) return 0;

    let totalMatches = 0;
    queryTerms.forEach(term => {
      const regex = new RegExp(term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      const matches = (content.match(regex) || []).length;
      totalMatches += matches;
    });

    // Calculate term frequency
    const termFrequency = totalMatches / contentWords;

    // Normalize and cap the score
    return Math.min(termFrequency * 10, 1.0);
  }

  private calculateIntentScore(result: any, intent?: string): number {
    if (!intent) return 0.5; // Neutral score if no intent

    const content = `${result.title || ''} ${result.content || ''}`.toLowerCase();
    const metadata = result.metadata || {};

    const intentPatterns: Record<string, string[]> = {
      retrieve: ['document', 'file', 'report', 'policy'],
      analyze: ['analysis', 'assessment', 'evaluation', 'review'],
      search: ['search', 'find', 'lookup', 'query'],
      compare: ['comparison', 'versus', 'difference', 'contrast'],
      explain: ['explanation', 'guide', 'tutorial', 'documentation'],
      troubleshoot: ['troubleshoot', 'problem', 'issue', 'solution'],
    };

    const patterns = intentPatterns[intent] || [];
    let matchScore = 0;

    patterns.forEach(pattern => {
      if (content.includes(pattern)) {
        matchScore += 0.2;
      }
      if (metadata.type === pattern || metadata.category === pattern) {
        matchScore += 0.3;
      }
    });

    return Math.min(matchScore, 1.0);
  }

  private calculateEntityScore(result: any, entities: any[]): number {
    if (entities.length === 0) return 0.5; // Neutral score if no entities

    const content = `${result.title || ''} ${result.content || ''}`.toLowerCase();
    let matchScore = 0;

    entities.forEach(entity => {
      const entityValue = entity.value.toLowerCase();
      if (content.includes(entityValue)) {
        matchScore += entity.confidence || 0.5;
      }
    });

    // Normalize by number of entities
    return Math.min(matchScore / entities.length, 1.0);
  }

  private calculateRecencyScore(metadata: any): number {
    if (!metadata || !metadata.createdAt) return 0;

    try {
      const createdAt = new Date(metadata.createdAt);
      const now = new Date();
      const daysSinceCreation = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);

      // Recent documents get higher score
      if (daysSinceCreation < 7) return 1.0; // Last week
      if (daysSinceCreation < 30) return 0.8; // Last month
      if (daysSinceCreation < 90) return 0.6; // Last quarter
      if (daysSinceCreation < 365) return 0.4; // Last year
      return 0.2; // Older than a year
    } catch (error) {
      return 0;
    }
  }

  private generateHighlights(
    result: any,
    query: string
  ): Array<{
    field: string;
    text: string;
    startIndex: number;
    endIndex: number;
  }> {
    const highlights: Array<{
      field: string;
      text: string;
      startIndex: number;
      endIndex: number;
    }> = [];
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(t => t.length > 0);

    // Highlight in title
    if (result.title) {
      queryTerms.forEach(term => {
        const regex = new RegExp(`\\b${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
        let match;
        while ((match = regex.exec(result.title)) !== null) {
          highlights.push({
            field: 'title',
            text: match[0],
            startIndex: match.index,
            endIndex: match.index + match[0].length,
          });
        }
      });
    }

    // Highlight in content (first few matches)
    if (result.content) {
      queryTerms.forEach(term => {
        const regex = new RegExp(`\\b${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
        let match;
        let matchCount = 0;
        while ((match = regex.exec(result.content)) !== null && matchCount < 3) {
          highlights.push({
            field: 'content',
            text: match[0],
            startIndex: match.index,
            endIndex: match.index + match[0].length,
          });
          matchCount++;
        }
      });
    }

    return highlights;
  }

  private getMatchedTerms(result: any, query: string): string[] {
    const content = `${result.title || ''} ${result.content || ''}`.toLowerCase();
    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(t => t.length > 0);

    return queryTerms.filter(term => content.includes(term));
  }

  private generateSnippet(content: string, query: string, maxLength: number = 200): string {
    if (!content) return '';

    const queryTerms = query
      .toLowerCase()
      .split(/\s+/)
      .filter(t => t.length > 0);

    // Find the best sentence containing query terms
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

    let bestSentence = '';
    let maxMatches = 0;

    sentences.forEach(sentence => {
      const sentenceLower = sentence.toLowerCase();
      const matches = queryTerms.filter(term => sentenceLower.includes(term)).length;

      if (matches > maxMatches) {
        maxMatches = matches;
        bestSentence = sentence.trim();
      }
    });

    // If no sentence with matches, take the first sentence
    if (!bestSentence && sentences.length > 0) {
      bestSentence = sentences[0].trim();
    }

    // Truncate if too long
    if (bestSentence.length > maxLength) {
      bestSentence = bestSentence.substring(0, maxLength - 3) + '...';
    }

    return bestSentence || content.substring(0, maxLength) + '...';
  }
}
