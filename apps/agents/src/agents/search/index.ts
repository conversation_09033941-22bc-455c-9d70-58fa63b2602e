import { AbstractAgent } from '../../base/agent';
import { AgentInput, AgentResult, AgentArchetype, AgentCapability } from '../../base/types';
import {
  VectorOperationsService,
  EmbeddingService,
  SemanticSearchServiceEnhanced,
} from '@anter/mcp-tools';
import { QueryExpansionService } from '../../orchestration/langgraph/helpers/query-expansion.service';
import { SearchRankingService } from './services/search-ranking.service';

export interface SearchAgentInput {
  query: string;
  organizationId: string;
  userId?: string;
  filters?: Record<string, any>;
  options?: {
    maxResults?: number;
    similarity_threshold?: number;
    useVectorSearch?: boolean;
    useQueryExpansion?: boolean;
  };
  context?: any;
}

/**
 * Reactive agent that orchestrates semantic, vector and traditional search
 * services in order to satisfy information-retrieval tasks. The agent is
 * intentionally lightweight and delegates all heavy-lifting to the
 * `EmbeddingService`, `SemanticSearchService`, `VectorOperationsService` and
 * various ranking utilities.
 *
 * Supported capabilities:
 * • Semantic search (dense vector similarity)
 * • Optional vector search fallback
 * • Query expansion and alternative generation
 * • Multi-factor post-ranking of results
 *
 * @extends AbstractAgent<SearchAgentInput, any>
 */
export class SearchAgent extends AbstractAgent {
  private embeddingService: EmbeddingService;
  private searchService: SemanticSearchServiceEnhanced;
  private vectorService: VectorOperationsService;
  private queryExpansionService: QueryExpansionService;
  private searchRankingService: SearchRankingService;

  /**
   * Create a new {@link SearchAgent} instance. All service dependencies can be
   * injected for testing; if omitted, reasonable defaults will be instantiated
   * internally.
   *
   * @param embeddingService       Dependency that turns text into vector
   *                               embeddings.
   * @param queryExpansionService  Optional query expansion component.
   * @param searchRankingService   Optional post-ranking component.
   */
  constructor(
    embeddingService?: EmbeddingService,
    queryExpansionService?: QueryExpansionService,
    searchRankingService?: SearchRankingService
  ) {
    super({
      name: 'search_agent',
      version: '0.1.0',
      description: 'Performs semantic and vector search for documents.',
      archetype: AgentArchetype.REACTIVE,
      capabilities: [AgentCapability.REASONING, AgentCapability.LLM_INTEGRATION],
    });
    this.embeddingService = embeddingService || new EmbeddingService();
    this.vectorService = new VectorOperationsService(this.embeddingService);
    this.searchService = new SemanticSearchServiceEnhanced(this.embeddingService);
    this.queryExpansionService = queryExpansionService || new QueryExpansionService();
    this.searchRankingService = searchRankingService || new SearchRankingService();
  }

  /**
   * Core invocation handler that receives an {@link SearchAgentInput}, enriches
   * the query (optional), executes the selected search strategy and finally
   * re-ranks the aggregated results.
   */
  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    const searchInput = input as any as SearchAgentInput;
    const { query, options } = searchInput;

    const queryOptimization = options?.useQueryExpansion
      ? await this.queryExpansionService.expandQuery(query, 3)
      : [query];

    let searchResults: any[];

    if (options?.useVectorSearch) {
      searchResults = await this.performVectorSearch(queryOptimization, searchInput);
    } else {
      searchResults = await this.performSearch(queryOptimization, searchInput);
    }

    const rankedResults = await this.rankResults(searchResults, searchInput);

    return {
      output: {
        response: rankedResults.map(result => result.title).join(', '),
        metadata: {},
        metrics: {},
      },
    };
  }

  /** @internal */
  protected async onInitialize(): Promise<void> {
    // No-op initialization for now
  }

  /** @internal */
  protected async onDestroy(): Promise<void> {
    // No-op cleanup
  }

  private async performSearch(queries: string[], searchInput: SearchAgentInput): Promise<any[]> {
    // Perform search for each expanded query and combine results
    const allResults: any[] = [];
    for (const query of queries) {
      const results = await this.searchService.search(query, {
        organizationId: searchInput.organizationId,
        filters: searchInput.filters,
        topK: searchInput.options?.maxResults,
        threshold: searchInput.options?.similarity_threshold,
      });
      allResults.push(...results);
    }

    // Deduplicate and return top results
    const uniqueResults = Array.from(new Map(allResults.map(r => [r.id, r])).values());
    return uniqueResults.slice(0, searchInput.options?.maxResults || 10);
  }

  private async performVectorSearch(
    queries: string[],
    searchInput: SearchAgentInput
  ): Promise<any[]> {
    // Perform vector search for each expanded query and combine results
    const allResults: any[] = [];
    for (const query of queries) {
      const results = await this.vectorService.findSimilarDocuments(query, {
        organizationId: searchInput.organizationId,
        filters: searchInput.filters,
        topK: searchInput.options?.maxResults,
        threshold: searchInput.options?.similarity_threshold,
      });
      allResults.push(...results);
    }

    // Deduplicate and return top results
    const uniqueResults = Array.from(new Map(allResults.map(r => [r.id, r])).values());
    return uniqueResults.slice(0, searchInput.options?.maxResults || 10);
  }

  private async rankResults(results: any[], searchInput: SearchAgentInput): Promise<any[]> {
    const rankedResults = await this.searchRankingService.rankResults(results, {
      query: searchInput.query,
    });
    return rankedResults;
  }
}
