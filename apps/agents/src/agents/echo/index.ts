import { AbstractAgent } from '../../base/agent';
import {
  <PERSON>In<PERSON>,
  AgentResult,
  AgentConfig,
  AgentArchetype,
  AgentCapability,
} from '../../base/types';

/**
 * Simple echo agent for testing
 * Returns the input back to the user
 */
export class EchoAgent extends AbstractAgent {
  protected async onInitialize(): Promise<void> {
    // No special initialization needed for echo agent
  }

  protected async onDestroy(): Promise<void> {
    // No cleanup needed
  }

  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    // Simulate some processing time
    await this.simulateProcessing();

    const response = this.formatResponse(input.input);

    return {
      output: {
        response,
        metadata: {},
        metrics: {},
      },
    };
  }

  protected async *doStream(
    input: AgentInput
  ): AsyncGenerator<import('../../base/types').AgentChunk> {
    const response = this.formatResponse(input.input);
    const words = response.split(' ');

    // Stream word by word
    for (const word of words) {
      await this.simulateProcessing(10); // 10ms delay between words
      yield {
        type: 'content',
        content: word + ' ',
      };
    }

    // Send metadata at the end
    yield {
      type: 'metadata',
      content: {
        tokensUsed: this.estimateTokens(response),
        latencyMs: words.length * 10,
        cost: 0,
      },
    };
  }

  private formatResponse(input: any): string {
    if (typeof input === 'string') {
      return `Echo: ${input}`;
    } else if (typeof input === 'object') {
      return `Echo: ${JSON.stringify(input, null, 2)}`;
    } else {
      return `Echo: ${String(input)}`;
    }
  }

  private estimateTokens(text: string): number {
    // Simple estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  private async simulateProcessing(ms: number = 50): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Default configuration for echo agent
 */
export const defaultEchoConfig: AgentConfig = {
  name: 'echo-agent',
  version: '0.0.1',
  description: 'Simple echo agent that returns the input',
  archetype: AgentArchetype.REACTIVE,
  capabilities: [AgentCapability.CHAT, AgentCapability.STREAMING],
  securityConfig: {
    inputSanitization: true,
    outputFiltering: false,
    promptInjectionDetection: false,
    piiDetection: false,
  },
  observabilityConfig: {
    telemetry: true,
    metrics: true,
    logging: {
      level: 'info',
      includePrompts: true,
      includeResponses: true,
    },
  },
};

/**
 * Factory function for creating echo agents
 */
export async function createEchoAgent(config: Partial<AgentConfig> = {}): Promise<EchoAgent> {
  const mergedConfig = {
    ...defaultEchoConfig,
    ...config,
  };

  return new EchoAgent(mergedConfig);
}
