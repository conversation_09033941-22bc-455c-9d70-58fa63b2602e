import { AbstractAgent } from '../../base/agent';
import {
  <PERSON><PERSON>n<PERSON>,
  AgentResult,
  AgentConfig,
  AgentArchetype,
  AgentCapability,
} from '../../base/types';
import { MCPConnectionManager } from '../../integration/mcp-server/connection-manager';
import { AskAIAgent } from '../ask-ai';

/**
 * ChatAgent is a simple forwarding agent that delegates all requests to the ask_ai agent.
 * This provides a clean 'chat' interface while leveraging the full RAG capabilities of ask_ai.
 */
export class ChatAgent extends AbstractAgent {
  private connectionManager: MCPConnectionManager;
  private askAIAgent: AskAIAgent | null = null;

  constructor(connectionManager: MCPConnectionManager) {
    const config: AgentConfig = {
      name: 'chat',
      version: '1.0.0',
      description: 'Simple chat agent that forwards requests to ask_ai for RAG processing.',
      archetype: AgentArchetype.HYBRID,
      capabilities: [
        AgentCapability.TOOL_USE,
        AgentCapability.LLM_INTEGRATION,
        AgentCapability.DATABASE_ACCESS,
        AgentCapability.MEMORY,
      ],
    };
    super(config);
    this.connectionManager = connectionManager;
  }

  /**
   * Initialize the underlying ask_ai agent
   */
  protected async onInitialize(): Promise<void> {
    this.askAIAgent = new AskAIAgent(this.connectionManager, 'single-shot');
    await this.askAIAgent.initialize({
      name: 'ask_ai',
      version: '2.0.0',
      description: 'Underlying ask_ai agent for chat processing',
      archetype: AgentArchetype.HYBRID,
      capabilities: [
        AgentCapability.TOOL_USE,
        AgentCapability.LLM_INTEGRATION,
        AgentCapability.DATABASE_ACCESS,
        AgentCapability.MEMORY,
        AgentCapability.STREAMING,
      ],
    });
  }

  /**
   * Clean up resources when the agent is destroyed
   */
  protected async onDestroy(): Promise<void> {
    if (this.askAIAgent) {
      await this.askAIAgent.destroy();
    }
  }

  /**
   * Forward all requests directly to the ask-ai agent
   */
  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    if (!this.askAIAgent) {
      throw new Error('Agent chat is not initialized');
    }

    // Simply forward the request to ask_ai and return its result
    return await this.askAIAgent.invoke(input);
  }
}
