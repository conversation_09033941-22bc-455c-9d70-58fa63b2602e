# AskAI Agent

The main AskAI agent that provides intelligent question answering using a modular traditional RAG (Retrieval-Augmented Generation) implementation with optional LangGraph workflow orchestration.

## Overview

This agent replaces the previous `ask_ai_v2` and `ask_ai_v2_refactored` agents with a single, unified implementation that:

- Uses the refactored traditional RAG implementation for better maintainability
- Supports both streaming and non-streaming responses
- Integrates with LangGraph for advanced workflow orchestration
- Provides comprehensive observability with Langfuse tracing
- Maintains backward compatibility with existing API calls

## Features

### Core Capabilities

- **Semantic Search**: Enhanced semantic search using MCP embedding services
- **Document Retrieval**: Intelligent document retrieval with relevance scoring
- **Response Generation**: Context-aware response generation using OpenAI models
- **Conversation Memory**: Maintains conversation history across sessions
- **Streaming Support**: Real-time streaming responses for better user experience

### Advanced Features

- **LangGraph Integration**: Optional advanced workflow orchestration
- **Dual-Path Execution**: Concurrent execution of LangGraph and traditional RAG
- **Confidence Scoring**: Built-in confidence assessment for response quality
- **Observability**: Comprehensive tracing and metrics with Langfuse
- **Fallback Mechanisms**: Robust error handling and fallback strategies

## Architecture

The agent uses a modular architecture with the following key components:

1. **Traditional RAG Pipeline**: Refactored implementation from `traditional-rag/`
2. **Streaming Pipeline**: Refactored streaming implementation from `traditional-rag-streaming-refactored.ts`
3. **LangGraph Integration**: Optional advanced workflow orchestration
4. **Service Layer**: MCP tools integration for enhanced capabilities

## Usage

### Registration

The agent is registered as `ask_ai` in the agent factory:

```typescript
AgentFactory.register('ask_ai', async config => {
  const connectionManager = new MCPConnectionManager(fastify);
  return new AskAIAgent(connectionManager);
});
```

### API Endpoints

- **Non-streaming**: `/api/v1/external/agents/ask_ai/invoke`
- **Streaming**: `/api/v1/external/agents/ask_ai/stream`

### Backward Compatibility

The agent routing configuration automatically maps old agent names:

- `ask_ai_v2` → `ask_ai`
- `ask_ai_v2_refactored` → `ask_ai`

## Configuration

### Environment Variables

- `ENABLE_LANGGRAPH`: Enable LangGraph workflow orchestration
- `USE_MANUAL_SUPERVISOR`: Use manual LangGraph supervisor
- `NODE_ENV`: Development mode for enhanced debugging

### Dependencies

- MCP Connection Manager for database access
- OpenAI API for LLM integration
- Redis for caching and session management
- Langfuse for observability

## Migration from Previous Versions

This agent consolidates the functionality of:

- `AskAIAgentV2` (original implementation)
- `AskAIAgentV2Refactored` (refactored implementation)

All existing API calls will continue to work with automatic routing to the new `ask_ai` agent.

## Performance

The agent includes comprehensive performance monitoring:

- Execution time tracking
- Embedding call metrics
- Token usage monitoring
- Dual-path winner tracking
- Langfuse score metrics

## Error Handling

Robust error handling with:

- Graceful fallbacks between execution paths
- Detailed error logging and tracing
- User-friendly error messages
- Automatic retry mechanisms
