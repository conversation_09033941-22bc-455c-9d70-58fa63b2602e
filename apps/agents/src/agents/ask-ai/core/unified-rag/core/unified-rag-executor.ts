import { AgentInput, AgentMemory, MemoryEntry } from '../../../../../base/types';
import { getLoggerWithTraceId, getLogger } from '../../../../../observability/global-logger';
import { getLangfuseClient } from '../../../../../observability/langfuse';
import {
  UnifiedRAGContext,
  UnifiedRAGExecutionContext,
  UnifiedSemanticSearchResult,
  UnifiedRAGConfig,
  UnifiedRAGMetadata,
} from '../types/unified-rag-types';
import {
  ContentHashService,
  EmbeddingService,
  SearchOptionsEnhanced,
  SearchResultEnhanced,
  SemanticSearchServiceEnhanced,
} from '@anter/mcp-tools';

/**
 * Unified RAG Executor that contains the shared core logic
 * for both streaming and non-streaming RAG implementations
 */
export class UnifiedRAGExecutor {
  private embeddingService: EmbeddingService;
  private contentHashService: ContentHashService;
  private semanticSearchEnhanced: SemanticSearchServiceEnhanced;
  private memory: AgentMemory;
  private logger: any;
  private redisClient?: any;

  constructor(memory: AgentMemory, logger?: any, redisClient?: any) {
    this.memory = memory;
    this.logger = logger || getLogger();
    this.redisClient = redisClient;
    this.embeddingService = new EmbeddingService();
    this.contentHashService = new ContentHashService();
    this.semanticSearchEnhanced = new SemanticSearchServiceEnhanced(
      this.embeddingService,
      this.contentHashService,
      this.logger,
      undefined,
      this.redisClient
    );
  }

  /**
   * Execute the unified RAG pipeline.
   * This contains the shared core logic for both streaming and non-streaming modes.
   * We get here the context with the search results from the redis embedding index,
   * then we will pass this context to the response strategy to generate the response.
   */
  async execute(
    input: AgentInput,
    sessionId: string | null,
    config: UnifiedRAGConfig,
    traceId?: string
  ): Promise<UnifiedRAGContext> {
    const logger = traceId ? getLoggerWithTraceId(traceId) : this.logger;

    logger.info('Unified RAG pipeline execution started', {
      sessionId,
      organizationId: input.organizationId,
      userId: input.userId,
      executor: 'unified-rag-executor',
    });

    if (!input.organizationId) {
      throw new Error('Organization ID is required');
    }

    // Create execution context
    const executionContext: UnifiedRAGExecutionContext = {
      sessionId: sessionId || undefined,
      organizationId: input.organizationId,
      userId: input.userId,
      traceId,
      startTime: Date.now(),
      input: this.extractUserInput(input),
    };

    // Create Langfuse span for RAG execution
    const langfuseClient = getLangfuseClient();
    let ragSpan = null;

    if (langfuseClient && traceId) {
      try {
        ragSpan = langfuseClient.span({
          traceId,
          name: 'unified_rag_execution',
          input: executionContext.input,
          metadata: {
            sessionId,
            organizationId: input.organizationId,
            userId: input.userId,
            config: {
              topK: config.topK,
              promptType: config.promptType,
            },
            tags: ['unified-rag', 'executor', 'pipeline'],
          },
        });
        logger.info(`[UnifiedRAGExecutor] Langfuse span created | sessionId=${sessionId}`);
      } catch (error) {
        logger.warn('[UnifiedRAGExecutor] Failed to create Langfuse span');
      }
    }

    try {
      // Stage 1: Load conversation history
      const memory = await this.loadConversationHistory(executionContext, logger);

      // Stage 2: Store user input
      await this.storeUserInput(executionContext, logger);

      // Stage 3: Retrieve documents
      // NOTE: We do not need this anymore, we assume our redis already contains the embeddings of the documents
      // const documents = await this.retrieveDocuments(executionContext, logger);

      // Stage 4: Execute semantic search
      const searchResults = await this.executeSemanticSearch(executionContext, config, logger);

      // Stage 5: Build unified context
      const metadata = this.buildMetadata(executionContext, Date.now(), searchResults, logger);

      // Final context to be passed to the response strategy for LLM to generate the response.
      const context: UnifiedRAGContext = {
        executionContext,
        searchResults,
        memory,
        metadata,
      };

      const totalTime = Date.now() - executionContext.startTime;

      logger.info('Unified RAG pipeline execution completed', {
        sessionId,
        totalTime,
        searchResultsCount: searchResults.metadata.resultsCount,
        averageScore: searchResults.averageScore,
      });

      // Update span with success
      if (ragSpan) {
        try {
          ragSpan.update({
            output: `RAG execution completed with ${searchResults.metadata.resultsCount} search results`,
            metadata: {
              executionTime: totalTime,
              searchResultsCount: searchResults.metadata.resultsCount,
              averageScore: searchResults.averageScore,
              success: true,
            },
          });
          ragSpan.end();
        } catch (error) {
          logger.warn('[UnifiedRAGExecutor] Failed to update Langfuse span');
        }
      }

      return context;
    } catch (error) {
      const totalTime = Date.now() - executionContext.startTime;
      logger.error(error, 'Unified RAG pipeline execution failed', {
        sessionId,
        executor: 'unified-rag-executor',
        totalTime,
      });

      // Update span with error
      if (ragSpan) {
        try {
          ragSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: totalTime,
              success: false,
            },
          });
          ragSpan.end();
        } catch (spanError) {
          logger.warn('[UnifiedRAGExecutor] Failed to update Langfuse span with error');
        }
      }

      throw error;
    }
  }

  /**
   * Load conversation history for the session.
   * We use Redis as memory store for the conversation history (thread-like conversation)
   * so we can use this information to better understand the user's intent.
   * https://github.com/anter-ai/anter-ai/issues/326
   */
  private async loadConversationHistory(
    executionContext: UnifiedRAGExecutionContext,
    logger: any
  ): Promise<MemoryEntry[]> {
    // Skip conversation history loading if no session ID
    // Theres no point finding the conversation history if we don't have a session ID
    if (!executionContext.sessionId) {
      logger.info('No session ID provided - skipping conversation history loading', {
        executor: 'unified-rag-executor',
      });
      return [];
    }

    try {
      // Use session-specific loading if available (Redis-backed memory)
      if (typeof (this.memory as any).loadSession === 'function') {
        const history = await (this.memory as any).loadSession(
          executionContext.sessionId,
          executionContext.organizationId
        );

        logger.info('Conversation history loaded from session', {
          sessionId: executionContext.sessionId,
          historyEntries: history.length,
          executor: 'unified-rag-executor',
        });

        // Return more history entries for better context
        return history.slice(-10); // Increased from 6 to 10 entries
      }

      // Fallback to generic loading and filtering
      const entries = await this.memory.get();
      const history = entries
        .filter((e: MemoryEntry) => e.metadata?.sessionId === executionContext.sessionId)
        .slice(-10) as MemoryEntry[]; // Increased from 6 to 10 entries

      logger.info('Conversation history loaded (fallback method)', {
        sessionId: executionContext.sessionId,
        historyEntries: history.length,
        executor: 'unified-rag-executor',
      });

      return history;
    } catch (error) {
      logger.error(error, 'Failed to load conversation history', {
        sessionId: executionContext.sessionId,
        executor: 'unified-rag-executor',
      });
      return [];
    }
  }

  /**
   * Store user input in memory.
   * We use Redis as memory store for the conversation history (thread-like conversation)
   * so we can use this information to better understand the user's intent.
   * https://github.com/anter-ai/anter-ai/issues/326
   */
  private async storeUserInput(
    executionContext: UnifiedRAGExecutionContext,
    logger: any
  ): Promise<void> {
    // Skip user input storage if no session ID
    if (!executionContext.sessionId) {
      logger.info('No session ID provided - skipping user input storage', {
        executor: 'unified-rag-executor',
      });
      return;
    }

    try {
      const entry: MemoryEntry = {
        id: `${executionContext.sessionId}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        timestamp: new Date(),
        type: 'user',
        content: executionContext.input,
        metadata: {
          sessionId: executionContext.sessionId,
          organizationId: executionContext.organizationId,
        },
      };

      await this.memory.add(entry);

      logger.info('User input stored', {
        sessionId: executionContext.sessionId,
        inputLength: executionContext.input.length,
        executor: 'unified-rag-executor',
      });
    } catch (error) {
      logger.error(error, 'Failed to store user input', {
        sessionId: executionContext.sessionId,
        executor: 'unified-rag-executor',
      });
    }
  }

  /**
   * Retrieve documents for the organization.
   * We use Redis as memory store for the conversation history (thread-like conversation)
   * so we can use this information to better understand the user's intent.
   * https://github.com/anter-ai/anter-ai/issues/326
   */
  // private async retrieveDocuments(
  //   executionContext: UnifiedRAGExecutionContext,
  //   logger: any
  // ): Promise<ProcessedDocument[]> {
  //   const startTime = Date.now();

  //   try {
  //     const result = await this.documentService.retrieveAllDocuments(
  //       executionContext.organizationId
  //     );
  //     const documents = result.documents;

  //     logger.info('Documents retrieved', {
  //       sessionId: executionContext.sessionId,
  //       documentsCount: documents.length,
  //       retrievalTime: Date.now() - startTime,
  //       executor: 'unified-rag-executor',
  //     });

  //     return documents;
  //   } catch (error) {
  //     logger.error(error, 'Failed to retrieve documents', {
  //       sessionId: executionContext.sessionId,
  //       executor: 'unified-rag-executor',
  //     });
  //     return [];
  //   }
  // }

  /**
   * Execute semantic search on documents in redis which has been indexed by the embedding service.
   * Organization ID is required to execute the semantic search. We retrieve embeddings from redis for the organization.
   * Format: `[embeddings:<organizationId>:<documentId>::1]`
   */
  private async executeSemanticSearch(
    executionContext: UnifiedRAGExecutionContext,
    config: UnifiedRAGConfig,
    logger: any
  ): Promise<UnifiedSemanticSearchResult> {
    const searchStartTime = Date.now();

    // Create Langfuse span for semantic search
    const langfuseClient = getLangfuseClient();
    let searchSpan = null;

    // Create Langfuse span for semantic search for tracing purposes
    if (langfuseClient && executionContext.traceId) {
      try {
        searchSpan = langfuseClient.span({
          traceId: executionContext.traceId,
          name: 'semantic_search_execution',
          input: executionContext.input,
          metadata: {
            sessionId: executionContext.sessionId,
            topK: config.topK,
            tags: ['semantic-search', 'unified-rag'],
          },
        });
        logger.info(
          `[UnifiedRAGExecutor] Semantic search span created | sessionId=${executionContext.sessionId}`
        );
      } catch (error) {
        logger.warn('[UnifiedRAGExecutor] Failed to create semantic search span');
      }
    }

    try {
      // Execute semantic search
      const searchResults: SearchResultEnhanced[] = await this.semanticSearchEnhanced.search(
        executionContext.input,
        {
          topK: config.topK,
          organizationId: executionContext.organizationId,
          logger: logger,
          redisClient: this.redisClient,
        } as SearchOptionsEnhanced
      );

      // Calculate average score
      const averageScore = this.calculateAverageScore(searchResults);

      // Assess relevance quality
      const relevanceQuality = this.assessRelevanceQuality(averageScore);

      // Build enriched context
      const enrichedContext = this.buildEnrichedContext(searchResults);

      const searchTime = Date.now() - searchStartTime;

      logger.info('Semantic search completed', {
        sessionId: executionContext.sessionId,
        searchTime,
        resultsCount: searchResults.length,
        averageScore,
        relevanceQuality,
        executor: 'unified-rag-executor',
      });

      const result: UnifiedSemanticSearchResult = {
        averageScore,
        enrichedContext,
        metadata: {
          resultsCount: searchResults.length,
          relevanceQuality,
          syncPerformed: true,
          queries: [executionContext.input],
          executionTime: searchTime,
          sources: searchResults.map((result: SearchResultEnhanced) => result.documentId),
        },
      };

      // Update span with success
      if (searchSpan) {
        try {
          searchSpan.update({
            output: `Found ${searchResults.length} relevant documents with average score ${averageScore.toFixed(3)}`,
            metadata: {
              executionTime: searchTime,
              resultsCount: searchResults.length,
              averageScore,
              relevanceQuality,
              success: true,
            },
          });
          searchSpan.end();
        } catch (error) {
          logger.warn('[UnifiedRAGExecutor] Failed to update semantic search span');
        }
      }

      return result;
    } catch (error) {
      const searchTime = Date.now() - searchStartTime;
      logger.error(error, 'Semantic search failed', {
        sessionId: executionContext.sessionId,
        searchTime,
        executor: 'unified-rag-executor',
      });

      // Update span with error
      if (searchSpan) {
        try {
          searchSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: searchTime,
              success: false,
            },
          });
          searchSpan.end();
        } catch (spanError) {
          logger.warn('[UnifiedRAGExecutor] Failed to update semantic search span with error');
        }
      }

      throw error;
    }
  }

  /**
   * Build enriched context from search results
   */
  private buildEnrichedContext(searchResults: SearchResultEnhanced[]): string {
    if (!searchResults.length) return '';

    const contexts = searchResults
      .map((result: SearchResultEnhanced) => result.content)
      .filter(Boolean)
      .join('\n\n');

    return contexts;
  }

  /**
   * Calculate average score from search results
   */
  private calculateAverageScore(searchResults: any[]): number {
    if (!searchResults.length) return 0;
    const sum = searchResults.reduce((acc, result) => acc + (result.score || 0), 0);
    return sum / searchResults.length;
  }

  /**
   * Assess relevance quality based on average score
   */
  private assessRelevanceQuality(averageScore: number): string {
    if (averageScore >= 0.7) return 'high';
    if (averageScore >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * Build metadata for the unified context
   */
  private buildMetadata(
    executionContext: UnifiedRAGExecutionContext,
    endTime: number,
    searchResults: UnifiedSemanticSearchResult,
    logger: any
  ): UnifiedRAGMetadata {
    const totalTime = endTime - executionContext.startTime;

    const metadata: UnifiedRAGMetadata = {
      totalTime,
      searchTime: searchResults.metadata.executionTime,
      generationTime: 0, // Will be set by response strategy
      memoryTime: 0, // Will be set by response strategy
      embeddingCalls: 0, // Will be set by response strategy
      tokensUsed: 0, // Will be set by response strategy
    };

    logger.debug('Metadata built', {
      sessionId: executionContext.sessionId,
      metadata,
      executor: 'unified-rag-executor',
    });

    return metadata;
  }

  /**
   * Extract user input from AgentInput
   */
  private extractUserInput(input: AgentInput): string {
    // Handle case where input.input is a string directly
    if (typeof input.input === 'string') {
      return input.input.trim();
    }

    // Handle case where input.input is an object
    if (typeof input.input === 'object' && input.input !== null) {
      const obj: any = input.input;

      // Try various common field names for user input
      const possibleFields = ['prompt', 'query', 'message', 'input', 'text', 'question', 'content'];

      for (const field of possibleFields) {
        if (obj[field] && typeof obj[field] === 'string' && obj[field].trim()) {
          return obj[field].trim();
        }
      }

      // If no string field found, try to stringify the object
      if (Object.keys(obj).length > 0) {
        return JSON.stringify(obj);
      }
    }

    // Handle case where input.input is undefined, null, or empty
    if (input.input === undefined || input.input === null) {
      throw new Error('Input is undefined or null');
    }

    // Handle case where input.input is a primitive type
    if (typeof input.input === 'number' || typeof input.input === 'boolean') {
      return String(input.input);
    }

    // If we get here, we couldn't extract a meaningful input
    throw new Error('Invalid input format: user input not found in any expected field');
  }
}
