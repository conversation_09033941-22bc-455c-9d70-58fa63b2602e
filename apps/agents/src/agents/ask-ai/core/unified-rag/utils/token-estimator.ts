/**
 * Utility for estimating token usage in conversations
 */
export class TokenEstimator {
  /**
   * Estimate tokens for a conversation
   */
  static estimateConversationTokens(
    context: string,
    query: string,
    response: string,
    systemPrompt: string
  ): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    const contextTokens = Math.ceil(context.length / 4);
    const queryTokens = Math.ceil(query.length / 4);
    const responseTokens = Math.ceil(response.length / 4);
    const systemTokens = Math.ceil(systemPrompt.length / 4);

    // Add overhead for message formatting
    const overhead = 50;

    return contextTokens + queryTokens + responseTokens + systemTokens + overhead;
  }

  /**
   * Estimate tokens for a single text
   */
  static estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }
}
