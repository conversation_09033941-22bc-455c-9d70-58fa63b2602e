import { StreamingChunk } from '../types/streaming-types';

/**
 * Utility for handling errors in streaming responses
 */
export class StreamingErrorHandler {
  /**
   * Categorize error for streaming
   */
  static categorizeError(error: Error, elapsedMs: number): any {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return {
        type: 'TIMEOUT_ERROR',
        message: 'Streaming operation timed out',
        elapsedMs,
        timestamp: new Date().toISOString(),
      };
    }

    if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
      return {
        type: 'RATE_LIMIT_ERROR',
        message: 'Rate limit exceeded',
        elapsedMs,
        timestamp: new Date().toISOString(),
      };
    }

    if (errorMessage.includes('authentication') || errorMessage.includes('auth')) {
      return {
        type: 'AUTH_ERROR',
        message: 'Authentication error',
        elapsedMs,
        timestamp: new Date().toISOString(),
      };
    }

    return {
      type: 'GENERAL_ERROR',
      message: error.message,
      elapsedMs,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create error chunk
   */
  static createErrorChunk(error: any): StreamingChunk {
    return {
      type: 'error',
      content: error.message || 'An error occurred during streaming',
      metadata: {
        errorType: error.type,
        elapsedMs: error.elapsedMs,
        timestamp: error.timestamp,
        ...error.metadata,
      },
    };
  }

  /**
   * Handle timeout error
   */
  static handleTimeoutError(
    lastChunkTime: number,
    timeoutMs: number,
    elapsedMs: number
  ): any | null {
    const timeSinceLastChunk = Date.now() - lastChunkTime;

    if (timeSinceLastChunk > timeoutMs) {
      return {
        type: 'TIMEOUT_ERROR',
        message: `No response received for ${timeoutMs}ms`,
        elapsedMs,
        timestamp: new Date().toISOString(),
      };
    }

    return null;
  }

  /**
   * Log error
   */
  static logError(error: any, logger: any, context: Record<string, any>): void {
    logger.error('Streaming error occurred', {
      errorType: error.type,
      message: error.message,
      elapsedMs: error.elapsedMs,
      ...context,
    });
  }
}
