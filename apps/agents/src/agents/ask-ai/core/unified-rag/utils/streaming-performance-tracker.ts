/**
 * Utility for tracking streaming performance metrics
 */
export class StreamingPerformanceTracker {
  private startTime: number = 0;
  private lastChunkTime: number = 0;
  private totalChunks: number = 0;
  private chunkTimes: number[] = [];

  /**
   * Start tracking
   */
  start(): void {
    this.startTime = Date.now();
    this.lastChunkTime = this.startTime;
    this.totalChunks = 0;
    this.chunkTimes = [];
  }

  /**
   * Record a chunk
   */
  recordChunk(): void {
    const currentTime = Date.now();
    this.chunkTimes.push(currentTime - this.lastChunkTime);
    this.lastChunkTime = currentTime;
    this.totalChunks++;
  }

  /**
   * Stop tracking and get metrics
   */
  stop(): {
    totalTimeMs: number;
    totalChunks: number;
    averageChunkTimeMs: number;
    maxChunkTimeMs: number;
    minChunkTimeMs: number;
    chunksPerSecond: number;
    streamingEfficiency: number;
  } {
    const totalTimeMs = Date.now() - this.startTime;
    const averageChunkTimeMs =
      this.chunkTimes.length > 0
        ? this.chunkTimes.reduce((a, b) => a + b, 0) / this.chunkTimes.length
        : 0;
    const maxChunkTimeMs = this.chunkTimes.length > 0 ? Math.max(...this.chunkTimes) : 0;
    const minChunkTimeMs = this.chunkTimes.length > 0 ? Math.min(...this.chunkTimes) : 0;
    const chunksPerSecond = totalTimeMs > 0 ? (this.totalChunks / totalTimeMs) * 1000 : 0;
    const streamingEfficiency = totalTimeMs > 0 ? (this.totalChunks / totalTimeMs) * 100 : 0;

    return {
      totalTimeMs,
      totalChunks: this.totalChunks,
      averageChunkTimeMs,
      maxChunkTimeMs,
      minChunkTimeMs,
      chunksPerSecond,
      streamingEfficiency,
    };
  }

  /**
   * Check if streaming has timed out
   */
  hasTimedOut(timeoutMs: number): boolean {
    return Date.now() - this.lastChunkTime > timeoutMs;
  }

  /**
   * Get connection state
   */
  getConnectionState(timeoutMs: number): {
    lastChunkTime: number;
    timeSinceLastChunk: number;
    isTimedOut: boolean;
  } {
    const timeSinceLastChunk = Date.now() - this.lastChunkTime;
    return {
      lastChunkTime: this.lastChunkTime,
      timeSinceLastChunk,
      isTimedOut: timeSinceLastChunk > timeoutMs,
    };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): Record<string, number> {
    const metrics = this.stop();
    this.start(); // Restart tracking
    return metrics;
  }

  /**
   * Check if performance is acceptable
   */
  isPerformanceAcceptable(): { isAcceptable: boolean; issues: string[] } {
    const metrics = this.getPerformanceMetrics();
    const issues: string[] = [];

    if (metrics.averageChunkTimeMs > 1000) {
      issues.push('Average chunk time is too high');
    }

    if (metrics.chunksPerSecond < 1) {
      issues.push('Chunk rate is too low');
    }

    if (metrics.streamingEfficiency < 50) {
      issues.push('Streaming efficiency is poor');
    }

    return {
      isAcceptable: issues.length === 0,
      issues,
    };
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): Record<string, any> {
    const metrics = this.getPerformanceMetrics();
    const acceptability = this.isPerformanceAcceptable();

    return {
      ...metrics,
      isAcceptable: acceptability.isAcceptable,
      issues: acceptability.issues,
    };
  }
}
