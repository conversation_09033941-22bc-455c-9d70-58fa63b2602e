import { MemoryEntry } from '../../../../../base/types';

/**
 * Unified execution context shared across all RAG pipeline stages
 */
export interface UnifiedRAGExecutionContext {
  sessionId: string | undefined;
  organizationId: string;
  userId?: string;
  traceId?: string;
  startTime: number;
  input: string;
}

/**
 * Unified configuration for RAG execution
 */
export interface UnifiedRAGConfig {
  confidenceThreshold: number;
  topK: number;
  useExpansion: boolean;
  promptType: 'text' | 'chat';
  fallbackMessage: string;
  streamingTimeout?: number; // Timeout for streaming chunks in milliseconds
  maxRetries?: number; // Maximum retries for streaming operations
  chunkTimeout?: number; // Timeout between chunks in milliseconds
}

/**
 * Unified semantic search result with enriched metadata
 */
export interface UnifiedSemanticSearchResult {
  averageScore: number;
  metadata: UnifiedSearchMetadata;
  enrichedContext: string;
}

export interface UnifiedSearchDocument {
  id: string;
  documentId: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
}

export interface UnifiedSearchMetadata {
  resultsCount: number;
  relevanceQuality: string;
  syncPerformed: boolean;
  queries: string[];
  executionTime: number;
  sources: string[]; // array of document ids
}

/**
 * Unified generation result with confidence assessment
 */
export interface UnifiedGenerationResult {
  response: string;
  confidence: UnifiedConfidenceAssessment;
  tokensUsed: number;
  metadata: UnifiedGenerationMetadata;
  isComplete?: boolean; // For streaming compatibility
}

export interface UnifiedConfidenceAssessment {
  score: number; // 1-10
  reason: string;
  isHighConfidence: boolean;
  isParsed?: boolean; // For streaming compatibility
}

export interface UnifiedGenerationMetadata {
  promptType: string;
  temperature: number;
  includeHistory: boolean;
  historyEntries: number;
  chunkCount?: number; // For streaming compatibility
  totalTimeMs?: number; // For streaming compatibility
  averageChunkTimeMs?: number; // For streaming compatibility
}

/**
 * Unified RAG context that flows through the pipeline
 */
export interface UnifiedRAGContext {
  executionContext: UnifiedRAGExecutionContext;
  searchResults: UnifiedSemanticSearchResult;
  memory: MemoryEntry[];
  metadata: UnifiedRAGMetadata;
}

export interface UnifiedRAGMetadata {
  totalTime: number;
  searchTime: number;
  generationTime: number;
  memoryTime: number;
  embeddingCalls: number;
  tokensUsed: number;
  chunkCount?: number; // For streaming compatibility
  averageChunkTime?: number; // For streaming compatibility
  streamingLatency?: number; // For streaming compatibility
}

/**
 * Response strategy options
 */
export interface ResponseOptions {
  streaming?: boolean;
  maxTokens?: number;
  temperature?: number;
  topK?: number;
  model?: string;
  promptType?: 'text' | 'chat';
  streamingTimeout?: number;
  maxRetries?: number;
  chunkTimeout?: number;
}

/**
 * Base response result interface
 */
export interface ResponseResult {
  content: string;
  metadata: ResponseMetadata;
}

export interface ResponseMetadata {
  tokens?: number;
  latency?: number;
  model?: string;
  streaming?: boolean;
  confidence?: number; // Added for unified confidence
  confidenceReason?: string; // Added for unified confidence
  isLowConfidence?: boolean; // Added for unified confidence
  isError?: boolean; // Added for error handling
  generationMetadata?: UnifiedGenerationMetadata; // Added for detailed metadata
  [key: string]: any;
}

/**
 * Single shot response result
 */
export interface SingleShotResult extends ResponseResult {
  type: 'single-shot';
}

/**
 * Streaming response result
 */
export interface StreamingResult extends ResponseResult {
  type: 'streaming';
  stream: AsyncIterable<string>;
}

/**
 * Unified error types
 */
export type UnifiedErrorType =
  | 'GENERAL_ERROR'
  | 'TIMEOUT_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'AUTH_ERROR'
  | 'CONNECTION_ERROR'
  | 'LOW_CONFIDENCE_ERROR'
  | 'LOW_RELEVANCE_ERROR'
  | 'GENERATION_ERROR'; // Added for streaming errors

export interface UnifiedError {
  type: UnifiedErrorType;
  message: string;
  elapsedMs: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Observability span interface
 */
export interface ManagedSpan {
  id: string;
  name: string;
  addMetrics(metrics: Record<string, number>): void;
  setOutput(output: any): void;
  setError(error: Error): void;
  end(): void;
}

export interface PromptConfigParams {
  organizationId: string;
  promptName: string;
  version: number;
  projectId?: string;
}
