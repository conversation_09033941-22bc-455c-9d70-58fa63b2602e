// MemoryEntry is used in the service files

/**
 * Generation result with confidence assessment
 */
export interface GenerationResult {
  response: string;
  confidence: ConfidenceAssessment;
  tokensUsed: number;
  metadata: GenerationMetadata;
}

/**
 * Confidence assessment for generation results
 */
export interface ConfidenceAssessment {
  score: number; // 1-10
  reason: string;
  isHighConfidence: boolean;
}

/**
 * Generation metadata
 */
export interface GenerationMetadata {
  promptType: string;
  temperature: number;
  includeHistory: boolean;
  historyEntries: number;
}
