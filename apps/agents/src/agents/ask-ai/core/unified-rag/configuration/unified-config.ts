import { UnifiedRAGConfig } from '../types/unified-rag-types';

/**
 * Default configuration for unified RAG execution
 */
export const DEFAULT_UNIFIED_RAG_CONFIG: UnifiedRAGConfig = {
  confidenceThreshold: 0.7,
  topK: 3,
  useExpansion: true,
  promptType: 'text',
  fallbackMessage:
    'I apologize, but I was unable to find relevant information to answer your question. Please try rephrasing your query or ask a different question.',
  streamingTimeout: 60000, // 60 seconds
  maxRetries: 3,
  chunkTimeout: 30000, // 30 seconds
};

/**
 * Configuration builder for unified RAG
 */
export class UnifiedRAGConfigBuilder {
  private config: Partial<UnifiedRAGConfig>;

  constructor() {
    this.config = {};
  }

  /**
   * Set confidence threshold
   */
  withConfidenceThreshold(threshold: number): UnifiedRAGConfigBuilder {
    this.config.confidenceThreshold = threshold;
    return this;
  }

  /**
   * Set top K results
   */
  withTopK(topK: number): UnifiedRAGConfigBuilder {
    this.config.topK = topK;
    return this;
  }

  /**
   * Enable/disable query expansion
   */
  withExpansion(useExpansion: boolean): UnifiedRAGConfigBuilder {
    this.config.useExpansion = useExpansion;
    return this;
  }

  /**
   * Set prompt type
   */
  withPromptType(promptType: 'text' | 'chat'): UnifiedRAGConfigBuilder {
    this.config.promptType = promptType;
    return this;
  }

  /**
   * Set fallback message
   */
  withFallbackMessage(message: string): UnifiedRAGConfigBuilder {
    this.config.fallbackMessage = message;
    return this;
  }

  /**
   * Set streaming timeout
   */
  withStreamingTimeout(timeout: number): UnifiedRAGConfigBuilder {
    this.config.streamingTimeout = timeout;
    return this;
  }

  /**
   * Set max retries
   */
  withMaxRetries(retries: number): UnifiedRAGConfigBuilder {
    this.config.maxRetries = retries;
    return this;
  }

  /**
   * Set chunk timeout
   */
  withChunkTimeout(timeout: number): UnifiedRAGConfigBuilder {
    this.config.chunkTimeout = timeout;
    return this;
  }

  /**
   * Build the final configuration
   */
  build(): UnifiedRAGConfig {
    return {
      ...DEFAULT_UNIFIED_RAG_CONFIG,
      ...this.config,
    };
  }

  /**
   * Create configuration for single-shot mode
   */
  static forSingleShot(): UnifiedRAGConfig {
    return new UnifiedRAGConfigBuilder().withPromptType('text').build();
  }

  /**
   * Create configuration for streaming mode
   */
  static forStreaming(): UnifiedRAGConfig {
    return new UnifiedRAGConfigBuilder()
      .withPromptType('text')
      .withStreamingTimeout(60000)
      .withChunkTimeout(30000)
      .withMaxRetries(3)
      .build();
  }
}
