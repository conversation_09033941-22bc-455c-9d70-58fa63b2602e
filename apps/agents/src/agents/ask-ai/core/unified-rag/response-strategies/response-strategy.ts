import type {
  UnifiedRAGContext,
  ResponseOptions,
  ResponseResult,
  StreamingResult,
  PromptConfigParams,
} from '../types/unified-rag-types';
import { getLogger } from '../../../../../observability/global-logger';
import { UnifiedResponderImpl } from '../services/unified/unified-responder';
import { getLangfuseClient } from '../../../../../observability/langfuse';
// Local contract type used by adapters
export type ResponseStrategy = {
  generateResponse(context: UnifiedRAGContext, options: ResponseOptions): Promise<ResponseResult>;
};

/**
 * Base interface for response generation strategies
 * This allows for different response patterns (single-shot vs streaming)
 * while sharing the same core RAG logic
 */
// Strategy interface is imported from shared contracts

/**
 * Factory for creating response strategies based on mode
 */
export class ResponseStrategyFactory {
  /**
   * Create a response strategy based on the specified mode
   * @param mode The response mode ('single-shot' or 'streaming')
   * @returns The appropriate response strategy instance
   */
  static createStrategy(
    mode: 'single-shot' | 'streaming',
    promptConfigParams: PromptConfigParams
  ): ResponseStrategy {
    const promptConfig: PromptConfigParams = promptConfigParams;
    const core = new UnifiedResponderImpl(getLogger(), promptConfig);
    return mode === 'single-shot'
      ? new SingleShotDeliveryAdapter(core, promptConfigParams)
      : new StreamingDeliveryAdapter(core, promptConfigParams);
  }
}

class SingleShotDeliveryAdapter implements ResponseStrategy {
  private logger = getLogger();
  constructor(
    private core: UnifiedResponderImpl,
    _params: PromptConfigParams
  ) {}
  async generateResponse(
    context: UnifiedRAGContext,
    options: ResponseOptions
  ): Promise<ResponseResult> {
    const startTime = Date.now();
    this.logger.info('[SingleShotDeliveryAdapter] start');
    const langfuse = getLangfuseClient();
    const span = langfuse?.span({
      name: 'single_shot_delivery',
      input: context.executionContext.input,
      metadata: { model: options.model },
    });

    const result = await this.core.respond(context, options);
    const latency = Date.now() - startTime;
    span?.update({ metadata: { latency, tokens: result.metadata.tokens } });
    span?.end();
    this.logger.info('[SingleShotDeliveryAdapter] end', { latency });
    return {
      content: result.content,
      metadata: { ...result.metadata, latency, model: options.model || 'gpt-4o-mini' },
    };
  }
}

class StreamingDeliveryAdapter implements ResponseStrategy {
  private logger = getLogger();
  constructor(
    private core: any,
    _params: PromptConfigParams
  ) {}
  async generateResponse(
    context: UnifiedRAGContext,
    options: ResponseOptions
  ): Promise<ResponseResult> {
    const startTime = Date.now();
    this.logger.info('[StreamingDeliveryAdapter] start');
    const langfuse = getLangfuseClient();
    const span = langfuse?.span({
      name: 'streaming_delivery',
      input: context.executionContext.input,
      metadata: { model: options.model },
    });
    let fullResponse = '';
    const chunks: string[] = [];

    for await (const chunk of this.core.respondStream(context, options)) {
      if (chunk.type === 'content' && typeof chunk.content === 'string') {
        fullResponse += chunk.content;
        chunks.push(chunk.content);
      }
    }

    const latency = Date.now() - startTime;
    const stream = (async function* () {
      for (const c of chunks) yield c;
    })();
    const result: StreamingResult = {
      content: fullResponse,
      metadata: {
        tokens: fullResponse.length,
        latency,
        model: options.model || 'gpt-4o-mini',
        streaming: true,
      },
      type: 'streaming',
      stream,
    };
    this.logger.info('[StreamingDeliveryAdapter] end', { latency, length: fullResponse.length });
    span?.update({ metadata: { latency, tokens: fullResponse.length } });
    span?.end();
    return result;
  }
}
