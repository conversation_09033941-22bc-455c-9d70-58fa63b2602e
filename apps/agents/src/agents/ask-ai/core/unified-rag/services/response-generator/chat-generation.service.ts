import { Chat<PERSON>penAI } from '@langchain/openai';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { AgentInput, MemoryEntry } from '../../../../../../base/types';
import { langfusePromptAdapter } from '../../../../../../services/langfuse-prompt-adapter.service';
import { getLangfuseClient } from '../../../../../../observability/langfuse';
import { GenerationResult, GenerationMetadata } from '../../types/rag-types';
import { TokenEstimator } from '../../utils/token-estimator';
import { PromptConfigParams } from '../../types/unified-rag-types';

/**
 * Service responsible for chat generation and LLM interactions
 */
import { ILogger } from '@/integration/mcp-server/mcp-bridge-enhanced';
import { toText } from '../../../../../../utils/text-utils';

export class ChatGenerationService {
  constructor(private logger: ILogger) {
    this.logger.info('[ChatGenerationService] initialized');
  }

  /**
   * Generates a response using the chat agent with confidence assessment
   */
  async generateResponse(
    context: string,
    input: AgentInput,
    history: MemoryEntry[] = [],
    promptType: 'text' | 'chat' = 'text',
    promptConfigParams: PromptConfigParams
  ): Promise<GenerationResult> {
    const generationStartTime = Date.now();

    this.logger.info('Starting chat generation', {
      contextLength: context.length,
      historyEntries: history.length,
      promptType,
      executor: 'chat-generation-service',
    });

    // Create Langfuse span for chat generation
    const langfuseClient = getLangfuseClient();
    let generationSpan = null;

    if (langfuseClient) {
      try {
        generationSpan = langfuseClient.span({
          name: 'chat_generation',
          input: typeof input.input === 'string' ? input.input : JSON.stringify(input.input),
          metadata: {
            contextLength: context.length,
            historyEntries: history.length,
            promptType,
            organizationId: promptConfigParams.organizationId,
            promptName: promptConfigParams.promptName,
            promptVersion: promptConfigParams.version,
            tags: ['chat-generation', 'llm', 'openai'],
          },
        });
        this.logger.info('[ChatGenerationService] Langfuse span created');
      } catch (error) {
        this.logger.warn('[ChatGenerationService] Failed to create Langfuse span');
      }
    }

    try {
      // Get prompt configuration from Langfuse using the new prompt adapter
      const promptConfig = await langfusePromptAdapter.getPromptConfig(
        promptConfigParams.organizationId,
        promptConfigParams.promptName,
        promptConfigParams.version,
        promptConfigParams.projectId
      );

      // Initialize LLM
      const llm = new ChatOpenAI({
        model: 'gpt-4o-mini',
        temperature: promptConfig.temperature,
      });

      // Build conversation messages
      const messages = this.buildConversationMessages(promptConfig, history);

      // Add current context and query
      messages.push(['human', `Context:\n{context}\n\nQuery: {query}`]);

      // Create prompt template and chain
      const promptWithContext = ChatPromptTemplate.fromMessages(messages);
      const responseChain = promptWithContext.pipe(llm);

      // Generate response
      const response = await responseChain.invoke({ context, query: input.input });
      const responseContent = toText(response?.content);

      this.logger.info('Original LLM Response', { responseContent });
      const finalResponse = responseContent;

      // Calculate token usage
      const tokensUsed = TokenEstimator.estimateConversationTokens(
        context,
        typeof input.input === 'string' ? input.input : JSON.stringify(input.input),
        finalResponse,
        promptConfig.systemPrompt
      );

      const executionTime = Date.now() - generationStartTime;

      this.logger.info('Chat generation completed', {
        responseLength: finalResponse.length,
        tokensUsed,
        executionTime,
        executor: 'chat-generation-service',
      });

      // Update span with success
      if (generationSpan) {
        try {
          generationSpan.update({
            output: finalResponse.substring(0, 1000),
            metadata: {
              executionTime,
              responseLength: finalResponse.length,
              tokensUsed,
              success: true,
            },
          });
          generationSpan.end();
        } catch (error) {
          this.logger.warn('[ChatGenerationService] Failed to update Langfuse span');
        }
      }

      return {
        response: finalResponse,
        confidence: {
          score: 0,
          reason: 'Confidence assessment performed upstream',
          isHighConfidence: false,
        },
        tokensUsed,
        metadata: this.buildGenerationMetadata(promptConfig, history, executionTime, promptType),
      };
    } catch (error) {
      const executionTime = Date.now() - generationStartTime;
      this.logger.error(error, 'Chat generation failed', {
        executionTime,
        executor: 'chat-generation-service',
      });

      // Update span with error
      if (generationSpan) {
        try {
          generationSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime,
              success: false,
            },
          });
          generationSpan.end();
        } catch (spanError) {
          this.logger.warn('[ChatGenerationService] Failed to update Langfuse span with error');
        }
      }

      throw error;
    }
  }

  /**
   * Builds conversation messages for the prompt template
   */
  private buildConversationMessages(
    promptConfig: any,
    history: MemoryEntry[]
  ): Array<[string, string]> {
    // Escape curly braces in the system prompt so that literal JSON/examples
    // are not interpreted as template variables by LangChain format strings
    const safeSystemPrompt =
      typeof promptConfig.systemPrompt === 'string'
        ? promptConfig.systemPrompt.replaceAll('{', '{{').replaceAll('}', '}}')
        : '';

    const messages: Array<[string, string]> = [['system', safeSystemPrompt]];

    // Add conversation history if enabled
    if (promptConfig.includeConversationHistory && history.length > 0) {
      // Get conversation memory configuration
      const memoryConfig = promptConfig.conversationMemory || {
        maxHistoryEntries: 10,
        includeUserMessages: true,
        includeAssistantMessages: true,
        contextWindow: 4000,
        summarizeLongHistory: false,
      };

      // Filter and limit history based on configuration
      let filteredHistory = history;

      if (memoryConfig.maxHistoryEntries) {
        filteredHistory = history.slice(-memoryConfig.maxHistoryEntries);
      }

      // Add conversation history with proper formatting
      filteredHistory.forEach(entry => {
        if (entry.type === 'user' && memoryConfig.includeUserMessages) {
          messages.push(['human', entry.content]);
        } else if (entry.type === 'assistant' && memoryConfig.includeAssistantMessages) {
          messages.push(['ai', entry.content]);
        }
      });

      // Add conversation context summary if history is long
      if (memoryConfig.summarizeLongHistory && history.length > memoryConfig.maxHistoryEntries) {
        const summaryMessage = `[Previous conversation context: ${history.length - memoryConfig.maxHistoryEntries} earlier messages not shown for brevity]`;
        messages.push(['system', summaryMessage]);
      }
    }

    return messages;
  }

  // utility moved to shared agents utils to satisfy monorepo rules

  /**
   * Builds generation metadata
   */
  private buildGenerationMetadata(
    promptConfig: any,
    history: MemoryEntry[],
    // @ts-ignore - executionTime parameter not used
    executionTime: number,
    promptType: string
  ): GenerationMetadata {
    return {
      promptType,
      temperature: promptConfig.temperature,
      includeHistory: promptConfig.includeConversationHistory || false,
      historyEntries: history.length,
    };
  }

  /**
   * Tests different prompt variations for comparison
   */
  async testPromptVariations(
    context: string,
    input: AgentInput,
    history: MemoryEntry[] = []
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {};

    // Create default prompt config params for testing
    const defaultPromptConfigParams: PromptConfigParams = {
      organizationId: input.organizationId || 'default',
      promptName: 'unified-rag',
      version: 1,
    };

    for (const promptType of ['text', 'chat'] as const) {
      try {
        const result = await this.generateResponse(
          context,
          input,
          history,
          promptType,
          defaultPromptConfigParams
        );
        results[promptType] = {
          response: result.response,
          confidence: result.confidence,
          tokensUsed: result.tokensUsed,
          metadata: result.metadata,
        };
      } catch (error) {
        results[promptType] = {
          error: error instanceof Error ? error.message : String(error),
        };
      }
    }

    return results;
  }

  /**
   * Validates generation input parameters
   */
  static validateInput(context: string, input: AgentInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!context || context.trim().length === 0) {
      errors.push('Context cannot be empty');
    }

    if (!input.input) {
      errors.push('Input query cannot be empty');
    }

    if (context.length > 50000) {
      errors.push('Context exceeds maximum length (50,000 characters)');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Gets generation performance metrics
   */
  getGenerationMetrics(result: GenerationResult): Record<string, number> {
    return {
      response_length: result.response.length,
      confidence_score: result.confidence.score,
      tokens_used: result.tokensUsed,
      temperature: result.metadata.temperature,
      history_entries: result.metadata.historyEntries,
    };
  }
}
