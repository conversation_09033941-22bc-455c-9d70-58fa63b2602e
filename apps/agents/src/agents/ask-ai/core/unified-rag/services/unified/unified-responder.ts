import { ILogger } from '@/integration/mcp-server/mcp-bridge-enhanced';
import {
  UnifiedRAGContext,
  ResponseOptions,
  ResponseResult,
  PromptConfigParams,
} from '../../types/unified-rag-types';
import { ChatGenerationService } from '../response-generator/chat-generation.service';
import { StreamingChatGenerationService } from '../response-generator/streaming-chat-generation.service';

export class UnifiedResponderImpl {
  private chatService?: ChatGenerationService;
  private streamingService?: StreamingChatGenerationService;

  constructor(
    private logger: ILogger,
    private promptConfig: PromptConfigParams
  ) {
    // Lazily instantiate services to avoid misleading logs in streaming-only flows
    this.streamingService = new StreamingChatGenerationService(this.logger);
  }

  /**
   * This is the final stage where we generate a response for the given context.
   * We will use the chat generation service to generate the response.
   * @param context - The context object.
   * @param options - The options object.
   * @returns The response result.
   */
  async respond(context: UnifiedRAGContext, options: ResponseOptions): Promise<ResponseResult> {
    const agentInput = {
      input: context.executionContext.input,
      sessionId: context.executionContext.sessionId,
      organizationId: context.executionContext.organizationId,
      userId: context.executionContext.userId,
    };

    let contextString = this.buildContextString(context);

    // Central confidence gate based on semantic search quality
    if (this.isLowConfidenceContext(context)) {
      contextString = this.buildContextStringForLowConfidence(context);
    }

    if (!this.chatService) {
      this.chatService = new ChatGenerationService(this.logger);
    }

    const result = await this.chatService.generateResponse(
      contextString,
      agentInput as any,
      context.memory,
      options.promptType || 'text',
      this.promptConfig
    );

    return {
      content: result.response,
      metadata: {
        tokens: result.tokensUsed,
        model: options.model || 'gpt-4o-mini',
        isLowConfidence: false,
        generationMetadata: result.metadata,
      },
    };
  }

  /**
   * Generate a streaming response for the given context.
   * We will use the streaming chat generation service to generate the response.
   * @param context - The context object.
   * @param options - The options object.
   * @returns The streaming response.
   */
  async *respondStream(context: UnifiedRAGContext, options: ResponseOptions): AsyncGenerator<any> {
    const agentInput = {
      input: context.executionContext.input,
      sessionId: context.executionContext.sessionId,
      organizationId: context.executionContext.organizationId,
      userId: context.executionContext.userId,
    };

    let contextString = this.buildContextString(context);

    // Central confidence gate for streaming mode as well
    if (this.isLowConfidenceContext(context)) {
      contextString = this.buildContextStringForLowConfidence(context);
      // const fallback = this.buildLowConfidenceFallback(context);
      // yield { type: 'content', content: fallback, metadata: { isLowConfidence: true } } as any;
      // yield { type: 'complete', content: '', metadata: { isLowConfidence: true } } as any;
      // return;
    }

    if (!this.streamingService) {
      this.streamingService = new StreamingChatGenerationService(this.logger);
    }

    const stream = this.streamingService.generateStreamingResponse(
      contextString,
      agentInput as any,
      context.memory,
      options.promptType || 'text',
      {
        streamingTimeout: options.streamingTimeout,
        maxRetries: options.maxRetries,
        chunkTimeout: options.chunkTimeout,
      },
      this.promptConfig
    );

    for await (const chunk of stream) {
      yield chunk;
    }
  }

  /**
   * Build the context string for the agent.
   * @param context - The context object.
   * @returns The context string.
   */
  private buildContextString(context: UnifiedRAGContext): string {
    let contextString = '';
    if (context.searchResults.enrichedContext) {
      contextString = context.searchResults.enrichedContext;
    } else if (context.searchResults.metadata.resultsCount > 0) {
      contextString = 'Relevant Information:\n';
    }
    const { resultsCount, relevanceQuality } = context.searchResults.metadata;
    contextString += `\n\n[Search Context: Found ${resultsCount} relevant documents with ${relevanceQuality.toLowerCase()} relevance (average score: ${context.searchResults.averageScore.toFixed(2)})]`;
    return contextString || 'No specific context available for this query.';
  }

  /**
   * Check if the context is low confidence.
   * @param ctx - The context object.
   * @returns True if the context is low confidence, false otherwise.
   */
  private isLowConfidenceContext(ctx: UnifiedRAGContext): boolean {
    const { averageScore, metadata } = ctx.searchResults;
    if (!metadata.resultsCount) return true;
    return averageScore < 0.3;
  }

  /**
   * Build the context string for low confidence. We still want to proceed to the next step which is the chat generation.
   * We do not want to stop the flow. However, we want to give additional instructions to LLM to look at the conversation history if the user query is following up on a previous conversation.
   * If not, the LLM should provide a helpful hint to the user to provide more details.
   * @param ctx - The context object.
   * @returns The context string for low confidence.
   */
  private buildContextStringForLowConfidence(ctx: UnifiedRAGContext): string {
    const { relevanceQuality, resultsCount } = ctx.searchResults.metadata;
    const hasConversationHistory = ctx.memory.length > 0;
    const hasSearchResults = resultsCount > 0;

    // Brief diagnostics to inform the LLM about current confidence state
    const diagnosticsTag = `[Low Confidence: Retrieved context has limited relevance (avg score: ${ctx.searchResults.averageScore.toFixed(
      2
    )})]`;

    // Summarize available signals the model can rely on
    const conversationTag = hasConversationHistory
      ? `[Conversation Context: Found ${ctx.memory.length} prior message(s)]`
      : '';
    const searchTag = hasSearchResults
      ? `[Search Context: Found ${resultsCount} document(s) with ${relevanceQuality.toLowerCase()} relevance (average score: ${ctx.searchResults.averageScore.toFixed(
          2
        )})]`
      : '';

    // Keep diagnostics/metadata only; avoid instruction-like guidance to prevent duplication with Langfuse system prompt
    const parts = [diagnosticsTag, conversationTag, searchTag].filter(Boolean);
    const contextString = parts.join(' ');

    return contextString.trim().length > 0
      ? contextString
      : 'No specific context available. [Guidance: Ask ONE concise clarifying question before answering.]';
  }
}
