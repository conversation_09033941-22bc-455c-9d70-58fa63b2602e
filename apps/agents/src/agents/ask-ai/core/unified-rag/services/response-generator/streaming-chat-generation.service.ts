import { AgentInput, MemoryEntry } from '../../../../../../base/types';
import { StreamingChunk, StreamingChunkType } from '../../types/streaming-types';
import { StreamingErrorHandler } from '../../utils/streaming-error-handler';
import { StreamingPerformanceTracker } from '../../utils/streaming-performance-tracker';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';
import { langfusePromptAdapter } from '../../../../../../services/langfuse-prompt-adapter.service';
import { getLangfuseClient } from '../../../../../../observability/langfuse';
import { ILogger } from '@/integration/mcp-server/mcp-bridge-enhanced';
import { PromptConfigParams } from '../../types/unified-rag-types';
import { toText } from '../../../../../../utils/text-utils';

/**
 * Streaming chat generation service for RAG operations
 */
export class StreamingChatGenerationService {
  private performanceTracker: StreamingPerformanceTracker;

  constructor(private logger: ILogger) {
    this.performanceTracker = new StreamingPerformanceTracker();
  }

  /**
   * Generate streaming response with confidence assessment
   */
  async *generateStreamingResponse(
    context: string,
    input: AgentInput,
    history: MemoryEntry[] = [],
    promptType: 'text' | 'chat' = 'text',
    config: {
      streamingTimeout?: number;
      maxRetries?: number;
      chunkTimeout?: number;
    } = {},
    promptConfigParams: PromptConfigParams
  ): AsyncGenerator<StreamingChunk> {
    const startTime = Date.now();
    this.performanceTracker.start();

    // Create Langfuse span for streaming generation
    const langfuseClient = getLangfuseClient();
    let streamingSpan = null;

    if (langfuseClient) {
      try {
        streamingSpan = langfuseClient.span({
          name: 'streaming_chat_generation',
          input: typeof input.input === 'string' ? input.input : JSON.stringify(input.input),
          metadata: {
            contextLength: context.length,
            historyEntries: history.length,
            promptType,
            organizationId: promptConfigParams.organizationId,
            promptName: promptConfigParams.promptName,
            promptVersion: promptConfigParams.version,
            streamingTimeout: config.streamingTimeout,
            maxRetries: config.maxRetries,
            chunkTimeout: config.chunkTimeout,
            tags: ['streaming-chat-generation', 'llm', 'openai', 'streaming'],
          },
        });
        this.logger.info('[StreamingChatGenerationService] Langfuse span created');
      } catch (error) {
        this.logger.warn('[StreamingChatGenerationService] Failed to create Langfuse span');
      }
    }

    try {
      // Use the Langfuse prompt configuration from the new prompt adapter
      const promptConfig = await langfusePromptAdapter.getPromptConfig(
        promptConfigParams.organizationId,
        promptConfigParams.promptName,
        promptConfigParams.version,
        promptConfigParams.projectId
      );
      this.logger.debug(
        `[streaming-chat-generation] promptConfig: ${JSON.stringify(promptConfig)}`
      );

      const llm = new ChatOpenAI({
        model: 'gpt-4o-mini',
        temperature: promptConfig.temperature,
        streaming: true,
        maxRetries: config.maxRetries || 3,
        timeout: config.streamingTimeout || 60000,
      });

      // Send initial connection event
      yield this.createChunk('stream_started', '', {
        event: 'stream_started',
        timestamp: new Date().toISOString(),
      });

      // Generate actual streaming response (confidence handled upstream)
      yield* this.generateActualResponse(
        context,
        input,
        history,
        promptConfig,
        llm,
        config,
        startTime
      );

      // Update span with success
      if (streamingSpan) {
        try {
          const executionTime = Date.now() - startTime;
          const metrics = this.performanceTracker.getPerformanceMetrics();
          streamingSpan.update({
            metadata: {
              executionTime,
              chunkCount: metrics.totalChunks || 0,
              averageChunkTime: metrics.averageChunkTimeMs || 0,
              success: true,
            },
          });
          streamingSpan.end();
        } catch (error) {
          this.logger.warn('[StreamingChatGenerationService] Failed to update Langfuse span');
        }
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(error, 'Streaming generation failed', {
        executionTime,
        executor: 'streaming-chat-generation-service',
      });

      // Update span with error
      if (streamingSpan) {
        try {
          streamingSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime,
              success: false,
            },
          });
          streamingSpan.end();
        } catch (spanError) {
          this.logger.warn(
            '[StreamingChatGenerationService] Failed to update Langfuse span with error'
          );
        }
      }

      // Send error chunk
      yield StreamingErrorHandler.createErrorChunk(error);
    }
  }

  // Confidence assessment removed; handled upstream

  /**
   * Stage 2: Generate the actual response (without confidence prefix)
   */
  private async *generateActualResponse(
    context: string,
    input: AgentInput,
    history: MemoryEntry[],
    promptConfig: any,
    llm: ChatOpenAI,
    config: any,
    startTime: number
  ): AsyncGenerator<StreamingChunk> {
    // Create Langfuse span for actual streaming generation
    const langfuseClient = getLangfuseClient();
    let actualGenerationSpan = null;

    if (langfuseClient) {
      try {
        actualGenerationSpan = langfuseClient.span({
          name: 'actual_streaming_generation',
          input: typeof input.input === 'string' ? input.input : JSON.stringify(input.input),
          metadata: {
            contextLength: context.length,
            historyEntries: history.length,
            temperature: promptConfig.temperature,
            streamingTimeout: config.streamingTimeout,
            maxRetries: config.maxRetries,
            chunkTimeout: config.chunkTimeout,
            tags: ['actual-streaming-generation', 'llm', 'openai'],
          },
        });
        this.logger.info('[StreamingChatGenerationService] Actual generation span created');
      } catch (error) {
        this.logger.warn(
          '[StreamingChatGenerationService] Failed to create actual generation span'
        );
      }
    }

    const generationStartTime = Date.now();
    let totalContent = '';
    let chunkCount = 0;

    try {
      // Build conversation messages for actual response
      const messages = this.buildConversationMessages(promptConfig, history);
      // Mirror non-streaming shape: provide context and query in a single human message
      messages.push(['human', `Context:\n{context}\n\nQuery: {query}`]);

      const promptWithContext = ChatPromptTemplate.fromMessages(messages);
      // Log formatted prompt with variables resolved for debugging
      try {
        const formatted = await (promptWithContext as any).format({ context, query: input.input });
        this.logger.debug('[StreamingChatGenerationService] formattedPrompt:', formatted);
      } catch (formatError) {
        this.logger.debug(
          '[StreamingChatGenerationService] Failed to format prompt for debug logging',
          { error: formatError instanceof Error ? formatError.message : String(formatError) }
        );
      }
      const responseChain = promptWithContext.pipe(llm);

      // Generate streaming response
      const stream = await responseChain.stream({ context, query: input.input });

      // Process streaming chunks
      for await (const chunk of stream) {
        const chunkContent = toText(chunk?.content);
        if (chunkContent) {
          totalContent += chunkContent;
          chunkCount++;

          // Record chunk for performance tracking
          this.performanceTracker.recordChunk();

          // Send content chunk
          yield this.createChunk('content', chunkContent, {
            chunkIndex: chunkCount,
            elapsedMs: Date.now() - startTime,
          });
        }
      }

      const generationTime = Date.now() - generationStartTime;

      // Send completion chunk
      yield this.createChunk('complete', '', {
        totalChunks: chunkCount,
        totalContentLength: totalContent.length,
        elapsedMs: Date.now() - startTime,
        generationTime,
      });

      // Update span with success
      if (actualGenerationSpan) {
        try {
          actualGenerationSpan.update({
            output: totalContent.substring(0, 1000),
            metadata: {
              executionTime: generationTime,
              totalChunks: chunkCount,
              totalContentLength: totalContent.length,
              success: true,
            },
          });
          actualGenerationSpan.end();
        } catch (error) {
          this.logger.warn(
            '[StreamingChatGenerationService] Failed to update actual generation span'
          );
        }
      }

      this.logger.info('Actual streaming generation completed', {
        totalChunks: chunkCount,
        totalContentLength: totalContent.length,
        generationTime,
        executor: 'streaming-chat-generation-service',
      });
    } catch (error) {
      const generationTime = Date.now() - generationStartTime;
      this.logger.error(error, 'Actual streaming generation failed', {
        generationTime,
        chunkCount,
        executor: 'streaming-chat-generation-service',
      });

      // Update span with error
      if (actualGenerationSpan) {
        try {
          actualGenerationSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: generationTime,
              chunkCount,
              success: false,
            },
          });
          actualGenerationSpan.end();
        } catch (spanError) {
          this.logger.warn(
            '[StreamingChatGenerationService] Failed to update actual generation span with error'
          );
        }
      }

      throw error;
    }
  }

  /**
   * Builds conversation messages for the prompt template
   */
  private buildConversationMessages(
    promptConfig: any,
    history: MemoryEntry[]
  ): Array<[string, string]> {
    // Escape curly braces in the system prompt so that literal JSON/examples
    // are not interpreted as template variables by LangChain format strings
    const safeSystemPrompt =
      typeof promptConfig.systemPrompt === 'string'
        ? promptConfig.systemPrompt.replaceAll('{', '{{').replaceAll('}', '}}')
        : '';

    const messages: Array<[string, string]> = [['system', safeSystemPrompt]];

    // Add conversation history if enabled
    if (promptConfig.includeConversationHistory && history.length > 0) {
      // Get conversation memory configuration
      const memoryConfig = promptConfig.conversationMemory || {
        maxHistoryEntries: 10,
        includeUserMessages: true,
        includeAssistantMessages: true,
        contextWindow: 4000,
        summarizeLongHistory: false,
      };

      // Filter and limit history based on configuration
      let filteredHistory = history;

      if (memoryConfig.maxHistoryEntries) {
        filteredHistory = history.slice(-memoryConfig.maxHistoryEntries);
      }

      // Add conversation history with proper formatting
      filteredHistory.forEach(entry => {
        if (entry.type === 'user' && memoryConfig.includeUserMessages) {
          messages.push(['human', entry.content]);
        } else if (entry.type === 'assistant' && memoryConfig.includeAssistantMessages) {
          messages.push(['ai', entry.content]);
        }
      });

      // Add conversation context summary if history is long
      if (memoryConfig.summarizeLongHistory && history.length > memoryConfig.maxHistoryEntries) {
        const summaryMessage = `[Previous conversation context: ${history.length - memoryConfig.maxHistoryEntries} earlier messages not shown for brevity]`;
        messages.push(['system', summaryMessage]);
      }
    }

    return messages;
  }

  /**
   * Create a streaming chunk
   */
  private createChunk(
    type: StreamingChunkType,
    content: string,
    metadata?: Record<string, any>
  ): StreamingChunk {
    return {
      type,
      content,
      metadata,
    };
  }

  /**
   * Validate input for streaming generation
   */
  static validateInput(
    context: string,
    input: AgentInput,
    history: MemoryEntry[]
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!context || context.trim().length === 0) {
      errors.push('Context cannot be empty');
    }

    if (!input || !input.input) {
      errors.push('Input cannot be empty');
    }

    if (context.length > 50000) {
      errors.push('Context is too long (max 50,000 characters)');
    }

    if (history.length > 20) {
      errors.push('History is too long (max 20 entries)');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get generation metrics from a completed streaming session
   */
  getGenerationMetrics(): Record<string, number> {
    const metrics = this.performanceTracker.getPerformanceMetrics();

    return {
      totalChunks: metrics.totalChunks,
      totalTimeMs: metrics.totalTimeMs,
      averageChunkTimeMs: metrics.averageChunkTimeMs,
      maxChunkTimeMs: metrics.maxChunkTimeMs,
      minChunkTimeMs: metrics.minChunkTimeMs,
      chunksPerSecond: metrics.chunksPerSecond,
      streamingEfficiency: metrics.streamingEfficiency,
    };
  }

  /**
   * Check if performance is acceptable
   */
  isPerformanceAcceptable(): { isAcceptable: boolean; issues: string[] } {
    return this.performanceTracker.isPerformanceAcceptable();
  }

  /**
   * Get performance summary for logging
   */
  getPerformanceSummary(): Record<string, any> {
    return this.performanceTracker.getPerformanceSummary();
  }
}
