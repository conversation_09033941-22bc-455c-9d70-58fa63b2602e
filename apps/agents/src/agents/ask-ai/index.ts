import { AbstractAgent } from '../../base/agent';
import {
  <PERSON><PERSON>n<PERSON>,
  AgentResult,
  AgentConfig,
  AgentArchetype,
  AgentCapability,
  AgentMemory,
  StandardAgentOutput,
  AgentChunk,
} from '../../base/types';
import { MCPConnectionManager } from '../../integration/mcp-server/connection-manager';
// import { LangGraphIntegration } from '../../integration/langgraph/langgraph-integration';
import { InMemoryAgentMemory, RedisBackedMemory } from '../../base/memory';

// Import the unified RAG implementation
import {
  UnifiedRAGExecutor,
  ResponseStrategyFactory,
  UnifiedRAGConfigBuilder,
  UnifiedRAGContext,
  ResponseOptions,
  SingleShotResult,
  StreamingResult,
  PromptConfigParams,
} from './core/unified-rag';

// Langfuse observability
import { getLogger, getLoggerWithTraceId } from '../../observability/global-logger';
import { SharedEnv } from '@anter/shared-services';
import { getLangfuseClient } from '../../observability/langfuse';

/**
 * AskAIAgent - The main AskAI agent using the unified RAG implementation
 * that consolidates streaming and non-streaming logic into a single, maintainable system.
 *
 * This version uses the unified RAG architecture that eliminates code duplication
 * while preserving distinct response patterns for streaming and non-streaming modes.
 */
export class AskAIAgent extends AbstractAgent {
  private connectionManager: MCPConnectionManager;
  private memory: AgentMemory;
  private responseMode: 'single-shot' | 'streaming';

  /** LangGraph integration for advanced workflow orchestration */
  // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
  // private langGraphIntegration: LangGraphIntegration;

  /** Unified RAG executor for shared core logic */
  private unifiedRAGExecutor: UnifiedRAGExecutor;

  constructor(
    connectionManager: MCPConnectionManager,
    responseMode: 'single-shot' | 'streaming' = 'single-shot',
    memory?: AgentMemory
  ) {
    const config: AgentConfig = {
      name: 'ask_ai',
      version: '0.0.1',
      description:
        'AskAI agent using unified RAG implementation that consolidates streaming and non-streaming logic.',
      archetype: AgentArchetype.HYBRID,
      capabilities: [
        AgentCapability.TOOL_USE,
        AgentCapability.LLM_INTEGRATION,
        AgentCapability.DATABASE_ACCESS,
        AgentCapability.MEMORY,
        AgentCapability.STREAMING,
      ],
    };
    super(config);

    this.connectionManager = connectionManager;
    this.responseMode = responseMode;

    // Use Redis-backed memory if Redis is available, otherwise fallback to in-memory
    if (connectionManager.getRedisClient()) {
      this.memory =
        memory ||
        new RedisBackedMemory(
          100, // maxEntries
          connectionManager.getRedisClient(),
          {
            redisKeyPrefix: 'conversation_memory:',
            redisTtlSeconds: 86400, // 24 hours
            logger: getLogger(),
          }
        );
    } else {
      this.memory = memory || new InMemoryAgentMemory(100);
    }

    // Initialize LangGraph integration for advanced workflow orchestration
    // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
    // this.langGraphIntegration = new LangGraphIntegration({
    //   enableDebug: SharedEnv.enableDebug,
    //   maxConcurrentWorkflows: 5,
    //   maxWorkflowDuration: 30000,
    //   maxStepsPerWorkflow: 10,
    //   fallbackEnabled: true,
    // });

    // Initialize unified RAG executor
    this.unifiedRAGExecutor = new UnifiedRAGExecutor(
      this.memory,
      getLogger(),
      this.connectionManager.getRedisClient()
    );
  }

  /**
   * Initialize the agent
   */
  protected async onInitialize(): Promise<void> {
    try {
      // Initialize LangGraph supervisor
      // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
      // this.langGraphIntegration.initializeSupervisor(this.connectionManager);

      getLogger().info('AskAIAgent initialized successfully', {
        agentName: this.config.name,
        version: this.config.version,
        responseMode: this.responseMode,
      });
    } catch (error) {
      getLogger().error(error, 'Failed to initialize AskAIAgent', {
        agentName: this.config.name,
      });
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  protected async onDestroy(): Promise<void> {
    try {
      await this.connectionManager.shutdown();
      getLogger().info('AskAIAgent destroyed successfully', {
        agentName: this.config.name,
      });
    } catch (error) {
      getLogger().error(error, 'Error during AskAIAgent destruction', {
        agentName: this.config.name,
      });
    }
  }

  /**
   * Handle single-shot requests
   */
  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    const sessionId = this.generateSessionId(input);
    const traceId = this.generateTraceId();
    const logger = getLoggerWithTraceId(traceId);

    logger.info('AskAIAgent doInvoke started', {
      sessionId: sessionId || 'none',
      responseMode: this.responseMode,
      inputLength: this.extractUserInput(input).length,
    });

    const startTime = Date.now();

    // Create Langfuse span for agent execution
    const langfuseClient = getLangfuseClient();
    let agentSpan = null;

    if (langfuseClient && traceId) {
      try {
        agentSpan = langfuseClient.span({
          traceId,
          name: 'ask_ai_agent_execution',
          input: this.extractUserInput(input),
          metadata: {
            sessionId,
            organizationId: input.organizationId,
            userId: input.userId,
            responseMode: this.responseMode,
            agentName: this.config.name,
            agentVersion: this.config.version,
            tags: ['ask-ai'],
          },
        });
        logger.info(`[AskAIAgent] Langfuse span created | sessionId=${sessionId}`);
      } catch (error) {
        logger.warn('[AskAIAgent] Failed to create Langfuse span');
      }
    }

    if (!input.organizationId) {
      throw new Error('organizationId is required for secure context isolation');
    }

    try {
      // Check if we should use LangGraph workflow
      // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
      // if (this.shouldUseLangGraphWorkflow(input)) {
      //   const result = await this.executeLangGraphWorkflow(input, sessionId, traceId);

      //   // Update span with success
      //   if (agentSpan) {
      //     try {
      //       agentSpan.update({
      //         output: result.output.response.substring(0, 1000),
      //         metadata: {
      //           executionTime: Date.now() - startTime,
      //           workflowType: 'langgraph',
      //           success: true,
      //         },
      //       });
      //       agentSpan.end();
      //     } catch (error) {
      //       logger.warn('[AskAIAgent] Failed to update Langfuse span');
      //     }
      //   }

      //   return result;
      // }

      // Use unified RAG for single-shot
      const result = await this.executeUnifiedRAG(input, sessionId, traceId, logger);

      // Update span with success
      if (agentSpan) {
        try {
          agentSpan.update({
            output: result.output.response.substring(0, 1000),
            metadata: {
              executionTime: Date.now() - startTime,
              workflowType: 'unified-rag',
              success: true,
            },
          });
          agentSpan.end();
        } catch (error) {
          logger.warn('[AskAIAgent] Failed to update Langfuse span');
        }
      }

      return result;
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'AskAIAgent doInvoke failed', {
        sessionId,
        elapsedMs,
      });

      // Update span with error
      if (agentSpan) {
        try {
          agentSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: elapsedMs,
              success: false,
            },
          });
          agentSpan.end();
        } catch (spanError) {
          logger.warn('[AskAIAgent] Failed to update Langfuse span with error');
        }
      }

      return this.createErrorResult(error as Error, sessionId, elapsedMs);
    }
  }

  /**
   * Handle streaming requests
   */
  protected async *doStream(input: AgentInput): AsyncGenerator<AgentChunk> {
    const sessionId = this.generateSessionId(input);
    const traceId = this.generateTraceId();
    const logger = getLoggerWithTraceId(traceId);

    logger.info('AskAIAgent doStream started', {
      sessionId,
      responseMode: this.responseMode,
      inputLength: this.extractUserInput(input).length,
    });

    const startTime = Date.now();

    // Create Langfuse span for agent execution
    const langfuseClient = getLangfuseClient();
    let agentSpan = null;

    if (langfuseClient && traceId) {
      try {
        agentSpan = langfuseClient.span({
          traceId,
          name: 'ask_ai_agent_streaming',
          input: this.extractUserInput(input),
          metadata: {
            sessionId,
            organizationId: input.organizationId,
            userId: input.userId,
            responseMode: this.responseMode,
            agentName: this.config.name,
            agentVersion: this.config.version,
            tags: ['ask-ai', 'agent', 'streaming'],
          },
        });
        logger.info(`[AskAIAgent] Langfuse span created | sessionId=${sessionId}`);
      } catch (error) {
        logger.warn('[AskAIAgent] Failed to create Langfuse span');
      }
    }

    if (!input.organizationId) {
      throw new Error('organizationId is required for secure context isolation');
    }

    try {
      // Check if we should use LangGraph workflow
      // TODO: Permanently remove this here. We will setup another agent and endpoint for this.
      // if (this.shouldUseLangGraphWorkflow(input)) {
      //   yield* this.streamLangGraphWorkflow(input, sessionId, traceId);

      //   // Update span with success
      //   if (agentSpan) {
      //     try {
      //       agentSpan.update({
      //         metadata: {
      //           executionTime: Date.now() - startTime,
      //           workflowType: 'langgraph',
      //           success: true,
      //         },
      //       });
      //       agentSpan.end();
      //     } catch (error) {
      //       logger.warn('[AskAIAgent] Failed to update Langfuse span');
      //     }
      //   }
      //   return;
      // }

      // Use unified RAG for streaming
      yield* this.streamUnifiedRAG(input, sessionId, traceId, logger);

      // Update span with success
      if (agentSpan) {
        try {
          agentSpan.update({
            metadata: {
              executionTime: Date.now() - startTime,
              workflowType: 'unified-rag',
              success: true,
            },
          });
          agentSpan.end();
        } catch (error) {
          logger.warn('[AskAIAgent] Failed to update Langfuse span');
        }
      }
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'AskAIAgent doStream failed', {
        sessionId,
        elapsedMs,
      });

      // Update span with error
      if (agentSpan) {
        try {
          agentSpan.update({
            metadata: {
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: elapsedMs,
              success: false,
            },
          });
          agentSpan.end();
        } catch (spanError) {
          logger.warn('[AskAIAgent] Failed to update Langfuse span with error');
        }
      }

      yield this.createErrorChunk(error as Error, sessionId, elapsedMs);
    }
  }

  /**
   * Execute unified RAG for single-shot responses
   */
  private async executeUnifiedRAG(
    input: AgentInput,
    sessionId: string | null,
    traceId: string,
    logger: any
  ): Promise<AgentResult> {
    const startTime = Date.now();

    try {
      // Get configuration based on response mode
      const config =
        this.responseMode === 'streaming'
          ? UnifiedRAGConfigBuilder.forStreaming()
          : UnifiedRAGConfigBuilder.forSingleShot();

      // Execute unified RAG pipeline
      // We get here the context with the search results from the redis embedding index
      const ragContext: UnifiedRAGContext = await this.unifiedRAGExecutor.execute(
        input,
        sessionId || null,
        config,
        traceId
      );

      // Generate response using appropriate strategy
      // NOTE: organizationId is required so consider to return error if this is not provided
      const promptConfigParams: PromptConfigParams = {
        organizationId: input.organizationId || 'anter-ai',
        promptName: SharedEnv.langfuseDefaultPrompt,
        version: 1,
      };

      const responseStrategy = ResponseStrategyFactory.createStrategy(
        this.responseMode,
        promptConfigParams
      );
      const responseOptions: ResponseOptions = {
        streaming: false,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        topK: config.topK,
        promptType: config.promptType,
      };

      const responseResult = await responseStrategy.generateResponse(ragContext, responseOptions);

      // Store assistant response
      await this.storeAssistantResponse(
        ragContext,
        responseResult as SingleShotResult | StreamingResult,
        logger
      );

      // Build and return result
      const totalTime = Date.now() - startTime;
      return this.buildAgentResult(
        responseResult as SingleShotResult | StreamingResult,
        ragContext,
        sessionId,
        totalTime
      );
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'Unified RAG execution failed', {
        sessionId,
        elapsedMs,
      });
      throw error;
    }
  }

  /**
   * Execute unified RAG for streaming responses
   */
  private async *streamUnifiedRAG(
    input: AgentInput,
    sessionId: string | null,
    traceId: string,
    logger: any
  ): AsyncGenerator<AgentChunk> {
    const startTime = Date.now();

    try {
      // Get configuration for streaming
      const config = UnifiedRAGConfigBuilder.forStreaming();

      // Execute unified RAG pipeline.
      const ragContext: UnifiedRAGContext = await this.unifiedRAGExecutor.execute(
        input,
        sessionId,
        config,
        traceId
      );

      // Generate streaming response
      // NOTE: organizationId is required so consider to return error if this is not provided
      const promptConfigParams: PromptConfigParams = {
        organizationId: input.organizationId || 'anter-ai',
        promptName: SharedEnv.langfuseDefaultPrompt,
        version: 1,
      };

      const responseStrategy = ResponseStrategyFactory.createStrategy(
        'streaming',
        promptConfigParams
      );
      const responseOptions: ResponseOptions = {
        streaming: true,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        topK: config.topK,
        promptType: config.promptType,
      };

      const responseResult = await responseStrategy.generateResponse(ragContext, responseOptions);

      // Handle streaming response
      if ('stream' in responseResult && responseResult.stream) {
        const stream = responseResult.stream as AsyncIterable<string>;
        for await (const chunk of stream) {
          const elapsedMs = Date.now() - startTime;
          yield this.createStreamingChunk(chunk, sessionId, elapsedMs);
        }
      } else {
        // Fallback to single chunk
        const elapsedMs = Date.now() - startTime;
        yield this.createStreamingChunk(responseResult.content, sessionId, elapsedMs);
      }

      // Store assistant response
      await this.storeAssistantResponse(
        ragContext,
        responseResult as SingleShotResult | StreamingResult,
        logger
      );

      // Send completion chunk
      const finalElapsedMs = Date.now() - startTime;
      yield this.createCompletionChunk(sessionId, finalElapsedMs, {
        tokensUsed: responseResult.metadata?.tokens || 0,
        toolsInvoked: [],
        cost: 0,
        warnings: [],
        sources: ragContext.searchResults.metadata.sources, // array of document ids
      });
    } catch (error) {
      const elapsedMs = Date.now() - startTime;
      logger.error(error, 'Unified RAG streaming failed', {
        sessionId,
        elapsedMs,
      });
      yield this.createErrorChunk(error as Error, sessionId, elapsedMs);

      // Send completion chunk even on error
      yield this.createCompletionChunk(sessionId, elapsedMs, {
        error: true,
        errorMessage: (error as Error).message,
      });
    }
  }

  /**
   * Execute LangGraph workflow for single-shot
   */
  // TODO: MOVE THIS TO ANOTHER AGENT AND ENDPOINT
  // private async executeLangGraphWorkflow(
  //   input: AgentInput,
  //   sessionId: string | null,
  //   traceId: string
  // ): Promise<AgentResult> {
  //   const result = await this.langGraphIntegration.executeWorkflow({
  //     sessionId: sessionId || undefined,
  //     input: this.extractUserInput(input),
  //     organizationId: input.organizationId || 'default',
  //     userId: input.userId,
  //     traceId,
  //   });

  //   return this.mapLangGraphResultToAgentResult(result, sessionId);
  // }

  /**
   * Execute LangGraph workflow for streaming
   */
  // TODO: MOVE THIS TO ANOTHER AGENT AND ENDPOINT
  // private async *streamLangGraphWorkflow(
  //   input: AgentInput,
  //   sessionId: string | null,
  //   traceId: string
  // ): AsyncGenerator<AgentChunk> {
  //   const startTime = Date.now();

  //   try {
  //     // For now, just return a single chunk with the result
  //     const result = await this.langGraphIntegration.executeWorkflow({
  //       sessionId: sessionId || undefined,
  //       input: this.extractUserInput(input),
  //       organizationId: input.organizationId || 'default',
  //       userId: input.userId,
  //       traceId,
  //     });

  //     yield this.createStreamingChunk(result.output.response, sessionId, result.executionTime);

  //     // Send completion chunk
  //     const finalElapsedMs = Date.now() - startTime;
  //     yield this.createCompletionChunk(sessionId, finalElapsedMs, {
  //       executionTime: result.executionTime,
  //       toolsInvoked: (result as any).toolsInvoked || [],
  //     });
  //   } catch (error) {
  //     const elapsedMs = Date.now() - startTime;
  //     yield this.createErrorChunk(error as Error, sessionId, elapsedMs);

  //     // Send completion chunk even on error
  //     yield this.createCompletionChunk(sessionId, elapsedMs, {
  //       error: true,
  //       errorMessage: (error as Error).message,
  //     });
  //   }
  // }

  /**
   * Store assistant response in memory
   */
  private async storeAssistantResponse(
    ragContext: UnifiedRAGContext,
    responseResult: SingleShotResult | StreamingResult,
    logger: any
  ): Promise<void> {
    try {
      const entry = {
        id: `${ragContext.executionContext.sessionId}_assistant_${Date.now()}`,
        timestamp: new Date(),
        type: 'assistant' as const,
        content: responseResult.content,
        metadata: {
          sessionId: ragContext.executionContext.sessionId,
          organizationId: ragContext.executionContext.organizationId,
          responseMode: this.responseMode,
          confidence: responseResult.metadata.confidence,
          tokens: responseResult.metadata.tokens,
        },
      };

      await this.memory.add(entry);

      logger.info('Assistant response stored', {
        sessionId: ragContext.executionContext.sessionId,
        responseLength: responseResult.content.length,
        responseMode: this.responseMode,
      });
    } catch (error) {
      logger.error(error, 'Failed to store assistant response', {
        sessionId: ragContext.executionContext.sessionId,
      });
    }
  }

  /**
   * Build agent result from response
   */
  private buildAgentResult(
    responseResult: SingleShotResult | StreamingResult,
    ragContext: UnifiedRAGContext,
    sessionId: string | null,
    totalTime: number
  ): AgentResult {
    const output: StandardAgentOutput = {
      response: responseResult.content,
      metadata: {
        session_id: sessionId || undefined,
        conversation_entries: ragContext.memory.length,
        semantic_search_results: ragContext.searchResults.metadata.resultsCount,
        semantic_search_average_score: ragContext.searchResults.averageScore,
      },
      metrics: {
        tokensUsed: responseResult.metadata.tokens || 0,
        latencyMs: responseResult.metadata.latency || totalTime,
        toolsInvoked: [],
        cost: 0,
        warnings: [],
      },
    };

    return {
      output,
      metadata: {
        tokensUsed: responseResult.metadata.tokens || 0,
        latencyMs: responseResult.metadata.latency || totalTime,
        toolsInvoked: [],
        cost: 0,
        warnings: [],
        sources: ragContext.searchResults.metadata.sources, // array of document ids
      },
    };
  }

  /**
   * Create streaming chunk
   */
  private createStreamingChunk(
    content: string,
    sessionId: string | null,
    elapsedMs: number
  ): AgentChunk {
    return {
      type: 'content',
      content,
      metadata: {
        sessionId: sessionId || undefined,
        elapsedMs,
        responseMode: this.responseMode,
      },
    };
  }

  /**
   * Create completion chunk to signal end of stream
   */
  private createCompletionChunk(
    sessionId: string | null,
    elapsedMs: number,
    metadata?: Record<string, any>
  ): AgentChunk {
    return {
      type: 'complete',
      content: null,
      metadata: {
        sessionId: sessionId || undefined,
        elapsedMs,
        responseMode: this.responseMode,
        completed: true,
        ...metadata,
      },
    };
  }

  /**
   * Create error result
   */
  private createErrorResult(
    error: Error,
    sessionId: string | null,
    elapsedMs: number
  ): AgentResult {
    const output: StandardAgentOutput = {
      response: 'I encountered an error while processing your request. Please try again.',
      metadata: {
        session_id: sessionId || undefined,
        error: error.message,
      },
      metrics: {
        tokensUsed: 0,
        latencyMs: elapsedMs,
        toolsInvoked: [],
        cost: 0,
        warnings: [error.message],
      },
    };

    return {
      output,
      metadata: {
        tokensUsed: 0,
        latencyMs: elapsedMs,
        toolsInvoked: [],
        cost: 0,
        warnings: [error.message],
      },
    };
  }

  /**
   * Create error chunk for streaming
   */
  private createErrorChunk(error: Error, sessionId: string | null, elapsedMs: number): AgentChunk {
    return {
      type: 'error',
      content: 'Error: ' + error.message,
      metadata: {
        sessionId: sessionId || undefined,
        error: error.message,
        elapsedMs,
      },
    };
  }

  /**
   * Determine if we should use LangGraph workflow
   */
  // private shouldUseLangGraphWorkflow(_input: AgentInput): boolean {
  //   // For now, always use unified RAG
  //   // In the future, this could check for specific conditions
  //   return false;
  // }

  /**
   * Generate session ID from input or return null if not provided
   */
  private generateSessionId(input: AgentInput): string | null {
    // Use sessionId from input if provided
    if (input.sessionId) {
      return input.sessionId;
    }

    // Log warning if no sessionId provided
    const logger = getLogger();
    logger.warn('No sessionId provided in AgentInput - conversation memory will not be available', {
      organizationId: input.organizationId,
      userId: input.userId,
      hasMetadata: !!input.metadata,
    });

    return null;
  }

  /**
   * Generate trace ID
   */
  private generateTraceId(): string {
    const crypto = require('crypto');
    const randomPart = crypto.randomBytes(4).toString('hex');
    return `trace_${Date.now()}_${randomPart}`;
  }

  /**
   * Extract user input from AgentInput
   */
  private extractUserInput(input: AgentInput): string {
    if (typeof input.input === 'string') {
      return input.input.trim();
    }

    if (typeof input.input === 'object' && input.input !== null) {
      const obj: any = input.input;
      const possibleFields = ['prompt', 'query', 'message', 'input', 'text', 'question', 'content'];

      for (const field of possibleFields) {
        if (obj[field] && typeof obj[field] === 'string' && obj[field].trim()) {
          return obj[field].trim();
        }
      }

      if (Object.keys(obj).length > 0) {
        return JSON.stringify(obj);
      }
    }

    if (input.input === undefined || input.input === null) {
      throw new Error('Input is undefined or null');
    }

    if (typeof input.input === 'number' || typeof input.input === 'boolean') {
      return String(input.input);
    }

    throw new Error('Invalid input format: user input not found in any expected field');
  }

  /**
   * Map LangGraph result to agent result
   */
  // private mapLangGraphResultToAgentResult(result: any, sessionId: string | null): AgentResult {
  //   const output: StandardAgentOutput = {
  //     response: result.output.response || 'No response generated',
  //     metadata: {
  //       session_id: sessionId || undefined,
  //       workflowId: result.workflowId,
  //       workflowType: result.workflowType,
  //     },
  //     metrics: {
  //       tokensUsed: result.output.metadata?.tokens || 0,
  //       latencyMs: result.executionTime || 0,
  //       toolsInvoked: result.toolsUsed || [],
  //       cost: 0,
  //       warnings: result.warning ? [result.warning] : [],
  //     },
  //   };

  //   return {
  //     output,
  //     metadata: {
  //       tokensUsed: result.output.metadata?.tokens || 0,
  //       latencyMs: result.executionTime || 0,
  //       toolsInvoked: result.toolsUsed || [],
  //       cost: 0,
  //       warnings: result.warning ? [result.warning] : [],
  //     },
  //   };
  // }
}
