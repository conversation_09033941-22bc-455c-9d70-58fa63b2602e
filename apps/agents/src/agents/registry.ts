import { AgentFactory } from '../base/agent-factory';
import { AskAIAgent } from './ask-ai';
import { ChatAgent } from './chat';
import { EchoAgent, defaultEchoConfig } from './echo';
import { SearchAgent } from './search';
import {
  MCPConnectionManager,
  IConnectionManager,
} from '../integration/mcp-server/connection-manager';
import { RedisBackedMemory } from '../base/memory/redis-backed-memory';
import { InMemoryAgentMemory } from '../base/memory/in-memory';

/**
 * Register all built-in agents with the agent factory
 */
export function registerAgents(): void {
  // Register AskAI agent for single-shot mode
  AgentFactory.register('ask_ai', async (_config, dependencies) => {
    if (!dependencies || typeof dependencies !== 'object') {
      throw new Error('Invalid dependencies: expected object with database property');
    }

    // The dependencies object has a database property with the connection methods
    const deps = dependencies as any;
    if (!deps.database || typeof deps.database.dbInternalWithTenant !== 'function') {
      throw new Error('Invalid dependencies: missing database.dbInternalWithTenant method');
    }

    // Create a proper IConnectionManager adapter from the database object
    const apiConnectionManager: IConnectionManager = {
      dbWithTenant: deps.database.dbWithTenant,
      dbInternalWithTenant: deps.database.dbInternalWithTenant,
      dbBypassRLS: async () => {
        throw new Error('dbBypassRLS not implemented in test environment');
      },
      dbInternalBypassRLS: async () => {
        throw new Error('dbInternalBypassRLS not implemented in test environment');
      },
      getDatabaseConfig: (schema: 'internal' | 'external') => ({
        connectionString: schema === 'internal' ? 'test-internal' : 'test-external',
        hasRLS: true,
        schema,
      }),
      log: deps.logger || console,
      redis: deps.database.redis,
    };

    const connectionManager = new MCPConnectionManager(apiConnectionManager);

    // Create Redis-backed memory if Redis is available, otherwise use in-memory
    const memory = deps.database.redis
      ? new RedisBackedMemory(100, deps.database.redis, { logger: deps.logger })
      : new InMemoryAgentMemory(100);

    return new AskAIAgent(connectionManager, 'single-shot', memory);
  });

  // Register AskAI agent for streaming mode
  AgentFactory.register('ask_ai_streaming', async (_config, dependencies) => {
    if (!dependencies || typeof dependencies !== 'object') {
      throw new Error('Invalid dependencies: expected object with database property');
    }

    // The dependencies object has a database property with the connection methods
    const deps = dependencies as any;
    if (!deps.database || typeof deps.database.dbInternalWithTenant !== 'function') {
      throw new Error('Invalid dependencies: missing database.dbInternalWithTenant method');
    }

    // Create a proper IConnectionManager adapter from the database object
    const apiConnectionManager: IConnectionManager = {
      dbWithTenant: deps.database.dbWithTenant,
      dbInternalWithTenant: deps.database.dbInternalWithTenant,
      dbBypassRLS: async () => {
        throw new Error('dbBypassRLS not implemented in test environment');
      },
      dbInternalBypassRLS: async () => {
        throw new Error('dbInternalBypassRLS not implemented in test environment');
      },
      getDatabaseConfig: (schema: 'internal' | 'external') => ({
        connectionString: schema === 'internal' ? 'test-internal' : 'test-external',
        hasRLS: true,
        schema,
      }),
      log: deps.logger || console,
      redis: deps.database.redis,
    };

    const connectionManager = new MCPConnectionManager(apiConnectionManager);

    // Create Redis-backed memory if Redis is available, otherwise use in-memory
    const memory = deps.database.redis
      ? new RedisBackedMemory(100, deps.database.redis, { logger: deps.logger })
      : new InMemoryAgentMemory(100);

    return new AskAIAgent(connectionManager, 'streaming', memory);
  });

  // Register legacy names for backward compatibility
  AgentFactory.register('ask_ai_v2', async (_config, dependencies) => {
    const connectionManager = new MCPConnectionManager(
      dependencies as unknown as IConnectionManager
    );

    // Create Redis-backed memory if Redis is available, otherwise use in-memory
    const deps = dependencies as any;
    const memory = deps.database?.redis
      ? new RedisBackedMemory(100, deps.database.redis, { logger: deps.logger })
      : new InMemoryAgentMemory(100);

    return new AskAIAgent(connectionManager, 'single-shot', memory);
  });

  AgentFactory.register('ask_ai_v2_refactored', async (_config, dependencies) => {
    const connectionManager = new MCPConnectionManager(
      dependencies as unknown as IConnectionManager
    );

    // Create Redis-backed memory if Redis is available, otherwise use in-memory
    const deps = dependencies as any;
    const memory = deps.database?.redis
      ? new RedisBackedMemory(100, deps.database.redis, { logger: deps.logger })
      : new InMemoryAgentMemory(100);

    return new AskAIAgent(connectionManager, 'single-shot', memory);
  });

  // Register Chat agent
  AgentFactory.register('chat', async (_config, dependencies) => {
    const connectionManager = new MCPConnectionManager(
      dependencies as unknown as IConnectionManager
    );
    return new ChatAgent(connectionManager);
  });

  // Register Echo agent
  AgentFactory.register('echo', async (_config, _dependencies) => {
    return new EchoAgent(defaultEchoConfig);
  });

  // Register Search agent
  AgentFactory.register('search', async (_config, _dependencies) => {
    return new SearchAgent();
  });
}

/**
 * Initialize the agent system
 * @param fastify Optional Fastify instance for additional configuration
 */
export function initializeAgentSystem(_fastify?: unknown): void {
  registerAgents();
}

// Export for backward compatibility
export const registerBuiltInAgents = registerAgents;
