import { MemoryEntry } from '../base/types';
import { RankedDocument } from '../types/chat';

export interface DomainRole {
  name: string;
  keywords: string[];
  tone: string;
  terminology: string[];
  responseStructure: string;
  expertiseAreas: string[];
}

export class PromptBuilderService {
  private domainRoles: DomainRole[] = [
    {
      name: 'Marketing',
      keywords: [
        'campaign',
        'brand',
        'marketing',
        'advertising',
        'promotion',
        'lead generation',
        'content marketing',
        'social media',
        'SEO',
        'analytics',
        'conversion',
        'engagement',
        'customer acquisition',
        'social media policy',
        'marketing KB',
      ],
      tone: 'Creative, persuasive, metrics-driven, and brand-focused',
      terminology: [
        'ROI',
        'CTR',
        'conversion funnel',
        'brand awareness',
        'customer journey',
        'touchpoints',
        'attribution',
        'A/B testing',
        'personas',
        'market segmentation',
      ],
      responseStructure:
        'Start with business impact, provide creative solutions, include metrics and KPIs, suggest testing approaches',
      expertiseAreas: [
        'digital marketing',
        'brand management',
        'customer acquisition',
        'marketing automation',
        'campaign optimization',
      ],
    },
    {
      name: 'Sales',
      keywords: [
        'sales',
        'revenue',
        'prospect',
        'deal',
        'pipeline',
        'quota',
        'CRM',
        'lead',
        'closing',
        'negotiation',
        'territory',
        'commission',
        'forecasting',
        'sales goals',
        'sales CRM',
        'track sales',
      ],
      tone: 'Results-oriented, persuasive, relationship-focused, and goal-driven',
      terminology: [
        'pipeline velocity',
        'close rate',
        'deal size',
        'sales cycle',
        'qualified leads',
        'prospect nurturing',
        'objection handling',
        'upselling',
        'cross-selling',
      ],
      responseStructure:
        'Focus on revenue impact, provide actionable tactics, include success metrics, emphasize relationship building',
      expertiseAreas: [
        'sales process optimization',
        'lead qualification',
        'deal negotiation',
        'customer relationship management',
        'sales forecasting',
      ],
    },
    {
      name: 'HR_Benefits_Compliance',
      keywords: [
        'employee',
        'hiring',
        'benefits',
        'policy',
        'performance',
        'training',
        'onboarding',
        'compensation',
        'workplace',
        'culture',
        'harassment',
        'compliance',
        'diversity',
        'retention',
        'DEI policy',
        'HR handbook',
        'employee handbook',
        'benefits enrollment',
        'PTO',
        'vacation',
        'sick leave',
      ],
      tone: 'Professional, empathetic, policy-focused, and people-centered',
      terminology: [
        'talent acquisition',
        'employee engagement',
        'performance management',
        'succession planning',
        'workplace culture',
        'compensation benchmarking',
        'compliance framework',
        'DEI initiatives',
        'benefits administration',
      ],
      responseStructure:
        'Reference relevant policies, emphasize legal compliance, provide supportive guidance, include process steps',
      expertiseAreas: [
        'talent management',
        'employee relations',
        'compliance',
        'organizational development',
        'workplace policies',
        'benefits administration',
      ],
    },
    {
      name: 'IT_Security',
      keywords: [
        'server',
        'network',
        'infrastructure',
        'deployment',
        'monitoring',
        'backup',
        'database',
        'system',
        'uptime',
        'performance',
        'troubleshooting',
        'maintenance',
        'security',
        'threat',
        'vulnerability',
        'risk',
        'audit',
        'encryption',
        'authentication',
        'authorization',
        'incident',
        'breach',
        'firewall',
        'malware',
        'phishing',
        'VPN',
        'password',
        'reset password',
        'security FAQ',
        'IT support',
      ],
      tone: 'Technical, precise, reliability-focused, security-first, and solution-oriented',
      terminology: [
        'SLA',
        'uptime',
        'scalability',
        'redundancy',
        'failover',
        'monitoring',
        'alerting',
        'capacity planning',
        'incident response',
        'change management',
        'threat landscape',
        'attack vectors',
        'security posture',
        'risk assessment',
        'security controls',
        'threat intelligence',
      ],
      responseStructure:
        'Provide technical specifications, include monitoring recommendations, emphasize reliability and security, suggest best practices',
      expertiseAreas: [
        'infrastructure management',
        'system monitoring',
        'incident response',
        'capacity planning',
        'service reliability',
        'cybersecurity',
        'risk management',
        'security architecture',
      ],
    },
    {
      name: 'Finance',
      keywords: [
        'budget',
        'cost',
        'revenue',
        'profit',
        'expense',
        'financial',
        'accounting',
        'invoice',
        'payment',
        'cash flow',
        'reporting',
        'audit',
        'tax',
        'investment',
        'expense report',
        'reimbursement',
        'purchase order',
      ],
      tone: 'Analytical, detail-oriented, compliance-focused, and cost-conscious',
      terminology: [
        'ROI',
        'CAPEX',
        'OPEX',
        'cash flow',
        'budget variance',
        'financial modeling',
        'cost center',
        'P&L',
        'balance sheet',
        'financial controls',
      ],
      responseStructure:
        'Include financial impact analysis, provide cost-benefit considerations, reference accounting standards, suggest financial controls',
      expertiseAreas: [
        'financial analysis',
        'budgeting',
        'cost management',
        'financial reporting',
        'compliance',
      ],
    },
    {
      name: 'Legal_Compliance',
      keywords: [
        'contract',
        'legal',
        'compliance',
        'regulation',
        'policy',
        'liability',
        'terms',
        'agreement',
        'lawsuit',
        'intellectual property',
        'privacy',
        'GDPR',
        'audit',
        'regulatory',
        'legal review',
        'contract review',
        'data privacy',
        'CCPA',
        'SOX',
      ],
      tone: 'Precise, cautious, regulation-focused, and risk-averse',
      terminology: [
        'regulatory compliance',
        'legal framework',
        'contractual obligations',
        'liability assessment',
        'due diligence',
        'intellectual property',
        'data privacy',
      ],
      responseStructure:
        'Reference applicable regulations, emphasize compliance requirements, include risk mitigation strategies, suggest legal review when appropriate',
      expertiseAreas: [
        'regulatory compliance',
        'contract management',
        'risk assessment',
        'intellectual property',
        'data privacy',
      ],
    },
    {
      name: 'Virtual_Assistant',
      keywords: [
        'help',
        'information',
        'question',
        'support',
        'general',
        'what',
        'how',
        'when',
        'where',
        'why',
        'can you',
        'please',
        'thank you',
        'hello',
        'hi',
      ],
      tone: 'Professional, helpful, neutral, and adaptive',
      terminology: [
        'company policy',
        'documentation',
        'best practices',
        'guidelines',
        'procedures',
        'resources',
        'support team',
        'contact information',
      ],
      responseStructure:
        'Provide clear, accurate information based on available documentation, guide to appropriate teams when needed, maintain professional tone',
      expertiseAreas: [
        'general company information',
        'policy guidance',
        'department routing',
        'basic support',
        'information retrieval',
      ],
    },
  ];

  /**
   * Build RAG-enhanced prompt combining user input with relevant document context
   */
  buildRAGPrompt(
    userInput: string,
    relevantDocs: RankedDocument[],
    conversationHistory: MemoryEntry[] = [],
    maxTokens: number = 16000
  ): string {
    if (!relevantDocs || relevantDocs.length === 0) {
      // Still include conversation history even without documents
      if (conversationHistory.length > 0) {
        return this.buildPromptWithHistory(userInput, conversationHistory);
      }
      // Use Virtual Assistant role when no context is available
      return this.buildBasicPrompt(userInput);
    }

    // Build conversation history section
    const historySection = this.buildConversationHistory(conversationHistory);

    // Build context section from relevant documents
    const contextSection = this.buildDocumentContext(relevantDocs, maxTokens * 0.6); // Reserve 60% for context

    // Identify the most appropriate domain role for this query
    const identifiedRole = this.identifyDomainRole(userInput, relevantDocs);
    const roleAdaptationInstructions = this.buildRoleAdaptationInstructions(
      identifiedRole,
      // TODO: Get company name from the user input
      ''
    );

    // Build the enhanced prompt with dynamic role adaptation
    const enhancedPrompt = `You are a helpful support resource serving employees across our organization. You have the ability to dynamically adapt your role and expertise based on the nature of each query, effectively becoming a subject matter expert from the most relevant department.

${roleAdaptationInstructions}

${historySection ? `CONVERSATION HISTORY:\n${historySection}\n` : ''}COMPANY KNOWLEDGE CONTEXT:
${contextSection}

USER QUERY:
${userInput}

CORE INSTRUCTIONS:
- Use the provided company knowledge context to inform your response
- Consider the conversation history when providing your answer
- Cite specific documents when referencing information (use document names)
- If the context doesn't contain relevant information, clearly state this
- Maintain factual accuracy while adapting to the identified role
- Use department-specific terminology and communication patterns naturally
- Provide practical, actionable advice relevant to the department's expertise

RESPONSE REQUIREMENTS:
1. Respond naturally with ${identifiedRole.name} expertise
2. Use appropriate terminology and knowledge from this domain internally
3. Structure your response to be helpful and informative
4. Include relevant best practices and guidance from this domain
5. Maintain a conversational, professional tone
6. Route to appropriate teams when specialized support is needed
7. Never refer to yourself as an AI, assistant, or any type of automated system

Please provide a helpful response using your knowledge and expertise:`;

    return enhancedPrompt;
  }

  /**
   * Identify the most appropriate domain role for a given query
   */
  private identifyDomainRole(userInput: string, relevantDocs: RankedDocument[]): DomainRole {
    const lowercaseInput = userInput.toLowerCase();
    const documentContent = relevantDocs.map(doc => doc.content.toLowerCase()).join(' ');
    const combinedText = `${lowercaseInput} ${documentContent}`;

    // Score each domain based on keyword matches
    const roleScores = this.domainRoles.map(role => {
      const keywordMatches = role.keywords.filter((keyword: string) =>
        combinedText.includes(keyword.toLowerCase())
      ).length;

      // Weight user input higher than document content
      const inputMatches = role.keywords.filter((keyword: string) =>
        lowercaseInput.includes(keyword.toLowerCase())
      ).length;

      return {
        role,
        score: inputMatches * 2 + keywordMatches, // Input matches weighted 2x
      };
    });

    // Sort by score and return the highest scoring role
    roleScores.sort((a: any, b: any) => b.score - a.score);

    // If no clear match, default to Virtual Assistant
    return roleScores[0].score > 0
      ? roleScores[0].role
      : this.domainRoles.find((r: DomainRole) => r.name === 'Virtual_Assistant')!;
  }

  /**
   * Build role-specific adaptation instructions
   */
  private buildRoleAdaptationInstructions(role: DomainRole, companyName: string): string {
    return `ROLE ADAPTATION - You are now a ${role.name} Expert:

DOMAIN FOCUS: ${role.name}
COMMUNICATION TONE: ${role.tone}
EXPERTISE AREAS: ${role.expertiseAreas.join(', ')}

KEY TERMINOLOGY TO USE:
${role.terminology.map((term: string) => `- ${term}`).join('\n')}

RESPONSE STRUCTURE: ${role.responseStructure}

ROLE-SPECIFIC EXAMPLES:
${this.getRoleSpecificExamples(role, companyName)}

Remember to maintain this role perspective throughout your response while staying factually accurate and helpful.`;
  }

  /**
   * Get role-specific communication examples
   */
  private getRoleSpecificExamples(role: DomainRole, companyName: string): string {
    // Standard general prompt for all domains
    const standardPrompt = `You are a professional, helpful support resource for ${companyName}, supporting employees across departments including HR, Benefits, IT, Security, Compliance, Sales, and Marketing.

Always provide information based on official company documentation and policy. If a question requires specialist support, politely guide the employee to the right team or contact. Do not speculate or guess.

Your tone is clear, respectful, and company-appropriate. Maintain privacy and confidentiality at all times. Never refer to yourself as an AI, virtual assistant, or any type of assistant.`;

    const examples: { [key: string]: string } = {
      Marketing: `${standardPrompt}

Domain-specific examples:
- "This could impact our customer acquisition funnel by..."
- "I'd recommend A/B testing this approach to optimize conversion rates..."
- "Looking at our attribution data, we should consider..."`,

      Sales: `${standardPrompt}

Domain-specific examples:
- "This directly impacts our revenue pipeline by..."
- "From a deal velocity standpoint..."
- "I'd suggest qualifying this opportunity based on our BANT criteria..."
- "This could help us exceed our quarterly targets by..."`,

      HR_Benefits_Compliance: `${standardPrompt}

Domain-specific examples:
- "According to our employee handbook policy..."
- "This aligns with our talent development framework..."
- "From a compliance perspective, we need to ensure..."
- "I recommend following our standard HR process which includes..."`,

      IT_Security: `${standardPrompt}

Domain-specific examples:
- "This could impact our SLA commitments..."
- "I recommend implementing monitoring and alerting for..."
- "Based on our capacity planning models..."`,

      Finance: `${standardPrompt}

Domain-specific examples:
- "The financial impact analysis shows..."
- "From a budget variance perspective..."
- "This investment would provide an ROI of..."
- "According to our financial controls framework..."`,

      Legal_Compliance: `${standardPrompt}

Domain-specific examples:
- "This could create potential liability exposure..."
- "Our contractual obligations require..."
- "I recommend a legal review to ensure..."`,

      Virtual_Assistant: `${standardPrompt}

Domain-specific examples:
- "Based on our company documentation, here's what I can share about..."
- "For specific assistance with this matter, I recommend contacting..."
- "According to our general policies and procedures..."
- "You'll want to connect with the appropriate team for specialized support..."`,
    };

    return (
      examples[role.name] ||
      `${standardPrompt}

Domain-specific examples:
- "This aligns with our ${role.name.toLowerCase()} best practices..."
- "I recommend following our standard ${role.name.toLowerCase()} procedures..."`
    );
  }

  /**
   * Build conversation history section
   */
  private buildConversationHistory(conversationHistory: MemoryEntry[]): string {
    if (!conversationHistory || conversationHistory.length === 0) {
      return '';
    }

    const historyParts: string[] = [];

    // Include only user and assistant messages, skip system messages
    conversationHistory
      .filter(entry => entry.type === 'user' || entry.type === 'assistant')
      .forEach(entry => {
        const role = entry.type === 'user' ? 'Human' : 'Assistant';
        const content =
          typeof entry.content === 'string' ? entry.content : JSON.stringify(entry.content);

        // Truncate very long messages in history
        // TODO: Remove this when we have a better way to handle long messages
        const truncatedContent = content.length > 500 ? content.substring(0, 497) + '...' : content;

        historyParts.push(`${role}: ${truncatedContent}`);
      });

    return historyParts.join('\n');
  }

  /**
   * Build basic prompt when no documents or history are available
   */
  buildBasicPrompt(userInput: string): string {
    // Default to Virtual Assistant role when no context is available
    const virtualAssistantRole = this.domainRoles.find(
      (r: DomainRole) => r.name === 'Virtual_Assistant'
    )!;
    const roleAdaptationInstructions = this.buildRoleAdaptationInstructions(
      virtualAssistantRole,
      'AskInfosec'
    );

    return `You are a helpful support resource serving employees across our organization. You have the ability to dynamically adapt your role and expertise based on the nature of each query, effectively becoming a subject matter expert from the most relevant department.

${roleAdaptationInstructions}

USER QUERY:
${userInput}

CORE INSTRUCTIONS:
- Provide helpful, accurate information based on general company knowledge
- If the query requires specialized knowledge, guide the user to the appropriate team
- Maintain a conversational, professional tone
- Do not speculate or provide information you're not certain about
- Never refer to yourself as an AI, assistant, or any type of automated system

Please provide a helpful response:`;
  }

  /**
   * Build basic prompt when no documents or history are available
   */
  buildBasicPromptWithContext(userInput: string, context: string): string {
    // Default to Virtual Assistant role when no context is available
    const virtualAssistantRole = this.domainRoles.find(
      (r: DomainRole) => r.name === 'Virtual_Assistant'
    )!;
    const roleAdaptationInstructions = this.buildRoleAdaptationInstructions(
      virtualAssistantRole,
      'AskInfosec'
    );

    return `You are a helpful support resource serving employees across our organization. You have the ability to dynamically adapt your role and expertise based on the nature of each query, effectively becoming a subject matter expert from the most relevant department.

${roleAdaptationInstructions}

CONTEXT:
${context}

USER QUERY:
${userInput}

CORE INSTRUCTIONS:
- Provide helpful, accurate information based on general company knowledge
- If the query requires specialized knowledge, guide the user to the appropriate team
- Maintain a conversational, professional tone
- Do not speculate or provide information you're not certain about
- Never refer to yourself as an AI, assistant, or any type of automated system

Please provide a helpful response:`;
  }

  /**
   * Build prompt with only conversation history (no documents)
   */
  private buildPromptWithHistory(userInput: string, conversationHistory: MemoryEntry[]): string {
    const historySection = this.buildConversationHistory(conversationHistory);

    if (!historySection) {
      return userInput;
    }

    // Identify the most appropriate domain role for this query (without documents)
    const identifiedRole = this.identifyDomainRole(userInput, []);
    const roleAdaptationInstructions = this.buildRoleAdaptationInstructions(
      identifiedRole,
      'AskInfosec'
    );

    return `You are a helpful support resource serving employees across our organization. You have the ability to dynamically adapt your role and expertise based on the nature of each query, effectively becoming a subject matter expert from the most relevant department.

${roleAdaptationInstructions}

CONVERSATION HISTORY:
${historySection}

USER QUERY:
${userInput}

CORE INSTRUCTIONS:
- Consider the conversation history when providing your answer
- Maintain factual accuracy while adapting to the identified role
- Use department-specific terminology and communication patterns naturally
- Provide practical, actionable advice relevant to the department's expertise
- If you need more specific information, guide the user to the appropriate team
- Never refer to yourself as an AI, assistant, or any type of automated system

RESPONSE REQUIREMENTS:
1. Respond naturally with ${identifiedRole.name} expertise
2. Use appropriate terminology and knowledge from this domain internally
3. Structure your response to be helpful and informative
4. Include relevant best practices and guidance from this domain
5. Maintain a conversational, professional tone
6. Route to appropriate teams when specialized support is needed
7. Never refer to yourself as an AI, assistant, or any type of automated system

Please provide a helpful response using your knowledge and expertise:`;
  }

  /**
   * Build document context section with token management
   */
  buildDocumentContext(relevantDocs: RankedDocument[], maxContextTokens: number): string {
    if (!relevantDocs || relevantDocs.length === 0) {
      return 'No relevant documents found in company knowledge base.';
    }

    const contextParts: string[] = [];
    let currentTokenCount = 0;

    for (const doc of relevantDocs) {
      // Format document entry
      const docHeader = `Document: "${doc.name}" (Relevance: ${(doc.similarityScore * 100).toFixed(1)}%)`;
      const docContent = this.truncateDocumentContent(doc.content, 4000); // Max ~4000 chars per doc
      const docEntry = `${docHeader}\n${docContent}\n`;

      // Estimate token count (rough: 1 token ≈ 4 characters)
      const entryTokens = Math.ceil(docEntry.length / 4);

      // Check if adding this document would exceed token limit
      if (currentTokenCount + entryTokens > maxContextTokens) {
        // Try to fit a truncated version
        const remainingTokens = maxContextTokens - currentTokenCount;
        const remainingChars = remainingTokens * 4;

        if (remainingChars > 200) {
          // Only add if we have meaningful space
          const truncatedContent = this.truncateDocumentContent(
            doc.content,
            remainingChars - docHeader.length - 20
          );
          contextParts.push(`${docHeader}\n${truncatedContent}...\n`);
        }
        break;
      }

      contextParts.push(docEntry);
      currentTokenCount += entryTokens;
    }

    if (contextParts.length === 0) {
      return 'Relevant documents found but content too large to include in context.';
    }

    // Add summary header
    const summary = `Found ${relevantDocs.length} relevant document(s). Showing ${contextParts.length}:\n\n`;
    return summary + contextParts.join('---\n');
  }

  /**
   * Truncate document content while preserving readability
   */
  truncateDocumentContent(content: string, maxLength: number): string {
    if (!content || content.length <= maxLength) {
      return content;
    }

    // Try to find a good breaking point (sentence or paragraph)
    const truncated = content.substring(0, maxLength);

    // Look for sentence endings near the truncation point
    const lastSentence = Math.max(
      truncated.lastIndexOf('. '),
      truncated.lastIndexOf('.\n'),
      truncated.lastIndexOf('? '),
      truncated.lastIndexOf('! ')
    );

    if (lastSentence > maxLength * 0.7) {
      // Good sentence break found
      return truncated.substring(0, lastSentence + 1);
    }

    // Look for paragraph breaks
    const lastParagraph = truncated.lastIndexOf('\n\n');
    if (lastParagraph > maxLength * 0.5) {
      return truncated.substring(0, lastParagraph);
    }

    // Look for any line break
    const lastLine = truncated.lastIndexOf('\n');
    if (lastLine > maxLength * 0.8) {
      return truncated.substring(0, lastLine);
    }

    // Look for word boundary
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > maxLength * 0.8) {
      return truncated.substring(0, lastSpace);
    }

    // Hard truncation as last resort
    return truncated;
  }

  /**
   * Estimate token count for text (rough approximation)
   */
  estimateTokenCount(text: string): number {
    if (!text) return 0;

    // Rough estimation: 1 token ≈ 4 characters for English text
    // This is conservative and accounts for tokenizer variations
    return Math.ceil(text.length / 4);
  }
}
