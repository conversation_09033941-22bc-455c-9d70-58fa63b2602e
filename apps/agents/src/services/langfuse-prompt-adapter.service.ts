import { getChatPromptConfig } from '../integration/langfuse/prompt-manager';
import { ChatPromptConfig } from '../types/prompts/chat-prompts';
import { getLogger } from '../observability/global-logger';

/**
 * Adapter service that uses the updated prompt-manager.ts to retrieve prompts from Langfuse
 * This provides a bridge between the new Langfuse-based prompt manager and existing chat generation services
 */
export class LangfusePromptAdapterService {
  private logger: any;
  private static instance: LangfusePromptAdapterService;

  private constructor() {
    this.logger = getLogger();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): LangfusePromptAdapterService {
    if (!LangfusePromptAdapterService.instance) {
      LangfusePromptAdapterService.instance = new LangfusePromptAdapterService();
    }
    return LangfusePromptAdapterService.instance;
  }

  /**
   * Get chat prompt configuration from Langfuse by name
   * This method maintains compatibility with the existing prompt adapter interface
   */
  async getPromptConfig(
    organizationId: string,
    promptName: string,
    version: number,
    projectId?: string
  ): Promise<ChatPromptConfig> {
    try {
      this.logger.info(
        `[LangfusePromptAdapter] Looking for prompt: name=${promptName}, version=${version}, orgId=${organizationId}, projectId=${projectId}`
      );

      // Use the updated prompt manager to get prompt from Langfuse
      const promptConfig = await getChatPromptConfig({
        promptName,
        logger: this.logger,
      });

      if (!promptConfig) {
        // Fallback to default configuration if prompt not found in Langfuse
        this.logger.warn(
          `[LangfusePromptAdapter] Prompt ${promptName} not found in Langfuse, using default configuration`
        );
        return this.getDefaultPromptConfig(promptName);
      }

      this.logger.info(`[LangfusePromptAdapter] Retrieved prompt ${promptName} from Langfuse`);
      return promptConfig;
    } catch (error) {
      this.logger.error(
        error,
        `[LangfusePromptAdapter] Failed to retrieve prompt ${promptName} from Langfuse`
      );

      // Fallback to default configuration on error
      return this.getDefaultPromptConfig(promptName);
    }
  }

  /**
   * Get default prompt configuration as fallback
   */
  private getDefaultPromptConfig(promptName: string): ChatPromptConfig {
    this.logger.info(`[LangfusePromptAdapter] Using default configuration for ${promptName}`);

    return {
      systemPrompt: `You are a helpful AI assistant. Respond to the user's query based on the provided context.`,
      temperature: 0.7,
      includeConversationHistory: true,
      responseFormat: 'natural',
    };
  }

  /**
   * Update prompt configuration (not implemented for Langfuse adapter)
   */
  async updatePrompt(_promptType: string, _config: Partial<ChatPromptConfig>): Promise<void> {
    this.logger.warn(`[LangfusePromptAdapter] Update prompt not implemented for Langfuse adapter`);
    throw new Error('Update prompt not supported in Langfuse adapter');
  }

  /**
   * Get available prompt types (not implemented for Langfuse adapter)
   */
  async getAvailablePromptTypes(): Promise<string[]> {
    this.logger.warn(
      `[LangfusePromptAdapter] Get available prompt types not implemented for Langfuse adapter`
    );
    return ['text', 'chat'];
  }

  /**
   * Close the adapter (no cleanup needed for Langfuse adapter)
   */
  async close(): Promise<void> {
    this.logger.info(`[LangfusePromptAdapter] Adapter closed`);
  }
}

// Export singleton instance
export const langfusePromptAdapter = LangfusePromptAdapterService.getInstance();
