import { PromptManager, InMemoryPromptStorage } from '@anter/mcp-tools';
import { getLogger } from '../observability/global-logger';
import { ChatPromptConfig, ChatPromptType } from '../types/prompts/chat-prompts';
import { existsSync } from 'fs';
import { join } from 'path';

/**
 * Adapter service that bridges the new prompt management system with existing chat generation services
 * This maintains backward compatibility while using the new prompt management infrastructure
 */
export class PromptAdapterService {
  private promptManager: PromptManager;
  private logger = getLogger();
  private static instance: PromptAdapterService;

  private constructor() {
    // Look for prompts.json in the API data directory
    // When running from apps/api, process.cwd() is /path/to/workspace/apps/api
    // So we need to go up one level and then to apps/api/data/prompts.json
    const workspaceRoot = process.cwd();
    const apiDataFilePath = workspaceRoot.includes('/apps/api')
      ? join(workspaceRoot, 'data/prompts.json') // We're already in apps/api
      : join(workspaceRoot, 'apps/api/data/prompts.json'); // We're in workspace root

    // Verify that the prompts.json file exists
    if (!existsSync(apiDataFilePath)) {
      throw new Error(
        `Prompts data file not found: ${apiDataFilePath}. Please ensure the file exists and is accessible.`
      );
    }

    this.logger.info(`[PromptAdapter] Loading prompts from: ${apiDataFilePath}`);

    const storage = new InMemoryPromptStorage({
      dataFilePath: apiDataFilePath,
      autoSave: true,
    });

    this.promptManager = new PromptManager(
      {
        defaultOrganizationId: 'anter-ai',
        defaultProjectId: undefined,
        cacheEnabled: true,
        cacheTTL: 300000, // 5 minutes
        autoIncrementVersion: true,
      },
      storage,
      this.logger
    );
  }

  /**
   * Get singleton instance
   */
  static getInstance(): PromptAdapterService {
    if (!PromptAdapterService.instance) {
      PromptAdapterService.instance = new PromptAdapterService();
    }
    return PromptAdapterService.instance;
  }

  /**
   * Get chat prompt configuration - maintains backward compatibility
   */
  async getPromptConfig(
    organizationId: string,
    promptName: string,
    version: number,
    projectId?: string
  ): Promise<ChatPromptConfig> {
    try {
      // Debug logging
      this.logger.info(
        `[PromptAdapter] Looking for prompt: orgId=${organizationId}, name=${promptName}, version=${version}, projectId=${projectId}`
      );

      // Get all prompts to debug
      const allPrompts = await this.promptManager.searchPrompts({});
      this.logger.info(`[PromptAdapter] Total prompts loaded: ${allPrompts.length}`);
      allPrompts.forEach(p => {
        this.logger.info(
          `[PromptAdapter] Available prompt: ${p.name} (org: ${p.organizationId}, version: ${p.version})`
        );
      });

      // Get prompt from the new management system
      const prompt = await this.promptManager.getPromptByName(
        organizationId,
        promptName,
        version,
        projectId,
        true // Use cache
      );

      if (!prompt) {
        throw new Error(
          `[PromptAdapter] Prompt ${promptName} not found in prompts.json. Please ensure the prompt exists in the data file.`
        );
      }

      this.logger.info(
        `[PromptAdapter] Prompt ${prompt.name} found using version ${prompt.version} and config ${JSON.stringify(prompt.config)}`
      );

      // Convert to ChatPromptConfig format
      const config: ChatPromptConfig = {
        systemPrompt: prompt.prompt as string,
        temperature: prompt.config.temperature ?? 0.7,
        includeConversationHistory: prompt.config.includeConversationHistory ?? true,
        responseFormat: prompt.config.responseFormat ?? 'natural',
      };

      this.logger.debug(`[PromptAdapter] Retrieved prompt ${promptName} from management system`);
      return config;
    } catch (error) {
      this.logger.error(error, `[PromptAdapter] Error getting prompt config for ${promptName}`);
      throw error;
    }
  }

  /**
   * Update a prompt in the management system
   */
  async updatePrompt(promptType: ChatPromptType, config: Partial<ChatPromptConfig>): Promise<void> {
    try {
      const prompt = await this.promptManager.getPromptByName(
        'anter-ai',
        promptType,
        1,
        'chat-agents'
      );

      if (!prompt) {
        throw new Error(`Prompt ${promptType} not found`);
      }

      await this.promptManager.updatePrompt(prompt.id, {
        prompt: config.systemPrompt,
        config: {
          temperature: config.temperature,
          includeConversationHistory: config.includeConversationHistory,
          responseFormat: config.responseFormat,
          conversationMemory: config.conversationMemory,
        },
        commitMessage: `Updated ${promptType} prompt configuration`,
      });

      this.logger.info(`[PromptAdapter] Updated prompt ${promptType}`);
    } catch (error) {
      this.logger.error(error, `[PromptAdapter] Failed to update prompt ${promptType}`);
      throw error;
    }
  }

  /**
   * Get all available prompt types
   */
  async getAvailablePromptTypes(): Promise<string[]> {
    try {
      const prompts = await this.promptManager.searchPrompts({
        organizationId: 'anter-ai',
        projectId: 'chat-agents',
      });

      return prompts.map((p: any) => p.name);
    } catch (error) {
      this.logger.error(error, `[PromptAdapter] Failed to get available prompt types`);
      return ['text', 'chat'];
    }
  }

  /**
   * Close the prompt manager
   */
  async close(): Promise<void> {
    try {
      await this.promptManager.close();
      this.logger.info('[PromptAdapter] Closed prompt manager');
    } catch (error) {
      this.logger.error(error, `[PromptAdapter] Failed to close prompt manager`);
    }
  }
}

// Export singleton instance
export const promptAdapter = PromptAdapterService.getInstance();
