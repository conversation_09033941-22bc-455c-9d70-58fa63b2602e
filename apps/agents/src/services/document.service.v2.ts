import { MCPConnectionManager } from '../integration/mcp-server/connection-manager';
import {
  DocumentAnalyzerService,
  DocumentProcessor,
  ProcessedDocument,
  DocumentCacheService,
} from '@anter/mcp-tools';
import { PromptBuilderService } from './prompt-builder.service';
import { DocumentSanitizerService } from '@anter/mcp-tools';
import { DocumentFetcherAdapter } from './document-fetcher.adapter';
import { getLogger } from '../observability/global-logger';

export class DocumentServiceV2 {
  private promptBuilder = new PromptBuilderService();
  private sanitizer = new DocumentSanitizerService();
  private processor: DocumentProcessor;
  private cacheService: DocumentCacheService;
  private logger = getLogger();

  constructor(
    private connectionManager: MCPConnectionManager,
    _analyzer?: DocumentAnalyzerService
  ) {
    this.processor = new DocumentProcessor(this.sanitizer);

    // Create document fetcher adapter
    const documentFetcher = new DocumentFetcherAdapter(this.connectionManager, this.processor);

    // Get Redis client if available
    let redisClient: any = undefined;
    if (typeof (this.connectionManager as any).getRedisClient === 'function') {
      try {
        redisClient = (this.connectionManager as any).getRedisClient();
      } catch (_) {
        redisClient = undefined;
      }
    }

    // Create generic cache service
    this.cacheService = new DocumentCacheService(documentFetcher, redisClient);
    this.cacheService.setLogger(this.logger);
  }

  getPromptBuilder() {
    return this.promptBuilder;
  }

  async retrieveAllDocuments(
    orgId: string,
    userId?: string,
    traceId?: string
  ): Promise<{ documents: ProcessedDocument[]; source: 'memory' | 'redis' | 'database' }> {
    this.logger.info('[DocumentServiceV2] retrieveAllDocuments called', { orgId });

    // Step 1: Get documents from cache service (this handles the full flow)
    const cacheResult = await this.cacheService.getDocuments(orgId, userId, traceId);
    const { documents, source, cacheStats } = cacheResult;

    // Step 2: Document indexing is now handled externally via /v1/external/embeddings/update
    // We assume embeddings are already up-to-date and go straight to semantic search
    this.logger.info('[DocumentServiceV2] Documents retrieved, proceeding to semantic search', {
      documentCount: documents.length,
      source,
      cacheStats,
    });

    this.logger.info('[DocumentServiceV2] Returning documents', {
      documentCount: documents.length,
      source,
      cacheStats,
    });
    return { documents, source };
  }

  /**
   * List only the document IDs for the given organization
   */
  async listDocumentIds(orgId: string, userId?: string, traceId?: string): Promise<string[]> {
    const res = await this.connectionManager.executeToolCall(
      orgId,
      'get_all_internal_documents',
      { includeIdsOnly: true, limit: 10000 },
      userId,
      traceId
    );

    if (res.success && Array.isArray(res.data?.documents)) {
      return res.data.documents.map((d: any) => d.id);
    }

    this.logger.error(res, '[DocumentServiceV2] Unexpected listDocumentIds result');
    return [];
  }

  /**
   * Fetch full documents by their IDs and process them
   */
  async fetchDocuments(
    orgId: string,
    userId: string | undefined,
    ids: string[],
    traceId?: string
  ): Promise<ProcessedDocument[]> {
    if (ids.length === 0) return [];

    const res = await this.connectionManager.executeToolCall(
      orgId,
      'get_all_internal_documents',
      { filter: { ids }, limit: ids.length, offset: 0, includeFullContent: true },
      userId,
      traceId
    );

    if (!res.success || !Array.isArray(res.data?.documents)) {
      throw new Error('Invalid document retrieval response for fetchDocuments');
    }

    const processedNested = await Promise.all(
      res.data.documents.map((d: any) => this.processor.processDocumentContent(d))
    );

    return processedNested.flat().filter(Boolean) as ProcessedDocument[];
  }
}
