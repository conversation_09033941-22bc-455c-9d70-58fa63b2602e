import { DocumentFetcher, DocumentMetadata, ProcessedDocument } from '@anter/mcp-tools';
import { MCPConnectionManager } from '../integration/mcp-server/connection-manager';
import { DocumentProcessor } from '@anter/mcp-tools';
import { getLogger } from '../observability/global-logger';

/**
 * Adapter that implements the DocumentFetcher interface for the agents app.
 * Bridges the gap between the generic DocumentCacheService and the agents-specific MCPConnectionManager.
 */
export class DocumentFetcherAdapter implements DocumentFetcher {
  private processor: DocumentProcessor;
  private logger = getLogger();

  constructor(
    private connectionManager: MCPConnectionManager,
    processor?: DocumentProcessor
  ) {
    this.processor = processor || new DocumentProcessor();
  }

  /**
   * Fetch document IDs and timestamps from the database
   */
  async fetchDocumentIds(
    orgId: string,
    userId?: string,
    traceId?: string
  ): Promise<DocumentMetadata[]> {
    this.logger.info('[DocumentFetcherAdapter] fetchDocumentIds called', { orgId });

    const res = await this.connectionManager.executeToolCall(
      orgId,
      'get_all_internal_documents',
      { includeIdsOnly: true, limit: 10000 }, // High limit to fetch all
      userId,
      traceId
    );

    this.logger.info('[DocumentFetcherAdapter] executeToolCall result', {
      success: res.success,
      hasData: !!res.data,
      hasDocuments: Array.isArray(res.data?.documents),
      previewData: res.data?.documents,
    });

    if (res.success && Array.isArray(res.data?.documents)) {
      this.logger.info('[DocumentFetcherAdapter] Returning document IDs', {
        documentCount: res.data.documents.length,
      });
      return res.data.documents;
    }

    this.logger.warn('[DocumentFetcherAdapter] Unexpected get_all_documents result shape', {
      success: res.success,
      hasData: !!res.data,
      hasDocuments: Array.isArray(res.data?.documents),
    });
    throw new Error('Invalid document metadata retrieval response');
  }

  /**
   * Fetch full documents by their IDs and process them
   */
  async fetchDocumentsByIds(
    orgId: string,
    userId: string | undefined,
    ids: string[],
    traceId?: string
  ): Promise<ProcessedDocument[]> {
    if (ids.length === 0) return [];

    this.logger.info('[DocumentFetcherAdapter] fetchDocumentsByIds called', {
      orgId,
      documentCount: ids.length,
    });

    // Use get_all_internal_documents tool with ids filter
    const res = await this.connectionManager.executeToolCall(
      orgId,
      'get_all_internal_documents',
      { filter: { ids }, limit: ids.length, offset: 0, includeFullContent: true },
      userId,
      traceId
    );

    if (!res.success || !Array.isArray(res.data?.documents)) {
      this.logger.error(
        res,
        '[DocumentFetcherAdapter] Invalid document retrieval response for missing docs'
      );
      throw new Error('Invalid document retrieval response for missing docs');
    }

    this.logger.info('[DocumentFetcherAdapter] Processing documents', {
      documentCount: res.data.documents.length,
    });

    // Process each doc
    const documents = Array.isArray(res.data.documents) ? res.data.documents : [];
    const processedNested = await Promise.all(
      documents.map((d: any) => this.processor.processDocumentContent(d))
    );

    const finalDocs = processedNested
      .flat()
      .filter((doc): doc is ProcessedDocument => doc !== null && doc !== undefined);
    this.logger.info('[DocumentFetcherAdapter] Documents processed', {
      originalCount: documents.length,
      processedCount: finalDocs.length,
    });

    return finalDocs;
  }
}
