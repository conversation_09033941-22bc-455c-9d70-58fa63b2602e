export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
  sanitized?: any;
}

export interface AgentInputSchema {
  type: 'object' | 'string' | 'number' | 'boolean' | 'array';
  required?: boolean;
  properties?: Record<string, AgentInputSchema>;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  enum?: any[];
  items?: AgentInputSchema;
}

export interface SqlQueryValidationOptions {
  allowedOperations: string[];
  maxQueryLength: number;
  allowedTables?: string[];
  blockedPatterns?: RegExp[];
  requireWhere?: boolean;
}

/**
 * Comprehensive validation utilities for agent inputs
 * Provides SQL injection prevention, input sanitization, and schema validation
 */
export class AgentValidator {
  private static readonly DEFAULT_SQL_OPTIONS: SqlQueryValidationOptions = {
    allowedOperations: ['SELECT'],
    maxQueryLength: 5000,
    blockedPatterns: [
      /;\s*(DROP|DELETE|UPDATE|INSERT|ALTER|CREATE|TRUNCATE|GRANT|REVOKE)/i,
      /UNION.*SELECT/i,
      /\/\*.*\*\//g, // Block comments
      /--.*$/gm, // Block line comments in dangerous contexts
    ],
    requireWhere: false,
  };

  /**
   * Validate SQL query for safety and compliance
   */
  static validateSqlQuery(
    query: string,
    options?: Partial<SqlQueryValidationOptions>
  ): ValidationResult {
    const opts = { ...AgentValidator.DEFAULT_SQL_OPTIONS, ...options };
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic input validation
    if (!query || typeof query !== 'string') {
      return {
        valid: false,
        errors: ['Query must be a non-empty string'],
      };
    }

    // Length validation
    if (query.length > opts.maxQueryLength) {
      errors.push(`Query exceeds maximum length of ${opts.maxQueryLength} characters`);
    }

    const trimmedQuery = query.trim();
    const upperQuery = trimmedQuery.toUpperCase();

    // Check for empty query
    if (trimmedQuery.length === 0) {
      errors.push('Query cannot be empty');
    }

    // Operation validation
    const operation = AgentValidator.extractOperation(upperQuery);
    if (!opts.allowedOperations.includes(operation)) {
      errors.push(
        `Operation '${operation}' is not allowed. Allowed operations: ${opts.allowedOperations.join(', ')}`
      );
    }

    // Pattern-based security checks
    for (const pattern of opts.blockedPatterns || []) {
      if (pattern.test(query)) {
        errors.push(`Query contains blocked pattern: ${pattern.source}`);
      }
    }

    // Advanced SQL injection checks
    const injectionCheck = AgentValidator.checkSqlInjectionPatterns(query);
    if (!injectionCheck.valid) {
      errors.push(...injectionCheck.errors);
    }

    // Table validation (if specified)
    if (opts.allowedTables && opts.allowedTables.length > 0) {
      const tableCheck = AgentValidator.validateTableAccess(query, opts.allowedTables);
      if (!tableCheck.valid) {
        errors.push(...tableCheck.errors);
      }
    }

    // WHERE clause requirement
    if (opts.requireWhere && operation === 'SELECT') {
      if (!upperQuery.includes('WHERE')) {
        warnings.push('Query does not include WHERE clause, which may return large result sets');
      }
    }

    // Additional security checks
    const securityCheck = AgentValidator.performAdvancedSecurityChecks(query);
    if (!securityCheck.valid) {
      errors.push(...securityCheck.errors);
    }
    if (securityCheck.warnings) {
      warnings.push(...securityCheck.warnings);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined,
      sanitized: errors.length === 0 ? trimmedQuery : undefined,
    };
  }

  /**
   * Validate agent context data
   */
  static validateAgentContext(context: any): ValidationResult {
    const errors: string[] = [];

    if (!context || typeof context !== 'object') {
      return {
        valid: false,
        errors: ['Context must be a valid object'],
      };
    }

    // Check for required fields
    if (!context.organizationId) {
      errors.push('Context must include organizationId');
    }

    // Validate organization ID format
    if (context.organizationId && typeof context.organizationId !== 'string') {
      errors.push('organizationId must be a string');
    }

    // Validate user ID if present
    if (context.userId && typeof context.userId !== 'string') {
      errors.push('userId must be a string');
    }

    // Check for suspicious data
    const suspiciousCheck = AgentValidator.checkForSuspiciousData(context);
    if (!suspiciousCheck.valid) {
      errors.push(...suspiciousCheck.errors);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate input against schema
   */
  static validateAgainstSchema(input: any, schema: AgentInputSchema): ValidationResult {
    const errors: string[] = [];

    try {
      AgentValidator.validateValue(input, schema, '', errors);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Schema validation error';
      errors.push(errorMessage);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Sanitize general input data
   */
  static sanitizeInput(input: any): { sanitized: any; warnings: string[] } {
    const warnings: string[] = [];

    if (typeof input === 'string') {
      // Remove potentially dangerous characters
      let sanitized = input
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Control characters
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();

      // Check for base64 encoded content (potential data exfiltration)
      if (AgentValidator.looksLikeBase64(sanitized)) {
        warnings.push('Input contains base64-like content');
      }

      // Check for URL patterns
      if (AgentValidator.containsUrls(sanitized)) {
        warnings.push('Input contains URL patterns');
      }

      return { sanitized, warnings };
    }

    if (typeof input === 'object' && input !== null) {
      const sanitized: any = Array.isArray(input) ? [] : {};

      for (const [key, value] of Object.entries(input)) {
        // Sanitize key
        const cleanKey = key.replace(/[^\w_-]/g, '');
        if (cleanKey !== key) {
          warnings.push(`Sanitized key: ${key} -> ${cleanKey}`);
        }

        // Recursively sanitize value
        const result = AgentValidator.sanitizeInput(value);
        sanitized[cleanKey] = result.sanitized;
        warnings.push(...result.warnings);
      }

      return { sanitized, warnings };
    }

    return { sanitized: input, warnings };
  }

  // Private helper methods

  private static extractOperation(query: string): string {
    const trimmed = query.trim();
    const firstWord = trimmed.split(/\s+/)[0];
    return firstWord || 'UNKNOWN';
  }

  private static checkSqlInjectionPatterns(query: string): ValidationResult {
    const errors: string[] = [];

    // Common SQL injection patterns
    const injectionPatterns = [
      /(\'|\'\''|\'|;\s*\')/i, // Quote manipulation
      /((\%27)|(\'))\s*((\%6F)|o|(\%4F))\s*((\%72)|r|(\%52))/i, // 'or' patterns
      /((\%27)|(\'))\s*((\%6F)|o|(\%4F))\s*((\%72)|r|(\%52))\s*((\%27)|(\'))/i,
      /exec(\s|\+)+(s|x)p\w+/i, // Stored procedure execution
      /union\s+select/i, // UNION SELECT attacks
      /select\s+.*\s+from\s+information_schema/i, // Information schema queries
      /select\s+.*\s+from\s+sys\./i, // System table queries
      /benchmark\s*\(/i, // MySQL benchmark attacks
      /sleep\s*\(/i, // MySQL sleep attacks
      /waitfor\s+delay/i, // SQL Server delay attacks
    ];

    for (const pattern of injectionPatterns) {
      if (pattern.test(query)) {
        errors.push(`Potential SQL injection pattern detected: ${pattern.source}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static validateTableAccess(query: string, allowedTables: string[]): ValidationResult {
    const errors: string[] = [];

    // Extract table names from FROM and JOIN clauses
    const tablePattern = /(?:FROM|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi;
    const matches = Array.from(query.matchAll(tablePattern));

    for (const match of matches) {
      const tableName = match[1].toLowerCase();
      const allowed = allowedTables.some(allowed => allowed.toLowerCase() === tableName);
      if (!allowed) {
        errors.push(`Access to table '${tableName}' is not allowed`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static performAdvancedSecurityChecks(query: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for nested queries (potential for complexity attacks)
    const nestedSelectCount = (query.match(/SELECT/gi) || []).length;
    if (nestedSelectCount > 3) {
      warnings.push('Query contains multiple nested SELECT statements');
    }

    // Check for excessive wildcards
    if ((query.match(/\*/g) || []).length > 2) {
      warnings.push('Query contains multiple wildcard selections');
    }

    // Check for function calls that might be dangerous
    const dangerousFunctions = [
      'LOAD_FILE',
      'INTO OUTFILE',
      'INTO DUMPFILE',
      'SYSTEM',
      'SHELL',
      'EVAL',
      'EXECUTE',
      'EXEC',
      'OPENQUERY',
      'OPENROWSET',
    ];

    for (const func of dangerousFunctions) {
      if (query.toUpperCase().includes(func)) {
        errors.push(`Dangerous function detected: ${func}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  private static validateValue(
    value: any,
    schema: AgentInputSchema,
    path: string,
    errors: string[]
  ): void {
    if (schema.required && (value === undefined || value === null)) {
      errors.push(`${path} is required`);
      return;
    }

    if (value === undefined || value === null) {
      return; // Optional field
    }

    switch (schema.type) {
      case 'string':
        if (typeof value !== 'string') {
          errors.push(`${path} must be a string`);
          return;
        }
        if (schema.minLength && value.length < schema.minLength) {
          errors.push(`${path} must be at least ${schema.minLength} characters`);
        }
        if (schema.maxLength && value.length > schema.maxLength) {
          errors.push(`${path} must be at most ${schema.maxLength} characters`);
        }
        if (schema.pattern && !schema.pattern.test(value)) {
          errors.push(`${path} does not match required pattern`);
        }
        if (schema.enum && !schema.enum.includes(value)) {
          errors.push(`${path} must be one of: ${schema.enum.join(', ')}`);
        }
        break;

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          errors.push(`${path} must be a valid number`);
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          errors.push(`${path} must be a boolean`);
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          errors.push(`${path} must be an array`);
          return;
        }
        if (schema.items) {
          value.forEach((item, index) => {
            AgentValidator.validateValue(item, schema.items!, `${path}[${index}]`, errors);
          });
        }
        break;

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          errors.push(`${path} must be an object`);
          return;
        }
        if (schema.properties) {
          for (const [key, propSchema] of Object.entries(schema.properties)) {
            const propPath = path ? `${path}.${key}` : key;
            AgentValidator.validateValue(value[key], propSchema, propPath, errors);
          }
        }
        break;
    }
  }

  private static checkForSuspiciousData(data: any): ValidationResult {
    const errors: string[] = [];

    const dataStr = JSON.stringify(data);

    // Check for script injection
    if (/<script|javascript:|data:/i.test(dataStr)) {
      errors.push('Suspicious script content detected in context');
    }

    // Check for file system paths
    if (/\.\.\/|\\\\|\/etc\/|C:\\|~\//i.test(dataStr)) {
      errors.push('Suspicious file system paths detected');
    }

    // Check for command injection patterns
    if (/[;&|`$()]/g.test(dataStr)) {
      errors.push('Potential command injection patterns detected');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static looksLikeBase64(str: string): boolean {
    // Simple base64 detection
    const base64Pattern = /^[A-Za-z0-9+/]+=*$/;
    return str.length > 20 && str.length % 4 === 0 && base64Pattern.test(str);
  }

  private static containsUrls(str: string): boolean {
    const urlPattern = /(https?|ftp):\/\/[^\s/$.?#].[^\s]*/gi;
    return urlPattern.test(str);
  }
}

/**
 * Pre-defined schemas for common agent inputs
 */
export const AGENT_INPUT_SCHEMAS = {
  askAI: {
    type: 'object' as const,
    properties: {
      query: {
        type: 'string' as const,
        required: true,
        minLength: 1,
        maxLength: 5000,
      },
    },
  },

  contextUpdate: {
    type: 'object' as const,
    properties: {
      key: {
        type: 'string' as const,
        required: true,
        pattern: /^[a-zA-Z0-9_-]+$/,
        maxLength: 100,
      },
      data: {
        type: 'object' as const,
        required: true,
      },
    },
  },
};
