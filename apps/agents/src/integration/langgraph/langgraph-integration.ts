/**
 * LangGraphIntegration is the high-level façade that lets agents run
 * LangGraph workflows **safely** inside the AskInfosec runtime.
 *
 * Key features (2025-06 update):
 * • Delegates execution to {@link LangGraphSupervisor}, which now includes
 *   cycle-detection via a `completedSteps` state property that guarantees
 *   every agent node runs at most once per workflow – eliminating infinite
 *   loops and related `GRAPH_RECURSION_LIMIT` errors.
 * • Uses {@link WorkflowResourceManager} to enforce hard limits on
 *   concurrency, total runtime and step count, whilst automatically
 *   cleaning up `AbortController` listeners to prevent memory leaks.
 * • Provides first-class observability: each workflow receives a unique ID
 *   and execution metrics (latency, queue pressure, utilisation) are logged
 *   in real time when `enableDebug` is on.
 * • Graceful-degradation: if a workflow fails or resource limits are hit the
 *   class returns a structured fallback {@link WorkflowResult} instead of
 *   throwing – allowing the caller to fall back to the classic RAG path.
 *
 * @remarks
 * The class is intentionally stateless apart from its internal
 * {@link WorkflowResourceManager}. You are expected to call
 * {@link initializeSupervisor} once per process with a shared
 * {@link MCPConnectionManager} so that database connections and tool
 * sessions are pooled.
 */

import { LangGraphSupervisor } from '../../orchestration/langgraph/supervisor';
import { IdiomaticLangGraphSupervisor } from '../../orchestration/langgraph/agents/idiomatic-supervisor';
import { WorkflowResourceManager } from '../../orchestration/langgraph/workflow-resource-manager';
import { MCPConnectionManager } from '../mcp-server/connection-manager';
import { WorkflowInput, WorkflowResult } from '../../orchestration/langgraph/types';
import { setMaxListeners } from 'events';

// Langfuse observability
import { getLangfuseClient } from '../../observability/langfuse';
import { ILogger } from '../mcp-server/mcp-bridge-enhanced';
import { getLogger } from '../../observability/global-logger';

setMaxListeners(25);

export interface LangGraphConfig {
  enableDebug?: boolean;
  maxConcurrentWorkflows?: number;
  maxWorkflowDuration?: number;
  maxStepsPerWorkflow?: number;
  fallbackEnabled?: boolean;
  useIdiomaticSupervisor?: boolean; // New option to use idiomatic supervisor
  outputMode?: 'full_history' | 'last_message';
  enablePersistence?: boolean;
  maxConversationLength?: number;
}

/**
 * Production-grade LangGraph integration with proper resource management.
 *
 * This class provides:
 * - Proper concurrency control with queue management
 * - Individual resource tracking per workflow
 * - Automatic cleanup and memory leak prevention
 * - Comprehensive monitoring and health checks
 * - Graceful degradation under high load
 *
 * @example
 * ```typescript
 * const integration = new LangGraphIntegration({
 *   enableDebug: true,
 *   maxConcurrentWorkflows: 5,
 *   maxWorkflowDuration: 30000,
 *   maxStepsPerWorkflow: 10
 * });
 *
 * const result = await integration.executeWorkflow({
 *   sessionId: 'session123',
 *   input: 'What are the password requirements?',
 *   organizationId: 'org456'
 * });
 * ```
 */
export class LangGraphIntegration {
  private resourceManager: WorkflowResourceManager;
  private supervisor: LangGraphSupervisor | null = null;
  private idiomaticSupervisor: IdiomaticLangGraphSupervisor | null = null;
  private config: Required<LangGraphConfig>;
  private isInitialized = false;
  private isShuttingDown = false;
  private logger: ILogger;

  constructor(config: LangGraphConfig = {}) {
    this.logger = getLogger();
    this.config = {
      enableDebug: process.env.NODE_ENV === 'development',
      maxConcurrentWorkflows: 10,
      maxWorkflowDuration: 30000, // 30 seconds
      maxStepsPerWorkflow: 15,
      fallbackEnabled: true,
      useIdiomaticSupervisor: false, // Switch to manual supervisor (more reliable)
      outputMode: 'last_message',
      enablePersistence: true,
      maxConversationLength: 100,
      ...config,
    };

    // Initialize resource manager with production-grade settings
    this.resourceManager = new WorkflowResourceManager({
      maxConcurrentWorkflows: this.config.maxConcurrentWorkflows,
      maxWorkflowDuration: this.config.maxWorkflowDuration,
      maxStepsPerWorkflow: this.config.maxStepsPerWorkflow,
      cleanupInterval: 60000, // 1 minute cleanup cycle
      abortSignalMaxListeners: 10, // Reasonable limit per workflow
    });

    if (this.config.enableDebug) {
      this.logger.info(
        '[LangGraphIntegration] Initialized with production-grade resource management'
      );
    }
  }

  /**
   * Initialize the supervisor with connection manager
   */
  initializeSupervisor(connectionManager: MCPConnectionManager): void {
    if (!this.isInitialized) {
      if (this.config.useIdiomaticSupervisor) {
        // Use the idiomatic supervisor (experimental - has state update issues)
        this.idiomaticSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
          enableDebug: this.config.enableDebug,
          fallbackEnabled: this.config.fallbackEnabled,
          maxExecutionTime: this.config.maxWorkflowDuration,
          outputMode: this.config.outputMode,
          enablePersistence: this.config.enablePersistence,
          maxConversationLength: this.config.maxConversationLength,
        });

        if (this.config.enableDebug) {
          this.logger.info(
            '[LangGraphIntegration] Idiomatic LangGraph supervisor initialized (experimental)'
          );
        }
      } else {
        // Use the reliable manual supervisor (recommended)
        this.supervisor = new LangGraphSupervisor(connectionManager, {
          enableDebug: this.config.enableDebug,
          fallbackEnabled: this.config.fallbackEnabled,
          maxExecutionTime: this.config.maxWorkflowDuration,
        });

        if (this.config.enableDebug) {
          this.logger.info(
            '[LangGraphIntegration] Manual LangGraph supervisor initialized (recommended)'
          );
        }
      }

      this.isInitialized = true;
    }
  }

  /**
   * Executes a workflow with production-grade resource management
   *
   * @param input - Workflow input containing user query and context
   * @returns Promise resolving to workflow result
   * @throws Error if resource limits are exceeded or workflow fails
   */
  async executeWorkflow(input: WorkflowInput): Promise<WorkflowResult> {
    if (this.isShuttingDown) {
      throw new Error('LangGraphIntegration is shutting down, cannot execute new workflows');
    }

    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.logger.info(`[LangGraphIntegration] Workflow started | workflowId=${workflowId}`);

    let resource: any = null;

    // Create Langfuse span for workflow execution observability
    const langfuseClient = getLangfuseClient();
    let workflowSpan = null;

    if (langfuseClient && input.traceId) {
      try {
        workflowSpan = langfuseClient.span({
          traceId: input.traceId,
          name: 'langgraph_workflow',
          input: input.input, // Add the actual workflow input
          metadata: {
            workflowId,
            sessionId: input.sessionId,
            organizationId: input.organizationId,
            useIdiomaticSupervisor: this.config.useIdiomaticSupervisor,
            tags: ['langgraph', 'workflow', 'chat', 'ask-ai-v2', 'ask-ai'],
          },
        });
        this.logger.info(`[LangGraphIntegration] Langfuse span created | workflowId=${workflowId}`);
      } catch (error) {
        this.logger.warn('[LangGraphIntegration] Failed to create Langfuse span');
      }
    }

    try {
      if (this.config.enableDebug) {
        this.logger.info(`[LangGraphIntegration] Starting workflow ${workflowId}`);
        this.logger.debug(`[LangGraphIntegration] Input: ${JSON.stringify(input, null, 2)}`);
      }

      // Acquire resource slot
      resource = await this.resourceManager.acquireWorkflowSlot(workflowId);

      // Check resource availability before execution
      const metrics = this.resourceManager.getMetrics();
      if (metrics.utilization > 95) {
        this.logger.warn(
          `[LangGraphIntegration] High resource utilization: ${metrics.utilization}%`
        );

        if (metrics.queued > 20) {
          throw new Error(
            'System at capacity - too many queued workflows. Please try again later.'
          );
        }
      }

      const startTime = Date.now();
      // Execute workflow with resource management
      const result = await this.executeWorkflowWithResource(
        input,
        workflowId,
        resource,
        workflowSpan
      );
      const executionTime = Date.now() - startTime;

      // Update span with success
      if (workflowSpan) {
        try {
          workflowSpan.update({
            output: result.output.response.substring(0, 1000), // First 1000 chars of response text
            metadata: {
              executionTime,
              toolsUsed: result.toolsUsed?.length || 0,
              success: true,
            },
          });
          workflowSpan.end();
        } catch (error) {
          this.logger.warn('[LangGraphIntegration] Failed to update Langfuse span');
        }
      }

      if (this.config.enableDebug) {
        this.logger.info(
          `[LangGraphIntegration] Workflow ${workflowId} completed in ${executionTime}ms`
        );
        this.logger.info(
          `[LangGraphIntegration] Result => has output? [result.output.response]: ${result.output && result.output.response ? 'Yes' : 'No'}`
        );
        this.logResourceMetrics();
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Update span with error
      if (workflowSpan) {
        try {
          workflowSpan.update({
            metadata: {
              error: errorMessage,
              success: false,
            },
          });
          workflowSpan.end();
        } catch (spanError) {
          this.logger.error(
            spanError,
            '[LangGraphIntegration] Failed to update Langfuse span with error'
          );
        }
      }

      if (this.config.enableDebug) {
        this.logger.error(errorMessage, `[LangGraphIntegration] Workflow ${workflowId} failed`);
      }

      // Return fallback result if enabled
      if (this.config.fallbackEnabled) {
        return this.createFallbackResult(input, errorMessage);
      }

      throw error;
    } finally {
      // Always clean up resources
      if (resource) {
        await resource.cleanup();
      }
    }
  }

  /**
   * Executes workflow with proper resource management using LangGraph supervisor
   */
  private async executeWorkflowWithResource(
    input: WorkflowInput,
    workflowId: string,
    resource: any,
    workflowSpan: any | null
  ): Promise<WorkflowResult> {
    if (!this.supervisor && !this.idiomaticSupervisor) {
      throw new Error('LangGraph supervisor not initialized. Call initializeSupervisor() first.');
    }

    try {
      // Check resource limits before execution
      if (!this.resourceManager.incrementStepCount(workflowId)) {
        throw new Error(`Workflow ${workflowId} terminated due to resource limits`);
      }

      // Check abort signal
      if (resource.abortController.signal.aborted) {
        throw new Error(`Workflow ${workflowId} was aborted`);
      }

      if (this.config.enableDebug) {
        this.logger.info(`[LangGraphIntegration] Executing real LangGraph workflow ${workflowId}`);
      }

      // Execute the actual LangGraph supervisor workflow
      const result =
        this.config.useIdiomaticSupervisor && this.idiomaticSupervisor
          ? await this.idiomaticSupervisor.executeWorkflow(input, workflowSpan)
          : await this.supervisor!.executeWorkflow(input);

      if (this.config.enableDebug) {
        this.logger.info(`[LangGraphIntegration] LangGraph workflow ${workflowId} completed`);
      }

      // Return the supervisor result, ensuring it matches WorkflowResult interface
      return {
        ...result,
        workflowId,
        workflowType: 'langgraph-supervisor',
        mcpCompliant: true,
        contextDocIds: result.debugInfo?.performanceMetrics?.documentsProcessed
          ? Array.from(
              { length: result.debugInfo.performanceMetrics.documentsProcessed },
              (_, i) => `doc-${i}`
            )
          : [],
        embeddingCalls: 1, // Estimate
        documentCount: result.debugInfo?.performanceMetrics?.documentsProcessed || 0,
        conversationEntries: 0,
      };
    } catch (error) {
      throw new Error(`LangGraph workflow execution failed: ${error}`);
    }
  }

  /**
   * Creates a fallback result for failed workflows
   */
  private createFallbackResult(_input: WorkflowInput, errorMessage: string): WorkflowResult {
    return {
      output: {
        response:
          'I encountered an issue processing your request. Please try rephrasing your question or try again in a moment.',
        metadata: {},
        metrics: {},
      },
      executionTime: 0,
      workflowId: `fallback_${Date.now()}`,
      workflowType: 'fallback',
      toolsUsed: [],
      mcpCompliant: true,
      contextDocIds: [],
      embeddingCalls: 0,
      documentCount: 0,
      conversationEntries: 0,
      warning: `Fallback response due to: ${errorMessage}`,
      metadata: {
        warnings: [errorMessage],
      },
    };
  }

  /**
   * Gets comprehensive system metrics including resource utilization
   */
  getMetrics() {
    const resourceMetrics = this.resourceManager.getMetrics();

    return {
      ...resourceMetrics,
      isInitialized: this.isInitialized,
      isShuttingDown: this.isShuttingDown,
      configuration: {
        maxConcurrentWorkflows: this.config.maxConcurrentWorkflows,
        maxWorkflowDuration: this.config.maxWorkflowDuration,
        maxStepsPerWorkflow: this.config.maxStepsPerWorkflow,
        debugEnabled: this.config.enableDebug,
        fallbackEnabled: this.config.fallbackEnabled,
      },
    };
  }

  /**
   * Gets a list of currently active workflow IDs.
   * Useful for monitoring and debugging workflow execution.
   *
   * @returns Array of active workflow IDs
   */
  getActiveWorkflows(): string[] {
    const metrics = this.getMetrics();
    return Array.from({ length: metrics.active }, (_, i) => `workflow_${i}`);
  }

  /**
   * Gets basic information about workflow execution.
   * Useful for debugging and monitoring workflow progress.
   *
   * @param workflowId - ID of the workflow to inspect
   * @returns Basic workflow information
   */
  getWorkflowState(workflowId: string): any {
    return {
      workflowId,
      status: 'running',
      metrics: this.getMetrics(),
    };
  }

  /**
   * Performs a health check of the system
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      const metrics = this.getMetrics();

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      const issues: string[] = [];

      // Check resource utilization
      if (metrics.utilization > 90) {
        status = status === 'healthy' ? 'degraded' : status;
        issues.push(`High utilization: ${metrics.utilization}%`);
      }

      if (metrics.utilization > 98) {
        status = 'unhealthy';
        issues.push(`Critical utilization: ${metrics.utilization}%`);
      }

      // Check queue length
      if (metrics.queued > 10) {
        status = status === 'healthy' ? 'degraded' : status;
        issues.push(`High queue length: ${metrics.queued}`);
      }

      if (metrics.queued > 25) {
        status = 'unhealthy';
        issues.push(`Critical queue length: ${metrics.queued}`);
      }

      // Check for shutdown state
      if (this.isShuttingDown) {
        status = 'unhealthy';
        issues.push('System is shutting down');
      }

      return {
        status,
        details: {
          metrics,
          issues,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Logs current resource metrics for monitoring
   */
  private logResourceMetrics(): void {
    if (!this.config.enableDebug) return;

    const metrics = this.getMetrics();
    this.logger.debug(
      `[LangGraphIntegration] Resource Metrics: ${JSON.stringify(metrics, null, 2)}`
    );
    /* {
      active: metrics.active,
      queued: metrics.queued,
      utilization: `${metrics.utilization.toFixed(1)}%`,
      capacity: metrics.capacity,
    }); */
  }

  /**
   * Graceful shutdown with comprehensive resource cleanup
   */
  async destroy(): Promise<void> {
    if (this.isShuttingDown) {
      this.logger.warn('[LangGraphIntegration] Shutdown already in progress');
      return;
    }

    this.isShuttingDown = true;

    try {
      if (this.config.enableDebug) {
        this.logger.info('[LangGraphIntegration] Starting graceful shutdown...');
      }

      // Shutdown supervisors
      if (this.supervisor) {
        await this.supervisor.shutdown();
        this.supervisor = null;
      }

      if (this.idiomaticSupervisor) {
        await this.idiomaticSupervisor.shutdown();
        this.idiomaticSupervisor = null;
      }

      // Shutdown resource manager
      await this.resourceManager.shutdown();

      this.isInitialized = false;

      if (this.config.enableDebug) {
        this.logger.info('[LangGraphIntegration] Shutdown completed successfully');
      }
    } catch (error) {
      this.logger.error(error, '[LangGraphIntegration] Error during shutdown');
      throw error;
    }
  }

  /**
   * Static method to check if LangGraph is enabled via environment
   */
  static isEnabled(): boolean {
    return process.env.ENABLE_LANGGRAPH === 'true';
  }

  /**
   * Static method to create a configured instance
   */
  static create(config?: LangGraphConfig): LangGraphIntegration {
    return new LangGraphIntegration(config);
  }
}
