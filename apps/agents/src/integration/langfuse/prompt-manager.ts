import { ChatPromptConfig } from '../../types/prompts/chat-prompts';
import { getLangfuseClient } from '../../observability/langfuse';
import { getLogger } from '../../observability/global-logger';

// In-memory cache for prompt configurations
const promptCache: Record<string, ChatPromptConfig> = {};

/**
 * Retrieve a chat prompt configuration from Langfuse by name.
 * Returns the prompt configuration if found, null otherwise.
 * Results are cached in-memory for subsequent calls.
 */
export async function getChatPromptConfig({
  promptName,
  promptLabel = 'latest',
  logger = getLogger(),
}: {
  promptName: string;
  promptLabel?: string;
  logger?: any;
}): Promise<ChatPromptConfig | null> {
  // Fast-path: return from cache when available
  if (promptCache[promptName]) {
    logger.debug(`[PromptManager] Returning cached prompt for ${promptName}`);
    return promptCache[promptName];
  }

  const langfuseClient = getLangfuseClient();
  if (!langfuseClient) {
    logger.warn(`[PromptManager] Langfuse is disabled - cannot retrieve prompt ${promptName}`);
    return null;
  }

  try {
    const existingPrompt = await langfuseClient.getPrompt(promptName, undefined, {
      label: promptLabel,
    });
    if (existingPrompt) {
      const cfg: ChatPromptConfig = {
        systemPrompt: existingPrompt.prompt,
        temperature: (existingPrompt.config as any)?.temperature ?? 0.7,
        includeConversationHistory:
          (existingPrompt.config as any)?.includeConversationHistory ?? true,
        responseFormat: (existingPrompt.config as any)?.responseFormat ?? 'natural',
        // Add conversation memory configuration
        conversationMemory: {
          maxHistoryEntries: (existingPrompt.config as any)?.maxHistoryEntries ?? 10,
          includeUserMessages: (existingPrompt.config as any)?.includeUserMessages ?? true,
          includeAssistantMessages:
            (existingPrompt.config as any)?.includeAssistantMessages ?? true,
          contextWindow: (existingPrompt.config as any)?.contextWindow ?? 4000,
          summarizeLongHistory: (existingPrompt.config as any)?.summarizeLongHistory ?? false,
        },
      };

      promptCache[promptName] = cfg;
      logger.info(`[PromptManager] Retrieved prompt from Langfuse: ${promptName}`);
      return cfg;
    }
  } catch (err) {
    logger.error(err, `[PromptManager] Failed to fetch prompt ${promptName}`);
  }

  logger.warn(`[PromptManager] Prompt not found in Langfuse: ${promptName}`);
  return null;
}

/**
 * Clear the in-memory prompt cache.
 * Useful for testing or when prompts are updated externally.
 */
export function clearPromptCache(): void {
  Object.keys(promptCache).forEach(key => delete promptCache[key]);
}
