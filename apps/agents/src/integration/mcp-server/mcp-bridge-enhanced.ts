// Remove direct imports from API package and create abstractions
export interface ILogger {
  debug(message: string, meta?: Record<string, any>): void;
  info(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  error(error: any, message: string, meta?: Record<string, any>): void;
}

export interface MCPSession {
  id: string;
  organizationId: string;
  userId?: string;
  createdAt: Date;
  lastActivity: Date;
}

export interface JWTPayload {
  id: string;
  email: string;
  organization_id: string;
  type: string;
}

export interface MCPServerInterface {
  /**
   * Handle a JSON-RPC message from the MCP server. Real implementation is in connection-manager.ts file `realMCPServer`
   * @param sessionId - The session ID
   * @param message - The JSON-RPC message
   * @returns The response from the MCP server
   */
  handleJsonRpcMessage(sessionId: string, message: any): Promise<any>;
}

export interface MCPSessionManagerInterface {
  validateSession(sessionId: string): Promise<MCPSession | null>;
  createJWTSession(payload: JWTPayload): Promise<MCPSession>;
}

export interface AgentMCPError {
  code:
    | 'MCP_SESSION_FAILED'
    | 'MCP_TOOL_ERROR'
    | 'QUERY_UNSAFE'
    | 'PARSE_ERROR'
    | 'SESSION_EXPIRED';
  message: string;
  originalError?: any;
  retryable: boolean;
}

export interface AgentMCPResult {
  success: boolean;
  data?: any;
  error?: AgentMCPError;
  sessionId?: string;
  duration?: number;
}

export interface MCPToolCallOptions {
  timeout?: number;
  retryAttempts?: number;
  validateInput?: boolean;
}

/**
 * `AgentMCPBridge` is an in-process bridge allowing agents to interact with the
 * MCP server via direct method calls instead of WebSocket/HTTP transport. It
 * handles session creation/validation and offers advanced features such as
 * retry logic, timeout enforcement, and safety validation for database queries.
 *
 * This enhanced version includes additional features for better monitoring,
 * error handling, and performance tracking.
 */
export class AgentMCPBridge {
  private mcpServer: MCPServerInterface;
  private sessionManager: MCPSessionManagerInterface;
  private activeSessions: Map<string, MCPSession> = new Map();
  private logger: ILogger;

  /**
   * Constructs a new bridge instance.
   *
   * @param logger - Logger implementation used for diagnostics.
   * @param mcpServer - Concrete MCP server implementation (JSON-RPC handler).
   * @param sessionManager - Strategy for session validation/creation (JWT).
   */
  constructor(
    logger: ILogger,
    mcpServer: MCPServerInterface,
    sessionManager: MCPSessionManagerInterface
  ) {
    this.mcpServer = mcpServer;
    this.sessionManager = sessionManager;
    this.logger = logger;
  }

  /**
   * Creates (or reuses) an MCP session for a given organisation.
   *
   * @param organizationId - Tenant identifier.
   * @param userId - _(optional)_ User identifier used for JWT creation.
   * @returns The active `MCPSession` object.
   */
  async createAgentSession(organizationId: string, userId?: string): Promise<MCPSession> {
    try {
      // Check if we have an active session for this organization
      const existingSessionKey = `org_${organizationId}`;
      const existingSession = this.activeSessions.get(existingSessionKey);

      if (existingSession) {
        // Validate existing session
        const validSession = await this.sessionManager.validateSession(existingSession.id);
        if (validSession) {
          this.logger.debug(`Reusing MCP session ${existingSession.id} for org ${organizationId}`);
          return existingSession;
        } else {
          // Remove invalid session
          this.activeSessions.delete(existingSessionKey);
        }
      }

      // Create new session using JWT payload structure
      const userPayload: JWTPayload = {
        id: userId || `agent_user_${organizationId}`,
        email: `agent@org_${organizationId}`,
        organization_id: organizationId,
        type: 'access',
      };

      const session = await this.sessionManager.createJWTSession(userPayload);
      this.activeSessions.set(existingSessionKey, session);

      this.logger.info(
        `[MCPBridge] Session created | sessionId=${session.id} orgId=${organizationId}`
      );
      return session;
    } catch (error) {
      this.logger.error(error, `Failed to create MCP session for org ${organizationId}`);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw this.createError(
        'MCP_SESSION_FAILED',
        `Failed to create session: ${errorMessage}`,
        error
      );
    }
  }

  /**
   * Executes a tool call directly through the MCP server with retry and timeout.
   *
   * @param sessionId - Valid MCP session ID.
   * @param toolName - Registered tool name.
   * @param args - Tool parameters.
   * @param options - Execution options controlling timeout, retries, validation.
   * @returns Structured {@link AgentMCPResult} summarising success or failure.
   */
  async callToolDirect(
    sessionId: string,
    toolName: string,
    args: any,
    options: MCPToolCallOptions = {}
  ): Promise<AgentMCPResult> {
    const startTime = Date.now();
    const { timeout = 30000, retryAttempts = 1, validateInput = true } = options;

    try {
      // Validate session
      const session = await this.sessionManager.validateSession(sessionId);
      if (!session) {
        throw this.createError('SESSION_EXPIRED', 'Session expired or invalid', null, false);
      }

      // Basic input validation if requested
      if (validateInput && toolName === 'query_database') {
        const queryValidation = this.validateQueryInput(args.query);
        if (!queryValidation.valid) {
          throw this.createError('QUERY_UNSAFE', queryValidation.error!, null, false);
        }
      }

      // Create JSON-RPC message structure
      const jsonRpcMessage = {
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args,
        },
      };

      // Execute with timeout and retry logic
      let lastError: any;
      for (let attempt = 1; attempt <= retryAttempts; attempt++) {
        try {
          const response = await Promise.race([
            this.mcpServer.handleJsonRpcMessage(sessionId, jsonRpcMessage),
            this.createTimeoutPromise(timeout),
          ]);
          response.result &&
            response.result.content &&
            response.result.content.length &&
            response.result.content.length > 0 &&
            this.logger.info(`[MCPBridge] Tool call response received`, {
              hasContent: response.result.content.length > 0,
              toolName,
              sessionId,
              tags: ['mcp-server', 'mcp-server-tool-call-response-received'],
            });
          if (response.error) {
            throw new Error(`MCP Tool Error: ${response.error.message}`);
          }

          const duration = Date.now() - startTime;

          // Parse the result from MCP server
          const result = response.result;
          let parsedData = null;

          // Handle MCP tool response structure
          if (
            result &&
            result.content &&
            Array.isArray(result.content) &&
            result.content[0] &&
            result.content[0].text
          ) {
            try {
              parsedData = JSON.parse(result.content[0].text);
            } catch (parseError) {
              this.logger.error(parseError, 'Failed to parse MCP tool response');
              throw this.createError(
                'PARSE_ERROR',
                `Failed to parse tool response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`,
                parseError,
                false
              );
            }
          } else {
            // If the result doesn't have the expected structure, return it as-is
            parsedData = result;
          }

          this.logger.debug(`MCP tool ${toolName} completed in ${duration}ms`);

          return {
            success: true,
            data: parsedData,
            sessionId,
            duration,
          };
        } catch (error) {
          lastError = error;
          if (attempt < retryAttempts) {
            this.logger.warn(`[MCPBridge] Tool "${toolName}" attempt ${attempt} failed, retrying`);
            await this.delay(1000 * attempt); // Exponential backoff
          }
        }
      }

      throw lastError;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(error, `MCP tool ${toolName} failed after ${duration}ms`);

      // Type guard for AgentMCPError
      if (error && typeof error === 'object' && 'code' in error) {
        return {
          success: false,
          error: error as AgentMCPError,
          sessionId,
          duration,
        };
      }

      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        error: this.createError('MCP_TOOL_ERROR', `Tool execution failed: ${errorMessage}`, error),
        sessionId,
        duration,
      };
    }
  }

  /**
   * Retrieves or creates a session and returns its identifier (helper).
   */
  async getOrCreateSessionForOrg(organizationId: string, userId?: string): Promise<string> {
    const session = await this.createAgentSession(organizationId, userId);
    return session.id;
  }

  /**
   * Removes expired sessions and returns the count of removals.
   */
  async cleanupExpiredSessions(): Promise<number> {
    let cleanedCount = 0;

    for (const [key, session] of this.activeSessions.entries()) {
      const validSession = await this.sessionManager.validateSession(session.id);
      if (!validSession) {
        this.activeSessions.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.info(`Cleaned up ${cleanedCount} expired MCP sessions`);
    }

    return cleanedCount;
  }

  /**
   * Counts currently active sessions.
   */
  getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  /**
   * @internal Validates that an SQL query is read-only and safe to execute.
   */
  private validateQueryInput(query: string): { valid: boolean; error?: string } {
    if (!query || typeof query !== 'string') {
      return { valid: false, error: 'Query must be a non-empty string' };
    }

    const trimmedQuery = query.trim().toUpperCase();

    // Must start with SELECT
    if (!trimmedQuery.startsWith('SELECT')) {
      return { valid: false, error: 'Only SELECT queries are allowed' };
    }

    // Check for dangerous keywords
    const dangerousKeywords = [
      'DROP',
      'DELETE',
      'UPDATE',
      'INSERT',
      'ALTER',
      'CREATE',
      'TRUNCATE',
      'GRANT',
      'REVOKE',
      'EXECUTE',
      'EXEC',
      'CALL',
    ];

    for (const keyword of dangerousKeywords) {
      if (trimmedQuery.includes(keyword)) {
        return { valid: false, error: `Dangerous keyword detected: ${keyword}` };
      }
    }

    return { valid: true };
  }

  /**
   * Helper to build standardized {@link AgentMCPError} objects.
   */
  private createError(
    code: AgentMCPError['code'],
    message: string,
    originalError?: any,
    retryable: boolean = true
  ): AgentMCPError {
    return {
      code,
      message,
      originalError,
      retryable,
    };
  }

  /**
   * Create timeout promise for racing with actual operation
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Simple delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
