import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ogger } from './mcp-bridge-enhanced';
import { getLoggerWithTraceId } from '../../observability/global-logger';
import {
  DefaultMCPToolRegistry,
  QueryDatabaseTool,
  GetAllDocumentsTool,
  GetAllInternalDocumentsTool,
} from '@anter/mcp-tools';
import { AgentsSessionProvider } from './adapters';
import { EnhancedDatabaseProviderImpl } from '@anter/mcp-tools';

/**
 * Interface for the API layer's connection manager
 * Provides access to both external and internal database connections with tenant isolation
 */
export interface IConnectionManager {
  /** Get external database connection with tenant isolation (uses DATABASE_URL_ROOT_USER) */
  dbWithTenant(orgId: string): Promise<any>;
  /** Get internal database connection with tenant isolation (uses DATABASE_URL_INTERNAL) */
  dbInternalWithTenant(orgId: string): Promise<any>;
  /** Get external database connection with <PERSON><PERSON> bypassed */
  dbBypassRLS(): Promise<any>;
  /** Get internal database connection with <PERSON><PERSON> bypassed */
  dbInternalBypassRLS(): Promise<any>;
  /** Get database configuration for a specific schema */
  getDatabaseConfig(schema: 'internal' | 'external'): {
    connectionString: string;
    hasRLS: boolean;
    schema: 'internal' | 'external';
  };
  /** Logger instance for connection manager operations */
  log: ILogger;
  /** Optional Redis client for caching and session management */
  redis?: any;
}

/**
 * Configuration for connection pooling and session management
 */
export interface ConnectionPoolConfig {
  /** Maximum number of sessions per organization */
  maxSessionsPerOrg: number;
  /** Session time-to-live in milliseconds */
  sessionTTL: number;
  /** Interval for cleanup operations in milliseconds */
  cleanupInterval: number;
  /** Maximum number of retry attempts for failed operations */
  maxRetryAttempts: number;
  /** Interval for health checks in milliseconds */
  healthCheckInterval: number;
}

/**
 * Metrics for monitoring connection pool performance and health
 */
export interface ConnectionMetrics {
  /** Total number of sessions created since startup */
  totalSessions: number;
  /** Number of currently active sessions */
  activeSessions: number;
  /** Number of sessions that have expired */
  expiredSessions: number;
  /** Number of failed connection attempts */
  failedConnections: number;
  /** Average session duration in milliseconds */
  averageSessionDuration: number;
  /** Success rate as a percentage (0-100) */
  successRate: number;
}

/**
 * Information about a single MCP session
 */
export interface SessionInfo {
  /** Unique session identifier */
  sessionId: string;
  /** Organization/tenant identifier */
  organizationId: string;
  /** Optional user identifier for session affinity */
  userId?: string;
  /** When the session was created */
  createdAt: Date;
  /** When the session was last used */
  lastUsed: Date;
  /** Number of times this session has been used */
  usageCount: number;
  /** Whether the session is currently valid */
  isValid: boolean;
}

/**
 * MCPConnectionManager maintains a tenant-scoped session pool and exposes a
 * convenient wrapper for invoking MCP tools without worrying about session
 * lifecycle, retries, or metrics. It transparently re-uses sessions, performs
 * TTL/health checks, and emits aggregated connection statistics.
 *
 * Key Features:
 * - Session pooling with automatic cleanup
 * - Tenant isolation for database connections
 * - Support for both external and internal databases
 * - Automatic retry logic with configurable attempts
 * - Health monitoring and metrics collection
 * - Background maintenance tasks
 */
export class MCPConnectionManager {
  private logger: any;
  private bridge: AgentMCPBridge;
  private config: ConnectionPoolConfig;
  private sessionPools: Map<string, SessionInfo[]> = new Map();
  private metrics: ConnectionMetrics;
  private cleanupTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;

  /**
   * Creates a new connection manager instance.
   *
   * The constructor performs the following initialization:
   * 1. Sets up logging with fallback to no-op logger
   * 2. Initializes MCP tool registry with database and session providers
   * 3. Creates and configures database tools with appropriate providers:
   *    - External database tools use ConnectionManagerAdapter
   *    - Internal database tools use EnhancedDatabaseProviderImpl
   * 4. Sets up MCP server and session manager implementations
   * 5. Initializes the AgentMCPBridge
   * 6. Configures default settings and starts background tasks
   *
   * @param connectionManager - Connection manager from API layer providing database access
   * @param config - Optional pool configuration to override defaults
   */
  constructor(
    private connectionManager: IConnectionManager,
    config: Partial<ConnectionPoolConfig> = {}
  ) {
    // Use provided logger with safety check - fallback to no-op logger if not available
    this.logger = this.connectionManager?.log || {
      info: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {},
    };

    // Initialize MCP Tool Registry with proper separation of concerns
    const toolRegistry = new DefaultMCPToolRegistry();

    // Create and configure database providers
    // - EnhancedDatabaseProviderImpl: Supports both external and internal database operations
    const enhancedDbProvider = new EnhancedDatabaseProviderImpl(this.connectionManager);
    const sessionProvider = new AgentsSessionProvider(this);

    // Create and register tools with appropriate database providers
    // External database tools (use enhanced provider for external DB access)
    const queryDatabaseTool = new QueryDatabaseTool();
    queryDatabaseTool.setDatabaseProvider(enhancedDbProvider);
    queryDatabaseTool.setSessionProvider(sessionProvider);
    toolRegistry.registerTool(queryDatabaseTool);

    const getAllDocumentsTool = new GetAllDocumentsTool();
    getAllDocumentsTool.setDatabaseProvider(enhancedDbProvider);
    getAllDocumentsTool.setSessionProvider(sessionProvider);
    toolRegistry.registerTool(getAllDocumentsTool);

    // Internal database tools (use enhanced provider for internal DB access)
    const getAllInternalDocumentsTool = new GetAllInternalDocumentsTool();
    getAllInternalDocumentsTool.setDatabaseProvider(enhancedDbProvider);
    getAllInternalDocumentsTool.setSessionProvider(sessionProvider);
    toolRegistry.registerTool(getAllInternalDocumentsTool);

    // Create MCP server implementation for handling JSON-RPC messages
    const self = this;
    const mcpServer = {
      /**
       * Handles incoming JSON-RPC messages for tool execution and listing
       * Supports 'tools/call' and 'tools/list' methods
       */
      async handleJsonRpcMessage(sessionId: string, message: any): Promise<any> {
        try {
          if (message.method === 'tools/call') {
            const toolName = message.params?.name;
            const args = message.params?.arguments || {};

            const tool = toolRegistry.getTool(toolName);
            if (!tool) {
              return {
                jsonrpc: '2.0',
                id: message.id,
                error: {
                  code: -32601,
                  message: `Tool '${toolName}' not found`,
                },
              };
            }

            const context = {
              sessionId,
              organizationId: self.findSessionById(sessionId)?.organizationId,
              userId: self.findSessionById(sessionId)?.userId,
            };

            const result = await tool.execute(args, context);

            return {
              jsonrpc: '2.0',
              id: message.id,
              result,
            };
          }

          if (message.method === 'tools/list') {
            const tools = toolRegistry.listTools();
            return {
              jsonrpc: '2.0',
              id: message.id,
              result: {
                tools,
              },
            };
          }

          return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32601,
              message: `Method '${message.method}' not found`,
            },
          };
        } catch (error) {
          return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32603,
              message: error instanceof Error ? error.message : 'Internal error',
            },
          };
        }
      },
    };

    // Create session manager implementation for session validation and creation
    const sessionManager = {
      /**
       * Validates if a session exists and is still valid
       * Currently uses a simple prefix-based validation for development
       */
      async validateSession(sessionId: string): Promise<MCPSession | null> {
        // Simulate session validation - assume sessions are valid if they exist
        if (sessionId && sessionId.startsWith('session_')) {
          return {
            id: sessionId,
            organizationId: 'mock_org',
            userId: 'mock_user',
            createdAt: new Date(),
            lastActivity: new Date(),
          };
        }
        return null;
      },
      /**
       * Creates a new JWT-based session
       * Currently simulates session creation for development
       */
      async createJWTSession(payload: any): Promise<MCPSession> {
        // Simulate session creation
        return {
          id: `session_${Date.now()}_${payload.organization_id}`,
          organizationId: payload.organization_id,
          userId: payload.id,
          createdAt: new Date(),
          lastActivity: new Date(),
        };
      },
    };

    // Initialize the bridge with the correct constructor signature
    this.bridge = new AgentMCPBridge(this.logger, mcpServer, sessionManager);

    // Set default configuration with sensible defaults for production use
    this.config = {
      maxSessionsPerOrg: 5, // Maximum 5 sessions per organization
      sessionTTL: 30 * 60 * 1000, // 30 minutes session lifetime
      cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
      maxRetryAttempts: 3, // Retry failed operations up to 3 times
      healthCheckInterval: 2 * 60 * 1000, // Health check every 2 minutes
      ...config,
    };

    // Initialize metrics with starting values
    this.metrics = {
      totalSessions: 0,
      activeSessions: 0,
      expiredSessions: 0,
      failedConnections: 0,
      averageSessionDuration: 0,
      successRate: 100,
    };

    // Start background tasks for maintenance and health monitoring
    this.startBackgroundTasks();

    this.logger.info('MCPConnectionManager initialized with API layer connection manager');
  }

  /**
   * Retrieves a valid session for the organization or creates a new one.
   *
   * This method implements session pooling with the following logic:
   * 1. First tries to find an existing valid session from the pool
   * 2. If found, updates usage statistics and returns the session
   * 3. If no valid session exists, creates a new one via the bridge
   * 4. Adds the new session to the pool and updates metrics
   * 5. Handles errors by updating failure metrics and re-throwing
   *
   * @param organizationId - Tenant identifier
   * @param userId - Optional user identifier used to bias session re-use
   * @returns A valid SessionInfo ready for tool execution
   * @throws Error if session creation fails
   */
  async getOrCreateSession(organizationId: string, userId?: string): Promise<SessionInfo> {
    try {
      // Try to get existing valid session from pool
      const existingSession = await this.getValidSessionFromPool(organizationId, userId);
      if (existingSession) {
        existingSession.lastUsed = new Date();
        existingSession.usageCount++;
        return existingSession;
      }

      // Create new session via the bridge
      const mcpSession = await this.bridge.createAgentSession(organizationId, userId);

      const sessionInfo: SessionInfo = {
        sessionId: mcpSession.id,
        organizationId,
        userId,
        createdAt: new Date(),
        lastUsed: new Date(),
        usageCount: 1,
        isValid: true,
      };

      // Add to pool and update metrics
      this.addSessionToPool(organizationId, sessionInfo);
      this.metrics.totalSessions++;
      this.metrics.activeSessions++;

      return sessionInfo;
    } catch (error) {
      this.metrics.failedConnections++;
      this.logger.error(error, `Failed to get/create session for org ${organizationId}`);
      throw error;
    }
  }

  /**
   * Executes an MCP tool call ensuring sessions, retries, and metric updates.
   *
   * This is the main entry point for tool execution. It handles:
   * 1. Session management (get or create session)
   * 2. Tool execution via the bridge
   * 3. Success/failure metric updates
   * 4. Comprehensive logging and error handling
   * 5. Performance monitoring
   *
   * @param organizationId - Tenant identifier
   * @param toolName - Registered tool name to execute
   * @param args - Tool-specific argument object
   * @param userId - Optional caller identifier for session affinity
   * @param traceId - Optional trace ID for distributed tracing
   * @returns Structured AgentMCPResult from the bridge call
   * @throws Propagates errors from underlying bridge after metrics update
   */
  async executeToolCall(
    organizationId: string,
    toolName: string,
    args: any,
    userId?: string,
    traceId?: string
  ): Promise<AgentMCPResult> {
    const logger = traceId ? getLoggerWithTraceId(traceId) : this.logger;

    logger.info(`Starting MCP tool execution`, {
      organizationId,
      toolName,
      userId,
      tags: ['mcp-server', 'mcp-server-start'],
    });

    const sessionInfo = await this.getOrCreateSession(organizationId, userId);
    logger.debug(`Got session for tool execution`, {
      sessionId: sessionInfo.sessionId,
      organizationId,
    });

    try {
      logger.debug(`Calling bridge.callToolDirect`, {
        sessionId: sessionInfo.sessionId,
        toolName,
        args,
      });

      this.logger.info(`[MCPConnectionManager] About to call bridge.callToolDirect with:`, {
        sessionId: sessionInfo.sessionId,
        toolName,
        args,
      });

      const result: AgentMCPResult = await this.bridge.callToolDirect(
        sessionInfo.sessionId,
        toolName,
        args,
        {
          retryAttempts: this.config.maxRetryAttempts,
          timeout: 30000,
          validateInput: true,
        }
      );

      this.logger.info(`[MCPConnectionManager] bridge.callToolDirect result:`, result);

      logger.info(`MCP tool execution completed`, {
        toolName,
        success: result.success,
        duration: result.duration,
        hasData: !!result.data,
        dataSize: result.data ? JSON.stringify(result.data).length : 0,
      });

      if (result.error) {
        logger.warn(`MCP tool execution returned error`, {
          toolName,
          error: result.error,
        });
      }

      // Update success metrics
      if (result.success) {
        this.updateSuccessMetrics(result.duration || 0);
      } else {
        this.updateFailureMetrics(sessionInfo, result.error);
      }

      return result;
    } catch (error) {
      logger.error(error, `MCP tool execution failed`, {
        toolName,
        organizationId,
        sessionId: sessionInfo.sessionId,
      });
      this.updateFailureMetrics(sessionInfo, error);
      throw error;
    }
  }

  /**
   * Validates whether a session is still alive and within its TTL.
   *
   * Performs the following checks:
   * 1. Verifies the session exists in the pool
   * 2. Checks if the session has expired based on lastUsed timestamp
   * 3. Updates session validity status
   * 4. Returns false for any validation errors
   *
   * @param sessionId - Session identifier to validate
   * @returns true when valid; false otherwise
   */
  async validateSession(sessionId: string): Promise<boolean> {
    try {
      // Find session info in the pool
      const sessionInfo = this.findSessionById(sessionId);
      if (!sessionInfo) {
        return false;
      }

      // Check if session is expired based on TTL
      const now = new Date();
      const sessionAge = now.getTime() - sessionInfo.lastUsed.getTime();
      if (sessionAge > this.config.sessionTTL) {
        sessionInfo.isValid = false;
        return false;
      }

      // Session is valid if it exists and hasn't expired
      sessionInfo.isValid = true;
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.warn(`Session validation failed for ${sessionId}: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Purges expired or invalid sessions across all organizations.
   *
   * This method performs cleanup by:
   * 1. Iterating through all organization session pools
   * 2. Filtering out expired or invalid sessions
   * 3. Updating metrics for cleaned sessions
   * 4. Triggering bridge-level cleanup
   * 5. Logging cleanup results
   *
   * @param maxAge - Optional TTL override; defaults to configured sessionTTL
   * @returns Number of sessions cleaned up
   */
  async cleanupExpiredSessions(): Promise<number> {
    let cleanedCount = 0;
    const now = new Date();

    for (const [orgId, sessions] of this.sessionPools.entries()) {
      const validSessions = sessions.filter(session => {
        const sessionAge = now.getTime() - session.lastUsed.getTime();
        const isExpired = sessionAge > this.config.sessionTTL || !session.isValid;

        if (isExpired) {
          cleanedCount++;
          this.metrics.expiredSessions++;
          this.metrics.activeSessions = Math.max(0, this.metrics.activeSessions - 1);
        }

        return !isExpired;
      });

      if (validSessions.length < sessions.length) {
        this.sessionPools.set(orgId, validSessions);
      }
    }

    // Also cleanup in the bridge
    const bridgeCleanedCount = await this.bridge.cleanupExpiredSessions();
    cleanedCount += bridgeCleanedCount;

    if (cleanedCount > 0) {
      this.logger.info(`Cleaned up ${cleanedCount} expired sessions`);
    }

    return cleanedCount;
  }

  /**
   * Returns a snapshot of current pool metrics with lazy recomputation.
   *
   * Updates the current metrics before returning to ensure accuracy.
   *
   * @returns Current connection metrics
   */
  getMetrics(): ConnectionMetrics {
    this.updateCurrentMetrics();
    return { ...this.metrics };
  }

  /**
   * Returns session distribution by organization for monitoring dashboards.
   *
   * Provides a breakdown of active sessions per organization and total count.
   *
   * @returns Object containing session counts by organization and total
   */
  getSessionInfo(): { byOrganization: Record<string, number>; total: number } {
    const byOrganization: Record<string, number> = {};
    let total = 0;

    for (const [orgId, sessions] of this.sessionPools.entries()) {
      const validSessions = sessions.filter(s => s.isValid);
      byOrganization[orgId] = validSessions.length;
      total += validSessions.length;
    }

    return { byOrganization, total };
  }

  /**
   * Stops background timers and performs final cleanup.
   *
   * Should be called during application shutdown to ensure proper cleanup.
   */
  async shutdown(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    await this.cleanupExpiredSessions();
    this.logger.info('MCPConnectionManager shutdown complete');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Retrieves a valid session from the pool for the given organization.
   *
   * Implements session selection logic:
   * 1. Prefers user-specific sessions if userId is provided
   * 2. Falls back to any valid session for the organization
   * 3. Validates session before returning
   *
   * @param organizationId - Organization identifier
   * @param userId - Optional user identifier for session affinity
   * @returns Valid session info or null if none found
   */
  private async getValidSessionFromPool(
    organizationId: string,
    userId?: string
  ): Promise<SessionInfo | null> {
    const sessions = this.sessionPools.get(organizationId) || [];

    // Find a valid session (prefer user-specific if userId provided)
    for (const session of sessions) {
      if (!session.isValid) continue;

      // If userId specified, prefer matching user session
      if (userId && session.userId === userId) {
        const isValid = await this.validateSession(session.sessionId);
        if (isValid) return session;
      }

      // Otherwise, use any valid session for the org
      if (!userId || !session.userId) {
        const isValid = await this.validateSession(session.sessionId);
        if (isValid) return session;
      }
    }

    return null;
  }

  /**
   * Adds a session to the organization's session pool.
   *
   * Implements pool management:
   * 1. Removes oldest session if at max capacity
   * 2. Adds new session to the end of the pool
   * 3. Updates metrics accordingly
   *
   * @param organizationId - Organization identifier
   * @param sessionInfo - Session information to add
   */
  private addSessionToPool(organizationId: string, sessionInfo: SessionInfo): void {
    const sessions = this.sessionPools.get(organizationId) || [];

    // Remove oldest session if we're at max capacity
    if (sessions.length >= this.config.maxSessionsPerOrg) {
      const oldestSession = sessions.shift();
      if (oldestSession) {
        this.metrics.activeSessions = Math.max(0, this.metrics.activeSessions - 1);
      }
    }

    sessions.push(sessionInfo);
    this.sessionPools.set(organizationId, sessions);
  }

  /**
   * Finds a session by its ID across all organization pools.
   *
   * @param sessionId - Session identifier to find
   * @returns Session info if found, null otherwise
   */
  public findSessionById(sessionId: string): SessionInfo | null {
    for (const sessions of this.sessionPools.values()) {
      const session = sessions.find(s => s.sessionId === sessionId);
      if (session) return session;
    }
    return null;
  }

  /**
   * Updates success metrics using exponential moving averages.
   *
   * Provides smooth metric updates that are less sensitive to outliers.
   *
   * @param duration - Duration of the successful operation
   */
  private updateSuccessMetrics(duration: number): void {
    // Update success rate using exponential moving average
    const currentSuccessRate = this.metrics.successRate;
    this.metrics.successRate = currentSuccessRate * 0.9 + 1.0 * 0.1;

    // Update average duration using exponential moving average
    const currentAvgDuration = this.metrics.averageSessionDuration;
    this.metrics.averageSessionDuration = currentAvgDuration * 0.9 + duration * 0.1;
  }

  /**
   * Updates failure metrics and handles session invalidation.
   *
   * Marks sessions as invalid for certain types of errors.
   *
   * @param sessionInfo - Session that experienced the failure
   * @param error - Error that occurred
   */
  private updateFailureMetrics(sessionInfo: SessionInfo, error: any): void {
    // Update success rate
    const currentSuccessRate = this.metrics.successRate;
    this.metrics.successRate = currentSuccessRate * 0.9 + 0.0 * 0.1;

    // Mark session as invalid if it's a session-related error
    if (error?.code === 'SESSION_EXPIRED' || error?.code === 'MCP_SESSION_FAILED') {
      sessionInfo.isValid = false;
    }
  }

  /**
   * Updates current metrics by recalculating active session count.
   *
   * Called before returning metrics to ensure accuracy.
   */
  private updateCurrentMetrics(): void {
    let activeSessions = 0;
    for (const sessions of this.sessionPools.values()) {
      activeSessions += sessions.filter(s => s.isValid).length;
    }
    this.metrics.activeSessions = activeSessions;
  }

  /**
   * Starts background tasks for maintenance and health monitoring.
   *
   * Initializes two main background processes:
   * 1. Session cleanup timer
   * 2. Health check timer
   */
  private startBackgroundTasks(): void {
    // Cleanup expired sessions periodically
    this.cleanupTimer = setInterval(async () => {
      try {
        await this.cleanupExpiredSessions();
      } catch (error) {
        this.logger.error(error, 'Error during session cleanup');
      }
    }, this.config.cleanupInterval);

    // Health check sessions periodically
    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        this.logger.error(error, 'Error during health check');
      }
    }, this.config.healthCheckInterval);

    this.logger.info('[MCPConnectionManager] Background tasks started');
  }

  /**
   * Performs health check on all active sessions.
   *
   * Validates each active session and logs results for monitoring.
   */
  private async performHealthCheck(): Promise<void> {
    let checkedCount = 0;
    let invalidCount = 0;

    for (const sessions of this.sessionPools.values()) {
      for (const session of sessions) {
        if (session.isValid) {
          checkedCount++;
          const isValid = await this.validateSession(session.sessionId);
          if (!isValid) {
            invalidCount++;
          }
        }
      }
    }

    if (invalidCount > 0) {
      this.logger.warn(`Health check found ${invalidCount}/${checkedCount} invalid sessions`);
    } else if (checkedCount > 0) {
      this.logger.debug(`Health check validated ${checkedCount} sessions`);
    }
  }

  /**
   * Returns the Redis client instance that was surfaced via the injected
   * DatabaseProvider. Callers must handle the case where Redis is not
   * available and the value is undefined.
   *
   * Provides detailed logging about Redis client status for debugging.
   *
   * @returns Redis client instance or undefined if not available
   */
  public getRedisClient(): any | undefined {
    const redisClient = (this.connectionManager as IConnectionManager).redis;

    // Log Redis client status for debugging
    if (redisClient) {
      const redisAny = redisClient as any;
      const isMock = redisAny.isRedisMocked === true;
      this.logger.info(`[MCPConnectionManager] Redis client retrieved`, {
        isMock,
        hasRedisClient: true,
        clientType: isMock ? 'MOCK' : 'LIVE',
        tags: ['mcp-connection-manager', 'redis-client-status'],
      });
    } else {
      this.logger.warn(`[MCPConnectionManager] No Redis client available`, {
        hasRedisClient: false,
        databaseProviderHasRedis: 'redis' in this.connectionManager,
        databaseProviderKeys: Object.keys(this.connectionManager),
        tags: ['mcp-connection-manager', 'redis-client-status'],
      });
    }

    return redisClient;
  }
}
