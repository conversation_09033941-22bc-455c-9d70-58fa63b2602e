import { SessionProvider } from '@anter/mcp-tools';

/**
 * `AgentsSessionProvider` bridges the MCP tool runtime with the agent-specific
 * session store by implementing the `SessionProvider` interface. It enables
 * tools to resolve organisation and user IDs from a LangGraph session.
 */
export class AgentsSessionProvider implements SessionProvider {
  /**
   * @param sessionLookup - Reference to the `MCPConnectionManager` providing a
   *                        `findSessionById` helper.
   */
  constructor(
    private sessionLookup: {
      findSessionById(sessionId: string): { organizationId: string; userId?: string } | null;
    }
  ) {}

  /**
   * Returns the organisation ID associated with the provided session.
   *
   * @param sessionId - MCP session identifier.
   */
  async getOrganizationId(sessionId: string): Promise<string | null> {
    const session = this.sessionLookup.findSessionById(sessionId);
    return session?.organizationId || null;
  }

  /**
   * Returns the user ID associated with the provided session (if any).
   *
   * @param sessionId - MCP session identifier.
   */
  async getUserId(sessionId: string): Promise<string | null> {
    const session = this.sessionLookup.findSessionById(sessionId);
    return session?.userId || null;
  }
}
