/// <reference types="vitest" />
import { vi, beforeAll, afterEach } from 'vitest';

// Global test setup
beforeAll(() => {
  // Mock environment variables
  process.env.NODE_ENV = 'test';
  process.env.OPENAI_API_KEY = 'test-key';
});

// Reset mocks after each test
afterEach(() => {
  vi.clearAllMocks();
  vi.resetAllMocks();
});

// Global test utilities
global.createMockAgent = (overrides = {}) => {
  return {
    name: 'test-agent',
    version: '0.0.1',
    archetype: 'reactive',
    capabilities: ['chat'],
    invoke: vi.fn().mockResolvedValue({
      output: 'test output',
      metadata: {},
    }),
    stream: vi.fn(),
    initialize: vi.fn().mockResolvedValue(undefined),
    destroy: vi.fn().mockResolvedValue(undefined),
    healthCheck: vi.fn().mockResolvedValue({
      status: 'healthy',
      timestamp: new Date(),
      checks: {
        memory: true,
        dependencies: true,
        model: true,
      },
    }),
    getMetrics: vi.fn().mockReturnValue({
      invocations: 0,
      errors: 0,
      avgLatency: 0,
      p95Latency: 0,
      p99Latency: 0,
      tokensUsed: 0,
      cost: 0,
    }),
    ...overrides,
  };
};

// Declare global test utilities
declare global {
  var createMockAgent: (overrides?: any) => any;
}
