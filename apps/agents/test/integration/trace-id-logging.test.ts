import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AbstractAgent } from '../../src/base/agent';
import {
  AgentConfig,
  AgentArchetype,
  AgentCapability,
  AgentInput,
  AgentResult,
} from '../../src/base/types';
import { getLogger, setGlobalLogger } from '../../src/observability/global-logger';

// Mock concrete implementation for testing
class TestAgent extends AbstractAgent {
  constructor(config: AgentConfig) {
    super(config);
  }

  protected async onInitialize(): Promise<void> {}
  protected async onDestroy(): Promise<void> {}

  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    const logger = this.getLoggerWithTraceId(input.traceId);
    logger.info('Test message in doInvoke', { testData: 'value' });

    return {
      output: {
        response: 'Test response',
        metadata: {},
        metrics: {},
      },
    };
  }
}

describe('TraceId Logging', () => {
  let mockLogger: any;
  let testAgent: TestAgent;

  beforeEach(async () => {
    // Create mock logger
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    // Set global logger
    setGlobalLogger(mockLogger);

    // Create test agent
    const config: AgentConfig = {
      name: 'test-agent',
      version: '1.0.0',
      archetype: AgentArchetype.REACTIVE,
      capabilities: [AgentCapability.CHAT],
    };

    testAgent = new TestAgent(config);
    await testAgent.initialize(config);
  });

  it('should include traceId in logs when provided', async () => {
    const input: AgentInput = {
      input: 'test input',
      traceId: 'test-trace-123',
    };

    await testAgent.invoke(input);

    // Check that AbstractAgent's invoke method logged with traceId
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Starting agent invocation',
      expect.objectContaining({
        agent: 'test-agent',
        traceId: 'test-trace-123',
      })
    );

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Agent invocation completed successfully',
      expect.objectContaining({
        agent: 'test-agent',
        traceId: 'test-trace-123',
      })
    );

    // Check that doInvoke method logged with traceId
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Test message in doInvoke',
      expect.objectContaining({
        testData: 'value',
        traceId: 'test-trace-123',
      })
    );
  });

  it('should work without traceId', async () => {
    const input: AgentInput = {
      input: 'test input',
    };

    await testAgent.invoke(input);

    // Check that logs are still created without traceId
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Starting agent invocation',
      expect.objectContaining({
        agent: 'test-agent',
      })
    );

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Test message in doInvoke',
      expect.objectContaining({
        testData: 'value',
      })
    );

    // Should not contain traceId in metadata
    const callsWithoutTraceId = mockLogger.info.mock.calls.filter(
      (call: any) => call[1] && !call[1].traceId
    );
    expect(callsWithoutTraceId.length).toBeGreaterThan(0);
  });

  it('should handle errors with traceId logging', async () => {
    // Create failing agent
    class FailingAgent extends AbstractAgent {
      constructor(config: AgentConfig) {
        super(config);
      }

      protected async onInitialize(): Promise<void> {}
      protected async onDestroy(): Promise<void> {}

      protected async doInvoke(input: AgentInput): Promise<AgentResult> {
        throw new Error('Test error');
      }
    }

    const config: AgentConfig = {
      name: 'failing-agent',
      version: '1.0.0',
      archetype: AgentArchetype.REACTIVE,
      capabilities: [AgentCapability.CHAT],
    };

    const failingAgent = new FailingAgent(config);
    await failingAgent.initialize(config);

    const input: AgentInput = {
      input: 'test input',
      traceId: 'error-trace-456',
    };

    await expect(failingAgent.invoke(input)).rejects.toThrow('Test error');

    // Check that error was logged with traceId
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.any(Error),
      'Agent invocation failed',
      expect.objectContaining({
        agent: 'failing-agent',
        traceId: 'error-trace-456',
      })
    );
  });
});
