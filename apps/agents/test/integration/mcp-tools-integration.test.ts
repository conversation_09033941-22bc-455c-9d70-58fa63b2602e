import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  DefaultMCPToolRegistry,
  QueryDatabaseTool,
  GetAllDocumentsTool,
  DatabaseProvider,
  SessionProvider,
  ToolExecutionContext,
} from '@anter/mcp-tools';

// Mock database provider that simulates the real database adapter
const createMockDatabaseProvider = (): DatabaseProvider => ({
  async executeQuery(query: string, organizationId: string) {
    // Simulate different query types
    if (query.includes('COUNT(*)')) {
      return {
        rows: [{ total: 42 }],
        rowCount: 1,
      };
    }

    if (query.includes('SELECT id, name') && query.includes('documents')) {
      return {
        rows: [
          {
            id: 'doc-1',
            name: 'Security Report 2025',
            document_type: 'report',
            file_type: 'pdf',
            created_at: '2025-01-27T10:00:00Z',
            updated_at: '2025-01-27T10:00:00Z',
            content: 'This is a comprehensive security report covering threat analysis...',
            buffer_file: null,
          },
          {
            id: 'doc-2',
            name: 'Incident Response Plan',
            document_type: 'policy',
            file_type: 'docx',
            created_at: '2025-01-26T15:30:00Z',
            updated_at: '2025-01-26T15:30:00Z',
            content: null,
            buffer_file: Buffer.from('Binary document content for incident response...'),
          },
        ],
        rowCount: 2,
      };
    }

    // Default query response
    return {
      rows: [
        { id: 1, name: 'Test Record 1', status: 'active' },
        { id: 2, name: 'Test Record 2', status: 'inactive' },
        { id: 3, name: 'Test Record 3', status: 'pending' },
      ],
      rowCount: 3,
    };
  },

  async validateQuery(query: string) {
    const upperQuery = query.toUpperCase().trim();
    const dangerousKeywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE'];
    const hasDangerousKeywords = dangerousKeywords.some(keyword => upperQuery.includes(keyword));
    const isSelectQuery = upperQuery.startsWith('SELECT');
    return isSelectQuery && !hasDangerousKeywords;
  },
});

// Mock session provider that simulates the real session adapter
const createMockSessionProvider = (): SessionProvider => ({
  async getOrganizationId(sessionId: string) {
    const validSessions = {
      'session-123': 'org-security-team',
      'session-456': 'org-dev-team',
      'session-789': 'org-admin-team',
    };
    return validSessions[sessionId as keyof typeof validSessions] || null;
  },

  async getUserId(sessionId: string) {
    const validUsers = {
      'session-123': 'user-alice',
      'session-456': 'user-bob',
      'session-789': 'user-charlie',
    };
    return validUsers[sessionId as keyof typeof validUsers] || null;
  },
});

describe('MCP Tools Integration Test', () => {
  let registry: DefaultMCPToolRegistry;
  let queryTool: QueryDatabaseTool;
  let docsTool: GetAllDocumentsTool;
  let mockDbProvider: DatabaseProvider;
  let mockSessionProvider: SessionProvider;

  beforeEach(() => {
    // Initialize the registry and tools
    registry = new DefaultMCPToolRegistry();
    queryTool = new QueryDatabaseTool();
    docsTool = new GetAllDocumentsTool();

    // Create mock providers
    mockDbProvider = createMockDatabaseProvider();
    mockSessionProvider = createMockSessionProvider();

    // Set up dependency injection
    queryTool.setDatabaseProvider(mockDbProvider);
    queryTool.setSessionProvider(mockSessionProvider);
    docsTool.setDatabaseProvider(mockDbProvider);
    docsTool.setSessionProvider(mockSessionProvider);

    // Register tools
    registry.registerTool(queryTool);
    registry.registerTool(docsTool);
  });

  describe('Tool Registry Integration', () => {
    it('should register and list all tools correctly', () => {
      const tools = registry.listTools();

      expect(tools).toHaveLength(2);
      expect(tools.find(t => t.name === 'query_database')).toBeDefined();
      expect(tools.find(t => t.name === 'get_all_documents')).toBeDefined();

      // Verify tool metadata
      const queryToolMeta = tools.find(t => t.name === 'query_database');
      expect(queryToolMeta?.description).toContain('safe SQL queries');
      expect(queryToolMeta?.inputSchema.properties.query).toBeDefined();

      const docsToolMeta = tools.find(t => t.name === 'get_all_documents');
      expect(docsToolMeta?.description).toContain('paginated list of documents');
      expect(docsToolMeta?.inputSchema.properties.limit).toBeDefined();
    });

    it('should retrieve specific tools by name', () => {
      const retrievedQueryTool = registry.getTool('query_database');
      const retrievedDocsTool = registry.getTool('get_all_documents');

      expect(retrievedQueryTool).toBe(queryTool);
      expect(retrievedDocsTool).toBe(docsTool);
      expect(registry.getTool('nonexistent_tool')).toBeUndefined();
    });
  });

  describe('Query Database Tool Integration', () => {
    const validContext: ToolExecutionContext = {
      sessionId: 'session-123',
      organizationId: 'org-security-team',
      userId: 'user-alice',
    };

    it('should execute valid SQL queries successfully', async () => {
      const result = await queryTool.execute(
        { query: "SELECT id, name, status FROM security_events WHERE status = 'active' LIMIT 10" },
        validContext
      );

      expect(result.isError).toBe(false);
      expect(result.content).toHaveLength(1);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(true);
      expect(response.query_executed).toContain('SELECT id, name, status');
      expect(response.execution_time).toBeGreaterThanOrEqual(0);
      expect(response.rowCount).toBe(3);
      expect(response.data).toHaveLength(3);
      expect(response.data[0]).toHaveProperty('id');
      expect(response.data[0]).toHaveProperty('name');
      expect(response.data[0]).toHaveProperty('status');
    });

    it('should reject unsafe SQL operations', async () => {
      const unsafeQueries = [
        'DROP TABLE users',
        'DELETE FROM security_events',
        "UPDATE users SET password = 'hacked'",
        "INSERT INTO logs VALUES ('malicious')",
        'ALTER TABLE users ADD COLUMN backdoor TEXT',
      ];

      for (const query of unsafeQueries) {
        const result = await queryTool.execute({ query }, validContext);

        expect(result.isError).toBe(true);

        const response = JSON.parse(result.content[0].text!);
        expect(response.success).toBe(false);
        expect(response.error).toContain('potentially unsafe operations');
        expect(response.query_attempted).toBe(query);
      }
    });

    it('should handle database errors gracefully', async () => {
      // Create a provider that throws errors
      const errorProvider: DatabaseProvider = {
        ...mockDbProvider,
        async executeQuery() {
          throw new Error('Connection timeout to database');
        },
      };

      queryTool.setDatabaseProvider(errorProvider);

      const result = await queryTool.execute({ query: 'SELECT * FROM test_table' }, validContext);

      expect(result.isError).toBe(true);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(false);
      expect(response.error).toContain('Connection timeout to database');
      expect(response.query_attempted).toBe('SELECT * FROM test_table');
    });

    it('should handle session validation', async () => {
      const invalidContext: ToolExecutionContext = {
        sessionId: 'invalid-session',
        organizationId: 'unknown-org',
        userId: 'unknown-user',
      };

      const result = await queryTool.execute({ query: 'SELECT * FROM test_table' }, invalidContext);

      // The tool should still work but with the provided context
      // Session validation is handled by the session provider
      expect(result.isError).toBe(false);
    });
  });

  describe('Get All Documents Tool Integration', () => {
    const validContext: ToolExecutionContext = {
      sessionId: 'session-456',
      organizationId: 'org-dev-team',
      userId: 'user-bob',
    };

    it('should retrieve documents with default pagination', async () => {
      const result = await docsTool.execute({}, validContext);

      expect(result.isError).toBe(false);
      expect(result.content).toHaveLength(1);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(true);
      expect(response.documents).toHaveLength(2);
      expect(response.pagination.total).toBe(42);
      expect(response.pagination.limit).toBe(50);
      expect(response.pagination.offset).toBe(0);
      expect(response.pagination.returned).toBe(2);
      expect(response.pagination.hasMore).toBe(true);

      // Verify document structure
      const doc1 = response.documents[0];
      expect(doc1.id).toBe('doc-1');
      expect(doc1.name).toBe('Security Report 2025');
      expect(doc1.documentType || doc1.document_type).toBe('report');
      expect(doc1.fileType || doc1.file_type).toBe('pdf');
      expect(doc1.content).toContain('comprehensive security report');
    });

    it('should filter documents by a list of IDs', async () => {
      const result = await docsTool.execute({ filter: { ids: ['doc-1'] } }, validContext);

      expect(result.isError).toBe(false);
      expect(result.content).toHaveLength(1);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(true);
      expect(response.documents).toHaveLength(1);
      expect(response.documents[0].id).toBe('doc-1');
    });

    it('should handle custom pagination parameters', async () => {
      const result = await docsTool.execute({ limit: 25, offset: 10 }, validContext);

      expect(result.isError).toBe(false);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(true);
      expect(response.pagination.limit).toBe(25);
      expect(response.pagination.offset).toBe(10);
    });

    it('should apply document filters', async () => {
      const result = await docsTool.execute(
        {
          filter: {
            documentType: 'report',
            fileType: 'pdf',
            createdAfter: '2025-01-01T00:00:00Z',
            createdBefore: '2025-12-31T23:59:59Z',
          },
        },
        validContext
      );

      expect(result.isError).toBe(false);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(true);
      expect(response.documents).toBeDefined();
      expect(response.pagination).toBeDefined();
    });

    it('should enforce maximum limit constraints', async () => {
      const result = await docsTool.execute(
        { limit: 500 }, // Above maximum
        validContext
      );

      expect(result.isError).toBe(false);

      const response = JSON.parse(result.content[0].text!);
      expect(response.pagination.limit).toBe(200); // Should be capped at 200
    });

    it('should handle database errors gracefully', async () => {
      // Create a provider that throws errors
      const errorProvider: DatabaseProvider = {
        ...mockDbProvider,
        async executeQuery() {
          throw new Error('Database connection lost');
        },
      };

      docsTool.setDatabaseProvider(errorProvider);

      const result = await docsTool.execute({}, validContext);

      expect(result.isError).toBe(true);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(false);
      expect(response.error).toContain('Database connection lost');
      expect(response.documents).toEqual([]);
      expect(response.pagination.total).toBe(0);
    });
  });

  describe('End-to-End Workflow Simulation', () => {
    it('should simulate a complete MCP session workflow', async () => {
      const sessionId = 'session-789';
      const context: ToolExecutionContext = {
        sessionId,
        organizationId: 'org-admin-team',
        userId: 'user-charlie',
      };

      // Step 1: List available tools
      const availableTools = registry.listTools();
      expect(availableTools).toHaveLength(2);

      // Step 2: Execute a query to get system status
      const queryResult = await queryTool.execute(
        { query: 'SELECT COUNT(*) as total_records FROM system_status' },
        context
      );

      expect(queryResult.isError).toBe(false);
      const queryResponse = JSON.parse(queryResult.content[0].text!);
      expect(queryResponse.success).toBe(true);

      // Step 3: Retrieve recent documents
      const docsResult = await docsTool.execute(
        {
          limit: 10,
          filter: { createdAfter: '2025-01-01T00:00:00Z' },
        },
        context
      );

      expect(docsResult.isError).toBe(false);
      const docsResponse = JSON.parse(docsResult.content[0].text!);
      expect(docsResponse.success).toBe(true);
      expect(docsResponse.documents).toBeDefined();

      // Step 4: Verify session consistency
      // Note: organizationId is not returned in query response, only used for execution context
      expect(docsResponse.pagination).toBeDefined();
    });

    it('should handle concurrent tool executions', async () => {
      const context: ToolExecutionContext = {
        sessionId: 'session-123',
        organizationId: 'org-security-team',
        userId: 'user-alice',
      };

      // Execute multiple tools concurrently
      const [queryResult, docsResult] = await Promise.all([
        queryTool.execute(
          { query: "SELECT id, name FROM incidents WHERE status = 'open'" },
          context
        ),
        docsTool.execute({ limit: 5, filter: { documentType: 'incident' } }, context),
      ]);

      // Both should succeed
      expect(queryResult.isError).toBe(false);
      expect(docsResult.isError).toBe(false);

      const queryResponse = JSON.parse(queryResult.content[0].text!);
      const docsResponse = JSON.parse(docsResult.content[0].text!);

      expect(queryResponse.success).toBe(true);
      expect(docsResponse.success).toBe(true);
      expect(queryResponse.data).toBeDefined();
      expect(docsResponse.documents).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing providers gracefully', async () => {
      const toolWithoutProviders = new QueryDatabaseTool();

      const result = await toolWithoutProviders.execute(
        { query: 'SELECT * FROM test' },
        {
          sessionId: 'test-session',
          organizationId: 'test-org',
          userId: 'test-user',
        }
      );

      expect(result.isError).toBe(true);

      const response = JSON.parse(result.content[0].text!);
      expect(response.success).toBe(false);
      expect(response.error).toContain('provider not configured');
    });

    it('should handle malformed input gracefully', async () => {
      const context: ToolExecutionContext = {
        sessionId: 'session-123',
        organizationId: 'org-security-team',
        userId: 'user-alice',
      };

      // Test with various malformed inputs
      const malformedInputs = [
        { query: null },
        { query: undefined },
        { query: '' },
        { query: '   ' },
        { limit: -1 },
        { limit: 'invalid' },
        { offset: -5 },
      ];

      for (const input of malformedInputs) {
        const result = await queryTool.execute(input as any, context);
        // Should either handle gracefully or provide meaningful error
        expect(result.content).toHaveLength(1);
        expect(result.content[0].text).toBeDefined();
      }
    });
  });
});
