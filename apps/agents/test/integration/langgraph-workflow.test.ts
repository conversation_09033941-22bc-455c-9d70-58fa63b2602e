/**
 * Integration tests for LangGraph workflow implementation
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MCPConnectionManager } from '../../src/integration/mcp-server/connection-manager';
import { LangGraphSupervisor } from '../../src/orchestration/langgraph/supervisor';
import { CompatibilityAdapter } from '../../src/orchestration/langgraph/compatibility-adapter';
import { WorkflowInput } from '../../src/orchestration/langgraph/types';

describe('LangGraph Workflow Integration', () => {
  let connectionManager: MCPConnectionManager;
  let supervisor: LangGraphSupervisor;

  beforeEach(() => {
    // Mock the connection manager
    connectionManager = {
      executeToolCall: vi.fn().mockResolvedValue({
        success: true,
        data: [
          { id: '1', title: 'Document 1', content: 'Content 1' },
          { id: '2', title: 'Document 2', content: 'Content 2' },
        ],
      }),
    } as any;

    // Create supervisor
    supervisor = new LangGraphSupervisor(connectionManager);
  });

  describe('Simple Query Workflow', () => {
    it('should execute a simple query workflow', async () => {
      const input: WorkflowInput = {
        input: 'What is information security?',
        organizationId: 'test-org',
        userId: 'test-user',
        sessionId: 'test-session',
      };

      const result = await supervisor.executeWorkflow(input);

      expect(result).toBeDefined();
      expect(result.output).toBeDefined();
      expect(result.output.response).toContain('Retrieved 2 documents');
      expect(result.workflowType).toBe('simple');
      expect(result.toolsUsed).toContain('get-all-documents-tool');
    });

    it('should handle tool execution failures gracefully', async () => {
      // Mock tool failure
      connectionManager.executeToolCall = vi.fn().mockResolvedValue({
        success: false,
        error: 'Tool execution failed',
      });

      const input: WorkflowInput = {
        input: 'Test query',
        organizationId: 'test-org',
        userId: 'test-user',
      };

      const result = await supervisor.executeWorkflow(input);

      expect(result.output.response).toContain('error retrieving documents');
      expect(result.output.warning).toBeUndefined(); // Warning should be in result.warning
      expect(result.warning).toContain('Document retrieval failed');
    });
  });

  describe('Compatibility Adapter', () => {
    it('should transform legacy request formats', () => {
      const legacyRequests = [
        { prompt: 'Test prompt', organizationId: 'org1' },
        { query: 'Test query', userId: 'user1' },
        { message: 'Test message' },
        { input: 'Test input string' },
        { input: { prompt: 'Nested prompt' } },
      ];

      legacyRequests.forEach(request => {
        const transformed = CompatibilityAdapter.transformRequest(request);

        expect(transformed).toBeDefined();
        expect(typeof transformed.input).toBe('string');
        expect(transformed.organizationId).toBeDefined();
        expect(transformed.workflowHints?.maintainCompatibility).toBe(true);
      });
    });

    it('should generate session IDs when not provided', () => {
      const request = { prompt: 'Test', organizationId: 'org1' };
      const transformed = CompatibilityAdapter.transformRequest(request);

      expect(transformed.sessionId).toBeDefined();
      expect(transformed.sessionId).toContain('session_org1_');
    });

    it('should transform workflow results to legacy format', () => {
      const workflowResult = {
        output: {
          response: 'Test response',
          tool_used: 'test-tool',
          execution_time_ms: 100,
        },
        metadata: { latencyMs: 100 },
      };

      const transformed = CompatibilityAdapter.transformResponse(workflowResult);

      expect(transformed).toEqual(workflowResult.output);
    });
  });

  describe('Workflow State Management', () => {
    it('should track active workflows', async () => {
      const input: WorkflowInput = {
        input: 'Test query',
        organizationId: 'test-org',
      };

      // Start workflow
      const resultPromise = supervisor.executeWorkflow(input);

      // Check active workflows (might be empty if execution is too fast)
      const activeWorkflows = supervisor.getActiveWorkflows();
      expect(Array.isArray(activeWorkflows)).toBe(true);

      // Wait for completion
      await resultPromise;
    });

    it('should provide debug information when enabled', async () => {
      const input: WorkflowInput = {
        input: 'Test query',
        organizationId: 'test-org',
        workflowHints: {
          debugMode: true,
        },
      };

      const result = await supervisor.executeWorkflow(input);

      expect(result.debugInfo).toBeDefined();
      expect(result.debugInfo?.workflowSteps).toBeDefined();
      expect(result.debugInfo?.performanceMetrics).toBeDefined();
    });
  });
});
