import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { LangGraphIntegration } from '../../src/integration/langgraph/langgraph-integration';
import { MCPConnectionManager } from '../../src/integration/mcp-server/connection-manager';
import { StandardAgentOutput } from '../../src/base/types';
import { WorkflowResult } from '../../src/orchestration/langgraph/types';

// Mock the connection manager to avoid actual network/tool calls
vi.mock('../../src/integration/mcp-server/connection-manager');

describe('Multi-Agent Workflow Integration Tests', () => {
  let langGraphIntegration: LangGraphIntegration;
  let mockConnectionManager: MCPConnectionManager;

  beforeAll(() => {
    mockConnectionManager = new (MCPConnectionManager as any)();
    langGraphIntegration = new LangGraphIntegration({ enableDebug: false });
    // Initialize supervisor with mocked connection manager
    langGraphIntegration.initializeSupervisor(mockConnectionManager as any);
    process.env.ENABLE_LANGGRAPH = 'true';
    process.env.ENABLE_INTELLIGENCE_LAYER = 'true';
    process.env.ENABLE_SPECIALIZED_AGENTS = 'true';
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  it('should successfully execute a complex workflow involving search and document analysis', async () => {
    // Mock the supervisor's tool execution to simulate agent calls
    const mockResult: WorkflowResult = {
      output: {
        response:
          'Analysis of phishing prevention documents complete. Key recommendations are summarized.',
        metadata: {
          session_id: 'test-session-1',
          conversation_entries: 0,
        },
        metrics: {
          tool_used: 'multi_agent_workflow',
          execution_time_ms: 450,
          rows: 0,
        },
      } as StandardAgentOutput,
      metadata: { latencyMs: 450 },
      executionTime: 450,
      workflowId: 'wf-1',
      workflowType: 'multi-agent',
      toolsUsed: ['search_agent', 'document_agent'],
      success: true,
    };

    const mockExecute = vi
      .spyOn(langGraphIntegration, 'executeWorkflow')
      // @ts-ignore - simplify typing for mock
      .mockResolvedValue(mockResult);

    const input = {
      prompt:
        'Find documents related to "phishing prevention" and then analyze the most relevant one for key security recommendations.',
      organizationId: 'org_123',
      userId: 'user_abc',
    };

    const result = await langGraphIntegration.executeWorkflow({
      sessionId: 'session_test_1',
      input: input.prompt,
      organizationId: input.organizationId,
      userId: input.userId,
    });

    expect(result).toBeDefined();
    expect(result.output).toBeDefined();
    expect(result.output.response).toContain('phishing prevention');
    expect(result.output.response).toContain('recommendations');
    expect(result.output.metrics.tool_used).toBe('multi_agent_workflow');

    // More robustly check the list of agents used from the result object
    expect(result.toolsUsed).toContain('search_agent');
    expect(result.toolsUsed).toContain('document_agent');

    expect(result.metadata?.latencyMs).toBeLessThan(5000);
    mockExecute.mockRestore();
  }, 10000);

  it('should gracefully fallback when a specialized agent fails', async () => {
    // Mock the supervisor to simulate a workflow where an agent fails
    const mockResult2: WorkflowResult = {
      output: {
        response: 'Fallback response: The document agent failed, but I can help.',
        metadata: {
          session_id: 'test-session-2',
          conversation_entries: 0,
        },
        metrics: {
          tool_used: 'multi_agent_workflow',
          execution_time_ms: 350,
          rows: 0,
        },
      } as StandardAgentOutput,
      metadata: { latencyMs: 350 },
      executionTime: 350,
      workflowId: 'wf-2',
      workflowType: 'multi-agent',
      toolsUsed: ['search_agent', 'ask_ai_agent'],
      warning: 'Agent execution failed for document_agent: Simulated DocumentAgent failure',
      success: true,
    };

    const mockExecute2 = vi
      .spyOn(langGraphIntegration, 'executeWorkflow')
      // @ts-ignore
      .mockResolvedValue(mockResult2);

    const input = {
      prompt: 'Analyze the attached security policy for compliance gaps.',
      organizationId: 'org_123',
      userId: 'user_abc',
    };

    const result2 = await langGraphIntegration.executeWorkflow({
      sessionId: 'session_test_2',
      input: input.prompt,
      organizationId: input.organizationId,
      userId: input.userId,
    });

    expect(result2).toBeDefined();
    expect(result2.output.response).toContain('Fallback response');
    expect(result2.warning).toContain('Simulated DocumentAgent failure');

    // Check that the fallback agent was used and the failing one was not
    expect(result2.toolsUsed).toContain('ask_ai_agent');
    expect(result2.toolsUsed).not.toContain('document_agent');

    mockExecute2.mockRestore();
  });
});
