/**
 * Comparison tests between manual and idiomatic LangGraph supervisor implementations
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { MCPConnectionManager } from '../../src/integration/mcp-server/connection-manager';
import { LangGraphSupervisor } from '../../src/orchestration/langgraph/supervisor';
import { IdiomaticLangGraphSupervisor } from '../../src/orchestration/langgraph/idiomatic-supervisor';
import { LangGraphIntegration } from '../../src/integration/langgraph/langgraph-integration';
import { WorkflowInput } from '../../src/orchestration/langgraph/types';

describe('Supervisor Implementation Comparison', () => {
  let connectionManager: MCPConnectionManager;
  let manualSupervisor: LangGraphSupervisor;
  let idiomaticSupervisor: IdiomaticLangGraphSupervisor;
  let legacyIntegration: LangGraphIntegration;
  let idiomaticIntegration: LangGraphIntegration;

  beforeEach(() => {
    // Mock the connection manager with realistic data
    connectionManager = {
      executeToolCall: vi.fn().mockResolvedValue({
        success: true,
        data: [
          {
            id: '1',
            title: 'Password Security Policy',
            content:
              'Passwords must be at least 12 characters long, include uppercase, lowercase, numbers, and special characters. Enable multi-factor authentication.',
            type: 'policy',
            source: 'security-policies.pdf',
          },
          {
            id: '2',
            title: 'Network Security Best Practices',
            content:
              'Implement network segmentation, use firewalls, monitor traffic, and regularly update security patches.',
            type: 'guide',
            source: 'network-guide.pdf',
          },
          {
            id: '3',
            title: 'Incident Response Procedures',
            content:
              'In case of security incident: 1) Isolate affected systems, 2) Assess damage, 3) Notify stakeholders, 4) Document findings.',
            type: 'procedure',
            source: 'incident-response.pdf',
          },
        ],
      }),
    } as any;

    // Create both supervisor implementations
    manualSupervisor = new LangGraphSupervisor(connectionManager, {
      enableDebug: true,
      fallbackEnabled: true,
      maxExecutionTime: 30000,
    });

    idiomaticSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
      enableDebug: true,
      fallbackEnabled: true,
      maxExecutionTime: 30000,
      outputMode: 'last_message',
      enablePersistence: true,
    });

    // Create integration instances
    legacyIntegration = new LangGraphIntegration({
      enableDebug: true,
      useIdiomaticSupervisor: false,
      fallbackEnabled: true,
    });

    idiomaticIntegration = new LangGraphIntegration({
      enableDebug: true,
      useIdiomaticSupervisor: true,
      fallbackEnabled: true,
      outputMode: 'last_message',
      enablePersistence: true,
    });

    // Initialize integrations
    legacyIntegration.initializeSupervisor(connectionManager);
    idiomaticIntegration.initializeSupervisor(connectionManager);
  });

  afterEach(async () => {
    if (manualSupervisor) {
      await manualSupervisor.shutdown();
    }
    if (idiomaticSupervisor) {
      await idiomaticSupervisor.shutdown();
    }
    if (legacyIntegration) {
      await legacyIntegration.destroy();
    }
    if (idiomaticIntegration) {
      await idiomaticIntegration.destroy();
    }
  });

  describe('Functional Equivalence', () => {
    const testQueries = [
      'What are the password requirements?',
      'How should we handle security incidents?',
      'What are the network security best practices?',
      'Tell me about multi-factor authentication',
    ];

    testQueries.forEach(query => {
      it(`should produce similar results for query: "${query}"`, async () => {
        const input: WorkflowInput = {
          input: query,
          organizationId: 'test-org',
          userId: 'test-user',
          sessionId: `test-session-${Date.now()}`,
        };

        // Execute with both supervisors
        const [manualResult, idiomaticResult] = await Promise.all([
          manualSupervisor.executeWorkflow(input),
          idiomaticSupervisor.executeWorkflow(input),
        ]);

        // Both should succeed
        expect(manualResult).toBeDefined();
        expect(idiomaticResult).toBeDefined();

        // Both should have meaningful output
        expect(manualResult.output).toBeDefined();
        expect(idiomaticResult.output).toBeDefined();
        expect(typeof manualResult.output).toBe('string');
        expect(typeof idiomaticResult.output).toBe('string');
        expect(manualResult.output.length).toBeGreaterThan(10);
        expect(idiomaticResult.output.length).toBeGreaterThan(10);

        // Both should have execution metrics
        expect(manualResult.executionTime).toBeGreaterThan(0);
        expect(idiomaticResult.executionTime).toBeGreaterThan(0);

        // Both should have used tools
        expect(Array.isArray(manualResult.toolsUsed)).toBe(true);
        expect(Array.isArray(idiomaticResult.toolsUsed)).toBe(true);

        // Both should have workflow IDs
        expect(manualResult.workflowId).toBeDefined();
        expect(idiomaticResult.workflowId).toBeDefined();
      });
    });
  });

  describe('Integration Layer Compatibility', () => {
    it('should maintain backward compatibility through integration layer', async () => {
      const input: WorkflowInput = {
        input: 'What are the security policies?',
        organizationId: 'test-org',
        sessionId: 'compatibility-test',
      };

      // Execute through both integration instances
      const [legacyResult, idiomaticResult] = await Promise.all([
        legacyIntegration.executeWorkflow(input),
        idiomaticIntegration.executeWorkflow(input),
      ]);

      // Both should succeed and have similar structure
      expect(legacyResult).toBeDefined();
      expect(idiomaticResult).toBeDefined();

      // Check result structure compatibility
      expect(legacyResult.output).toBeDefined();
      expect(idiomaticResult.output).toBeDefined();
      expect(legacyResult.executionTime).toBeDefined();
      expect(idiomaticResult.executionTime).toBeDefined();
      expect(legacyResult.metadata).toBeDefined();
      expect(idiomaticResult.metadata).toBeDefined();

      // Both should have similar metadata structure
      expect(typeof legacyResult.metadata.latencyMs).toBe('number');
      expect(typeof idiomaticResult.metadata.latencyMs).toBe('number');
    });

    it('should handle configuration differences appropriately', () => {
      // Legacy integration metrics
      const legacyMetrics = legacyIntegration.getMetrics();
      expect(legacyMetrics).toBeDefined();

      // Idiomatic integration metrics
      const idiomaticMetrics = idiomaticIntegration.getMetrics();
      expect(idiomaticMetrics).toBeDefined();

      // Both should be healthy
      expect(legacyMetrics.status).toBe('healthy');
      expect(idiomaticMetrics.status).toBe('healthy');
    });
  });

  describe('Performance Characteristics', () => {
    it('should have comparable execution times', async () => {
      const input: WorkflowInput = {
        input: 'Performance test query about cybersecurity',
        organizationId: 'test-org',
      };

      // Measure execution times
      const manualStart = Date.now();
      const manualResult = await manualSupervisor.executeWorkflow(input);
      const manualTime = Date.now() - manualStart;

      const idiomaticStart = Date.now();
      const idiomaticResult = await idiomaticSupervisor.executeWorkflow(input);
      const idiomaticTime = Date.now() - idiomaticStart;

      // Both should complete in reasonable time
      expect(manualTime).toBeLessThan(30000); // 30 seconds
      expect(idiomaticTime).toBeLessThan(30000); // 30 seconds

      // Results should be valid
      expect(manualResult.output).toBeDefined();
      expect(idiomaticResult.output).toBeDefined();

      console.log(`Manual supervisor: ${manualTime}ms`);
      console.log(`Idiomatic supervisor: ${idiomaticTime}ms`);
    });

    it('should handle concurrent requests similarly', async () => {
      const concurrentRequests = 3;
      const inputs = Array.from({ length: concurrentRequests }, (_, i) => ({
        input: `Concurrent test query ${i + 1}`,
        organizationId: 'test-org',
        sessionId: `concurrent-${i}`,
      }));

      // Test manual supervisor concurrency
      const manualStart = Date.now();
      const manualResults = await Promise.all(
        inputs.map(input => manualSupervisor.executeWorkflow(input))
      );
      const manualConcurrentTime = Date.now() - manualStart;

      // Test idiomatic supervisor concurrency
      const idiomaticStart = Date.now();
      const idiomaticResults = await Promise.all(
        inputs.map(input => idiomaticSupervisor.executeWorkflow(input))
      );
      const idiomaticConcurrentTime = Date.now() - idiomaticStart;

      // All requests should succeed
      expect(manualResults).toHaveLength(concurrentRequests);
      expect(idiomaticResults).toHaveLength(concurrentRequests);

      manualResults.forEach(result => {
        expect(result.output).toBeDefined();
        expect(result.executionTime).toBeGreaterThan(0);
      });

      idiomaticResults.forEach(result => {
        expect(result.output).toBeDefined();
        expect(result.executionTime).toBeGreaterThan(0);
      });

      console.log(`Manual concurrent: ${manualConcurrentTime}ms`);
      console.log(`Idiomatic concurrent: ${idiomaticConcurrentTime}ms`);
    });
  });

  describe('Error Handling Consistency', () => {
    it('should handle tool failures consistently', async () => {
      // Mock tool failure
      connectionManager.executeToolCall = vi.fn().mockResolvedValue({
        success: false,
        error: 'Simulated tool failure',
      });

      const input: WorkflowInput = {
        input: 'Test query with tool failure',
        organizationId: 'test-org',
      };

      // Both supervisors should handle the failure gracefully
      const [manualResult, idiomaticResult] = await Promise.all([
        manualSupervisor.executeWorkflow(input),
        idiomaticSupervisor.executeWorkflow(input),
      ]);

      // Both should provide fallback responses
      expect(manualResult).toBeDefined();
      expect(idiomaticResult).toBeDefined();
      expect(manualResult.output).toBeDefined();
      expect(idiomaticResult.output).toBeDefined();

      // Both should indicate some form of error or limitation
      const manualHasError =
        manualResult.warning || (manualResult.metadata && manualResult.metadata.warnings);
      const idiomaticHasError =
        idiomaticResult.warning || (idiomaticResult.metadata && idiomaticResult.metadata.warnings);

      // At least one should indicate an error condition
      expect(manualHasError || idiomaticHasError).toBeTruthy();
    });
  });

  describe('Feature Parity', () => {
    it('should support similar configuration options', () => {
      // Manual supervisor metrics
      const manualMetrics = manualSupervisor.getMetrics();
      expect(manualMetrics.workflowInitialized).toBe(true);
      expect(manualMetrics.debugEnabled).toBe(true);
      expect(manualMetrics.fallbackEnabled).toBe(true);

      // Idiomatic supervisor metrics
      const idiomaticMetrics = idiomaticSupervisor.getMetrics();
      expect(idiomaticMetrics.workflowInitialized).toBe(true);
      expect(idiomaticMetrics.debugEnabled).toBe(true);
      expect(idiomaticMetrics.fallbackEnabled).toBe(true);

      // Idiomatic supervisor should have additional features
      expect(idiomaticMetrics.persistenceEnabled).toBe(true);
      expect(idiomaticMetrics.outputMode).toBe('last_message');
      expect(typeof idiomaticMetrics.activeThreads).toBe('number');
    });

    it('should support health checks', async () => {
      const manualHealth = await manualSupervisor.healthCheck();
      const idiomaticHealth = await idiomaticSupervisor.healthCheck();

      expect(manualHealth.status).toBe('healthy');
      expect(idiomaticHealth.status).toBe('healthy');
      expect(manualHealth.metrics).toBeDefined();
      expect(idiomaticHealth.metrics).toBeDefined();
    });

    it('should support graceful shutdown', async () => {
      // Create test instances for shutdown testing
      const testManual = new LangGraphSupervisor(connectionManager);
      const testIdiomatic = new IdiomaticLangGraphSupervisor(connectionManager);

      // Both should shutdown without errors
      await expect(testManual.shutdown()).resolves.not.toThrow();
      await expect(testIdiomatic.shutdown()).resolves.not.toThrow();
    });
  });
});
