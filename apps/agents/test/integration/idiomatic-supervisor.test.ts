/**
 * Integration tests for the idiomatic LangGraph supervisor implementation
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { MCPConnectionManager } from '../../src/integration/mcp-server/connection-manager';
import { IdiomaticLangGraphSupervisor } from '../../src/orchestration/langgraph/idiomatic-supervisor';
import { WorkflowInput } from '../../src/orchestration/langgraph/types';

describe('IdiomaticLangGraphSupervisor Integration Tests', () => {
  let connectionManager: MCPConnectionManager;
  let supervisor: IdiomaticLangGraphSupervisor;

  beforeEach(() => {
    // Mock the connection manager
    connectionManager = {
      executeToolCall: vi.fn().mockResolvedValue({
        success: true,
        data: [
          {
            id: '1',
            title: 'Cybersecurity Best Practices',
            content: 'Use strong passwords, enable 2FA, keep software updated.',
            type: 'guide',
          },
          {
            id: '2',
            title: 'Network Security Guidelines',
            content: 'Implement firewalls, monitor network traffic, use VPNs.',
            type: 'policy',
          },
        ],
      }),
    } as any;

    // Create idiomatic supervisor with test configuration
    supervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
      enableDebug: true,
      fallbackEnabled: true,
      maxExecutionTime: 30000,
      outputMode: 'last_message',
      enablePersistence: true,
      maxConversationLength: 50,
    });
  });

  afterEach(async () => {
    if (supervisor) {
      await supervisor.shutdown();
    }
  });

  describe('Initialization and Configuration', () => {
    it('should initialize successfully with default configuration', () => {
      const defaultSupervisor = new IdiomaticLangGraphSupervisor(connectionManager);
      expect(defaultSupervisor).toBeDefined();

      const metrics = defaultSupervisor.getMetrics();
      expect(metrics.workflowInitialized).toBe(true);
      expect(metrics.persistenceEnabled).toBe(true);
      expect(metrics.outputMode).toBe('last_message');
    });

    it('should initialize with custom configuration', () => {
      const customSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
        enableDebug: false,
        fallbackEnabled: false,
        outputMode: 'full_history',
        enablePersistence: false,
        maxConversationLength: 200,
      });

      const metrics = customSupervisor.getMetrics();
      expect(metrics.debugEnabled).toBe(false);
      expect(metrics.fallbackEnabled).toBe(false);
      expect(metrics.outputMode).toBe('full_history');
      expect(metrics.persistenceEnabled).toBe(false);
      expect(metrics.maxConversationLength).toBe(200);
    });

    it('should pass health check when properly initialized', async () => {
      const health = await supervisor.healthCheck();
      expect(health.status).toBe('healthy');
      expect(health.metrics.workflowInitialized).toBe(true);
    });
  });

  describe('Workflow Execution', () => {
    it('should execute a simple query workflow successfully', async () => {
      const input: WorkflowInput = {
        input: 'What are the best cybersecurity practices?',
        organizationId: 'test-org',
        userId: 'test-user',
        sessionId: 'test-session',
      };

      const result = await supervisor.executeWorkflow(input);

      expect(result).toBeDefined();
      expect(result.output).toBeDefined();
      expect(typeof result.output).toBe('string');
      expect(result.output.length).toBeGreaterThan(0);
      expect(result.executionTime).toBeGreaterThan(0);
      expect(result.workflowId).toBeDefined();
      expect(result.toolsUsed).toBeDefined();
      expect(Array.isArray(result.toolsUsed)).toBe(true);
    });

    it('should handle workflow with persistence and thread management', async () => {
      const sessionId = 'persistent-session-123';

      const input1: WorkflowInput = {
        input: 'What is information security?',
        organizationId: 'test-org',
        sessionId,
      };

      const result1 = await supervisor.executeWorkflow(input1);
      expect(result1.output).toBeDefined();

      // Second query in the same session
      const input2: WorkflowInput = {
        input: 'Tell me more about network security',
        organizationId: 'test-org',
        sessionId,
      };

      const result2 = await supervisor.executeWorkflow(input2);
      expect(result2.output).toBeDefined();

      // Check conversation history
      const history = await supervisor.getConversationHistory(sessionId);
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeGreaterThan(0);
    });

    it('should handle tool execution failures gracefully', async () => {
      // Mock tool failure
      connectionManager.executeToolCall = vi.fn().mockResolvedValue({
        success: false,
        error: 'Tool execution failed',
      });

      const input: WorkflowInput = {
        input: 'Test query with failing tools',
        organizationId: 'test-org',
        userId: 'test-user',
      };

      const result = await supervisor.executeWorkflow(input);

      // Should still return a result due to fallback
      expect(result).toBeDefined();
      expect(result.output).toBeDefined();
      expect(typeof result.output).toBe('string');
    });

    it('should respect execution time limits', async () => {
      const quickSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
        maxExecutionTime: 100, // Very short timeout
        enableDebug: true,
      });

      const input: WorkflowInput = {
        input: 'Complex query that might take time',
        organizationId: 'test-org',
      };

      const startTime = Date.now();
      const result = await quickSupervisor.executeWorkflow(input);
      const actualTime = Date.now() - startTime;

      expect(result).toBeDefined();
      // Should complete relatively quickly due to timeout or fallback
      expect(actualTime).toBeLessThan(5000); // 5 seconds max
    });
  });

  describe('Memory and Persistence Features', () => {
    it('should track active threads', async () => {
      const threadId1 = 'thread-1';
      const threadId2 = 'thread-2';

      expect(supervisor.getActiveThreadCount()).toBe(0);

      // Start workflows in different threads
      const promise1 = supervisor.executeWorkflow({
        input: 'Query 1',
        organizationId: 'test-org',
        sessionId: threadId1,
      });

      const promise2 = supervisor.executeWorkflow({
        input: 'Query 2',
        organizationId: 'test-org',
        sessionId: threadId2,
      });

      await Promise.all([promise1, promise2]);

      // Check thread tracking
      const activeThreads = supervisor.getActiveThreadIds();
      expect(Array.isArray(activeThreads)).toBe(true);
    });

    it('should provide memory statistics', () => {
      const memoryStats = supervisor.getMemoryStats();

      expect(memoryStats).toBeDefined();
      expect(typeof memoryStats.activeThreads).toBe('number');
      expect(typeof memoryStats.checkpointerType).toBe('string');
      expect(typeof memoryStats.storeType).toBe('string');
      expect(typeof memoryStats.persistenceEnabled).toBe('boolean');
    });

    it('should clear conversation history', async () => {
      const threadId = 'test-clear-thread';

      // Create some conversation history
      await supervisor.executeWorkflow({
        input: 'Initial query',
        organizationId: 'test-org',
        sessionId: threadId,
      });

      expect(supervisor.isThreadActive(threadId)).toBe(false); // Should be cleaned up after execution

      // Clear history
      await supervisor.clearConversationHistory(threadId);

      // Verify thread is no longer active
      expect(supervisor.isThreadActive(threadId)).toBe(false);
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle initialization errors gracefully', () => {
      // This test verifies that the supervisor handles errors during initialization
      expect(() => {
        new IdiomaticLangGraphSupervisor(connectionManager, {
          enableDebug: true,
          fallbackEnabled: true,
        });
      }).not.toThrow();
    });

    it('should provide fallback responses when enabled', async () => {
      const fallbackSupervisor = new IdiomaticLangGraphSupervisor(connectionManager, {
        fallbackEnabled: true,
        enableDebug: true,
      });

      // Mock a severe failure
      const originalExecute = fallbackSupervisor.executeWorkflow;
      fallbackSupervisor.executeWorkflow = vi.fn().mockImplementation(async input => {
        // Call the original method but expect it might fail
        try {
          return await originalExecute.call(fallbackSupervisor, input);
        } catch (error) {
          // Return fallback result
          return {
            output: 'Fallback response due to error',
            executionTime: 0,
            toolsUsed: [],
            metadata: { warnings: ['Fallback activated'] },
            workflowId: 'fallback-workflow',
          };
        }
      });

      const input: WorkflowInput = {
        input: 'Test query',
        organizationId: 'test-org',
      };

      const result = await fallbackSupervisor.executeWorkflow(input);
      expect(result).toBeDefined();
      expect(result.output).toBeDefined();
    });
  });

  describe('Metrics and Monitoring', () => {
    it('should provide comprehensive metrics', () => {
      const metrics = supervisor.getMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.workflowInitialized).toBe('boolean');
      expect(typeof metrics.debugEnabled).toBe('boolean');
      expect(typeof metrics.fallbackEnabled).toBe('boolean');
      expect(typeof metrics.maxExecutionTime).toBe('number');
      expect(typeof metrics.outputMode).toBe('string');
      expect(typeof metrics.persistenceEnabled).toBe('boolean');
      expect(typeof metrics.activeThreads).toBe('number');
      expect(typeof metrics.maxConversationLength).toBe('number');
      expect(typeof metrics.resourceLimitsEnabled).toBe('boolean');
    });

    it('should track workflow execution metrics', async () => {
      const input: WorkflowInput = {
        input: 'Metrics test query',
        organizationId: 'test-org',
      };

      const result = await supervisor.executeWorkflow(input);

      expect(result.metadata).toBeDefined();
      expect(typeof result.metadata.latencyMs).toBe('number');
      expect(result.metadata.latencyMs).toBeGreaterThan(0);
      expect(Array.isArray(result.metadata.toolsInvoked)).toBe(true);
    });
  });

  describe('Shutdown and Cleanup', () => {
    it('should shutdown gracefully', async () => {
      const testSupervisor = new IdiomaticLangGraphSupervisor(connectionManager);

      expect(testSupervisor.getMetrics().workflowInitialized).toBe(true);

      await testSupervisor.shutdown();

      // After shutdown, health check should indicate unhealthy status
      const health = await testSupervisor.healthCheck();
      expect(health.status).toBe('unhealthy');
    });
  });
});
