// import { describe, it, expect, beforeEach, Mock } from 'vitest';
// import {
//   OpenAIChatAgent,
//   createChatAgent,
//   ChatAgentConfig,
// } from '../../src/sub-agents/openai/chat-agent';

// Mock the OpenAI SDK
import { vi } from 'vitest';
vi.mock('@openai/agents', () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

// import { Agent, run } from '@openai/agents';

// describe('OpenAIChatAgent', () => {
//   let chatAgent: OpenAIChatAgent;
//   let mockRun: Mock;

//   beforeEach(() => {
//     vi.clearAllMocks();
//     mockRun = vi.mocked(run);
//     chatAgent = new OpenAIChatAgent({
//       model: 'gpt-4o-mini',
//       instructions: 'helpful AI assistant',
//       temperature: 0.7,
//       maxTokens: 1000,
//     });
//   });

//   describe('constructor', () => {
//     it('should create agent with default configuration', () => {
//       expect(Agent).toHaveBeenCalledWith({
//         name: 'InfoSec Chat Assistant',
//         instructions: expect.stringContaining('helpful AI assistant'),
//         model: 'gpt-4o-mini',
//       });
//     });

//     it('should create agent with custom configuration', () => {
//       const customConfig: Partial<ChatAgentConfig> = {
//         model: 'gpt-4o',
//         instructions: 'Custom instructions',
//         temperature: 0.5,
//       };

//       new OpenAIChatAgent(customConfig as ChatAgentConfig);

//       expect(Agent).toHaveBeenCalledWith({
//         name: 'InfoSec Chat Assistant',
//         instructions: 'Custom instructions',
//         model: 'gpt-4o',
//       });
//     });
//   });

//   describe('chat', () => {
//     it('should process user prompt and return response', async () => {
//       const mockResponse = 'Hello! How can I help you?';
//       mockRun.mockResolvedValue(mockResponse);

//       const result = await chatAgent.chat('Hello');

//       expect(mockRun).toHaveBeenCalledWith(
//         expect.any(Object), // the agent instance
//         'Hello',
//         expect.any(Object) // options
//       );
//       expect(result).toBe(mockResponse);
//     });

//     it('should handle object responses', async () => {
//       const mockResponse = { output: 'Response from output field' };
//       mockRun.mockResolvedValue(mockResponse);

//       const result = await chatAgent.chat('Test prompt');

//       expect(result).toBe('Response from output field');
//     });

//     it('should handle content field responses', async () => {
//       const mockResponse = { content: 'Response from content field' };
//       mockRun.mockResolvedValue(mockResponse);

//       const result = await chatAgent.chat('Test prompt');

//       expect(result).toBe('Response from content field');
//     });

//     it('should handle message field responses', async () => {
//       const mockResponse = { message: 'Response from message field' };
//       mockRun.mockResolvedValue(mockResponse);

//       const result = await chatAgent.chat('Test prompt');

//       expect(result).toBe('Response from message field');
//     });

//     it('should stringify complex objects', async () => {
//       const mockResponse = { data: { nested: 'value' } };
//       mockRun.mockResolvedValue(mockResponse);

//       const result = await chatAgent.chat('Test prompt');

//       expect(result).toBe(JSON.stringify(mockResponse));
//     });

//     it('should validate input prompt', async () => {
//       await expect(chatAgent.chat('')).rejects.toThrow(
//         'Invalid prompt: must be a non-empty string'
//       );
//       await expect(chatAgent.chat(null as any)).rejects.toThrow(
//         'Invalid prompt: must be a non-empty string'
//       );
//     });

//     it('should handle API errors gracefully', async () => {
//       const mockError = new Error('OpenAI API error');
//       mockRun.mockRejectedValue(mockError);

//       await expect(chatAgent.chat('Test')).rejects.toThrow('Chat agent failed: OpenAI API error');
//     });
//   });

//   describe('getConfig', () => {
//     it('should return current configuration', () => {
//       const config = chatAgent;

//       expect(config).toEqual({
//         model: 'gpt-4o-mini',
//         instructions: expect.stringContaining('helpful AI assistant'),
//         temperature: 0.7,
//         maxTokens: 1000,
//         organizationId: '',
//       });
//     });
//   });

//   describe('healthCheck', () => {
//     it('should return healthy status', async () => {
//       const health = await chatAgent.healthCheck();

//       expect(health).toEqual({
//         status: 'healthy',
//         message: 'Chat agent is operational',
//       });
//     });
//   });

//   describe('shutdown', () => {
//     it('should shutdown gracefully', async () => {
//       const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

//       // await chatAgent.shutdown();

//       expect(consoleSpy).toHaveBeenCalledWith('OpenAI Chat Agent shutting down');

//       consoleSpy.mockRestore();
//     });
//   });

//   describe('updateInstructions', () => {
//     it('should log warning for unimplemented feature', () => {
//       const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

//       chatAgent.invoke('New instructions');

//       expect(consoleSpy).toHaveBeenCalledWith('Dynamic instruction updates not yet implemented');

//       consoleSpy.mockRestore();
//     });
//   });
// });

// describe('createChatAgent factory', () => {
//   beforeEach(() => {
//     vi.clearAllMocks();
//   });

//   it('should create agent with environment configuration', () => {
//     // Mock environment variables
//     process.env.OPENAI_CHAT_MODEL = 'gpt-4o';
//     process.env.OPENAI_CHAT_INSTRUCTIONS = 'Custom env instructions';

//     const agent = createChatAgent();

//     expect(Agent).toHaveBeenCalledWith({
//       name: 'InfoSec Chat Assistant',
//       instructions: 'Custom env instructions',
//       model: 'gpt-4o',
//     });

//     // Clean up
//     delete process.env.OPENAI_CHAT_MODEL;
//     delete process.env.OPENAI_CHAT_INSTRUCTIONS;
//   });

//   it('should override environment with provided config', () => {
//     process.env.OPENAI_CHAT_MODEL = 'gpt-4o';

//     const agent = createChatAgent({ model: 'gpt-4o-mini' });

//     expect(Agent).toHaveBeenCalledWith(
//       expect.objectContaining({
//         model: 'gpt-4o-mini', // Should use provided config over env
//       })
//     );

//     // Clean up
//     delete process.env.OPENAI_CHAT_MODEL;
//   });
// });
