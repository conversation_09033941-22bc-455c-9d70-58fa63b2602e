import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AbstractAgent } from '../../src/base/agent';
import {
  AgentConfig,
  AgentArchetype,
  AgentCapability,
  AgentInput,
  AgentResult,
  AgentChunk,
  HealthStatus,
} from '../../src/base/types';

// Mock concrete implementation for testing
class TestAgent extends AbstractAgent {
  private shouldFail = false;
  private initializeCallback?: () => void;
  private destroyCallback?: () => void;

  constructor(config: AgentConfig) {
    super(config);
  }

  setFailure(shouldFail: boolean): void {
    this.shouldFail = shouldFail;
  }

  setInitializeCallback(callback: () => void): void {
    this.initializeCallback = callback;
  }

  setDestroyCallback(callback: () => void): void {
    this.destroyCallback = callback;
  }

  protected async onInitialize(): Promise<void> {
    this.initializeCallback?.();
  }

  protected async onDestroy(): Promise<void> {
    this.destroyCallback?.();
  }

  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    if (this.shouldFail) {
      throw new Error('Test error');
    }

    await new Promise(resolve => setTimeout(resolve, 10)); // Simulate work

    return {
      output: `Processed: ${JSON.stringify(input.input)}`,
      metadata: {
        tokensUsed: 50,
        cost: 0.001,
        latencyMs: 10,
      },
    };
  }

  protected async *doStream(input: AgentInput): AsyncGenerator<AgentChunk> {
    if (this.shouldFail) {
      throw new Error('Test streaming error');
    }

    const words = ['Hello', 'world', 'from', 'streaming'];
    for (const word of words) {
      yield {
        type: 'content',
        content: word,
      };
      await new Promise(resolve => setTimeout(resolve, 5));
    }

    yield {
      type: 'metadata',
      content: { tokensUsed: 20, cost: 0.0005 },
    };
  }
}

describe('AbstractAgent', () => {
  let agent: TestAgent;
  let config: AgentConfig;

  beforeEach(() => {
    config = {
      name: 'test-agent',
      version: '0.0.1',
      archetype: AgentArchetype.REACTIVE,
      capabilities: [AgentCapability.CHAT, AgentCapability.STREAMING],
    };
    agent = new TestAgent(config);
  });

  describe('configuration', () => {
    it('should expose correct configuration properties', () => {
      expect(agent.name).toBe('test-agent');
      expect(agent.version).toBe('1.0.0');
      expect(agent.archetype).toBe(AgentArchetype.REACTIVE);
      expect(agent.capabilities).toEqual([AgentCapability.CHAT, AgentCapability.STREAMING]);
    });

    it('should update configuration during initialization', async () => {
      const newConfig = {
        ...config,
        name: 'updated-agent',
        version: '2.0.0',
      };

      await agent.initialize(newConfig);

      expect(agent.name).toBe('updated-agent');
      expect(agent.version).toBe('2.0.0');
    });
  });

  describe('lifecycle management', () => {
    it('should initialize successfully', async () => {
      const initCallback = vi.fn();
      agent.setInitializeCallback(initCallback);

      await agent.initialize(config);

      expect(initCallback).toHaveBeenCalledOnce();
    });

    it('should throw error if already initialized', async () => {
      await agent.initialize(config);

      await expect(agent.initialize(config)).rejects.toThrow(
        'Agent test-agent is already initialized'
      );
    });

    it('should destroy successfully', async () => {
      const destroyCallback = vi.fn();
      agent.setDestroyCallback(destroyCallback);

      await agent.initialize(config);
      await agent.destroy();

      expect(destroyCallback).toHaveBeenCalledOnce();
    });

    it('should handle destroy when not initialized', async () => {
      // Should not throw
      await expect(agent.destroy()).resolves.toBeUndefined();
    });
  });

  describe('invocation', () => {
    beforeEach(async () => {
      await agent.initialize(config);
    });

    it('should invoke successfully and track metrics', async () => {
      const input: AgentInput = { input: 'test message' };
      const result = await agent.invoke(input);

      expect(result).toBeDefined();
      expect(result.output).toBe('Processed: "test message"');
      expect(result.metadata?.tokensUsed).toBe(50);
      expect(result.metadata?.cost).toBe(0.001);

      const metrics = agent.getMetrics();
      expect(metrics.invocations).toBe(1);
      expect(metrics.errors).toBe(0);
      expect(metrics.tokensUsed).toBe(50);
      expect(metrics.cost).toBe(0.001);
      expect(metrics.lastInvocation).toBeInstanceOf(Date);
    });

    it('should throw error when not initialized', async () => {
      const uninitializedAgent = new TestAgent(config);
      const input: AgentInput = { input: 'test' };

      await expect(uninitializedAgent.invoke(input)).rejects.toThrow(
        'Agent test-agent is not initialized'
      );
    });

    it('should handle invocation errors and update metrics', async () => {
      agent.setFailure(true);
      const input: AgentInput = { input: 'test' };

      await expect(agent.invoke(input)).rejects.toThrow('Test error');

      const metrics = agent.getMetrics();
      expect(metrics.invocations).toBe(1);
      expect(metrics.errors).toBe(1);
    });

    it('should update latency metrics correctly', async () => {
      const input: AgentInput = { input: 'test' };

      // Invoke multiple times to test latency tracking
      await agent.invoke(input);
      await agent.invoke(input);
      await agent.invoke(input);

      const metrics = agent.getMetrics();
      expect(metrics.invocations).toBe(3);
      expect(metrics.avgLatency).toBeGreaterThan(0);
      expect(metrics.p95Latency).toBeGreaterThan(0);
      expect(metrics.p99Latency).toBeGreaterThan(0);
    });
  });

  describe('streaming', () => {
    beforeEach(async () => {
      await agent.initialize(config);
    });

    it('should stream chunks successfully', async () => {
      const input: AgentInput = { input: 'test stream' };
      const chunks: AgentChunk[] = [];

      for await (const chunk of agent.stream(input)) {
        chunks.push(chunk);
      }

      expect(chunks).toHaveLength(5); // 4 content + 1 metadata
      expect(chunks[0].type).toBe('content');
      expect(chunks[0].content).toBe('Hello');
      expect(chunks[4].type).toBe('metadata');
      expect(chunks[4].content.tokensUsed).toBe(20);

      const metrics = agent.getMetrics();
      expect(metrics.invocations).toBe(1);
    });

    it('should throw error for non-streaming agents', async () => {
      const nonStreamingConfig = {
        ...config,
        capabilities: [AgentCapability.CHAT], // Remove streaming
      };
      const nonStreamingAgent = new TestAgent(nonStreamingConfig);
      await nonStreamingAgent.initialize(nonStreamingConfig);

      const input: AgentInput = { input: 'test' };

      await expect(async () => {
        for await (const _chunk of nonStreamingAgent.stream(input)) {
          // Should not reach here
        }
      }).rejects.toThrow('Agent test-agent does not support streaming');
    });

    it('should handle streaming errors', async () => {
      agent.setFailure(true);
      const input: AgentInput = { input: 'test' };
      const chunks: AgentChunk[] = [];

      for await (const chunk of agent.stream(input)) {
        chunks.push(chunk);
      }

      expect(chunks).toHaveLength(1);
      expect(chunks[0].type).toBe('error');
      expect(chunks[0].content).toBeInstanceOf(Error);

      const metrics = agent.getMetrics();
      expect(metrics.errors).toBe(1);
    });

    it('should throw error when not initialized', async () => {
      const uninitializedAgent = new TestAgent(config);
      const input: AgentInput = { input: 'test' };

      await expect(async () => {
        for await (const _chunk of uninitializedAgent.stream(input)) {
          // Should not reach here
        }
      }).rejects.toThrow('Agent test-agent is not initialized');
    });
  });

  describe('health checks', () => {
    beforeEach(async () => {
      await agent.initialize(config);
    });

    it('should return healthy status by default', async () => {
      const health = await agent.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.timestamp).toBeInstanceOf(Date);
      expect(health.checks.memory).toBe(true);
      expect(health.checks.dependencies).toBe(true);
      expect(health.checks.model).toBe(true);
      expect(health.details?.initialized).toBe(true);
    });
  });

  describe('metrics tracking', () => {
    beforeEach(async () => {
      await agent.initialize(config);
    });

    it('should initialize with zero metrics', () => {
      const metrics = agent.getMetrics();

      expect(metrics.invocations).toBe(0);
      expect(metrics.errors).toBe(0);
      expect(metrics.avgLatency).toBe(0);
      expect(metrics.p95Latency).toBe(0);
      expect(metrics.p99Latency).toBe(0);
      expect(metrics.tokensUsed).toBe(0);
      expect(metrics.cost).toBe(0);
      expect(metrics.lastInvocation).toBeUndefined();
    });

    it('should return a copy of metrics', () => {
      const metrics1 = agent.getMetrics();
      const metrics2 = agent.getMetrics();

      expect(metrics1).not.toBe(metrics2); // Different objects
      expect(metrics1).toEqual(metrics2); // Same content
    });

    it('should accumulate token usage and cost', async () => {
      const input: AgentInput = { input: 'test' };

      await agent.invoke(input);
      await agent.invoke(input);

      const metrics = agent.getMetrics();
      expect(metrics.tokensUsed).toBe(100); // 50 * 2
      expect(metrics.cost).toBe(0.002); // 0.001 * 2
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await agent.initialize(config);
    });

    it('should handle async errors properly', async () => {
      agent.setFailure(true);
      const input: AgentInput = { input: 'test' };

      await expect(agent.invoke(input)).rejects.toThrow('Test error');

      const metrics = agent.getMetrics();
      expect(metrics.errors).toBe(1);
      expect(metrics.invocations).toBe(1);
    });

    it('should preserve error information in metrics', async () => {
      agent.setFailure(true);
      const input: AgentInput = { input: 'test' };

      try {
        await agent.invoke(input);
      } catch {
        // Expected
      }

      const errorRate = agent.getMetrics().errors / agent.getMetrics().invocations;
      expect(errorRate).toBe(1.0);
    });
  });

  describe('edge cases', () => {
    it('should handle undefined metadata in results', async () => {
      class MinimalAgent extends AbstractAgent {
        protected async onInitialize(): Promise<void> {}
        protected async onDestroy(): Promise<void> {}
        protected async doInvoke(_input: AgentInput): Promise<AgentResult> {
          return { output: 'minimal' };
        }
      }

      const minimalAgent = new MinimalAgent(config);
      await minimalAgent.initialize(config);

      const result = await minimalAgent.invoke({ input: 'test' });
      expect(result.output).toBe('minimal');
      expect(result.metadata).toBeUndefined();

      const metrics = minimalAgent.getMetrics();
      expect(metrics.tokensUsed).toBe(0);
      expect(metrics.cost).toBe(0);
    });

    it('should handle null/undefined input gracefully', async () => {
      await agent.initialize(config);

      const result = await agent.invoke({ input: null });
      expect(result.output).toBe('Processed: null');
    });
  });
});
