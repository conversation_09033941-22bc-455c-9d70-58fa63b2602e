import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConsoleAgentLogger, createAgentLogger } from '../../src/observability/logging';
import {
  MetricsCollector,
  globalMetricsCollector,
  trackMetrics,
} from '../../src/observability/metrics';
import { ObservabilityConfig } from '../../src/base/types';

describe('Observability System', () => {
  describe('ConsoleAgentLogger', () => {
    let logger: ConsoleAgentLogger;

    const mockConfig: ObservabilityConfig['logging'] = {
      level: 'info',
      includePrompts: true,
      includeResponses: true,
    };

    beforeEach(() => {
      logger = new ConsoleAgentLogger('test-agent', mockConfig);
      vi.spyOn(console, 'info').mockImplementation(() => {});
      vi.spyOn(console, 'debug').mockImplementation(() => {});
      vi.spyOn(console, 'warn').mockImplementation(() => {});
      vi.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    describe('log levels', () => {
      it('should log info messages', () => {
        logger.info('Test info message');
        expect(console.info).toHaveBeenCalledWith(
          expect.stringContaining('[test-agent] Test info message')
        );
      });

      it('should log warning messages', () => {
        logger.warn('Test warning message');
        expect(console.warn).toHaveBeenCalledWith(
          expect.stringContaining('[test-agent] Test warning message')
        );
      });

      it('should log error messages', () => {
        const error = new Error('Test error');
        logger.error('Test error message', error);
        expect(console.error).toHaveBeenCalledWith(
          expect.stringContaining('[test-agent] Test error message')
        );
      });

      it('should not log debug messages when level is info', () => {
        logger.debug('Test debug message');
        expect(console.debug).not.toHaveBeenCalled();
      });

      it('should respect log level hierarchy', () => {
        const errorLogger = new ConsoleAgentLogger('test-agent', {
          ...mockConfig,
          level: 'error',
        });

        errorLogger.debug('Debug message');
        errorLogger.info('Info message');
        errorLogger.warn('Warning message');
        errorLogger.error('Error message');

        expect(console.debug).not.toHaveBeenCalled();
        expect(console.info).not.toHaveBeenCalled();
        expect(console.warn).not.toHaveBeenCalled();
        expect(console.error).toHaveBeenCalledOnce();
      });
    });

    describe('metadata handling', () => {
      it('should include metadata in log output', () => {
        const metadata = { userId: 'user123', sessionId: 'session456' };
        logger.info('Test with metadata', metadata);

        expect(console.info).toHaveBeenCalledWith(
          expect.stringContaining(JSON.stringify(metadata))
        );
      });

      it('should filter sensitive data', () => {
        const metadata = {
          password: 'secret123',
          token: 'bearer-token',
          apiKey: 'api-key-123',
          secret: 'top-secret',
          normalData: 'public-info',
        };

        logger.info('Test with sensitive data', metadata);

        const logCall = (console.info as any).mock.calls[0][0];
        expect(logCall).toContain('"normalData":"public-info"');
        expect(logCall).toContain('"password":"[REDACTED]"');
        expect(logCall).toContain('"token":"[REDACTED]"');
        expect(logCall).toContain('"apiKey":"[REDACTED]"');
        expect(logCall).toContain('"secret":"[REDACTED]"');
      });
    });
  });

  describe('MetricsCollector', () => {
    let collector: MetricsCollector;

    beforeEach(() => {
      collector = new MetricsCollector();
    });

    describe('invocation tracking', () => {
      it('should record successful invocations', () => {
        collector.recordInvocation('test-agent', 100, 50, 0.01);

        const metrics = collector.getMetrics('test-agent');
        expect(metrics.invocations).toBe(1);
        expect(metrics.errors).toBe(0);
        expect(metrics.avgLatency).toBe(100);
        expect(metrics.tokensUsed).toBe(50);
        expect(metrics.cost).toBe(0.01);
        expect(metrics.lastInvocation).toBeInstanceOf(Date);
      });

      it('should accumulate metrics across multiple invocations', () => {
        collector.recordInvocation('test-agent', 100, 25, 0.005);
        collector.recordInvocation('test-agent', 200, 75, 0.015);

        const metrics = collector.getMetrics('test-agent');
        expect(metrics.invocations).toBe(2);
        expect(metrics.avgLatency).toBe(150);
        expect(metrics.tokensUsed).toBe(100);
        expect(metrics.cost).toBe(0.02);
      });

      it('should track percentile latencies', () => {
        const latencies = [50, 100, 150, 200, 250];
        latencies.forEach(latency => {
          collector.recordInvocation('test-agent', latency);
        });

        const metrics = collector.getMetrics('test-agent');
        expect(metrics.p95Latency).toBe(250);
        expect(metrics.p99Latency).toBe(250);
      });
    });

    describe('error tracking', () => {
      it('should record errors', () => {
        collector.recordError('test-agent');
        collector.recordError('test-agent');

        const metrics = collector.getMetrics('test-agent');
        expect(metrics.errors).toBe(2);
        expect(metrics.invocations).toBe(0);
      });
    });

    describe('metrics reset', () => {
      it('should reset metrics for specific agent', () => {
        collector.recordInvocation('agent-1', 100);
        collector.recordInvocation('agent-2', 200);

        collector.resetMetrics('agent-1');

        const metrics1 = collector.getMetrics('agent-1');
        const metrics2 = collector.getMetrics('agent-2');

        expect(metrics1.invocations).toBe(0);
        expect(metrics2.invocations).toBe(1);
      });

      it('should reset all metrics', () => {
        collector.recordInvocation('agent-1', 100);
        collector.recordInvocation('agent-2', 200);

        collector.resetAllMetrics();

        const allMetrics = collector.getAllMetrics();
        expect(allMetrics.size).toBe(0);
      });
    });
  });
});
