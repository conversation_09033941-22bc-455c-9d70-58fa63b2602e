import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DocumentAgent } from '../../../src/agents/document';
import { MCPConnectionManager } from '../../../src/integration/mcp-server/connection-manager';
import { IntelligenceService } from '../../../src/intelligence';

// Mock dependencies
vi.mock('../../../src/integration/mcp-server/connection-manager');
vi.mock('../../../src/intelligence');

describe('DocumentAgent', () => {
  let documentAgent: DocumentAgent;
  let mockConnectionManager: MCPConnectionManager;
  let mockIntelligenceService: IntelligenceService;

  beforeEach(() => {
    mockConnectionManager = new MCPConnectionManager({} as any);
    mockIntelligenceService = new IntelligenceService();
    documentAgent = new DocumentAgent(mockConnectionManager, mockIntelligenceService);
  });

  describe('initialization', () => {
    it('should initialize with correct configuration', () => {
      expect(documentAgent.name).toBe('document_agent');
      expect(documentAgent.version).toBe('1.0.0');
      expect(documentAgent.archetype).toBe('deliberative');
      expect(documentAgent.capabilities).toContain('tool-use');
      expect(documentAgent.capabilities).toContain('reasoning');
      expect(documentAgent.capabilities).toContain('database-access');
    });

    it('should initialize successfully', async () => {
      await expect(
        documentAgent.initialize({
          name: 'document_agent',
          version: '0.0.1',
          archetype: 'deliberative' as any,
          capabilities: [] as any,
        })
      ).resolves.toBeUndefined();
    });
  });

  describe('document processing', () => {
    beforeEach(async () => {
      await documentAgent.initialize({
        name: 'document_agent',
        version: '0.0.1',
        archetype: 'deliberative' as any,
        capabilities: [] as any,
      });
    });

    it('should process simple text query', async () => {
      // Mock intelligence service response
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'retrieve', confidence: 0.9, alternatives: [] },
        entities: [{ type: 'document_type', value: 'policy', confidence: 0.8, metadata: {} }],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 50,
      });

      // Mock document retrieval
      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [
          {
            id: 'doc1',
            title: 'Security Policy',
            content: 'This is a security policy document',
            metadata: { type: 'policy' },
          },
        ],
      });

      const result = await documentAgent.invoke({
        input: 'Find security policies',
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.documents).toBeInstanceOf(Array);
      expect(result.output.processingTime).toBeGreaterThan(0);
      expect(result.output.toolsUsed).toContain('intelligence-analysis');
    });

    it('should handle object input with specific analysis type', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'analyze', confidence: 0.85, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 45,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [],
      });

      const result = await documentAgent.invoke({
        input: {
          query: 'Analyze security documents',
          analysisType: 'classification',
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.toolsUsed).toContain('document-classification');
    });

    it('should handle empty document results', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'retrieve', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 30,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [],
      });

      const result = await documentAgent.invoke({
        input: 'Find non-existent documents',
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.documents).toHaveLength(0);
      expect(result.output.aggregatedInsights.totalDocuments).toBe(0);
    });

    it('should throw error for invalid input', async () => {
      await expect(
        documentAgent.invoke({
          input: null,
          organizationId: 'org1',
          userId: 'user1',
        })
      ).rejects.toThrow('Invalid input format for Document Agent');
    });

    it('should throw error for missing query', async () => {
      await expect(
        documentAgent.invoke({
          input: { analysisType: 'content' },
          organizationId: 'org1',
          userId: 'user1',
        })
      ).rejects.toThrow('Query is required for Document Agent');
    });
  });

  describe('streaming support', () => {
    beforeEach(async () => {
      await documentAgent.initialize({
        name: 'document_agent',
        version: '0.0.1',
        archetype: 'deliberative' as any,
        capabilities: ['streaming'] as any,
      });
    });

    it('should support streaming document processing', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'retrieve', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 40,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [],
      });

      const chunks = [];
      const stream = documentAgent.stream({
        input: 'Stream document search',
        organizationId: 'org1',
        userId: 'user1',
      });

      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[0].type).toBe('metadata');
      expect(chunks[0].content.stage).toBe('starting');
    });
  });

  describe('health check', () => {
    it('should return healthy status', async () => {
      const health = await documentAgent.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.checks.memory).toBe(true);
      expect(health.checks.dependencies).toBe(true);
      expect(health.checks.model).toBe(true);
    });
  });

  describe('metrics', () => {
    it('should track invocation metrics', async () => {
      await documentAgent.initialize({
        name: 'document_agent',
        version: '0.0.1',
        archetype: 'deliberative' as any,
        capabilities: [] as any,
      });

      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'retrieve', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 25,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [],
      });

      await documentAgent.invoke({
        input: 'Test query',
        organizationId: 'org1',
        userId: 'user1',
      });

      const metrics = documentAgent.getMetrics();
      expect(metrics.invocations).toBe(1);
      expect(metrics.errors).toBe(0);
      expect(metrics.avgLatency).toBeGreaterThan(0);
    });
  });
});
