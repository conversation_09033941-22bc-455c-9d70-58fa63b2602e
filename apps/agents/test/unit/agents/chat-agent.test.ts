import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ChatAgentFactory } from '../../../src/orchestration/langgraph/agents/chat-agent';
import { SupervisorStateAnnotation } from '../../../src/orchestration/langgraph/agents/idiomatic-supervisor';
import { EnrichedSearchResult } from '../../../src/orchestration/langgraph/helpers/semantic-search.executor';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

// Mock the global logger
vi.mock('../../../src/observability/global-logger', () => ({
  getLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }),
}));

// Mock the LangChain imports
vi.mock('@langchain/openai', () => ({
  ChatOpenAI: vi.fn().mockImplementation(() => ({
    invoke: vi.fn(),
  })),
}));

vi.mock('../../../src/utils/llm-bindtools-polyfill', () => ({
  ensureBindTools: vi.fn(llm => llm),
}));

vi.mock('@langchain/langgraph/prebuilt', () => ({
  createReactAgent: vi.fn(() => ({ invoke: vi.fn() })),
}));

vi.mock('@langchain/core/prompts', () => ({
  ChatPromptTemplate: {
    fromMessages: vi.fn(() => ({
      pipe: vi.fn(() => ({
        invoke: vi.fn(),
      })),
    })),
  },
}));

vi.mock('../../../src/integration/langfuse/prompt-manager', () => ({
  getChatPromptConfig: vi.fn(),
}));

describe('ChatAgentFactory', () => {
  let chatAgentFactory: ChatAgentFactory;
  let mockLangfuseClient: any;
  let mockState: any;

  const mockEnrichedSearchResult: EnrichedSearchResult = {
    enrichedContext: 'This is enriched context from semantic search with metadata.',
    rawResults: [
      {
        document: { id: '1', title: 'Test Doc 1' },
        score: 0.9,
        content: 'Important security information about encryption',
      },
      {
        document: { id: '2', title: 'Test Doc 2' },
        score: 0.8,
        content: 'Additional security guidelines and best practices',
      },
    ],
    metadata: {
      documentCount: 2,
      averageScore: 0.85,
      relevanceQuality: 'High',
    },
  };

  const mockAnalysisResult = {
    summary: 'Comprehensive analysis of security documentation and best practices.',
    insights: [
      'Encryption is critical for data protection',
      'Regular security audits are recommended',
    ],
    themes: ['Security', 'Encryption', 'Best Practices'],
    relevanceScore: 7,
    recommendations: [
      'Implement multi-factor authentication',
      'Regular security training for staff',
    ],
  };

  beforeEach(() => {
    mockLangfuseClient = {
      span: vi.fn(() => ({
        end: vi.fn(),
      })),
    };

    mockState = {
      traceId: 'test-trace-id',
      supervisorSpanId: 'test-span-id',
      completedTools: [],
      messages: [new HumanMessage('What are the security best practices?')],
    };

    chatAgentFactory = new ChatAgentFactory(mockLangfuseClient);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Type Guards', () => {
    // Access the type guards through the module or create test instances
    it('should validate EnrichedSearchResult correctly', () => {
      // Test valid EnrichedSearchResult
      const result = mockEnrichedSearchResult;
      expect(result).toHaveProperty('enrichedContext');
      expect(result).toHaveProperty('rawResults');
      expect(result).toHaveProperty('metadata');
      expect(Array.isArray(result.rawResults)).toBe(true);
      expect(typeof result.metadata.documentCount).toBe('number');
      expect(typeof result.metadata.averageScore).toBe('number');
      expect(typeof result.metadata.relevanceQuality).toBe('string');
    });

    it('should validate AnalysisResult correctly', () => {
      // Test valid AnalysisResult
      const result = mockAnalysisResult;
      expect(typeof result.summary).toBe('string');
      expect(Array.isArray(result.insights)).toBe(true);
      expect(Array.isArray(result.themes)).toBe(true);
      expect(typeof result.relevanceScore).toBe('number');
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    it('should reject invalid EnrichedSearchResult', () => {
      const invalidResults = [
        null,
        undefined,
        {},
        { enrichedContext: 'test' }, // missing other properties
        { enrichedContext: 'test', rawResults: 'not-array', metadata: {} },
        {
          enrichedContext: 'test',
          rawResults: [],
          metadata: { documentCount: 'not-number' },
        },
      ];

      invalidResults.forEach(result => {
        expect(result).not.toEqual(mockEnrichedSearchResult);
      });
    });

    it('should reject invalid AnalysisResult', () => {
      const invalidResults = [
        null,
        undefined,
        {},
        { summary: 'test' }, // missing other properties
        { summary: 'test', insights: 'not-array' },
        { summary: 'test', insights: [], themes: 'not-array' },
      ];

      invalidResults.forEach(result => {
        expect(result).not.toEqual(mockAnalysisResult);
      });
    });
  });

  describe('Agent Creation', () => {
    it('should create agent with default configuration', () => {
      const agent = chatAgentFactory.createAgent(SupervisorStateAnnotation);
      expect(agent).toBeDefined();
    });

    it('should create agent with custom prompt configuration', () => {
      chatAgentFactory.setPromptConfig({
        temperature: 0.5,
        systemPrompt: 'Custom system prompt',
      });

      const agent = chatAgentFactory.createAgent(SupervisorStateAnnotation);
      expect(agent).toBeDefined();
    });
  });

  describe('Context Building', () => {
    let contextBuildingFactory: any;

    beforeEach(() => {
      contextBuildingFactory = new ChatAgentFactory(mockLangfuseClient);
    });

    it('should build enhanced context with search results and analysis', () => {
      // Access the private method through bracket notation for testing
      const buildEnhancedContext = (contextBuildingFactory as any).buildEnhancedContext.bind(
        contextBuildingFactory
      );

      const context = buildEnhancedContext(mockEnrichedSearchResult, mockAnalysisResult, mockState);

      expect(context).toContain('This is enriched context from semantic search');
      expect(context).toContain('Search Context: Found 2 relevant documents');
      expect(context).toContain('Analysis Summary');
      expect(context).toContain('Comprehensive analysis of security documentation');
      expect(context).toContain('Key Insights');
      expect(context).toContain('Encryption is critical for data protection');
      expect(context).toContain('Main Themes');
      expect(context).toContain('Security');
      expect(context).toContain('Recommendations');
      expect(context).toContain('Implement multi-factor authentication');
      expect(context).toContain('Analysis Confidence: 8/10');
    });

    it('should build context from raw search results when enriched context is missing', () => {
      const searchResultsWithoutEnriched = {
        ...mockEnrichedSearchResult,
        enrichedContext: '',
      };

      const buildEnhancedContext = (contextBuildingFactory as any).buildEnhancedContext.bind(
        contextBuildingFactory
      );
      const context = buildEnhancedContext(searchResultsWithoutEnriched, null, mockState);

      expect(context).toContain('Relevant Information:');
      expect(context).toContain('[Score: 0.90]');
      expect(context).toContain('Important security information');
      expect(context).toContain('[Score: 0.80]');
      expect(context).toContain('Additional security guidelines');
    });

    it('should handle missing search results and analysis gracefully', () => {
      const buildEnhancedContext = (contextBuildingFactory as any).buildEnhancedContext.bind(
        contextBuildingFactory
      );
      const context = buildEnhancedContext(undefined, undefined, mockState);

      expect(context).toContain('No specific context available for this query');
    });

    it('should handle analysis without optional fields', () => {
      const minimalAnalysis = {
        summary: 'Basic summary',
        insights: [],
        themes: [],
        relevanceScore: 5,
        recommendations: [],
      };

      const buildEnhancedContext = (contextBuildingFactory as any).buildEnhancedContext.bind(
        contextBuildingFactory
      );
      const context = buildEnhancedContext(undefined, minimalAnalysis, mockState);

      expect(context).toContain('Analysis Summary');
      expect(context).toContain('Basic summary');
      expect(context).toContain('Analysis Confidence: 5/10');
      expect(context).not.toContain('Key Insights');
      expect(context).not.toContain('Main Themes');
      expect(context).not.toContain('Recommendations');
    });
  });

  describe('Tool Execution', () => {
    let mockTool: any;

    beforeEach(() => {
      const agent = chatAgentFactory.createAgent(SupervisorStateAnnotation);
      // Mock the tool for testing purposes
      mockTool = {
        invoke: vi.fn(),
      };
    });

    it('should log proper debug information with search results and analysis', async () => {
      const testState = {
        ...mockState,
        searchResults: mockEnrichedSearchResult,
        analysis: mockAnalysisResult,
      };

      // Since we can't easily test the private tool function directly,
      // we test the components that would be called
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );
      const context = buildEnhancedContext(mockEnrichedSearchResult, mockAnalysisResult, testState);

      expect(context).toBeDefined();
      expect(context.length).toBeGreaterThan(0);
    });

    it('should handle missing state data gracefully', async () => {
      const testState = {
        ...mockState,
        searchResults: undefined,
        analysis: undefined,
      };

      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );
      const context = buildEnhancedContext(undefined, undefined, testState);

      expect(context).toContain('No specific context available');
    });
  });

  describe('Legacy Compatibility', () => {
    it('should maintain legacy buildContext method', () => {
      const buildContext = (chatAgentFactory as any).buildContext.bind(chatAgentFactory);

      const legacySearchResults = [
        { content: 'Legacy content 1' },
        { content: 'Legacy content 2' },
      ];

      const legacyAnalysis = {
        summary: 'Legacy analysis summary',
      };

      const context = buildContext(legacySearchResults, legacyAnalysis, mockState);

      expect(context).toContain('Relevant Information:');
      expect(context).toContain('Legacy content 1');
      expect(context).toContain('Legacy content 2');
      expect(context).toContain('Analysis Summary: Legacy analysis summary');
    });

    it('should handle legacy enrichedContext from state', () => {
      const buildContext = (chatAgentFactory as any).buildContext.bind(chatAgentFactory);

      const stateWithEnrichedContext = {
        ...mockState,
        enrichedContext: 'Legacy enriched context from state',
      };

      const context = buildContext([], null, stateWithEnrichedContext);

      expect(context).toBe('Legacy enriched context from state');
    });
  });

  describe('Conversation History', () => {
    it('should extract conversation history correctly', () => {
      const extractConversationHistory = (chatAgentFactory as any).extractConversationHistory.bind(
        chatAgentFactory
      );

      const messages = [
        new HumanMessage('First question'),
        new AIMessage('First response'),
        new HumanMessage('Second question'),
        new AIMessage('Second response'),
        new HumanMessage('Current question'),
      ];

      const history = extractConversationHistory(messages);

      expect(history).toHaveLength(5);
      expect(history[0]).toEqual(['human', 'First question']);
      expect(history[1]).toEqual(['ai', 'First response']);
      expect(history[4]).toEqual(['human', 'Current question']);
    });

    it('should handle empty conversation history', () => {
      const extractConversationHistory = (chatAgentFactory as any).extractConversationHistory.bind(
        chatAgentFactory
      );

      const history = extractConversationHistory([]);

      expect(history).toHaveLength(0);
    });

    it('should limit conversation history to recent messages', () => {
      const extractConversationHistory = (chatAgentFactory as any).extractConversationHistory.bind(
        chatAgentFactory
      );

      const manyMessages = Array.from(
        { length: 10 },
        (_, i) => new HumanMessage(`Message ${i + 1}`)
      );

      const history = extractConversationHistory(manyMessages);

      expect(history.length).toBeLessThanOrEqual(6);
      expect(history[history.length - 1][1]).toContain('Message 10');
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed search results', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      const malformedSearchResults = {
        enrichedContext: null,
        rawResults: [
          { content: 'Valid content', score: 0.8 },
          { score: 0.7 }, // missing content
          { content: 'Another valid content' }, // missing score
        ],
        metadata: {
          documentCount: 3,
          averageScore: 0.75,
          relevanceQuality: 'Medium',
        },
      };

      // This should handle gracefully without throwing
      expect(() => {
        const context = buildEnhancedContext(malformedSearchResults, null, mockState);
        expect(context).toBeDefined();
      }).not.toThrow();
    });

    it('should handle malformed analysis results', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      const malformedAnalysis = {
        summary: 'Valid summary',
        insights: ['Valid insight', null, 'Another insight'], // contains null
        themes: ['Theme 1'], // partial data
        relevanceScore: 'invalid', // wrong type
        recommendations: [], // empty array
      };

      // This should handle gracefully without throwing
      expect(() => {
        const context = buildEnhancedContext(undefined, malformedAnalysis, mockState);
        expect(context).toBeDefined();
      }).not.toThrow();
    });
  });

  describe('Security and Performance', () => {
    it('should not expose sensitive data in logs', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      const sensitiveSearchResults = {
        ...mockEnrichedSearchResult,
        rawResults: [
          {
            document: { id: '1', title: 'API_KEY=secret123' },
            score: 0.9,
            content: 'This contains PASSWORD=supersecret and other sensitive data',
          },
        ],
      };

      const context = buildEnhancedContext(sensitiveSearchResults, null, mockState);

      // Context should contain the content but this is expected behavior
      // Security filtering should happen at the data source level
      expect(context).toBeDefined();
    });

    it('should handle large context efficiently', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      const largeContent = 'x'.repeat(10000);
      const largeSearchResults = {
        enrichedContext: largeContent,
        rawResults: Array.from({ length: 100 }, (_, i) => ({
          document: { id: i.toString() },
          score: 0.8,
          content: `Large content chunk ${i}: ${largeContent.substring(0, 100)}`,
        })),
        metadata: {
          documentCount: 100,
          averageScore: 0.8,
          relevanceQuality: 'High',
        },
      };

      const startTime = Date.now();
      const context = buildEnhancedContext(largeSearchResults, null, mockState);
      const endTime = Date.now();

      expect(context).toBeDefined();
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined state', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      expect(() => {
        const context = buildEnhancedContext(undefined, undefined, undefined);
        expect(context).toContain('No specific context available');
      }).not.toThrow();
    });

    it('should handle circular references in state', () => {
      const buildEnhancedContext = (chatAgentFactory as any).buildEnhancedContext.bind(
        chatAgentFactory
      );

      const circularState: any = { ...mockState };
      circularState.self = circularState; // Create circular reference

      expect(() => {
        const context = buildEnhancedContext(undefined, undefined, circularState);
        expect(context).toBeDefined();
      }).not.toThrow();
    });
  });
});
