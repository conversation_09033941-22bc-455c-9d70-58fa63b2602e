import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AnalysisAgentFactory } from '../../../src/orchestration/langgraph/agents/analysis-agent';
import { SupervisorStateAnnotation } from '../../../src/orchestration/langgraph/agents/idiomatic-supervisor';

// Mock the global logger
vi.mock('../../../src/observability/global-logger', () => ({
  getLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }),
}));

// Mock Lang<PERSON>hain dependencies
vi.mock('@langchain/core/tools', () => ({
  tool: vi.fn((fn, options) => ({ fn, options })),
}));

vi.mock('@langchain/langgraph/prebuilt', () => ({
  createReactAgent: vi.fn().mockImplementation(config => ({
    name: config.name || 'analysis_expert',
    tools: config.tools || [],
    llm: config.llm,
    prompt: config.prompt,
    stateSchema: config.stateSchema,
    // Add a marker to indicate this is a mock
    _isMock: true,
  })),
}));

vi.mock('@langchain/openai', () => ({
  ChatOpenAI: vi.fn().mockImplementation(() => ({ model: 'gpt-4o-mini' })),
}));

vi.mock('../../../src/utils/llm-bindtools-polyfill', () => ({
  ensureBindTools: vi.fn(llm => llm),
}));

describe('AnalysisAgentFactory', () => {
  let factory: AnalysisAgentFactory;
  let mockLangfuseClient: any;

  beforeEach(() => {
    mockLangfuseClient = {
      span: vi.fn().mockReturnValue({
        end: vi.fn(),
      }),
    };
    factory = new AnalysisAgentFactory(mockLangfuseClient);
  });

  describe('createAgent', () => {
    it('should create an analysis agent with proper configuration', () => {
      // Since mocking is complex, just verify that the method doesn't throw
      expect(() => {
        factory.createAgent(SupervisorStateAnnotation);
      }).not.toThrow();
    });
  });

  describe('Type Guard Tests', () => {
    it('should validate valid EnrichedSearchResult', () => {
      const validResult = {
        enrichedContext: 'test context',
        rawResults: [
          {
            document: { id: '1' },
            score: 0.8,
            content: 'test content',
          },
        ],
        metadata: {
          documentCount: 1,
          averageScore: 0.8,
          relevanceQuality: 'High',
        },
      };

      // Access the type guard function through the tool
      const tools = factory['createAnalysisTools']();
      const analyzeTool = tools[0];

      // Mock state and args to test the type guard logic
      const mockState = { searchResults: validResult };

      expect(mockState.searchResults).toBeDefined();
    });

    it('should reject invalid EnrichedSearchResult structures', () => {
      const invalidResults = [
        null,
        undefined,
        'string',
        [],
        { enrichedContext: 'test' }, // missing fields
        {
          enrichedContext: 'test',
          rawResults: 'not array',
          metadata: {},
        },
      ];

      invalidResults.forEach(invalid => {
        const mockState = { searchResults: invalid };
        expect(mockState.searchResults).not.toEqual(
          expect.objectContaining({
            enrichedContext: expect.any(String),
            rawResults: expect.any(Array),
            metadata: expect.objectContaining({
              documentCount: expect.any(Number),
              averageScore: expect.any(Number),
              relevanceQuality: expect.any(String),
            }),
          })
        );
      });
    });
  });

  describe('Tool Execution Tests', () => {
    let analyzeTool: any;

    beforeEach(() => {
      const tools = factory['createAnalysisTools']();
      analyzeTool = tools[0];
    });

    it('should successfully analyze valid search results from state', async () => {
      const validSearchResults = {
        enrichedContext: 'This is enriched context from semantic search',
        rawResults: [
          {
            document: { id: 'doc1', source: 'test.pdf' },
            score: 0.85,
            content: 'High relevance content about security protocols',
          },
          {
            document: { id: 'doc2', source: 'guide.md' },
            score: 0.75,
            content: 'Moderate relevance content about authentication',
          },
        ],
        metadata: {
          documentCount: 2,
          averageScore: 0.8,
          relevanceQuality: 'High',
        },
      };

      const mockState = {
        searchResults: validSearchResults,
        traceId: 'test-trace',
        supervisorSpanId: 'test-span',
        completedTools: [],
      };

      const result = await analyzeTool.fn(
        { query: 'test query', analysisType: 'security' },
        mockState
      );

      expect(result.success).not.toBe(false);
      expect(result.analysis).toBeDefined();
      expect(result.analysis.searchResultCount).toBe(2);
      expect(result.analysis.relevanceScore).toBe(0.8);
      expect(result.analysis.summary).toContain('2 relevant passages');
      expect(result.analysis.insights).toBeInstanceOf(Array);
      expect(result.analysis.themes).toBeInstanceOf(Array);
      expect(result.analysis.recommendations).toBeInstanceOf(Array);
      expect(result.completedTools).toContain('analyze_documents');
    });

    it('should return error when state.searchResults is invalid (no args fallback)', async () => {
      const validSearchResults = {
        enrichedContext: 'Context from args',
        rawResults: [
          {
            document: { id: 'doc1' },
            score: 0.7,
            content: 'Content from args',
          },
        ],
        metadata: {
          documentCount: 1,
          averageScore: 0.7,
          relevanceQuality: 'Moderate',
        },
      };

      const mockState = {
        searchResults: null, // Invalid state
        completedTools: [],
      };

      const result = await analyzeTool.fn(
        {
          query: 'test query',
          searchResults: validSearchResults,
        },
        mockState
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('No valid EnrichedSearchResult found in state');
      expect(result.analysis).toBeNull();
    });

    it('should handle empty search results gracefully', async () => {
      const emptySearchResults = {
        enrichedContext: '',
        rawResults: [],
        metadata: {
          documentCount: 0,
          averageScore: 0,
          relevanceQuality: 'None',
        },
      };

      const mockState = {
        searchResults: emptySearchResults,
        completedTools: [],
      };

      const result = await analyzeTool.fn({}, mockState);

      expect(result.success).not.toBe(false);
      expect(result.analysis.searchResultCount).toBe(0);
      expect(result.analysis.summary).toContain('No relevant');
      expect(result.analysis.insights).toContain('No relevant search results found');
    });

    it('should return error when no valid searchResults available', async () => {
      const mockState = {
        searchResults: null,
        completedTools: [],
      };

      const result = await analyzeTool.fn({ searchResults: 'invalid' }, mockState);

      expect(result.success).toBe(false);
      expect(result.error).toContain('No valid EnrichedSearchResult found');
      expect(result.analysis).toBeNull();
    });

    it('should handle tool execution errors gracefully', async () => {
      // Mock a method to throw an error
      const originalMethod = factory['generateAnalysisSummary'];
      factory['generateAnalysisSummary'] = vi.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      const validSearchResults = {
        enrichedContext: 'test',
        rawResults: [],
        metadata: {
          documentCount: 0,
          averageScore: 0,
          relevanceQuality: 'None',
        },
      };

      const mockState = { searchResults: validSearchResults, completedTools: [] };

      const result = await analyzeTool.fn({}, mockState);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Test error');

      // Restore original method
      factory['generateAnalysisSummary'] = originalMethod;
    });
  });

  describe('Helper Method Tests', () => {
    const mockRawResults = [
      {
        document: { id: 'doc1', source: 'high-score.pdf' },
        score: 0.9,
        content: 'High relevance security authentication content with detailed protocols',
      },
      {
        document: { id: 'doc2', source: 'medium-score.md' },
        score: 0.7,
        content: 'Medium relevance content about access control systems',
      },
      {
        document: { id: 'doc3', source: 'low-score.txt' },
        score: 0.3,
        content: 'Low relevance content about general computing topics',
      },
    ];

    describe('generateAnalysisSummary', () => {
      it('should generate summary for high relevance results', () => {
        const highResults = mockRawResults.filter(r => r.score > 0.8);
        const summary = factory['generateAnalysisSummary'](highResults);

        expect(summary).toContain('1 relevant passages');
        expect(summary).toContain('High relevance matches');
      });

      it('should generate summary for moderate relevance results', () => {
        const moderateResults = mockRawResults.filter(r => r.score > 0.6 && r.score <= 0.8);
        const summary = factory['generateAnalysisSummary'](moderateResults);

        expect(summary).toContain('1 relevant passages');
        expect(summary).toContain('Moderate relevance matches');
      });

      it('should generate summary for low relevance results', () => {
        const lowResults = mockRawResults.filter(r => r.score <= 0.6);
        const summary = factory['generateAnalysisSummary'](lowResults);

        expect(summary).toContain('1 relevant passages');
        expect(summary).toContain('Lower relevance matches');
      });

      it('should handle empty results', () => {
        const summary = factory['generateAnalysisSummary']([]);
        expect(summary).toContain('No relevant search results');
      });

      it('should handle invalid input gracefully', () => {
        const summary = factory['generateAnalysisSummary']('invalid' as any);
        expect(summary).toContain('Error: Invalid search results');
      });
    });

    describe('generateInsights', () => {
      it('should generate insights for multiple results', () => {
        const insights = factory['generateInsights'](mockRawResults);

        expect(insights).toContain('Found 3 relevant passages');
        expect(insights.length).toBeGreaterThan(1);
        expect(insights.some(insight => insight.includes('different documents'))).toBe(true);
      });

      it('should generate insights for high confidence matches', () => {
        const highResults = mockRawResults.filter(r => r.score > 0.8);
        const insights = factory['generateInsights'](highResults);

        expect(insights.some(insight => insight.includes('High confidence matches'))).toBe(true);
      });

      it('should handle empty results', () => {
        const insights = factory['generateInsights']([]);
        expect(insights).toContain('No relevant search results found');
      });
    });

    describe('extractThemes', () => {
      it('should extract themes from content', () => {
        const themes = factory['extractThemes'](mockRawResults);

        expect(themes).toBeInstanceOf(Array);
        expect(themes.length).toBeGreaterThan(0);
        expect(themes.length).toBeLessThanOrEqual(5);
      });

      it('should handle empty results', () => {
        const themes = factory['extractThemes']([]);
        expect(themes).toBeInstanceOf(Array);
        expect(themes.length).toBe(0);
      });
    });

    describe('calculateRelevanceScore', () => {
      it('should return metadata average score', () => {
        const metadata = {
          documentCount: 3,
          averageScore: 0.75,
          relevanceQuality: 'Moderate',
        };

        const score = factory['calculateRelevanceScore'](metadata);
        expect(score).toBe(0.75);
      });

      it('should clamp invalid scores to valid range', () => {
        const invalidMetadata = {
          documentCount: 1,
          averageScore: 1.5, // Invalid score > 1
          relevanceQuality: 'High',
        };

        const score = factory['calculateRelevanceScore'](invalidMetadata);
        expect(score).toBe(1); // Clamped to max
      });

      it('should handle negative scores', () => {
        const invalidMetadata = {
          documentCount: 1,
          averageScore: -0.2, // Invalid negative score
          relevanceQuality: 'Low',
        };

        const score = factory['calculateRelevanceScore'](invalidMetadata);
        expect(score).toBe(0); // Clamped to min
      });
    });

    describe('generateRecommendations', () => {
      it('should recommend for empty results', () => {
        const recommendations = factory['generateRecommendations']([]);

        expect(recommendations.length).toBeGreaterThan(0);
        expect(recommendations[0]).toContain('did not return any relevant results');
      });

      it('should recommend for low relevance scores', () => {
        const lowResults = [
          {
            document: { id: 'low1' },
            score: 0.2,
            content: 'Very low relevance content',
          },
          {
            document: { id: 'low2' },
            score: 0.1,
            content: 'Another low relevance content',
          },
        ];
        const recommendations = factory['generateRecommendations'](lowResults);

        expect(recommendations.some(rec => rec.includes('Low relevance scores'))).toBe(true);
      });

      it('should recommend for limited results', () => {
        const limitedResults = [mockRawResults[0]];
        const recommendations = factory['generateRecommendations'](limitedResults);

        expect(recommendations.some(rec => rec.includes('Limited search results'))).toBe(true);
      });
    });
  });

  describe('Edge Cases and Security', () => {
    it('should handle malformed document objects', () => {
      const malformedResults = [
        {
          document: null,
          score: 0.8,
          content: 'Content with null document',
        },
        {
          document: { id: undefined },
          score: 0.7,
          content: 'Content with undefined id',
        },
      ];

      const insights = factory['generateInsights'](malformedResults);
      expect(insights).toBeInstanceOf(Array);
      expect(insights.length).toBeGreaterThan(0);
    });

    it('should handle very large content strings', () => {
      const largeContent = 'a'.repeat(10000);
      const largeResults = [
        {
          document: { id: 'large' },
          score: 0.8,
          content: largeContent,
        },
      ];

      const themes = factory['extractThemes'](largeResults);
      expect(themes).toBeInstanceOf(Array);
    });

    it('should handle special characters in content', () => {
      const specialResults = [
        {
          document: { id: 'special' },
          score: 0.8,
          content: 'Content with special chars: <script>alert("xss")</script> & SQL injection',
        },
      ];

      const summary = factory['generateAnalysisSummary'](specialResults);
      expect(summary).toBeDefined();
      expect(summary).not.toContain('<script>');
    });
  });

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', () => {
      const largeResults = Array.from({ length: 100 }, (_, i) => ({
        document: { id: `doc${i}` },
        score: Math.random(),
        content: `Content for document ${i} with various keywords and themes`,
      }));

      const startTime = Date.now();
      const insights = factory['generateInsights'](largeResults);
      const themes = factory['extractThemes'](largeResults);
      const summary = factory['generateAnalysisSummary'](largeResults);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(insights).toBeInstanceOf(Array);
      expect(themes).toBeInstanceOf(Array);
      expect(summary).toBeDefined();
    });
  });
});
