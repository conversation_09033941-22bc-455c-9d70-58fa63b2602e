import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SearchAgent } from '../../../src/agents/search';
import { MCPConnectionManager } from '../../../src/integration/mcp-server/connection-manager';
import { IntelligenceService } from '../../../src/intelligence';

// Mock dependencies
vi.mock('../../../src/integration/mcp-server/connection-manager');
vi.mock('../../../src/intelligence');

describe('SearchAgent', () => {
  let searchAgent: SearchAgent;
  let mockConnectionManager: MCPConnectionManager;
  let mockIntelligenceService: IntelligenceService;

  beforeEach(() => {
    mockConnectionManager = new MCPConnectionManager({} as any);
    mockIntelligenceService = new IntelligenceService();
    searchAgent = new SearchAgent(mockConnectionManager, mockIntelligenceService);
  });

  describe('initialization', () => {
    it('should initialize with correct configuration', () => {
      expect(searchAgent.name).toBe('search_agent');
      expect(searchAgent.version).toBe('1.0.0');
      expect(searchAgent.archetype).toBe('reactive');
      expect(searchAgent.capabilities).toContain('tool-use');
      expect(searchAgent.capabilities).toContain('reasoning');
      expect(searchAgent.capabilities).toContain('database-access');
      expect(searchAgent.capabilities).toContain('streaming');
    });

    it('should initialize successfully', async () => {
      await expect(
        searchAgent.initialize({
          name: 'search_agent',
          version: '0.0.1',
          archetype: 'reactive' as any,
          capabilities: [] as any,
        })
      ).resolves.toBeUndefined();
    });
  });

  describe('search processing', () => {
    beforeEach(async () => {
      await searchAgent.initialize({
        name: 'search_agent',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [] as any,
      });
    });

    it('should process simple semantic search', async () => {
      // Mock intelligence service response
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.95, alternatives: [] },
        entities: [{ type: 'keyword', value: 'vulnerability', confidence: 0.9, metadata: {} }],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 35,
      });

      const result = await searchAgent.invoke({
        input: 'Find vulnerability reports',
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.results).toBeInstanceOf(Array);
      expect(result.output.searchMetadata).toBeDefined();
      expect(result.output.searchMetadata.searchType).toBe('semantic');
      expect(result.output.performance).toBeDefined();
    });

    it('should process keyword search', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 30,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [
          {
            id: 'doc1',
            title: 'Security Report',
            content: 'This is a security report',
            score: 0.8,
          },
        ],
      });

      const result = await searchAgent.invoke({
        input: {
          query: 'security reports',
          searchType: 'keyword',
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.searchMetadata.searchType).toBe('keyword');
      expect(result.output.results).toBeInstanceOf(Array);
    });

    it('should process hybrid search', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.85, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 40,
      });

      vi.mocked(mockConnectionManager.callTool).mockResolvedValue({
        documents: [],
      });

      const result = await searchAgent.invoke({
        input: {
          query: 'security policies',
          searchType: 'hybrid',
          options: {
            maxResults: 10,
            expandQuery: true,
          },
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.searchMetadata.searchType).toBe('hybrid');
    });

    it('should handle search with filters', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 35,
      });

      const result = await searchAgent.invoke({
        input: {
          query: 'vulnerability assessment',
          filters: {
            documentTypes: ['report'],
            securityLevel: 'high',
            dateRange: {
              from: new Date('2024-01-01'),
              to: new Date('2024-12-31'),
            },
          },
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.searchMetadata.filtersApplied).toContain('document-types');
      expect(result.output.searchMetadata.filtersApplied).toContain('security-level');
      expect(result.output.searchMetadata.filtersApplied).toContain('date-range');
    });

    it('should generate aggregations and suggestions', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 30,
      });

      const result = await searchAgent.invoke({
        input: 'security documentation',
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output).toBeDefined();
      expect(result.output.aggregations).toBeDefined();
      expect(result.output.suggestions).toBeDefined();
      expect(result.output.suggestions.queryAlternatives).toBeInstanceOf(Array);
      expect(result.output.suggestions.relatedTerms).toBeInstanceOf(Array);
    });

    it('should throw error for invalid input', async () => {
      await expect(
        searchAgent.invoke({
          input: null,
          organizationId: 'org1',
          userId: 'user1',
        })
      ).rejects.toThrow('Invalid input format for Search Agent');
    });

    it('should throw error for missing query', async () => {
      await expect(
        searchAgent.invoke({
          input: { searchType: 'semantic' },
          organizationId: 'org1',
          userId: 'user1',
        })
      ).rejects.toThrow('Query is required for Search Agent');
    });
  });

  describe('streaming support', () => {
    beforeEach(async () => {
      await searchAgent.initialize({
        name: 'search_agent',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: ['streaming'] as any,
      });
    });

    it('should support streaming search', async () => {
      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 25,
      });

      const chunks = [];
      const stream = searchAgent.stream({
        input: 'Stream search test',
        organizationId: 'org1',
        userId: 'user1',
      });

      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[0].type).toBe('metadata');
      expect(chunks[0].content.stage).toBe('starting');
    });
  });

  describe('search types', () => {
    beforeEach(async () => {
      await searchAgent.initialize({
        name: 'search_agent',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [] as any,
      });

      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 30,
      });
    });

    it('should handle similarity search', async () => {
      const result = await searchAgent.invoke({
        input: {
          query: 'test query',
          searchType: 'similarity',
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output.searchMetadata.searchType).toBe('similarity');
    });

    it('should handle contextual search', async () => {
      const result = await searchAgent.invoke({
        input: {
          query: 'test query',
          searchType: 'contextual',
          context: {
            previousQueries: ['security policy'],
            userPreferences: { preferredCategories: ['security'] },
          },
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output.searchMetadata.searchType).toBe('contextual');
    });

    it('should default to semantic search for unknown type', async () => {
      const result = await searchAgent.invoke({
        input: {
          query: 'test query',
          searchType: 'unknown_type' as any,
        },
        organizationId: 'org1',
        userId: 'user1',
      });

      expect(result.output.searchMetadata.searchType).toBe('unknown_type');
    });
  });

  describe('health check', () => {
    it('should return healthy status', async () => {
      const health = await searchAgent.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.checks.memory).toBe(true);
      expect(health.checks.dependencies).toBe(true);
      expect(health.checks.model).toBe(true);
    });
  });

  describe('metrics', () => {
    it('should track search metrics', async () => {
      await searchAgent.initialize({
        name: 'search_agent',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [] as any,
      });

      vi.mocked(mockIntelligenceService.analyzeAndSelect).mockResolvedValue({
        intent: { type: 'search', confidence: 0.9, alternatives: [] },
        entities: [],
        selectedTools: [],
        executionPlan: { parallelExecution: false, steps: [] },
        processingTime: 20,
      });

      await searchAgent.invoke({
        input: 'Test search query',
        organizationId: 'org1',
        userId: 'user1',
      });

      const metrics = searchAgent.getMetrics();
      expect(metrics.invocations).toBe(1);
      expect(metrics.errors).toBe(0);
      expect(metrics.avgLatency).toBeGreaterThan(0);
    });
  });
});
