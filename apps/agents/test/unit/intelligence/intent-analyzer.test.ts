/**
 * Tests for the Intent Analyzer
 * Verifies intent classification and entity extraction capabilities
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { IntentAnalyzer } from '../../../src/intelligence/intent-analyzer';
import { Context } from '../../../src/intelligence/types';

describe('IntentAnalyzer', () => {
  let intentAnalyzer: IntentAnalyzer;

  beforeEach(() => {
    intentAnalyzer = new IntentAnalyzer({
      debugMode: true,
      confidenceThreshold: 0.5,
    });
  });

  describe('Basic Intent Classification', () => {
    it('should classify simple retrieval queries', async () => {
      const input = 'Show me all security documents';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('retrieve');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.intent.entities).toContainEqual(
        expect.objectContaining({
          type: 'document_type',
          value: expect.any(String),
        })
      );
    });

    it('should classify search queries', async () => {
      const input = 'Search for information about CVE-2024-1234';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('search');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.intent.entities).toContainEqual(
        expect.objectContaining({
          type: 'vulnerability',
          value: 'CVE-2024-1234',
        })
      );
    });

    it('should classify analysis queries', async () => {
      const input = 'Analyze the security implications of Windows vulnerabilities';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('analyze');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.intent.entities).toContainEqual(
        expect.objectContaining({
          type: 'technology',
          value: 'Windows',
        })
      );
    });

    it('should classify complex queries', async () => {
      const input =
        'First analyze the firewall logs, then compare with previous incidents, and finally recommend security improvements';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('complex');
      expect(result.intent.complexity).toBe('multi_step');
      expect(result.confidence).toBeGreaterThan(0.5);
    });
  });

  describe('Entity Extraction', () => {
    it('should extract vulnerability identifiers', async () => {
      const input = 'What do you know about CVE-2024-5678 and CWE-79?';
      const result = await intentAnalyzer.analyze(input);

      const vulnerabilityEntities = result.intent.entities.filter(e => e.type === 'vulnerability');
      expect(vulnerabilityEntities).toHaveLength(2);
      expect(vulnerabilityEntities.map(e => e.value)).toContain('CVE-2024-5678');
      expect(vulnerabilityEntities.map(e => e.value)).toContain('CWE-79');
    });

    it('should extract technology mentions', async () => {
      const input = 'How do I secure my Linux server running Apache and MySQL?';
      const result = await intentAnalyzer.analyze(input);

      const techEntities = result.intent.entities.filter(e => e.type === 'technology');
      expect(techEntities.length).toBeGreaterThan(0);
      expect(techEntities.map(e => e.value.toLowerCase())).toContain('linux');
      expect(techEntities.map(e => e.value.toLowerCase())).toContain('apache');
      expect(techEntities.map(e => e.value.toLowerCase())).toContain('mysql');
    });

    it('should extract threat-related terms', async () => {
      const input = 'Help me understand ransomware attacks and phishing attempts';
      const result = await intentAnalyzer.analyze(input);

      const threatEntities = result.intent.entities.filter(e => e.type === 'threat');
      expect(threatEntities.length).toBeGreaterThan(0);
      expect(threatEntities.map(e => e.value.toLowerCase())).toContain('ransomware');
      expect(threatEntities.map(e => e.value.toLowerCase())).toContain('phishing');
    });

    it('should extract date ranges', async () => {
      const input = 'Show me security incidents from last month';
      const result = await intentAnalyzer.analyze(input);

      const dateEntities = result.intent.entities.filter(e => e.type === 'date_range');
      expect(dateEntities.length).toBeGreaterThan(0);
      expect(dateEntities[0].value).toContain('last month');
    });
  });

  describe('Contextual Analysis', () => {
    it('should boost confidence for recent similar intents', async () => {
      const context: Context = {
        sessionId: 'test-session',
        organizationId: 'test-org',
        conversationHistory: [
          {
            timestamp: new Date(),
            userInput: 'Search for malware information',
            agentResponse: 'Found malware documents',
            intent: {
              type: 'search',
              entities: [],
              confidence: 0.8,
              complexity: 'simple',
            },
          },
        ],
      };

      const input = 'Find information about viruses';
      const result = await intentAnalyzer.analyze(input, context);

      expect(result.intent.type).toBe('search');
      // Confidence should be boosted due to recent similar intent
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    it('should include debug information in development mode', async () => {
      const input = 'Analyze network security policies';
      const result = await intentAnalyzer.analyze(input);

      expect(result.debugInfo).toBeDefined();
      expect(result.debugInfo?.rawClassification).toBeDefined();
      expect(result.debugInfo?.entityExtractionDetails).toBeDefined();
    });
  });

  describe('Query Complexity Assessment', () => {
    it('should identify simple queries', async () => {
      const input = 'Get documents';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.complexity).toBe('simple');
    });

    it('should identify moderate complexity queries', async () => {
      const input = 'Analyze the security posture and tell me about vulnerabilities';
      const result = await intentAnalyzer.analyze(input);

      expect(['moderate', 'complex']).toContain(result.intent.complexity);
    });

    it('should identify complex multi-step queries', async () => {
      const input =
        'First review the incident reports, then analyze patterns, and finally create recommendations';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.complexity).toBe('multi_step');
    });
  });

  describe('Domain Classification', () => {
    it('should classify security domain correctly', async () => {
      const input = 'Help me understand SQL injection vulnerabilities and XSS attacks';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.domain).toBe('security');
    });

    it('should classify infrastructure domain correctly', async () => {
      const input = 'Configure my Docker containers and Kubernetes deployment';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.domain).toBe('infrastructure');
    });
  });

  describe('Error Handling', () => {
    it('should handle empty input gracefully', async () => {
      const input = '';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('unknown');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should handle malformed input gracefully', async () => {
      const input = '!@#$%^&*()_+{}|:"<>?';
      const result = await intentAnalyzer.analyze(input);

      expect(result.intent.type).toBe('unknown');
      expect(result.confidence).toBeLessThan(0.5);
    });
  });

  describe('Performance', () => {
    it('should analyze intents within reasonable time', async () => {
      const input =
        'Analyze the security implications of the latest Windows vulnerabilities and provide recommendations for mitigation';
      const startTime = Date.now();

      const result = await intentAnalyzer.analyze(input);

      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(1000); // Should complete within 1 second
      expect(result.processingTime).toBeLessThan(500); // Internal processing should be under 500ms
    });
  });
});
