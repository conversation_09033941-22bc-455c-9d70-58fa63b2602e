import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DocumentServiceV2 } from '../../../src/services/document.service.v2';
import { DocumentCacheService } from '@anter/mcp-tools';
import { DocumentFetcherAdapter } from '../../../src/services/document-fetcher.adapter';

// Mock the MCPConnectionManager
const mockConnectionManager = {
  executeToolCall: vi.fn(),
  getRedisClient: vi.fn(),
} as any;

// Mock the DocumentAnalyzerService
const mockAnalyzer = {} as any;

describe('DocumentServiceV2 Refactoring', () => {
  let documentService: DocumentServiceV2;

  beforeEach(() => {
    vi.clearAllMocks();
    documentService = new DocumentServiceV2(mockConnectionManager, mockAnalyzer);
  });

  describe('Constructor', () => {
    it('should create DocumentServiceV2 with generic cache service', () => {
      expect(documentService).toBeInstanceOf(DocumentServiceV2);

      // Verify that the service has the expected properties
      expect(documentService).toHaveProperty('getPromptBuilder');
      expect(documentService).toHaveProperty('retrieveAllDocuments');
      expect(documentService).toHaveProperty('listDocumentIds');
      expect(documentService).toHaveProperty('fetchDocuments');
    });

    it('should handle missing Redis client gracefully', () => {
      mockConnectionManager.getRedisClient.mockReturnValue(undefined);

      // Should not throw an error
      expect(() => {
        new DocumentServiceV2(mockConnectionManager, mockAnalyzer);
      }).not.toThrow();
    });
  });

  describe('DocumentFetcherAdapter', () => {
    it('should be properly instantiated', () => {
      const adapter = new DocumentFetcherAdapter(mockConnectionManager);
      expect(adapter).toBeInstanceOf(DocumentFetcherAdapter);
    });

    it('should implement DocumentFetcher interface', () => {
      const adapter = new DocumentFetcherAdapter(mockConnectionManager);
      expect(typeof adapter.fetchDocumentIds).toBe('function');
      expect(typeof adapter.fetchDocumentsByIds).toBe('function');
    });
  });

  describe('Integration', () => {
    it('should use generic cache service for document retrieval', async () => {
      // Mock successful response
      mockConnectionManager.executeToolCall.mockResolvedValue({
        success: true,
        data: {
          documents: [
            { id: 'doc1', updatedAt: '2023-01-01T00:00:00Z' },
            { id: 'doc2', updatedAt: '2023-01-02T00:00:00Z' },
          ],
        },
      });

      const result = await documentService.retrieveAllDocuments('org1', 'user1', 'trace1');

      expect(result).toHaveProperty('documents');
      expect(result).toHaveProperty('source');
      expect(result.source).toBe('memory');
    });

    it('should handle errors gracefully', async () => {
      mockConnectionManager.executeToolCall.mockRejectedValue(new Error('Database error'));

      await expect(documentService.retrieveAllDocuments('org1', 'user1', 'trace1')).rejects.toThrow(
        'Database error'
      );
    });
  });
});
