import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AgentFactory, AgentBuilder, agent } from '../../src/base/agent-factory';
import { AbstractAgent } from '../../src/base/agent';
import {
  AgentArchetype,
  AgentCapability,
  AgentConfig,
  AgentInput,
  AgentContext,
  AgentResult,
} from '../../src/base/types';

// Mock agent implementation for testing
class MockAgent extends AbstractAgent {
  protected async onInitialize(): Promise<void> {
    // Mock initialization
  }

  protected async onDestroy(): Promise<void> {
    // Mock cleanup
  }

  protected async doInvoke(input: AgentInput): Promise<AgentResult> {
    return {
      output: `Mock response to: ${input.input}`,
      metadata: {
        tokensUsed: 10,
        latencyMs: 50,
      },
    };
  }
}

describe('AgentFactory', () => {
  beforeEach(() => {
    // Clear registry before each test
    AgentFactory.getRegisteredTypes().forEach(type => {
      AgentFactory.unregister(type);
    });
  });

  describe('register', () => {
    it('should register a new agent type', () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);

      AgentFactory.register('test-agent', factory, {
        description: 'Test agent',
        author: 'Test author',
        tags: ['test'],
      });

      expect(AgentFactory.getRegisteredTypes()).toContain('test-agent');
    });

    it('should throw error when registering duplicate type', () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);

      AgentFactory.register('test-agent', factory);

      expect(() => {
        AgentFactory.register('test-agent', factory);
      }).toThrow("Agent type 'test-agent' is already registered");
    });
  });

  describe('unregister', () => {
    it('should unregister an agent type', () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);

      AgentFactory.register('test-agent', factory);
      expect(AgentFactory.getRegisteredTypes()).toContain('test-agent');

      const result = AgentFactory.unregister('test-agent');
      expect(result).toBe(true);
      expect(AgentFactory.getRegisteredTypes()).not.toContain('test-agent');
    });

    it('should return false when unregistering non-existent type', () => {
      const result = AgentFactory.unregister('non-existent');
      expect(result).toBe(false);
    });
  });

  describe('create', () => {
    it('should create an agent instance', async () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);
      AgentFactory.register('test-agent', factory);

      const config: AgentConfig = {
        name: 'my-test-agent',
        version: '0.0.1',
        archetype: AgentArchetype.REACTIVE,
        capabilities: [AgentCapability.CHAT],
      };

      const agent = await AgentFactory.create('test-agent', config);

      expect(agent).toBeDefined();
      expect(agent.name).toBe('my-test-agent');
      expect(agent.version).toBe('1.0.0');
      expect(agent.archetype).toBe(AgentArchetype.REACTIVE);
    });

    it('should throw error for unregistered agent type', async () => {
      const config: AgentConfig = {
        name: 'test',
        version: '0.0.1',
        archetype: AgentArchetype.REACTIVE,
        capabilities: [AgentCapability.CHAT],
      };

      await expect(AgentFactory.create('non-existent', config)).rejects.toThrow(
        "Agent type 'non-existent' is not registered"
      );
    });

    it('should validate configuration', async () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);
      AgentFactory.register('test-agent', factory);

      // Missing name
      await expect(
        AgentFactory.create('test-agent', {
          version: '0.0.1',
          archetype: AgentArchetype.REACTIVE,
          capabilities: [AgentCapability.CHAT],
        } as any)
      ).rejects.toThrow('Agent name is required');

      // Missing version
      await expect(
        AgentFactory.create('test-agent', {
          name: 'test',
          archetype: AgentArchetype.REACTIVE,
          capabilities: [AgentCapability.CHAT],
        } as any)
      ).rejects.toThrow('Agent version is required');

      // Empty capabilities
      await expect(
        AgentFactory.create('test-agent', {
          name: 'test',
          version: '0.0.1',
          archetype: AgentArchetype.REACTIVE,
          capabilities: [],
        })
      ).rejects.toThrow('Agent must have at least one capability');
    });
  });

  describe('createBatch', () => {
    it('should create multiple agents in parallel', async () => {
      const factory = async (config: AgentConfig) => new MockAgent(config);
      AgentFactory.register('test-agent', factory);

      const specs = [
        {
          type: 'test-agent',
          config: {
            name: 'agent1',
            version: '0.0.1',
            archetype: AgentArchetype.REACTIVE,
            capabilities: [AgentCapability.CHAT],
          },
        },
        {
          type: 'test-agent',
          config: {
            name: 'agent2',
            version: '0.0.1',
            archetype: AgentArchetype.DELIBERATIVE,
            capabilities: [AgentCapability.REASONING],
          },
        },
      ];

      const agents = await AgentFactory.createBatch(specs);

      expect(agents).toHaveLength(2);
      expect(agents[0].name).toBe('agent1');
      expect(agents[1].name).toBe('agent2');
    });
  });
});

describe('AgentBuilder', () => {
  beforeEach(() => {
    const factory = async (config: AgentConfig) => new MockAgent(config);
    AgentFactory.register('test-agent', factory);
  });

  it('should build an agent with fluent API', async () => {
    const agent = await new AgentBuilder('test-agent')
      .name('fluent-agent')
      .version('2.0.0')
      .description('Built with fluent API')
      .archetype(AgentArchetype.HYBRID)
      .capabilities(AgentCapability.CHAT, AgentCapability.MEMORY)
      .modelConfig({
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
      })
      .build();

    expect(agent.name).toBe('fluent-agent');
    expect(agent.version).toBe('2.0.0');
    expect(agent.archetype).toBe(AgentArchetype.HYBRID);
    expect(agent.capabilities).toEqual([AgentCapability.CHAT, AgentCapability.MEMORY]);
  });

  it('should work with helper function', async () => {
    const myAgent = await agent('test-agent')
      .name('helper-agent')
      .version('1.0.0')
      .archetype(AgentArchetype.REACTIVE)
      .capabilities(AgentCapability.CHAT)
      .build();

    expect(myAgent.name).toBe('helper-agent');
  });
});
