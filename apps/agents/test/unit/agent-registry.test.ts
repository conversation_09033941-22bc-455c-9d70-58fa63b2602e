import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { registerBuiltInAgents, initializeAgentSystem } from '../../src/agents/registry';
import { AgentFactory } from '../../src/base/agent-factory';
import { createEchoAgent } from '../../src/agents/echo';
import { FastifyInstance } from 'fastify';

// Mock dependencies
vi.mock('../../src/agents/echo');
vi.mock('../../src/agents/ask-ai');

describe('Agent Registry', () => {
  let mockFastify: FastifyInstance;

  beforeEach(() => {
    // Clear any existing registrations
    AgentFactory.getRegisteredTypes().forEach(type => {
      AgentFactory.unregister(type);
    });

    mockFastify = {
      log: {
        info: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        debug: vi.fn(),
      },
    } as any;

    // Mock agent implementations
    (createEchoAgent as any).mockResolvedValue({
      name: 'echo',
      version: '0.0.1',
      invoke: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('registerBuiltInAgents', () => {
    it('should register echo agent without Fastify', () => {
      registerBuiltInAgents();

      const registeredTypes = AgentFactory.getRegisteredTypes();
      expect(registeredTypes).toContain('echo');
      expect(registeredTypes).not.toContain('ask_ai');
    });

    it('should register both agents with Fastify', () => {
      registerBuiltInAgents(mockFastify);

      const registeredTypes = AgentFactory.getRegisteredTypes();
      expect(registeredTypes).toContain('echo');
      expect(registeredTypes).toContain('ask_ai');
    });

    it('should handle multiple calls without errors', () => {
      registerBuiltInAgents(mockFastify);
      const firstRegistration = AgentFactory.getRegisteredTypes();

      expect(() => registerBuiltInAgents(mockFastify)).not.toThrow();
      const secondRegistration = AgentFactory.getRegisteredTypes();

      expect(firstRegistration).toEqual(secondRegistration);
    });
  });

  describe('echo agent registration', () => {
    it('should create echo agent factory correctly', async () => {
      registerBuiltInAgents();

      const config = {
        name: 'test-echo',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };

      const agent = await AgentFactory.create('echo', config);

      expect(createEchoAgent).toHaveBeenCalledWith(config);
      expect(agent).toBeDefined();
    });
  });

  describe('ask_ai agent registration', () => {
    it('should create ask_ai agent factory correctly with Fastify', async () => {
      registerBuiltInAgents(mockFastify);

      const config = {
        name: 'test-ask-ai',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };

      const mockAgentInstance = {
        name: 'test-ask-ai',
        initialize: vi.fn(),
      };

      const agent = await AgentFactory.create('ask_ai', config);

      expect(mockAgentInstance.initialize).toHaveBeenCalledWith(config);
      expect(agent).toBe(mockAgentInstance);
    });

    it('should not register ask_ai agent without Fastify', () => {
      registerBuiltInAgents();

      expect(AgentFactory.getRegisteredTypes()).not.toContain('ask_ai');
    });
  });

  describe('initializeAgentSystem', () => {
    it('should register built-in agents without Fastify', () => {
      initializeAgentSystem();

      const registeredTypes = AgentFactory.getRegisteredTypes();
      expect(registeredTypes).toContain('echo');
      expect(registeredTypes).not.toContain('ask_ai');
    });

    it('should register built-in agents with Fastify', () => {
      initializeAgentSystem(mockFastify);

      const registeredTypes = AgentFactory.getRegisteredTypes();
      expect(registeredTypes).toContain('echo');
      expect(registeredTypes).toContain('ask_ai');
    });

    it('should be idempotent', () => {
      initializeAgentSystem(mockFastify);
      const firstCall = AgentFactory.getRegisteredTypes();

      initializeAgentSystem(mockFastify);
      const secondCall = AgentFactory.getRegisteredTypes();

      expect(firstCall).toEqual(secondCall);
    });
  });

  describe('error handling', () => {
    it('should handle agent creation errors gracefully', async () => {
      (createEchoAgent as any).mockRejectedValue(new Error('Echo creation failed'));

      registerBuiltInAgents();

      const config = {
        name: 'test-echo',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };

      await expect(AgentFactory.create('echo', config)).rejects.toThrow('Echo creation failed');
    });

    it('should handle Fastify-dependent agent errors', async () => {
      registerBuiltInAgents(mockFastify);

      const config = {
        name: 'test-ask-ai',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };
    });
  });

  describe('integration scenarios', () => {
    it('should support creating multiple agent instances', async () => {
      registerBuiltInAgents(mockFastify);

      const echoConfig = {
        name: 'echo-1',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };

      const askAiConfig = {
        name: 'ask-ai-1',
        version: '0.0.1',
        archetype: 'reactive' as any,
        capabilities: [],
      };

      const mockAskAiInstance = {
        name: 'ask-ai-1',
        initialize: vi.fn(),
      };

      expect(createEchoAgent).toHaveBeenCalledWith(echoConfig);
    });
  });
});
