import { describe, it, expect, beforeEach } from 'vitest';
import { InMemoryAgentMemory } from '../../src/base/memory/in-memory';
import { MemoryEntry } from '../../src/base/types';

describe('InMemoryAgentMemory', () => {
  let memory: InMemoryAgentMemory;

  beforeEach(() => {
    memory = new InMemoryAgentMemory(5); // Small max for testing
  });

  describe('basic operations', () => {
    it('should initialize empty', () => {
      expect(memory.size()).toBe(0);
      expect(memory.getAllEntries()).toEqual([]);
    });

    it('should add memory entries', async () => {
      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: 'Hello world',
      };

      await memory.add(entry);

      expect(memory.size()).toBe(1);
      const entries = await memory.get();
      expect(entries).toHaveLength(1);
      expect(entries[0]).toEqual(entry);
    });

    it('should maintain chronological order', async () => {
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date('2023-01-01'), type: 'user', content: 'First' },
        { id: '2', timestamp: new Date('2023-01-02'), type: 'assistant', content: 'Second' },
        { id: '3', timestamp: new Date('2023-01-03'), type: 'user', content: 'Third' },
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }

      const retrieved = await memory.get();
      expect(retrieved).toHaveLength(3);
      expect(retrieved[0].content).toBe('First');
      expect(retrieved[1].content).toBe('Second');
      expect(retrieved[2].content).toBe('Third');
    });

    it('should limit entries to maxEntries', async () => {
      // Add more entries than the limit (5)
      for (let i = 1; i <= 7; i++) {
        await memory.add({
          id: i.toString(),
          timestamp: new Date(),
          type: 'user',
          content: `Message ${i}`,
        });
      }

      expect(memory.size()).toBe(5);
      const entries = await memory.get();
      expect(entries).toHaveLength(5);
      // Should keep the most recent entries (3, 4, 5, 6, 7)
      expect(entries[0].content).toBe('Message 3');
      expect(entries[4].content).toBe('Message 7');
    });
  });

  describe('retrieval', () => {
    beforeEach(async () => {
      // Add test data
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date(), type: 'user', content: 'First message' },
        { id: '2', timestamp: new Date(), type: 'assistant', content: 'First response' },
        { id: '3', timestamp: new Date(), type: 'user', content: 'Second message' },
        { id: '4', timestamp: new Date(), type: 'assistant', content: 'Second response' },
        { id: '5', timestamp: new Date(), type: 'system', content: 'System message' },
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }
    });

    it('should return all entries when no count specified', async () => {
      const entries = await memory.get();
      expect(entries).toHaveLength(5);
    });

    it('should return last N entries when count specified', async () => {
      const entries = await memory.get(3);
      expect(entries).toHaveLength(3);
      expect(entries[0].content).toBe('Second message');
      expect(entries[1].content).toBe('Second response');
      expect(entries[2].content).toBe('System message');
    });

    it('should return all entries when count exceeds available', async () => {
      const entries = await memory.get(10);
      expect(entries).toHaveLength(5);
    });

    it('should handle zero count', async () => {
      const entries = await memory.get(0);
      expect(entries).toHaveLength(5);
    });

    it('should handle negative count', async () => {
      const entries = await memory.get(-1);
      expect(entries).toHaveLength(4);
    });
  });

  describe('search functionality', () => {
    beforeEach(async () => {
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date(), type: 'user', content: 'Hello world' },
        { id: '2', timestamp: new Date(), type: 'assistant', content: 'Hello there!' },
        { id: '3', timestamp: new Date(), type: 'user', content: 'How are you?' },
        { id: '4', timestamp: new Date(), type: 'assistant', content: 'I am fine, thanks' },
        { id: '5', timestamp: new Date(), type: 'system', content: 'System ready' },
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }
    });

    it('should find entries by content match', async () => {
      const results = await memory.search('hello');
      expect(results).toHaveLength(2);
      expect(results[0].content).toBe('Hello world');
      expect(results[1].content).toBe('Hello there!');
    });

    it('should be case insensitive', async () => {
      const results = await memory.search('HELLO');
      expect(results).toHaveLength(2);
    });

    it('should handle partial matches', async () => {
      const results = await memory.search('fine');
      expect(results).toHaveLength(1);
      expect(results[0].content).toBe('I am fine, thanks');
    });

    it('should return empty array for no matches', async () => {
      const results = await memory.search('nonexistent');
      expect(results).toHaveLength(0);
    });

    it('should respect search limit', async () => {
      const results = await memory.search('e', 2); // Should match several entries
      expect(results.length).toBeLessThanOrEqual(2);
    });

    it('should return most recent matches', async () => {
      // Add more entries with same content
      await memory.add({
        id: '6',
        timestamp: new Date(),
        type: 'user',
        content: 'Hello again',
      });

      const results = await memory.search('hello', 2);
      expect(results).toHaveLength(2);
      // Should include the most recent match
      expect(results.some(r => r.content === 'Hello again')).toBe(true);
    });

    it('should search through object content', async () => {
      await memory.add({
        id: '6',
        timestamp: new Date(),
        type: 'tool',
        content: { action: 'search', query: 'database' },
      });

      const results = await memory.search('database');
      expect(results).toHaveLength(1);
      expect(results[0].id).toBe('6');
    });
  });

  describe('consolidation', () => {
    it('should remove duplicate consecutive entries', async () => {
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date(), type: 'user', content: 'Hello' },
        { id: '2', timestamp: new Date(), type: 'user', content: 'Hello' }, // Duplicate
        { id: '3', timestamp: new Date(), type: 'assistant', content: 'Hi' },
        { id: '4', timestamp: new Date(), type: 'assistant', content: 'Hi' }, // Duplicate
        { id: '5', timestamp: new Date(), type: 'user', content: 'Bye' },
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }

      expect(memory.size()).toBe(5);

      await memory.consolidate();

      expect(memory.size()).toBe(3);
      const consolidated = await memory.get();
      expect(consolidated[0].content).toBe('Hello');
      expect(consolidated[1].content).toBe('Hi');
      expect(consolidated[2].content).toBe('Bye');
    });

    it('should preserve non-consecutive duplicates', async () => {
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date(), type: 'user', content: 'Hello' },
        { id: '2', timestamp: new Date(), type: 'assistant', content: 'Hi' },
        { id: '3', timestamp: new Date(), type: 'user', content: 'Hello' }, // Same content but not consecutive
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }

      await memory.consolidate();

      expect(memory.size()).toBe(3);
    });

    it('should handle empty memory consolidation', async () => {
      await memory.consolidate();
      expect(memory.size()).toBe(0);
    });

    it('should handle single entry consolidation', async () => {
      await memory.add({
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: 'Single',
      });

      await memory.consolidate();
      expect(memory.size()).toBe(1);
    });

    it('should consider type differences', async () => {
      const entries: MemoryEntry[] = [
        { id: '1', timestamp: new Date(), type: 'user', content: 'Hello' },
        { id: '2', timestamp: new Date(), type: 'assistant', content: 'Hello' }, // Same content, different type
      ];

      for (const entry of entries) {
        await memory.add(entry);
      }

      await memory.consolidate();

      expect(memory.size()).toBe(2); // Should keep both due to different types
    });
  });

  describe('clear functionality', () => {
    it('should clear all entries', async () => {
      // Add some entries
      for (let i = 1; i <= 3; i++) {
        await memory.add({
          id: i.toString(),
          timestamp: new Date(),
          type: 'user',
          content: `Message ${i}`,
        });
      }

      expect(memory.size()).toBe(3);

      await memory.clear();

      expect(memory.size()).toBe(0);
      const entries = await memory.get();
      expect(entries).toHaveLength(0);
    });

    it('should handle clearing empty memory', async () => {
      await memory.clear();
      expect(memory.size()).toBe(0);
    });
  });

  describe('metadata handling', () => {
    it('should store and retrieve metadata', async () => {
      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: 'Hello',
        metadata: {
          userId: 'user123',
          sessionId: 'session456',
          confidence: 0.95,
        },
      };

      await memory.add(entry);

      const retrieved = await memory.get(1);
      expect(retrieved[0].metadata).toEqual(entry.metadata);
    });

    it('should search metadata content', async () => {
      await memory.add({
        id: '1',
        timestamp: new Date(),
        type: 'system',
        content: 'Log entry',
        metadata: { level: 'error', message: 'Database connection failed' },
      });

      const results = await memory.search('database');
      expect(results).toHaveLength(0);
    });
  });

  describe('edge cases', () => {
    it('should handle entries with undefined content', async () => {
      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'system',
        content: undefined as any,
      };

      await memory.add(entry);

      expect(memory.size()).toBe(1);
      const retrieved = await memory.get();
      expect(retrieved[0].content).toBeUndefined();
    });

    it('should handle entries with null content', async () => {
      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'system',
        content: null as any,
      };

      await memory.add(entry);

      const results = await memory.search('null');
      expect(results).toHaveLength(1);
    });

    it('should handle circular object references in content', async () => {
      const circular: any = { name: 'test' };
      circular.self = circular; // Create circular reference

      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'tool',
        content: circular,
      };

      // Should not throw despite circular reference
      await expect(memory.add(entry)).resolves.toBeUndefined();
    });

    it('should handle very large content objects', async () => {
      const largeContent = {
        data: 'x'.repeat(10000), // Large string
        array: Array(1000).fill('test'),
      };

      const entry: MemoryEntry = {
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: largeContent,
      };

      await memory.add(entry);
      expect(memory.size()).toBe(1);
    });

    it('should handle special characters in search', async () => {
      await memory.add({
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: 'Special chars: !@#$%^&*()[]{}|\\:";\'<>?,./',
      });

      const results = await memory.search('@#$');
      expect(results).toHaveLength(1);
    });

    it('should handle unicode characters', async () => {
      await memory.add({
        id: '1',
        timestamp: new Date(),
        type: 'user',
        content: '你好世界 🌍 emoji test',
      });

      const unicodeResults = await memory.search('你好');
      expect(unicodeResults).toHaveLength(1);

      const emojiResults = await memory.search('🌍');
      expect(emojiResults).toHaveLength(1);
    });
  });

  describe('constructor options', () => {
    it('should use default max entries when not specified', () => {
      const defaultMemory = new InMemoryAgentMemory();
      expect(defaultMemory.size()).toBe(0);

      // Test that it can hold more than 5 entries (our test default)
      const addPromises = Array.from({ length: 10 }, (_, i) =>
        defaultMemory.add({
          id: i.toString(),
          timestamp: new Date(),
          type: 'user',
          content: `Message ${i}`,
        })
      );

      return Promise.all(addPromises).then(() => {
        expect(defaultMemory.size()).toBe(10);
      });
    });

    it('should respect custom max entries', async () => {
      const customMemory = new InMemoryAgentMemory(3);

      for (let i = 1; i <= 5; i++) {
        await customMemory.add({
          id: i.toString(),
          timestamp: new Date(),
          type: 'user',
          content: `Message ${i}`,
        });
      }

      expect(customMemory.size()).toBe(3);
      const entries = await customMemory.get();
      expect(entries[0].content).toBe('Message 3');
      expect(entries[2].content).toBe('Message 5');
    });
  });
});
