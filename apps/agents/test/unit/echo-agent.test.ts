import { describe, it, expect, beforeEach } from 'vitest';
import { AgentArchetype, AgentCapability, AgentContext } from '../../src/base/types';
import { createEchoAgent, EchoAgent } from '../../src/agents/echo';

describe('EchoAgent', () => {
  let agent: EchoAgent;
  let context: AgentContext;

  beforeEach(async () => {
    context = {
      sessionId: 'test-session',
      requestId: 'test-request',
      timestamp: new Date(),
    };

    agent = await createEchoAgent({
      name: 'test-echo',
      version: '0.0.1',
    });

    // Initialize the agent
    await agent.initialize({} as any);
  });

  describe('basic functionality', () => {
    it('should echo string input', async () => {
      const result = await agent.invoke({ input: 'Hello, world!' });
      expect(result.output).toBe('Echo: Hello, world!');
      expect(result.metadata?.tokensUsed).toBeGreaterThan(0);
      expect(result.metadata?.latencyMs).toBeGreaterThan(0);
      expect(result.metadata?.cost).toBe(0);
    });

    it('should echo object input', async () => {
      const input = { message: 'test', value: 123 };
      const result = await agent.invoke({ input });
      expect(result.output).toBe(`Echo: ${JSON.stringify(input, null, 2)}`);
    });
  });

  describe('streaming', () => {
    it('should stream response word by word', async () => {
      const chunks: any[] = [];
      for await (const chunk of agent.stream({ input: 'Hello world' })) {
        chunks.push(chunk);
      }
      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[0].type).toBe('content');
      expect(chunks[chunks.length - 1].type).toBe('metadata');
    });
  });

  describe('lifecycle', () => {
    it('should have correct properties', () => {
      expect(agent.name).toBe('test-echo');
      expect(agent.version).toBe('1.0.0');
      expect(agent.archetype).toBe(AgentArchetype.REACTIVE);
      expect(agent.capabilities).toContain(AgentCapability.CHAT);
      expect(agent.capabilities).toContain(AgentCapability.STREAMING);
    });
  });
});
