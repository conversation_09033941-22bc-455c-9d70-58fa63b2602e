import { describe, it, expect, beforeEach } from 'vitest';
import { DocumentSanitizerService } from '@anter/mcp-tools';

describe('DocumentSanitizerService', () => {
  let sanitizer: DocumentSanitizerService;

  beforeEach(() => {
    sanitizer = new DocumentSanitizerService();
  });

  describe('sanitize', () => {
    it('should remove HTML tags and return clean text', () => {
      const htmlContent = '<p>This is a <strong>test</strong> document.</p>';
      const result = sanitizer.sanitize(htmlContent);
      expect(result).toBe('This is a test document.');
    });

    it('should handle FAQ-style HTML content', () => {
      const faqContent = `
        <div class="flex flex-col space-y-1.5 p-6">
          <h3 class="text-2xl font-semibold">Frequently Asked Questions</h3>
          <p>Q: How do I reset my password?</p>
          <p>A: AskInfosec uses passwordless authentication. You can sign in using Google SSO or by requesting a magic link to your email. There's no password to reset.</p>
        </div>
      `;
      const result = sanitizer.sanitize(faqContent);
      expect(result).toContain('Frequently Asked Questions');
      expect(result).toContain('Q: How do I reset my password?');
      expect(result).toContain('A: AskInfosec uses passwordless authentication');
      expect(result).not.toContain('<div');
      expect(result).not.toContain('class=');
    });

    it('should remove script and style tags completely', () => {
      const htmlWithScript = `
        <div>
          <p>Valid content</p>
          <script>alert('malicious');</script>
          <style>body { color: red; }</style>
        </div>
      `;
      const result = sanitizer.sanitize(htmlWithScript);
      expect(result).toBe('Valid content');
      expect(result).not.toContain('alert');
      expect(result).not.toContain('color: red');
    });

    it('should skip button and interactive elements', () => {
      const htmlWithButtons = `
        <div>
          <p>Important information</p>
          <button id="radix-123" class="btn">Click me</button>
          <input type="text" placeholder="Enter text">
        </div>
      `;
      const result = sanitizer.sanitize(htmlWithButtons);
      expect(result).toBe('Important information');
      expect(result).not.toContain('Click me');
      expect(result).not.toContain('Enter text');
    });

    it('should handle mixed HTML and plain text', () => {
      const mixedContent = `
        Plain text at start
        <p>HTML paragraph</p>
        More plain text
        <div>Another HTML block</div>
      `;
      const result = sanitizer.sanitize(mixedContent);
      expect(result).toContain('Plain text at start');
      expect(result).toContain('HTML paragraph');
      expect(result).toContain('More plain text');
      expect(result).toContain('Another HTML block');
      expect(result).not.toContain('<p>');
      expect(result).not.toContain('<div>');
    });

    it('should normalize whitespace', () => {
      const htmlWithExcessiveWhitespace = `
        <p>Text   with    multiple    spaces</p>
        
        
        <p>Another paragraph</p>
      `;
      const result = sanitizer.sanitize(htmlWithExcessiveWhitespace);
      expect(result).not.toMatch(/\s{3,}/); // No 3+ consecutive whitespace chars
      expect(result).toContain('Text with multiple spaces');
      expect(result).toContain('Another paragraph');
    });

    it('should return empty string for null or undefined input', () => {
      expect(sanitizer.sanitize(null as any)).toBe('');
      expect(sanitizer.sanitize(undefined as any)).toBe('');
      expect(sanitizer.sanitize('')).toBe('');
    });

    it('should return empty string for non-string input', () => {
      expect(sanitizer.sanitize(123 as any)).toBe('');
      expect(sanitizer.sanitize({} as any)).toBe('');
      expect(sanitizer.sanitize([] as any)).toBe('');
    });

    it('should handle binary content indicators', () => {
      const binaryContent = 'Some text with null byte\0and replacement char�';
      const result = sanitizer.sanitize(binaryContent);
      expect(result).toBe(''); // Should return empty for binary content
    });

    it('should handle content with low printable character ratio', () => {
      const binaryLikeContent = '\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A'; // Mostly non-printable
      const result = sanitizer.sanitize(binaryLikeContent);
      expect(result).toBe(''); // Should return empty for binary-like content
    });

    it('should preserve meaningful content even with some HTML entities', () => {
      const htmlWithEntities =
        '<p>Q: How do I change my password?</p><p>A: You can&apos;t change passwords because we use SSO &amp; magic links.</p>';
      const result = sanitizer.sanitize(htmlWithEntities);
      expect(result).toContain("You can't change passwords");
      expect(result).toContain('SSO & magic links');
      expect(result).not.toContain('&apos;');
      expect(result).not.toContain('&amp;');
    });

    it('should handle empty HTML tags', () => {
      const emptyHtml = '<div></div><p></p><span></span>';
      const result = sanitizer.sanitize(emptyHtml);
      expect(result).toBe('');
    });

    it('should handle complex nested HTML structure', () => {
      const complexHtml = `
        <div class="container">
          <header>
            <h1>Title</h1>
            <nav>
              <ul>
                <li><a href="#">Link 1</a></li>
                <li><a href="#">Link 2</a></li>
              </ul>
            </nav>
          </header>
          <main>
            <article>
              <h2>Article Title</h2>
              <p>This is the article content with <em>emphasis</em> and <strong>strong</strong> text.</p>
            </article>
          </main>
        </div>
      `;
      const result = sanitizer.sanitize(complexHtml);
      expect(result).toContain('Title');
      expect(result).toContain('Link 1');
      expect(result).toContain('Link 2');
      expect(result).toContain('Article Title');
      expect(result).toContain('This is the article content with emphasis and strong text.');
      expect(result).not.toContain('<');
      expect(result).not.toContain('>');
    });
  });
});
