import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { OpenAIChatAgent, createChatAgent } from '../../src/sub-agents/openai/chat-agent';

describe('OpenAIChatAgent Deprecation', () => {
  let consoleSpy: any;

  beforeEach(() => {
    // Spy on console.warn to capture deprecation warnings
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    // Reset the global deprecation warning flag
    delete (globalThis as any).__openaiChatAgentDeprecationWarningShown;
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    delete (globalThis as any).__openaiChatAgentDeprecationWarningShown;
  });

  describe('Class Constructor', () => {
    it('should show deprecation warning when creating new instance', () => {
      // Create a new instance (this should trigger the warning)
      new OpenAIChatAgent({
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
        instructions: 'Test instructions',
      });

      // Verify deprecation warning was shown
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️  DEPRECATION WARNING: OpenAIChatAgent is deprecated')
      );
    });

    it('should only show deprecation warning once per instance', () => {
      const agent = new OpenAIChatAgent({
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
        instructions: 'Test instructions',
      });

      // Clear the spy to check for subsequent calls
      consoleSpy.mockClear();

      // Call a method that might trigger the warning again
      agent.healthCheck();

      // Verify no additional deprecation warnings
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('⚠️  DEPRECATION WARNING: OpenAIChatAgent is deprecated')
      );
    });
  });

  describe('Factory Function', () => {
    it('should show deprecation warning when using createChatAgent', () => {
      // Call the factory function
      createChatAgent({
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
      });

      // Verify deprecation warning was shown
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️  DEPRECATION WARNING: createChatAgent factory is deprecated')
      );
    });

    it('should only show deprecation warning once per process', () => {
      // Call the factory function multiple times
      createChatAgent({ model: 'gpt-4o-mini' });
      createChatAgent({ model: 'gpt-4o-mini' });
      createChatAgent({ model: 'gpt-4o-mini' });

      // Verify deprecation warning was shown only once
      const deprecationCalls = consoleSpy.mock.calls.filter((call: any[]) =>
        call[0].includes('⚠️  DEPRECATION WARNING: createChatAgent factory is deprecated')
      );
      expect(deprecationCalls).toHaveLength(1);
    });
  });

  describe('Functionality Preservation', () => {
    it('should still create valid instances despite deprecation', () => {
      const agent = new OpenAIChatAgent({
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
        instructions: 'Test instructions',
      });

      // Verify the instance is still properly created
      expect(agent).toBeInstanceOf(OpenAIChatAgent);
      expect(agent).toHaveProperty('chat');
      expect(agent).toHaveProperty('healthCheck');
      expect(agent).toHaveProperty('invoke');
    });

    it('should still create valid instances via factory function', () => {
      const agent = createChatAgent({
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 1000,
      });

      // Verify the instance is still properly created
      expect(agent).toBeInstanceOf(OpenAIChatAgent);
      expect(agent).toHaveProperty('chat');
      expect(agent).toHaveProperty('healthCheck');
      expect(agent).toHaveProperty('invoke');
    });
  });
});
