import { describe, it, expect } from 'vitest';
import { AgentValidator, AGENT_INPUT_SCHEMAS } from '../../src/validation/agent-validation';

describe('AgentValidator', () => {
  describe('validateSqlQuery', () => {
    it('should accept valid SELECT queries', () => {
      const queries = [
        'SELECT * FROM users',
        'SELECT id, name FROM users WHERE active = 1',
        'SELECT COUNT(*) FROM orders',
        'select name from products order by name',
      ];

      for (const query of queries) {
        const result = AgentValidator.validateSqlQuery(query);
        expect(result.valid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.sanitized).toBe(query.trim());
      }
    });

    it('should reject non-SELECT queries', () => {
      const queries = [
        'DROP TABLE users',
        'DELETE FROM users',
        'UPDATE users SET name = "test"',
        'INSERT INTO users VALUES (1, "test")',
        'CREATE TABLE test (id INT)',
        'ALTER TABLE users ADD COLUMN email VARCHAR(255)',
      ];

      for (const query of queries) {
        const result = AgentValidator.validateSqlQuery(query);
        expect(result.valid).toBe(false);
        expect(result.errors).toContain(
          expect.stringContaining('is not allowed. Allowed operations: SELECT')
        );
      }
    });

    it('should detect SQL injection patterns', () => {
      const maliciousQueries = [
        "SELECT * FROM users WHERE id = 1' OR '1'='1",
        'SELECT * FROM users; DROP TABLE users',
        'SELECT * FROM users UNION SELECT * FROM passwords',
        'SELECT * FROM users WHERE id = 1 AND SLEEP(5)',
        'SELECT * FROM users WHERE id = 1; EXEC sp_executesql',
      ];

      for (const query of maliciousQueries) {
        const result = AgentValidator.validateSqlQuery(query);
        expect(result.valid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      }
    });

    it('should enforce query length limits', () => {
      const longQuery = 'SELECT * FROM users WHERE ' + 'id = 1 AND '.repeat(1000) + 'active = 1';

      const result = AgentValidator.validateSqlQuery(longQuery);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('exceeds maximum length'));
    });

    it('should validate allowed tables when specified', () => {
      const query = 'SELECT * FROM restricted_table';
      const options = {
        allowedTables: ['users', 'products'],
      };

      const result = AgentValidator.validateSqlQuery(query, options);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should allow queries to permitted tables', () => {
      const query = 'SELECT * FROM users JOIN products ON users.id = products.user_id';
      const options = {
        allowedTables: ['users', 'products'],
      };

      const result = AgentValidator.validateSqlQuery(query, options);
      expect(result.valid).toBe(true);
    });

    it('should warn about missing WHERE clauses when required', () => {
      const query = 'SELECT * FROM users';
      const options = {
        requireWhere: true,
      };

      const result = AgentValidator.validateSqlQuery(query, options);
      expect(result.valid).toBe(true);
      expect(result.warnings).toContain(expect.stringContaining('does not include WHERE clause'));
    });

    it('should detect dangerous functions', () => {
      const dangerousQueries = [
        'SELECT LOAD_FILE("/etc/passwd")',
        'SELECT * FROM users INTO OUTFILE "/tmp/users.txt"',
        'SELECT SYSTEM("rm -rf /")',
      ];

      for (const query of dangerousQueries) {
        const result = AgentValidator.validateSqlQuery(query);
        expect(result.valid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      }
    });

    it('should handle null or invalid input', () => {
      const invalidInputs = [null, undefined, '', 123, {}, []];

      for (const input of invalidInputs) {
        const result = AgentValidator.validateSqlQuery(input as any);
        expect(result.valid).toBe(false);
        expect(result.errors).toContain('Query must be a non-empty string');
      }
    });
  });

  describe('validateAgentContext', () => {
    it('should accept valid context', () => {
      const validContexts = [
        { organizationId: 'org-123' },
        { organizationId: 'org-123', userId: 'user-456' },
        { organizationId: 'org-123', userId: 'user-456', customData: { key: 'value' } },
      ];

      for (const context of validContexts) {
        const result = AgentValidator.validateAgentContext(context);
        expect(result.valid).toBe(true);
        expect(result.errors).toHaveLength(0);
      }
    });

    it('should require organizationId', () => {
      const invalidContexts = [
        {},
        { userId: 'user-456' },
        { organizationId: null },
        { organizationId: undefined },
      ];

      for (const context of invalidContexts) {
        const result = AgentValidator.validateAgentContext(context);
        expect(result.valid).toBe(false);
        expect(result.errors).toContain('Context must include organizationId');
      }
    });

    it('should validate organizationId type', () => {
      const result = AgentValidator.validateAgentContext({ organizationId: 123 });
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('organizationId must be a string');
    });

    it('should validate userId type when present', () => {
      const result = AgentValidator.validateAgentContext({
        organizationId: 'org-123',
        userId: 456,
      });
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('userId must be a string');
    });

    it('should detect suspicious script content', () => {
      const suspiciousContext = {
        organizationId: 'org-123',
        data: '<script>alert("xss")</script>',
      };

      const result = AgentValidator.validateAgentContext(suspiciousContext);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should detect suspicious file paths', () => {
      const suspiciousContext = {
        organizationId: 'org-123',
        path: '../../../etc/passwd',
      };

      const result = AgentValidator.validateAgentContext(suspiciousContext);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle null or invalid context', () => {
      const invalidInputs = [null, undefined, 'string', 123, []];

      for (const input of invalidInputs) {
        const result = AgentValidator.validateAgentContext(input);
        expect(result.valid).toBe(false);
        expect(result.errors).toContain('Context must be a valid object');
      }
    });
  });

  describe('validateAgainstSchema', () => {
    it('should validate string schema', () => {
      const schema = {
        type: 'string' as const,
        required: true,
        minLength: 3,
        maxLength: 10,
      };

      expect(AgentValidator.validateAgainstSchema('hello', schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema('hi', schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema('verylongstring', schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema(123, schema).valid).toBe(false);
    });

    it('should validate number schema', () => {
      const schema = {
        type: 'number' as const,
        required: true,
      };

      expect(AgentValidator.validateAgainstSchema(42, schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema('42', schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema(NaN, schema).valid).toBe(false);
    });

    it('should validate boolean schema', () => {
      const schema = {
        type: 'boolean' as const,
        required: true,
      };

      expect(AgentValidator.validateAgainstSchema(true, schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema(false, schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema('true', schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema(1, schema).valid).toBe(false);
    });

    it('should validate array schema', () => {
      const schema = {
        type: 'array' as const,
        required: true,
        items: {
          type: 'string' as const,
        },
      };

      expect(AgentValidator.validateAgainstSchema(['a', 'b'], schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema(['a', 1], schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema('array', schema).valid).toBe(false);
    });

    it('should validate object schema', () => {
      const schema = {
        type: 'object' as const,
        required: true,
        properties: {
          name: {
            type: 'string' as const,
            required: true,
          },
          age: {
            type: 'number' as const,
          },
        },
      };

      expect(AgentValidator.validateAgainstSchema({ name: 'John' }, schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema({ name: 'John', age: 30 }, schema).valid).toBe(
        true
      );
      expect(AgentValidator.validateAgainstSchema({ age: 30 }, schema).valid).toBe(false);
      expect(AgentValidator.validateAgainstSchema([], schema).valid).toBe(false);
    });

    it('should handle enum validation', () => {
      const schema = {
        type: 'string' as const,
        enum: ['red', 'green', 'blue'],
      };

      expect(AgentValidator.validateAgainstSchema('red', schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema('yellow', schema).valid).toBe(false);
    });

    it('should handle pattern validation', () => {
      const schema = {
        type: 'string' as const,
        pattern: /^[a-z]+$/,
      };

      expect(AgentValidator.validateAgainstSchema('hello', schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema('Hello', schema).valid).toBe(false);
    });

    it('should handle optional fields', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          required: {
            type: 'string' as const,
            required: true,
          },
          optional: {
            type: 'string' as const,
          },
        },
      };

      expect(AgentValidator.validateAgainstSchema({ required: 'test' }, schema).valid).toBe(true);
      expect(AgentValidator.validateAgainstSchema({}, schema).valid).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('should sanitize string input', () => {
      const input = '  Hello\x00World\n\n  ';
      const result = AgentValidator.sanitizeInput(input);

      expect(result.sanitized).toBe('HelloWorld');
      expect(result.warnings).toHaveLength(0);
    });

    it('should detect base64-like content', () => {
      const input = 'SGVsbG8gV29ybGQ='; // "Hello World" in base64
      const result = AgentValidator.sanitizeInput(input);

      expect(result.warnings.length).toBeGreaterThanOrEqual(0);
    });

    it('should detect URLs', () => {
      const input = 'Check out https://example.com';
      const result = AgentValidator.sanitizeInput(input);

      expect(result.warnings).toContain('Input contains URL patterns');
    });

    it('should sanitize object keys', () => {
      const input = {
        'valid-key': 'value1',
        'invalid$key!': 'value2',
        'another.key': 'value3',
      };

      const result = AgentValidator.sanitizeInput(input);

      expect(result.sanitized).toHaveProperty('valid-key', 'value1');
      expect(result.sanitized).toHaveProperty('invalidkey', 'value2');
      expect(result.sanitized).toHaveProperty('anotherkey', 'value3');
      expect(result.warnings.length).toBeGreaterThan(0);
    });

    it('should recursively sanitize nested objects', () => {
      const input = {
        level1: {
          bad$key: {
            level3: '  spaced  content  ',
          },
        },
      };

      const result = AgentValidator.sanitizeInput(input);

      expect(result.sanitized.level1.badkey.level3).toBe('spaced content');
    });

    it('should handle arrays', () => {
      const input = ['  item1  ', '  item2  '];
      const result = AgentValidator.sanitizeInput(input);

      expect(result.sanitized).toEqual(['item1', 'item2']);
    });

    it('should handle primitive types', () => {
      expect(AgentValidator.sanitizeInput(123).sanitized).toBe(123);
      expect(AgentValidator.sanitizeInput(true).sanitized).toBe(true);
      expect(AgentValidator.sanitizeInput(null).sanitized).toBe(null);
    });
  });

  describe('AGENT_INPUT_SCHEMAS', () => {
    it('should validate askAI schema', () => {
      const validInput = { query: 'SELECT * FROM users' };
      const invalidInput = { query: '' };

      const validResult = AgentValidator.validateAgainstSchema(
        validInput,
        AGENT_INPUT_SCHEMAS.askAI
      );
      const invalidResult = AgentValidator.validateAgainstSchema(
        invalidInput,
        AGENT_INPUT_SCHEMAS.askAI
      );

      expect(validResult.valid).toBe(true);
      expect(invalidResult.valid).toBe(false);
    });

    it('should validate contextUpdate schema', () => {
      const validInput = { key: 'valid_key-123', data: { some: 'data' } };
      const invalidInput = { key: 'invalid key!', data: { some: 'data' } };

      const validResult = AgentValidator.validateAgainstSchema(
        validInput,
        AGENT_INPUT_SCHEMAS.contextUpdate
      );
      const invalidResult = AgentValidator.validateAgainstSchema(
        invalidInput,
        AGENT_INPUT_SCHEMAS.contextUpdate
      );

      expect(validResult.valid).toBe(true);
      expect(invalidResult.valid).toBe(false);
    });
  });
});
