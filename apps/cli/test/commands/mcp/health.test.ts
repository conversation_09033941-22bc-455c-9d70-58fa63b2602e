import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MCPHealthCommand } from '../../../src/commands/mcp/health.js';

// Mock the HTTP client
vi.mock('../../../src/utils/http.js', () => ({
  httpClient: {
    get: vi.fn(),
  },
}));

// Mock the config
vi.mock('../../../src/core/config.js', () => ({
  config: {
    getApiUrl: vi.fn((path: string) => `http://localhost:8000${path}`),
  },
}));

describe('MCPHealthCommand', () => {
  let command: MCPHealthCommand;

  beforeEach(() => {
    command = new MCPHealthCommand();
    vi.clearAllMocks();
  });

  it('should be instantiable', () => {
    expect(command).toBeInstanceOf(MCPHealthCommand);
  });

  it('should have correct description', () => {
    expect(MCPHealthCommand.description).toBe('Check MCP server health status');
  });

  it('should have examples', () => {
    expect(MCPHealthCommand.examples).toHaveLength(3);
    expect(MCPHealthCommand.examples[0]).toBe('aisec mcp health');
  });

  // Note: More comprehensive tests would require mocking the HTTP responses
  // and testing the actual execution logic
});
