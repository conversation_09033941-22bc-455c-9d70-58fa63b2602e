# @anter/cli

Anter CLI - Simple API health and connection testing tool.

## Installation

### From Workspace (Development)

```bash
# Install dependencies
pnpm install

# Build the CLI
pnpm --filter @anter/cli build

# Link globally for development (REQUIRED to use 'anter' command)
pnpm --filter @anter/cli link:global
```

**Note**: You MUST run the link command after building to use the `anter` command globally. If linking fails, you can run the CLI directly with `node apps/cli/dist/index.js <command>`.

### Global Installation (Production)

```bash
npm install -g @anter/cli
```

## Usage

```bash
anter --help
```

### Commands

The CLI provides two simple commands:

```bash
# Check API health (uses internal secret authentication)
anter health

# Test connection to external API using internal secret
anter test

# Get help for a specific command
anter health --help
anter test --help
```

### Global Options

- `--debug`: Enable debug output
- `--config <path>`: Path to configuration file
- `--format <format>`: Output format (table, json, yaml)
- `--quiet`: Suppress non-essential output

## Configuration

The CLI supports multiple configuration sources:

### Environment Variables

```bash
export ANTER_API_URL="http://localhost:8000"
export INTERNAL_API_SECRET="dev-secret-key-123"
export ANTER_OUTPUT_FORMAT="json"
export ANTER_QUIET="true"
export DEBUG="anter*"  # Enable debug logging
```

### Configuration File

Create `.anterrc.json` in your project root or home directory:

```json
{
  "api": {
    "baseUrl": "http://localhost:8000",
    "timeout": 30000
  },
  "internal": {
    "secret": "dev-secret-key-123"
  },
  "output": {
    "format": "table",
    "quiet": false
  }
}
```

## Development

### Adding New Commands

1. Create command file in `src/commands/{command}.ts`
2. Extend `BaseCommand` class
3. Register in `src/commands/index.ts`
4. Add tests in `test/commands/`

Example command structure:

```typescript
import { BaseCommand } from './base.js';

export class MyCommand extends BaseCommand {
  static description = 'My command description';

  async execute(options: MyOptions): Promise<void> {
    // Implementation
  }
}
```

### Testing

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run with coverage
pnpm test:coverage
```

### Building

```bash
# Build the CLI
pnpm build

# Build in watch mode
pnpm dev
```

## Architecture

- **Commands**: Simple command structure with base class
- **Configuration**: Multi-source configuration with validation
- **HTTP Client**: Robust HTTP client with error handling
- **Logging**: Structured logging with debug support
- **Error Handling**: Comprehensive error handling with exit codes
- **Authentication**: Internal secret authentication via `X-Internal-Secret` header

## Exit Codes

- `0`: Success
- `1`: General error
- `2`: Validation error
- `3`: Network error
- `4`: Configuration error

## Examples

### Basic Usage

```bash
# Check API health
anter health

# Test connection with custom API URL
anter test --api-base http://staging.example.com

# Get health status in JSON format
anter health --format json

# Quiet mode for scripting
anter health --quiet
```

### Advanced Usage

```bash
# Use custom configuration
anter --config ./custom-config.json health

# Debug mode with quiet output
anter --debug test --quiet

# Pipe output for processing
anter health --format json | jq '.status'
```

## Troubleshooting

### Common Issues

1. **Command not found**: Ensure CLI is properly installed and linked

   ```bash
   # If 'anter' command not found, run directly:
   node apps/cli/dist/index.js health
   ```

2. **Network error: socket hang up**: API server is not running

   ```bash
   # Start the API server first:
   cd apps/api && pnpm dev
   # Then run health check:
   anter health
   ```

3. **Authentication errors**: Check `INTERNAL_API_SECRET` environment variable
4. **Network errors**: Verify API URL and connectivity
5. **Permission errors**: Check file permissions for config files

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
DEBUG=anter* anter health
# or
anter --debug health
```

## Contributing

1. Follow existing code patterns
2. Add tests for new functionality
3. Update documentation
4. Ensure TypeScript compilation passes
5. Run linter and formatter before committing
