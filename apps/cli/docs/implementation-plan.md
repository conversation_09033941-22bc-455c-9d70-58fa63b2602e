# Anter CLI – Implementation Plan

> **Scope**: This document captures the high-level design, implemented features, and future work for the `@anter/cli` package.

---

## 1. Objectives

1. Provide a simple command-line interface (`anter`) for basic API health and connection testing.
2. Keep the CLI clean and minimal for easy maintenance and future expansion.
3. Support health check and test connection functionality using internal secret authentication.
4. Ensure seamless configuration via environment variables, config files, and CLI flags.
5. Deliver a developer-friendly architecture with strong typing, tests, and documentation.

---

## 2. Project Structure

```text
apps/cli/
├── src/
│   ├── commands/           # Command implementations
│   │   ├── base.ts         # Shared command utilities
│   │   ├── health.ts       # API health check
│   │   ├── test.ts         # Connection test command
│   │   └── index.ts        # Command registration
│   ├── core/
│   │   ├── config.ts       # Multi-source configuration (env, file, flags)
│   │   └── logger.ts       # Structured logging (chalk, debug)
│   ├── utils/
│   │   └── http.ts         # HTTP client helpers
│   ├── types/              # Shared TypeScript types
│   └── index.ts            # CLI entry – registers commands
├── docs/                   # Documentation (you are here)
├── test/                   # Vitest unit tests
├── tsup.config.ts          # Build configuration
└── etc.
```

---

## 3. Core Components

### 3.1 Configuration System

- Sources: environment, `.anterrc.(json|yaml)`, CLI flags.
- Validation: `zod` schemas ensure correctness.
- Singleton pattern for global access.
- Internal secret configuration for authentication.

### 3.2 Command Architecture

- `BaseCommand` abstracts common behaviour.
- Simple command structure: `health` and `test` commands.
- Rich output formats: table (default), JSON, YAML.

### 3.3 HTTP Utilities

- Centralised `http.ts` with timeouts and typed errors.
- Support for internal secret authentication via `X-Internal-Secret` header.

### 3.4 Logging & Error Handling

- Structured logs via `chalk` colours + emoji.
- `CLIError` with numeric exit codes.
- Global handlers for `SIGINT`, unhandledRejection, uncaughtException.

---

## 4. Delivered Functionality (MVP)

| Command        | Description                                   |
| -------------- | --------------------------------------------- |
| `anter health` | Check API health status using internal secret |
| `anter test`   | Test connection using internal secret         |

Future command groups can be added as needed: `anter agents`, `anter db`, etc.

---

## 5. Technical Stack

- **TypeScript** (ESM) compiled via **tsup**
- **commander** for CLI parsing
- **chalk**, **ora**, and **table** for UX
- **cosmiconfig** for configuration discovery
- **vitest** for unit tests & coverage
- **update-notifier** for version checks

---

## 6. Test Strategy

- Unit tests for each command & core module
- Mocked HTTP using **nock**
- CI goal: ≥ 90 % coverage
- Manual e2e flow documented in [`testing.md`](./cli-testing.md)

---

## 7. Roadmap

1. **Build/Publish Pipeline** – automate versioning & release to npm.
2. **Additional Commands** – agents management, bulk document upload, etc.
3. **Integration Tests** – containerised API + Redis for e2e.
4. **Self-update Command** – `anter update` to upgrade globally installed CLI.
5. **Plugin System** – allow external packages to inject commands.

---

_Last updated: 2025-01-21_
