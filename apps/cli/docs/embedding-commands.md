# Embedding Commands Documentation

## Overview

The Anter CLI includes a comprehensive set of embedding management commands that allow you to interact with document and knowledge base embeddings. These commands provide a robust interface for managing the embedding lifecycle within your organization.

## Architecture

### Command Structure

The embedding commands follow a hierarchical structure with subcommands:

```
anter embedding <subcommand> [options]

Subcommands:
  list      - List embeddings for an organization
  create    - Create embeddings for documents or knowledge bases
  update    - Update existing embeddings
  delete    - Delete embeddings
  status    - Check embedding job status
  stats     - Show embedding statistics
```

### Design Principles

1. **Consistent Interface**: All commands follow the same pattern and use consistent option naming
2. **Robust Error Handling**: Comprehensive error handling with helpful error messages
3. **Flexible Output**: Support for multiple output formats (table, JSON, YAML)
4. **Organization-Aware**: All operations are scoped to specific organizations
5. **Job Management**: Support for asynchronous operations with job tracking
6. **Fallback Mode**: Temporary fallback to health endpoints while embedding APIs are being implemented

## Commands Reference

### List Embeddings

Lists embeddings for an organization with filtering options.

```bash
anter embedding list [options]
```

**Options:**

- `--organization <id>` - Organization ID
- `--type <type>` - Filter by type (document, knowledge-base, all) [default: "all"]
- `--limit <number>` - Limit number of results [default: "50"]
- `--offset <number>` - Offset for pagination [default: "0"]
- `--status <status>` - Filter by status (pending, completed, failed, all) [default: "all"]
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# List all embeddings for an organization
anter embedding list --organization org-123

# List only document embeddings
anter embedding list --organization org-123 --type document

# List completed embeddings with JSON output
anter embedding list --organization org-123 --status completed --format json

# List with pagination
anter embedding list --organization org-123 --limit 10 --offset 20
```

**Output Format:**
The list command displays comprehensive embedding data including:

- Organization summary (content hashes, document cache, embeddings count)
- Document embeddings grouped by document with chunk details
- Content hashes and document cache entries
- Total size and formatting information

**Note:** This command is fully implemented and uses the actual embeddings API endpoint to retrieve real data from Redis.

### Create Embeddings

Creates embeddings for documents or knowledge bases.

```bash
anter embedding create [options]
```

**Required Options:**

- `--organization <id>` - Organization ID
- `--type <type>` - Type (document, knowledge-base)
- `--id <id>` - Document or knowledge base ID

**Optional Options:**

- `--force` - Force recreation of existing embeddings
- `--wait` - Wait for job completion
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# Create embeddings for a document
anter embedding create --organization org-123 --type document --id doc-456

# Create embeddings for a knowledge base with force flag
anter embedding create --organization org-123 --type knowledge-base --id kb-789 --force

# Create and wait for completion
anter embedding create --organization org-123 --type document --id doc-456 --wait
```

### Update Embeddings

Updates existing embeddings.

```bash
anter embedding update [options]
```

**Required Options:**

- `--organization <id>` - Organization ID
- `--type <type>` - Type (document, knowledge-base)
- `--id <id>` - Document or knowledge base ID

**Optional Options:**

- `--force` - Force update even if not needed
- `--wait` - Wait for job completion
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# Update embeddings for a document
anter embedding update --organization org-123 --type document --id doc-456

# Force update and wait for completion
anter embedding update --organization org-123 --type knowledge-base --id kb-789 --force --wait
```

### Delete Embeddings

Deletes embeddings.

```bash
anter embedding delete [options]
```

**Required Options:**

- `--organization <id>` - Organization ID
- `--type <type>` - Type (document, knowledge-base)
- `--id <id>` - Document or knowledge base ID

**Optional Options:**

- `--force` - Force deletion without confirmation
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# Delete embeddings for a document
anter embedding delete --organization org-123 --type document --id doc-456

# Force delete embeddings
anter embedding delete --organization org-123 --type knowledge-base --id kb-789 --force
```

### Check Job Status

Checks the status of embedding jobs.

```bash
anter embedding status [options]
```

**Options:**

- `--organization <id>` - Organization ID
- `--jobId <id>` - Specific job ID
- `--type <type>` - Filter by type (document, knowledge-base, all) [default: "all"]
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# Check status of a specific job
anter embedding status --jobId job-123

# Check status for an organization
anter embedding status --organization org-123

# Check status for document embeddings only
anter embedding status --organization org-123 --type document
```

### Show Statistics

Shows embedding statistics.

```bash
anter embedding stats [options]
```

**Options:**

- `--organization <id>` - Organization ID
- `--period <period>` - Time period (day, week, month, all) [default: "all"]
- `--format <format>` - Output format (table, json, yaml) [default: "table"]
- `--quiet` - Suppress non-essential output

**Examples:**

```bash
# Show statistics for an organization
anter embedding stats --organization org-123

# Show weekly statistics
anter embedding stats --organization org-123 --period week

# Show all-time statistics
anter embedding stats --organization org-123 --period all
```

## Configuration

### Required Configuration

The embedding commands require the following configuration:

```json
{
  "api": {
    "baseUrl": "http://localhost:8000",
    "timeout": 30000
  },
  "internal": {
    "secret": "your-internal-secret"
  },
  "output": {
    "format": "table",
    "quiet": false
  }
}
```

### Environment Variables

You can also configure the CLI using environment variables:

- `ANTER_API_BASE_URL` - API base URL
- `ANTER_INTERNAL_SECRET` - Internal secret for authentication
- `ANTER_OUTPUT_FORMAT` - Default output format
- `ANTER_OUTPUT_QUIET` - Suppress non-essential output

## Implementation Details

### Current Status

**List Command**: The `anter embedding list` command is now fully implemented and uses the actual embeddings API endpoint (`GET /v1/external/embeddings`) to retrieve organization data from Redis.

**Other Commands**: The remaining embedding commands (create, update, delete, status, stats) currently operate in fallback mode, using the health endpoint as a temporary solution while the actual embedding API endpoints are being implemented.

### API Endpoints

#### Implemented Endpoints

- `GET /v1/external/embeddings` - List embeddings data for an organization (✅ Implemented)

#### Planned Endpoints

When the remaining embedding API endpoints are implemented, the commands will use the following endpoints:

- `POST /v1/internal/embeddings` - Create embeddings
- `PUT /v1/internal/embeddings/{id}` - Update embeddings
- `DELETE /v1/internal/embeddings/{id}` - Delete embeddings
- `GET /v1/internal/embeddings/status` - Get job status
- `GET /v1/internal/embeddings/stats` - Get statistics

### Error Handling

The commands include comprehensive error handling for:

- Network connectivity issues
- Authentication failures
- API endpoint not found (404)
- Timeout errors
- Validation errors

### Output Formats

All commands support multiple output formats:

- **Table** (default): Human-readable table format
- **JSON**: Machine-readable JSON format
- **YAML**: YAML format for configuration files

## Best Practices

### Organization Management

1. **Always specify organization**: Use the `--organization` flag to ensure operations are scoped correctly
2. **Use consistent IDs**: Maintain consistent naming conventions for organization, document, and knowledge base IDs
3. **Monitor job status**: Use the `--wait` flag for critical operations or check status separately

### Performance Optimization

1. **Use pagination**: For large datasets, use `--limit` and `--offset` to paginate results
2. **Filter results**: Use `--type` and `--status` filters to reduce data transfer
3. **Batch operations**: Consider batching multiple operations when possible

### Security

1. **Secure configuration**: Store sensitive configuration securely
2. **Use internal secrets**: Ensure proper authentication with internal secrets
3. **Validate inputs**: Always validate organization IDs and other inputs

## Troubleshooting

### Common Issues

1. **API server not running**

   ```
   ❌ API server is not running
   💡 To start the API server:
      cd apps/api && pnpm dev
   ```

2. **Authentication failed**

   ```
   ❌ Authentication failed
   💡 Check your internal secret configuration
   ```

3. **Embedding endpoints not found**
   ```
   ❌ Embedding endpoint not found
   💡 The embedding endpoints might not be implemented yet
   💡 For now, these commands will use health endpoints
   ```

### Debug Mode

Enable debug mode to get more detailed information:

```bash
anter embedding list --organization org-123 --debug
```

## Future Enhancements

### Planned Features

1. **Real-time monitoring**: WebSocket-based real-time job status updates
2. **Bulk operations**: Support for bulk create, update, and delete operations
3. **Advanced filtering**: More sophisticated filtering and search capabilities
4. **Export functionality**: Export embedding data in various formats
5. **Integration with external tools**: Integration with vector databases and embedding services

### API Integration

Once the embedding API endpoints are implemented, the CLI will automatically switch from fallback mode to use the actual endpoints without any changes to the command interface.

## Contributing

When implementing the actual embedding API endpoints:

1. Follow the existing API patterns in `apps/api/src/api/v1/internal/routes/`
2. Implement proper authentication and authorization
3. Add comprehensive error handling
4. Include proper validation for all inputs
5. Add unit and integration tests
6. Update this documentation with actual endpoint information
