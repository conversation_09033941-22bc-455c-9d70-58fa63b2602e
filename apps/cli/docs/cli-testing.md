# Anter CLI – Testing Guide

This guide shows **end-to-end steps** for building, linking and testing the `anter` command-line interface.

---

## 1. Prerequisites

- **Node.js ≥ 18** and **pnpm ≥ 8** installed.
- Local Anter API running (default `http://localhost:8000`).
- Bash/Zsh shell.

---

## 2. Building the CLI

### Workspace build (development)

```bash
# From repository root
pnpm --filter @anter/cli install     # install CLI deps
pnpm --filter @anter/cli build       # compile to ./dist

# Optional – watch mode during development
pnpm --filter @anter/cli dev
```

### Stand-alone build (inside directory)

```bash
cd apps/cli
pnpm install
pnpm build
```

---

## 3. Linking Globally

Linking places an executable **`anter`** in your global pnpm bin directory so it is available from any terminal:

```bash
# From repository root
pnpm --filter @anter/cli link:global

# OR from CLI folder
cd apps/cli
pnpm link --global
```

Validate:

```bash
which anter           # ➜ /Users/<USER>/Library/pnpm/anter
anter --version
```

If you update the CLI, run the link command again to refresh the global binary.

---

## 4. Setting Up Environment Variables

The CLI expects these environment variables for testing:

```bash
# Required for all commands
export ANTER_API_URL="http://localhost:8000"
export INTERNAL_API_SECRET="dev-secret-key-123"

# Optional
export DEBUG="anter*"  # for verbose logging
```

You can use the helper script to set these up:

```bash
cd apps/cli
# Safest: evaluate the *output* – keeps your shell alive on failure
eval "$(./get-token.sh)"
```

---

## 5. Running Tests Manually

With the environment variables exported you can exercise CLI commands:

```bash
# General help
anter --help

# Check API health (uses internal secret authentication)
anter health

# Test connection to external API (uses internal secret authentication)
anter test

# JSON output
anter health --format json | jq

# Quiet mode for scripting
anter health --quiet
```

Use `--debug` for wire-level logs:

```bash
anter --debug health
```

---

## 6. Automated Tests

The project ships with **vitest** suites.

```bash
# Unit tests only for the CLI package
pnpm --filter @anter/cli test

# All tests (monorepo)
pnpm test
```

Coverage:

```bash
pnpm --filter @anter/cli test:coverage
```

---

## 7. Diagram – Testing Workflow

```mermaid
flowchart TD
  A[Build CLI] --> B[Link globally]
  B --> C{Export env vars?}
  C -- no --> D[get-token.sh]
  D -->|eval output| E[Env vars exported]
  C -- yes --> E[Env vars exported]
  E --> F[anter --help]
  F --> G[Run test commands]
```

---

## 8. Troubleshooting Checklist

| Symptom                                    | Resolution                                                     |
| ------------------------------------------ | -------------------------------------------------------------- |
| `anter: command not found`                 | Re-run **`pnpm link --global`** in _apps/cli_                  |
| Terminal exits after `source get-token.sh` | Run `eval "$(./get-token.sh)"` instead (script exits on error) |
| 401 / authentication errors                | Verify `INTERNAL_API_SECRET` is set correctly                  |
| Network/ENOTFOUND errors                   | Check `ANTER_API_URL`, server reachable                        |

---

Happy testing! :rocket:
