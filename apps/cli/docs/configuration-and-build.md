# Anter CLI – Configuration & Build Guide

This document covers installation, configuration sources, and build workflows for running the `anter` CLI locally or in CI.

---

## 1. Prerequisites

- **Node.js ≥ 18**
- **pnpm ≥ 8** (the workspace uses pnpm workspaces)
- _(optional)_ Local Anter API at `http://localhost:8000` for end-to-end tests

---

## 2. Installation

### 2.1 Workspace (development)

```bash
# From repository root
pnpm install                 # installs all workspace deps

# Build the CLI package
pnpm --filter @anter/cli build  # output => apps/cli/dist/

# Link globally so you can use `anter` anywhere
pnpm --filter @anter/cli link:global
```

### 2.2 Stand-alone (inside package directory)

```bash
cd apps/cli
pnpm install
pnpm build
pnpm link --global      # adds anter to your PATH
```

**Check path**

```bash
which anter   # ➜ /Users/<USER>/Library/pnpm/anter
```

Re-run `pnpm link --global` whenever you rebuild the CLI.

---

## 3. Configuration Sources

Priority (highest → lowest):

1. **CLI flags** (e.g. `--config`, `--debug`, `--format`)
2. **Environment variables**
3. **Config files** (`.anterrc.{json,yaml}` in cwd or `$HOME`)
4. **Package defaults**

### 3.1 Environment Variables

| Variable              | Description                        | Default                 |
| --------------------- | ---------------------------------- | ----------------------- |
| `ANTER_API_URL`       | Base URL of Anter API              | `http://localhost:8000` |
| `INTERNAL_API_SECRET` | Internal secret for authentication | `dev-secret-key-123`    |
| `ANTER_OUTPUT_FORMAT` | `table`, `json`, or `yaml`         | `table`                 |
| `ANTER_QUIET`         | Suppress non-essential output      | `false`                 |
| `DEBUG`               | Enable debug logging (`anter*`)    | _unset_                 |

Example:

```bash
export ANTER_API_URL="https://api.anter.io"
export INTERNAL_API_SECRET="your-secret"
export DEBUG="anter*"
```

### 3.2 Config File Example (`.anterrc.json`)

```json
{
  "api": {
    "baseUrl": "https://api.anter.io",
    "timeout": 30000
  },
  "internal": {
    "secret": "your-secret"
  },
  "output": {
    "format": "json",
    "quiet": false
  }
}
```

---

## 4. Build Workflow Internals

The package is authored in TypeScript and bundled with **tsup** to ESM. The build script:

```bash
pnpm --filter @anter/cli build
```

performs:

1. Cleans `dist/` and coverage directories
2. Runs `tsup` (respecting `tsup.config.ts`)
3. Produces `dist/index.js` with proper shebang (`#!/usr/bin/env node`)

The helper script `build.sh` (Unix) automates install → build → link.

---

## 5. Running the CLI Locally

1. Export environment variables:

   ```bash
   cd apps/cli
   eval "$(./get-token.sh)"
   ```

2. Verify environment variables:

   ```bash
   echo $ANTER_API_URL
   echo $INTERNAL_API_SECRET
   ```

3. Run commands:

   ```bash
   anter --help
   anter health
   anter test --format json | jq
   ```

Use `--debug` or `DEBUG=anter*` for verbose logs.

---

## 6. Common Issues

| Issue                                       | Fix                                                       |
| ------------------------------------------- | --------------------------------------------------------- |
| `anter: command not found`                  | Run `pnpm link --global` inside _apps/cli_                |
| Build fails on Windows PowerShell           | Use `build.sh` on Git Bash or WSL, or follow manual steps |
| Terminal exits when sourcing `get-token.sh` | Evaluate the output instead: `eval "$(./get-token.sh)"`   |
| Authentication errors                       | Check `INTERNAL_API_SECRET` environment variable          |

---

Happy building! :hammer_and_wrench:
