{"name": "@anter/cli", "version": "0.0.1", "description": "Anter CLI - Simple API health and connection testing", "type": "module", "main": "dist/index.js", "bin": {"anter": "./dist/index.js"}, "exports": {".": "./dist/index.js", "./commands/*": "./dist/commands/*.js", "./utils/*": "./dist/utils/*.js"}, "files": ["dist", "README.md"], "scripts": {"build": "pnpm clean && tsup", "dev": "tsup --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "rm -rf dist coverage", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "link:global": "pnpm link --global"}, "dependencies": {"@anter/agents": "workspace:^", "@anter/domain-model": "workspace:^", "@types/ws": "^8.18.1", "chalk": "^5.3.0", "commander": "^12.0.0", "cosmiconfig": "^9.0.0", "inquirer": "^12.0.0", "ora": "^8.0.1", "semver": "^7.6.0", "table": "^6.8.2", "update-notifier": "^7.0.0", "ws": "^8.18.2", "zod": "^3.25.67"}, "devDependencies": {"@types/inquirer": "^9.0.7", "@types/node": "^24.0.1", "@types/semver": "^7.5.8", "@types/update-notifier": "^6.0.8", "@vitest/coverage-v8": "^2.1.8", "nock": "^13.5.4", "prettier": "^3.5.3", "tsup": "^8.0.2", "typescript": "^5.8.3", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/anter/anter-api.git", "directory": "apps/cli"}, "keywords": ["cli", "health-check", "api-testing", "anter"], "author": "<PERSON><PERSON>", "license": "ISC", "private": true}