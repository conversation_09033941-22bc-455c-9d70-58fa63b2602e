#!/bin/bash

# Build script for Anter CLI
set -e

echo "🔧 Building Anter CLI..."

# Install dependencies if needed
echo "📦 Installing dependencies..."
pnpm install

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist coverage

# Run TypeScript compilation
echo "🔨 Building with tsup..."
npx tsup

# Make the output executable
echo "🔧 Making CLI executable..."
chmod +x dist/index.js

echo "✅ Build complete!"
echo ""
echo "To link globally:"
echo "  pnpm link --global"
echo ""
echo "To test locally:"
echo "  node dist/index.js --help" 