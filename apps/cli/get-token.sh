#!/usr/bin/env bash
# ----------------------------------------------------------------------------
# get-token.sh
# ----------------------------------------------------------------------------
# Fetches a fresh JWT access token from the Anter API and prints a shell
# command that exports it as ANTER_JWT_TOKEN.  Evaluate the output to make the
# token available in the current shell session.
#
#   eval "$(./get-token.sh)"      # bash/zsh
#   source <(./get-token.sh)      # alternative
# ----------------------------------------------------------------------------

# Exit on error, undefined var, or pipe fail
set -euo pipefail

# --------------------------- Configuration -----------------------------------
API_URL="${ANTER_API_URL:-http://localhost:8000}"
EMAIL="${ANTER_EMAIL:-<EMAIL>}"
ORG_ID="${ANTER_ORG_ID:-cm9of7eyo0006i1o45sqroxev}"
INTERNAL_SECRET="${INTERNAL_API_SECRET:-dev-secret-key-123}"

# --------------------------- Build payload ------------------------------------
# No payload needed for external API token generation - only internal secret is required

# --------------------------- Fetch token --------------------------------------
token=$(curl --silent --fail --location "${API_URL}/api/v1/external/auth/token" \
  -H 'Content-Type: application/json' \
  -H "X-Internal-Secret: ${INTERNAL_SECRET}" | jq -r '.access_token')

# Ensure we got something that looks like a JWT
if [[ -z "$token" || "$token" == "null" ]]; then
  echo "Error: failed to retrieve access token" >&2
  exit 1
fi

# --------------------------- Export or echo -----------------------------------
# If the script is sourced, export directly; otherwise print the export command
if [[ "${BASH_SOURCE[0]:-}" != "${0}" ]]; then
  # Sourced
  export ANTER_JWT_TOKEN="$token"
  export ANTER_API_URL="https://localhost:8000"
  export ANTER_ORG_ID="$ORG_ID"
  export DEBUG="anter*"
else
  # Executed (use eval or source <(./get-token.sh))
  printf 'export ANTER_JWT_TOKEN="%s"\n' "$token"
  printf 'export ANTER_ORG_ID="%s"\n' "$ORG_ID"
fi
