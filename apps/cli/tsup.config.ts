import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['esm'],
  target: 'node18',
  outDir: 'dist',
  clean: true,
  dts: true,
  minify: false,
  sourcemap: true,
  banner: {
    js: '#!/usr/bin/env node',
  },

  external: [
    // Mark workspace packages as external
    '@anter/agents',
    '@anter/domain-model',
    // Mark Node.js built-ins as external
    'ws',
    'chalk',
    'ora',
    'commander',
    'inquirer',
    'cosmiconfig',
    'update-notifier',
    'semver',
    'table',
    'zod',
  ],
  // Ensure proper ESM output
  splitting: false,
  treeshake: true,
});
