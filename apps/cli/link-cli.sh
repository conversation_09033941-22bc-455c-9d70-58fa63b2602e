#!/bin/bash

# Anter CLI Linking Script
# This script helps link the CLI globally for development

set -e

echo "🔗 Linking Anter CLI globally..."

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "dist/index.js" ]; then
    echo "❌ Error: Must be run from the CLI directory with built files"
    echo "   Run: cd apps/cli && pnpm build && ./link-cli.sh"
    exit 1
fi

# Try pnpm link first
echo "📦 Attempting pnpm link..."
if pnpm link:global; then
    echo "✅ Successfully linked with pnpm"
    echo "🎉 You can now use 'anter' command globally"
    exit 0
fi

# Try npm link as fallback
echo "📦 pnpm link failed, trying npm link..."
if npm link; then
    echo "✅ Successfully linked with npm"
    echo "🎉 You can now use 'anter' command globally"
    exit 0
fi

# Manual linking as last resort
echo "📦 Automatic linking failed, setting up manual link..."
CLI_PATH=$(pwd)
GLOBAL_BIN=$(npm bin -g)

if [ -z "$GLOBAL_BIN" ]; then
    echo "❌ Could not determine global bin directory"
    echo "💡 You can run the CLI directly with:"
    echo "   node $CLI_PATH/dist/index.js <command>"
    exit 1
fi

# Create symlink manually
echo "🔗 Creating manual symlink..."
ln -sf "$CLI_PATH/dist/index.js" "$GLOBAL_BIN/anter"

if [ $? -eq 0 ]; then
    echo "✅ Successfully created manual symlink"
    echo "🎉 You can now use 'anter' command globally"
else
    echo "❌ Failed to create manual symlink"
    echo "💡 You can run the CLI directly with:"
    echo "   node $CLI_PATH/dist/index.js <command>"
    exit 1
fi 