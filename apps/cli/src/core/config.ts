import { cosmiconfigSync } from 'cosmiconfig';
import { Config, ConfigSchema, ConfigurationError } from '../types/index.js';

export class ConfigManager {
  private config: Config;
  private static instance: ConfigManager;
  private customConfigPath?: string;

  constructor() {
    this.config = this.loadConfig();
  }

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  // Set custom config path and reload config
  setConfigPath(path: string): void {
    this.customConfigPath = path;
    this.config = this.loadConfig();
  }

  private loadConfig(): Config {
    try {
      let result;

      if (this.customConfigPath) {
        // Load specific config file
        const explorer = cosmiconfigSync('anter');
        result = explorer.load(this.customConfigPath);
      } else {
        // Auto-discover config file
        const explorer = cosmiconfigSync('anter');
        result = explorer.search();
      }

      // Build configuration from multiple sources
      const rawConfig = {
        api: {
          baseUrl: process.env.ANTER_API_URL || 'https://localhost:8000',
          timeout: parseInt(process.env.ANTER_API_TIMEOUT || '30000', 10),
        },
        internal: {
          secret: process.env.INTERNAL_API_SECRET || 'dev-secret-key-123',
        },
        output: {
          format: (process.env.ANTER_OUTPUT_FORMAT as 'table' | 'json' | 'yaml') || 'table',
          quiet: process.env.ANTER_QUIET === 'true',
        },
        // Override with config file if found
        ...result?.config,
      };

      // Validate configuration
      const validatedConfig = ConfigSchema.parse(rawConfig);
      return validatedConfig;
    } catch (error) {
      if (error instanceof Error) {
        throw new ConfigurationError(`Configuration error: ${error.message}`);
      }
      throw new ConfigurationError('Unknown configuration error');
    }
  }

  get(): Config {
    return this.config;
  }

  getApiUrl(path: string = ''): string {
    const baseUrl = this.config.api.baseUrl.replace(/\/$/, '');
    const cleanPath = path.replace(/^\//, '');
    return cleanPath ? `${baseUrl}/${cleanPath}` : baseUrl;
  }

  getTimeout(): number {
    return this.config.api.timeout;
  }

  getInternalSecret(): string {
    return this.config.internal.secret;
  }

  getOutputFormat(): 'table' | 'json' | 'yaml' {
    return this.config.output.format;
  }

  isQuiet(): boolean {
    return this.config.output.quiet;
  }

  // Update configuration at runtime
  updateConfig(updates: Partial<Config>): void {
    try {
      const newConfig = { ...this.config, ...updates };
      this.config = ConfigSchema.parse(newConfig);
    } catch (error) {
      if (error instanceof Error) {
        throw new ConfigurationError(`Configuration update error: ${error.message}`);
      }
      throw new ConfigurationError('Unknown configuration update error');
    }
  }
}

// Singleton instance
export const config = ConfigManager.getInstance();
