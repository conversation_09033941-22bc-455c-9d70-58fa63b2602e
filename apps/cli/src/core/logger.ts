import chalk from 'chalk';

export class Logger {
  private debugEnabled = process.env.DEBUG?.includes('aisec') || false;
  private quiet = false;

  setQuiet(quiet: boolean) {
    this.quiet = quiet;
  }

  info(message: string, data?: any) {
    if (this.quiet) return;
    console.log(chalk.blue('ℹ'), message);
    if (data && this.debugEnabled) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  success(message: string, data?: any) {
    if (this.quiet) return;
    console.log(chalk.green('✓'), message);
    if (data && this.debugEnabled) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  error(message: string, error?: Error | any) {
    console.error(chalk.red('✗'), message);
    if (error && this.debugEnabled) {
      if (error instanceof Error) {
        console.error(chalk.red(error.stack || error.message));
      } else {
        console.error(chalk.red(JSON.stringify(error, null, 2)));
      }
    }
  }

  warn(message: string, data?: any) {
    if (this.quiet) return;
    console.warn(chalk.yellow('⚠'), message);
    if (data && this.debugEnabled) {
      console.warn(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  debug(message: string, data?: any) {
    if (!this.debugEnabled) return;
    console.log(chalk.gray('🔍'), chalk.gray(message));
    if (data) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  table(data: any[], headers?: string[]) {
    if (this.quiet) return;

    if (data.length === 0) {
      this.info('No data to display');
      return;
    }

    // Simple table formatting - could be enhanced with the 'table' package
    if (headers) {
      console.log(chalk.bold(headers.join('\t')));
      console.log(headers.map(() => '---').join('\t'));
    }

    data.forEach(row => {
      if (Array.isArray(row)) {
        console.log(row.join('\t'));
      } else if (typeof row === 'object') {
        console.log(Object.values(row).join('\t'));
      } else {
        console.log(row);
      }
    });
  }

  json(data: any) {
    console.log(JSON.stringify(data, null, 2));
  }

  step(step: number, total: number, message: string) {
    if (this.quiet) return;
    const progress = `[${step}/${total}]`;
    console.log(chalk.cyan(progress), message);
  }
}

// Singleton instance
export const logger = new Logger();
