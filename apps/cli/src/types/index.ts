import { z } from 'zod';

// Configuration schema
export const ConfigSchema = z.object({
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number().default(30000),
  }),
  internal: z.object({
    secret: z.string(),
  }),
  output: z.object({
    format: z.enum(['table', 'json', 'yaml']).default('table'),
    quiet: z.boolean().default(false),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;

// Command interfaces
export interface CommandOptions {
  quiet?: boolean;
  format?: 'table' | 'json' | 'yaml';
  config?: string;
}

export interface HealthOptions extends CommandOptions {
  component?: string;
}

export interface TestOptions extends CommandOptions {
  // No additional options needed for test command
}

// Embedding command interfaces
export interface EmbeddingListOptions extends CommandOptions {
  organization?: string;
  type?: 'document' | 'knowledge-base' | 'all';
  limit?: number;
  offset?: number;
  status?: 'pending' | 'completed' | 'failed' | 'all';
}

export interface EmbeddingCreateOptions extends CommandOptions {
  organization: string;
  type: 'document' | 'knowledge-base';
  id: string;
  force?: boolean;
  wait?: boolean;
}

export interface EmbeddingUpdateOptions extends CommandOptions {
  organization: string;
  type?: 'document' | 'knowledge-base';
  id?: string;
  force?: boolean;
  wait?: boolean;
}

export interface EmbeddingDeleteOptions extends CommandOptions {
  organization: string;
  type: 'document' | 'knowledge-base';
  id: string;
  force?: boolean;
}

export interface EmbeddingStatusOptions extends CommandOptions {
  organization?: string;
  jobId?: string;
  type?: 'document' | 'knowledge-base' | 'all';
}

export interface EmbeddingStatsOptions extends CommandOptions {
  organization?: string;
  period?: 'day' | 'week' | 'month' | 'all';
}

// API response types
export interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
  version: string;
  uptime: number;
  summary?: {
    healthy: number;
    total: number;
  };
  components?: Record<string, any>;
}

// Embedding API response types
export interface EmbeddingListResponse {
  embeddings: EmbeddingItem[];
  total: number;
  limit: number;
  offset: number;
}

// Actual API response types for the embeddings endpoint
export interface EmbeddingsApiResponse {
  organizationId: string;
  summary: {
    contentHashes: number;
    docCache: number;
    embeddings: number;
    totalKeys: number;
    totalSize: number;
    totalSizeFormatted: string;
  };
  data: {
    contentHashes: KeyMetadata[];
    docCache: KeyMetadata[];
    embeddings: DocumentGroup[];
  };
  timestamp: string;
}

export interface KeyMetadata {
  key: string;
  size: number;
  sizeFormatted: string;
  type: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface DocumentGroup {
  documentId: string;
  chunkCount: number;
  totalSize: number;
  sizeFormatted: string;
  chunks: KeyMetadata[];
}

export interface EmbeddingItem {
  id: string;
  organizationId: string;
  type: 'document' | 'knowledge-base';
  status: 'pending' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface EmbeddingCreateResponse {
  jobId: string;
  message: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface EmbeddingStatusResponse {
  jobId: string;
  status: 'pending' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface EmbeddingStatsResponse {
  total: number;
  completed: number;
  failed: number;
  pending: number;
  averageProcessingTime?: number;
  period: string;
  breakdown?: Record<string, number>;
}

// Error types
export class CLIError extends Error {
  constructor(
    message: string,
    public code: string = 'CLI_ERROR',
    public exitCode: number = 1
  ) {
    super(message);
    this.name = 'CLIError';
  }
}

export class ValidationError extends CLIError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 2);
  }
}

export class NetworkError extends CLIError {
  constructor(message: string) {
    super(message, 'NETWORK_ERROR', 3);
  }
}

export class ConfigurationError extends CLIError {
  constructor(message: string) {
    super(message, 'CONFIGURATION_ERROR', 4);
  }
}
