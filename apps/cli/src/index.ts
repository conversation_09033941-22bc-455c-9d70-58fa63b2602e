import { Command } from 'commander';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import updateNotifier from 'update-notifier';
import { registerCommands } from './commands/index.js';
import { logger } from './core/logger.js';
import { config } from './core/config.js';
import { CLIError } from './types/index.js';

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Secure JSON parsing with validation
function parsePackageJson(filePath: string): any {
  try {
    const content = readFileSync(filePath, 'utf8');
    const parsed = JSON.parse(content);

    // Validate required fields
    if (typeof parsed.version !== 'string') {
      throw new Error('Invalid package.json: missing or invalid version field');
    }

    return parsed;
  } catch (error) {
    throw new CLIError(
      `Failed to parse package.json: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

const packageJson = parsePackageJson(join(__dirname, '..', 'package.json'));

// Check for updates
updateNotifier({ pkg: packageJson }).notify();

async function main() {
  const program = new Command();

  // Configure main program
  program
    .name('anter')
    .description('Anter CLI - Simple API health and connection testing')
    .version(packageJson.version, '-v, --version', 'Display version number')
    .helpOption('-h, --help', 'Display help for command')
    .configureHelp({
      sortSubcommands: true,
      subcommandTerm: cmd => cmd.name(),
    });

  // Global options
  program
    .option('--debug', 'Enable debug output')
    .option('--config <path>', 'Path to configuration file')
    .hook('preAction', thisCommand => {
      // Set debug mode if requested - using a more secure approach
      if (thisCommand.opts().debug) {
        // Only set if not already set to avoid overriding existing debug config
        if (!process.env.DEBUG) {
          process.env.DEBUG = 'anter*';
        }
      }

      // Handle custom config file
      const configPath = thisCommand.opts().config;
      if (configPath) {
        try {
          config.setConfigPath(configPath);
        } catch (error) {
          logger.error(`Failed to load config file: ${configPath}`);
          if (error instanceof Error) {
            logger.error(error.message);
          }
          process.exit(1);
        }
      }
    });

  // Register commands
  registerCommands(program);

  // Default action when no command is provided
  program.action(() => {
    program.help();
  });

  // Global error handling
  program.exitOverride(err => {
    if (err.code === 'commander.help') {
      process.exit(0);
    } else if (err.code === 'commander.version') {
      process.exit(0);
    } else {
      process.exit(err.exitCode || 1);
    }
  });

  // Parse arguments
  try {
    await program.parseAsync(process.argv);
  } catch (error) {
    handleError(error);
  }
}

function handleError(error: unknown): void {
  if (error instanceof CLIError) {
    logger.error(error.message);
    process.exit(error.exitCode);
  } else if (error instanceof Error) {
    logger.error('An unexpected error occurred', error);
    process.exit(1);
  } else {
    logger.error('An unknown error occurred');
    process.exit(1);
  }
}

// Global error handlers
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise);
  logger.error('Reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', error => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle SIGINT gracefully
process.on('SIGINT', () => {
  logger.info('\nReceived SIGINT. Exiting gracefully...');
  process.exit(0);
});

// Run the CLI
main().catch(handleError);
