import { BaseCommand } from './base.js';
import { httpClient } from '../utils/http.js';
import { config } from '../core/config.js';
import { HealthOptions, HealthResponse } from '../types/index.js';

export class HealthCommand extends BaseCommand {
  static description = 'Check API health and test connection';
  static examples = ['anter health', 'anter health --format json'];

  async execute(options: HealthOptions): Promise<void> {
    this.validateOptions(options);

    const apiUrl = config.getApiUrl('/v1/external/health');

    try {
      const health = await this.withSpinner('Checking API health', async () => {
        const internalSecret = config.getInternalSecret();

        const headers: Record<string, string> = {
          'X-Internal-Secret': internalSecret,
        };

        return await httpClient.get<HealthResponse>(apiUrl, headers);
      });

      if (options.format) {
        this.formatOutput(health, options.format);
      } else {
        this.displayHealthStatus(health);
      }
    } catch (error) {
      // Provide helpful error messages for common issues
      if (error instanceof Error) {
        if (error.message.includes('socket hang up') || error.message.includes('ECONNREFUSED')) {
          this.logger.error('❌ API server is not running');
          this.logger.info('💡 To start the API server:');
          this.logger.info('   cd apps/api && pnpm dev');
          this.logger.info('');
          this.logger.info('🔗 API URL being checked:', apiUrl);
        } else if (error.message.includes('timeout')) {
          this.logger.error('❌ API request timed out');
          this.logger.info('💡 The API server might be starting up or overloaded');
        } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          this.logger.error('❌ Authentication failed');
          this.logger.info('💡 Check your internal secret configuration');
        } else if (error.message.includes('404') || error.message.includes('not found')) {
          this.logger.error('❌ Health endpoint not found');
          this.logger.info(
            '💡 The health endpoint might not be registered or the API server needs to be restarted'
          );
          this.logger.info('🔗 API URL being checked:', apiUrl);
        } else {
          this.logger.error('❌ Failed to check health status:', error.message);
          this.logger.info('🔗 API URL being checked:', apiUrl);
        }
      } else {
        this.logger.error('❌ Failed to check health status', error);
        this.logger.info('🔗 API URL being checked:', apiUrl);
      }
      throw error;
    }
  }

  private displayHealthStatus(health: HealthResponse): void {
    // Overall status
    const statusIcon = health.status === 'healthy' ? '✅' : '❌';
    this.logger.info(`${statusIcon} API Status: ${health.status}`);

    // Uptime
    const uptimeMinutes = Math.round(health.uptime / 60);
    const uptimeHours = Math.round(uptimeMinutes / 60);
    const uptimeDisplay = uptimeHours > 1 ? `${uptimeHours} hours` : `${uptimeMinutes} minutes`;
    this.logger.info(`⏱️  Uptime: ${uptimeDisplay}`);

    // Component summary
    if (health.summary) {
      this.logger.info(`📊 Components: ${health.summary.healthy}/${health.summary.total} healthy`);
    }

    // Individual components
    if (health.components && Object.keys(health.components).length > 0) {
      this.logger.info('\n📋 Component Details:');
      Object.entries(health.components).forEach(([name, component]: [string, any]) => {
        const icon = component.status === 'healthy' ? '✅' : '❌';
        this.logger.info(`  ${icon} ${name}: ${component.status}`);
      });
    }
  }
}
