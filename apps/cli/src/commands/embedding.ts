import { BaseCommand } from './base.js';
import { httpClient } from '../utils/http.js';
import { config } from '../core/config.js';
import {
  EmbeddingListOptions,
  EmbeddingCreateOptions,
  EmbeddingUpdateOptions,
  EmbeddingDeleteOptions,
  EmbeddingStatusOptions,
  EmbeddingStatsOptions,
  EmbeddingListResponse,
  EmbeddingCreateResponse,
  EmbeddingStatusResponse,
  EmbeddingStatsResponse,
  EmbeddingsApiResponse,
  ValidationError,
  DocumentGroup,
  KeyMetadata,
} from '../types/index.js';

export class EmbeddingCommand extends BaseCommand {
  static description = 'Manage document and knowledge base embeddings';
  static examples = [
    'anter embedding list --organization org-123',
    'anter embedding create --organization org-123 --type document --id doc-456',
    'anter embedding status --jobId job-789',
    'anter embedding stats --organization org-123 --period week',
  ];

  async execute(subcommand: string, options: any): Promise<void> {
    this.validateOptions(options);

    switch (subcommand) {
      case 'list':
        await this.list(options);
        break;
      case 'create':
        await this.create(options);
        break;
      case 'update':
        await this.update(options);
        break;
      case 'delete':
        await this.delete(options);
        break;
      case 'status':
        await this.status(options);
        break;
      case 'stats':
        await this.stats(options);
        break;
      default:
        throw new ValidationError(`Unknown subcommand: ${subcommand}`);
    }
  }

  private async list(options: EmbeddingListOptions): Promise<void> {
    if (!options.organization) {
      throw new ValidationError('Organization ID is required for embedding list command');
    }

    const apiUrl = config.getApiUrl('/v1/external/embeddings');

    try {
      const headers = {
        ...this.getAuthHeaders(),
        'X-Organization-Id': options.organization,
      };

      const response = await this.withSpinner('Fetching embeddings data', async () => {
        return await httpClient.get(apiUrl, headers);
      });

      if (options.format) {
        this.formatOutput(response, options.format);
      } else {
        this.displayEmbeddingList(response);
      }
    } catch (error) {
      this.handleEmbeddingError(error, 'list');
    }
  }

  private async create(options: EmbeddingCreateOptions): Promise<void> {
    // Temporary: Use health endpoint as fallback
    const apiUrl = config.getApiUrl('/v1/external/health');

    try {
      this.logger.info(
        '🔧 Note: Using health endpoint as embedding endpoints are not yet implemented'
      );

      const response = await this.withSpinner('Simulating embedding creation', async () => {
        // For now, just return a mock response since we can't POST to health endpoint
        return {
          jobId: `mock-job-${Date.now()}`,
          message: 'Embedding creation job initiated successfully (mock)',
          status: 'pending' as const,
          organizationId: options.organization,
          type: options.type,
          id: options.id,
          force: options.force || false,
        };
      });

      if (options.format) {
        this.formatOutput(response, options.format);
      } else {
        this.displayEmbeddingCreate(response);
      }

      // If wait option is enabled, show mock completion
      if (options.wait) {
        this.logger.info('⏳ Simulating job completion...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.logger.success('✅ Mock job completed successfully');
      }
    } catch (error) {
      this.handleEmbeddingError(error, 'create');
    }
  }

  private async update(options: EmbeddingUpdateOptions): Promise<void> {
    if (!options.organization) {
      throw new ValidationError('Organization ID is required for embedding update command');
    }

    const apiUrl = config.getApiUrl('/v1/external/embeddings/update');

    try {
      const headers = {
        ...this.getAuthHeaders(),
        'X-Organization-Id': options.organization,
      };

      const response = await this.withSpinner('Updating embeddings', async () => {
        return await httpClient.post(apiUrl, {}, headers);
      });

      if (options.format) {
        this.formatOutput(response, options.format);
      } else {
        this.displayEmbeddingUpdate(response);
      }
    } catch (error) {
      this.handleEmbeddingError(error, 'update');
    }
  }

  private async delete(options: EmbeddingDeleteOptions): Promise<void> {
    // Temporary: Use health endpoint as fallback
    const apiUrl = config.getApiUrl('/v1/external/health');

    try {
      this.logger.info(
        '🔧 Note: Using health endpoint as embedding endpoints are not yet implemented'
      );

      await this.withSpinner('Simulating embedding deletion', async () => {
        // Simulate deletion delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      });

      this.logger.success('✅ Embeddings deleted successfully (mock)');
      this.logger.info(`   Organization: ${options.organization}`);
      this.logger.info(`   Type: ${options.type}`);
      this.logger.info(`   ID: ${options.id}`);
    } catch (error) {
      this.handleEmbeddingError(error, 'delete');
    }
  }

  private async status(options: EmbeddingStatusOptions): Promise<void> {
    // Temporary: Use health endpoint as fallback
    const apiUrl = config.getApiUrl('/v1/external/health');

    try {
      this.logger.info(
        '🔧 Note: Using health endpoint as embedding endpoints are not yet implemented'
      );

      const response = await this.withSpinner(
        'Fetching system status (embedding status not available)',
        async () => {
          return await httpClient.get(apiUrl, this.getAuthHeaders());
        }
      );

      if (options.format) {
        this.formatOutput(response, options.format);
      } else {
        this.displayFallbackResponse('Embedding Status', response, options);
      }
    } catch (error) {
      this.handleEmbeddingError(error, 'status');
    }
  }

  private async stats(options: EmbeddingStatsOptions): Promise<void> {
    // Temporary: Use health endpoint as fallback
    const apiUrl = config.getApiUrl('/v1/external/health');

    try {
      this.logger.info(
        '🔧 Note: Using health endpoint as embedding endpoints are not yet implemented'
      );

      const response = await this.withSpinner(
        'Fetching system status (embedding stats not available)',
        async () => {
          return await httpClient.get(apiUrl, this.getAuthHeaders());
        }
      );

      if (options.format) {
        this.formatOutput(response, options.format);
      } else {
        this.displayFallbackResponse('Embedding Statistics', response, options);
      }
    } catch (error) {
      this.handleEmbeddingError(error, 'stats');
    }
  }

  private displayFallbackResponse(operation: string, response: any, options: any): void {
    this.logger.info(`📊 ${operation} (Fallback Mode)`);
    this.logger.info('🔧 Embedding endpoints are not yet implemented');
    this.logger.info('💡 Using health endpoint as temporary fallback');
    this.logger.info('');

    if (options.organization) {
      this.logger.info(`🏢 Organization: ${options.organization}`);
    }

    this.logger.info('📋 System Status:');
    if (response.status) {
      const statusIcon = response.status === 'healthy' ? '✅' : '❌';
      this.logger.info(`   ${statusIcon} Status: ${response.status}`);
    }

    if (response.service) {
      this.logger.info(`   🔧 Service: ${response.service}`);
    }

    if (response.version) {
      this.logger.info(`   📦 Version: ${response.version}`);
    }

    if (response.uptime) {
      const uptimeMinutes = Math.round(response.uptime / 60);
      this.logger.info(`   ⏱️  Uptime: ${uptimeMinutes} minutes`);
    }

    this.logger.info('');
    this.logger.info('🚀 To implement embedding endpoints:');
    this.logger.info('   1. Create API routes in apps/api/src/api/v1/internal/routes/');
    this.logger.info('   2. Implement embedding service logic');
    this.logger.info('   3. Update CLI to use actual endpoints');
  }

  private displayEmbeddingList(response: EmbeddingsApiResponse): void {
    this.logger.info(`📊 Embeddings Data for Organization: ${response.organizationId}`);
    this.logger.info(`📅 Retrieved: ${new Date(response.timestamp).toLocaleString()}`);

    // Display summary
    this.logger.info('\n📋 Summary:');
    this.logger.info(`   📄 Content Hashes: ${response.summary.contentHashes}`);
    this.logger.info(`   📚 Document Cache: ${response.summary.docCache}`);
    this.logger.info(`   🔗 Embeddings: ${response.summary.embeddings}`);
    this.logger.info(`   📊 Total Keys: ${response.summary.totalKeys}`);
    this.logger.info(`   💾 Total Size: ${response.summary.totalSizeFormatted}`);

    // Display document groups
    if (response.data.embeddings && response.data.embeddings.length > 0) {
      this.logger.info('\n📄 Document Embeddings:');
      response.data.embeddings.forEach((docGroup: DocumentGroup, index: number) => {
        this.logger.info(`${index + 1}. 📄 Document: ${docGroup.documentId}`);
        this.logger.info(`   📊 Chunks: ${docGroup.chunkCount}`);
        this.logger.info(`   💾 Size: ${docGroup.sizeFormatted}`);

        // Show chunk details if available
        if (docGroup.chunks && docGroup.chunks.length > 0) {
          this.logger.info(`   📋 Chunks:`);
          docGroup.chunks.forEach((chunk: KeyMetadata, chunkIndex: number) => {
            this.logger.info(`      ${chunkIndex + 1}. ${chunk.key} (${chunk.sizeFormatted})`);
          });
        }
        this.logger.info('');
      });
    } else {
      this.logger.info('\n📄 No document embeddings found');
    }

    // Display content hashes if any
    if (response.data.contentHashes && response.data.contentHashes.length > 0) {
      this.logger.info('\n🔗 Content Hashes:');
      response.data.contentHashes.forEach((hash: KeyMetadata, index: number) => {
        this.logger.info(`${index + 1}. ${hash.key} (${hash.sizeFormatted})`);
      });
      this.logger.info('');
    }

    // Display document cache if any
    if (response.data.docCache && response.data.docCache.length > 0) {
      this.logger.info('\n📚 Document Cache:');
      response.data.docCache.forEach((cache: KeyMetadata, index: number) => {
        this.logger.info(`${index + 1}. ${cache.key} (${cache.sizeFormatted})`);
      });
      this.logger.info('');
    }
  }

  private displayEmbeddingCreate(response: EmbeddingCreateResponse): void {
    const statusIcon = this.getStatusIcon(response.status);
    this.logger.success(`${statusIcon} ${response.message}`);
    this.logger.info(`Job ID: ${response.jobId}`);
    this.logger.info(`Status: ${response.status}`);
  }

  private displayEmbeddingUpdate(response: EmbeddingCreateResponse): void {
    const statusIcon = this.getStatusIcon(response.status);
    this.logger.success(`${statusIcon} Embeddings updated successfully`);
    this.logger.info(`Job ID: ${response.jobId}`);
    this.logger.info(`Status: ${response.status}`);
  }

  private displayEmbeddingStatus(
    response: EmbeddingStatusResponse | EmbeddingStatusResponse[]
  ): void {
    if (Array.isArray(response)) {
      this.logger.info(`📊 Found ${response.length} jobs`);
      response.forEach((job, index) => {
        this.displaySingleJobStatus(job, index + 1);
      });
    } else {
      this.displaySingleJobStatus(response);
    }
  }

  private displaySingleJobStatus(job: EmbeddingStatusResponse, index?: number): void {
    const statusIcon = this.getStatusIcon(job.status);
    const prefix = index ? `${index}. ` : '';

    this.logger.info(`${prefix}${statusIcon} Job: ${job.jobId}`);
    this.logger.info(`   Status: ${job.status}`);
    if (job.progress !== undefined) {
      this.logger.info(`   Progress: ${job.progress}%`);
    }
    if (job.message) {
      this.logger.info(`   Message: ${job.message}`);
    }
    this.logger.info(`   Created: ${new Date(job.createdAt).toLocaleString()}`);
    this.logger.info(`   Updated: ${new Date(job.updatedAt).toLocaleString()}`);
    this.logger.info('');
  }

  private displayEmbeddingStats(response: EmbeddingStatsResponse): void {
    this.logger.info(`📊 Embedding Statistics (${response.period})`);
    this.logger.info(`📈 Total: ${response.total}`);
    this.logger.info(`✅ Completed: ${response.completed}`);
    this.logger.info(`❌ Failed: ${response.failed}`);
    this.logger.info(`⏳ Pending: ${response.pending}`);

    if (response.averageProcessingTime) {
      const avgTime = Math.round(response.averageProcessingTime / 1000);
      this.logger.info(`⏱️  Average Processing Time: ${avgTime}s`);
    }

    if (response.breakdown) {
      this.logger.info('\n📋 Breakdown:');
      Object.entries(response.breakdown).forEach(([key, value]) => {
        this.logger.info(`   ${key}: ${value}`);
      });
    }
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'completed':
        return '✅';
      case 'pending':
        return '⏳';
      case 'failed':
        return '❌';
      default:
        return '❓';
    }
  }

  private handleEmbeddingError(error: unknown, operation: string): void {
    if (error instanceof Error) {
      if (error.message.includes('socket hang up') || error.message.includes('ECONNREFUSED')) {
        this.logger.error('❌ API server is not running');
        this.logger.info('💡 To start the API server:');
        this.logger.info('   cd apps/api && pnpm dev');
      } else if (error.message.includes('timeout')) {
        this.logger.error('❌ API request timed out');
        this.logger.info('💡 The API server might be starting up or overloaded');
      } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        this.logger.error('❌ Authentication failed');
        this.logger.info('💡 Check your internal secret configuration');
      } else if (error.message.includes('404') || error.message.includes('not found')) {
        this.logger.error('❌ Embedding endpoint not found');
        this.logger.info('💡 The embedding endpoints might not be implemented yet');
        this.logger.info('💡 For now, these commands will use health endpoints');
      } else {
        this.logger.error(`❌ Failed to ${operation} embeddings:`, error.message);
      }
    } else {
      this.logger.error(`❌ Failed to ${operation} embeddings`, error);
    }
    throw error;
  }
}
