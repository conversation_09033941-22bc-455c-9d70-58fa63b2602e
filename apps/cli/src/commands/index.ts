import { Command } from 'commander';
import { HealthCommand } from './health.js';
import { TestCommand } from './test.js';
import { EmbeddingCommand } from './embedding.js';

export function registerCommands(program: Command): void {
  // Health command
  program
    .command('health')
    .description(HealthCommand.description)
    .option('--component <name>', 'Check specific component')
    .option('--format <format>', 'Output format (table, json, yaml)', 'table')
    .option('--quiet', 'Suppress non-essential output')
    .action(async options => {
      const command = new HealthCommand();
      await command.execute(options);
    });

  // Test command
  program
    .command('test')
    .description(TestCommand.description)
    .option('--format <format>', 'Output format (table, json, yaml)', 'table')
    .option('--quiet', 'Suppress non-essential output')
    .action(async options => {
      const command = new TestCommand();
      await command.execute(options);
    });

  // Embedding command with subcommands
  const embeddingCommand = program
    .command('embedding')
    .description(EmbeddingCommand.description)
    .option('--format <format>', 'Output format (table, json, yaml)', 'table')
    .option('--quiet', 'Suppress non-essential output');

  // List subcommand
  embeddingCommand
    .command('list')
    .description('List embeddings for an organization')
    .option('--organization <id>', 'Organization ID')
    .option('--type <type>', 'Filter by type (document, knowledge-base, all)', 'all')
    .option('--limit <number>', 'Limit number of results', '50')
    .option('--offset <number>', 'Offset for pagination', '0')
    .option('--status <status>', 'Filter by status (pending, completed, failed, all)', 'all')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('list', { ...command.parent?.opts(), ...options });
    });

  // Create subcommand
  embeddingCommand
    .command('create')
    .description('Create embeddings for documents or knowledge bases')
    .requiredOption('--organization <id>', 'Organization ID')
    .requiredOption('--type <type>', 'Type (document, knowledge-base)')
    .requiredOption('--id <id>', 'Document or knowledge base ID')
    .option('--force', 'Force recreation of existing embeddings')
    .option('--wait', 'Wait for job completion')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('create', { ...command.parent?.opts(), ...options });
    });

  // Update subcommand
  embeddingCommand
    .command('update')
    .description('Update existing embeddings')
    .requiredOption('--organization <id>', 'Organization ID')
    .option('--type <type>', 'Type (document, knowledge-base)')
    .option('--id <id>', 'Document or knowledge base ID')
    .option('--force', 'Force update even if not needed')
    .option('--wait', 'Wait for job completion')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('update', { ...command.parent?.opts(), ...options });
    });

  // Delete subcommand
  embeddingCommand
    .command('delete')
    .description('Delete embeddings')
    .requiredOption('--organization <id>', 'Organization ID')
    .requiredOption('--type <type>', 'Type (document, knowledge-base)')
    .requiredOption('--id <id>', 'Document or knowledge base ID')
    .option('--force', 'Force deletion without confirmation')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('delete', { ...command.parent?.opts(), ...options });
    });

  // Status subcommand
  embeddingCommand
    .command('status')
    .description('Check embedding job status')
    .option('--organization <id>', 'Organization ID')
    .option('--jobId <id>', 'Specific job ID')
    .option('--type <type>', 'Filter by type (document, knowledge-base, all)', 'all')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('status', { ...command.parent?.opts(), ...options });
    });

  // Stats subcommand
  embeddingCommand
    .command('stats')
    .description('Show embedding statistics')
    .option('--organization <id>', 'Organization ID')
    .option('--period <period>', 'Time period (day, week, month, all)', 'all')
    .action(async (options, command) => {
      const embeddingCmd = new EmbeddingCommand();
      await embeddingCmd.execute('stats', { ...command.parent?.opts(), ...options });
    });
}

// Export individual commands for testing
export { HealthCommand, TestCommand, EmbeddingCommand };
