import { BaseCommand } from './base.js';
import { httpClient } from '../utils/http.js';
import { config } from '../core/config.js';
import { TestOptions, HealthResponse } from '../types/index.js';

export class TestCommand extends BaseCommand {
  static description = 'Test connection to external API using internal secret';
  static examples = ['anter test', 'anter test --format json'];

  async execute(options: TestOptions): Promise<void> {
    this.validateOptions(options);

    try {
      const result = await this.withSpinner('Testing API connection', async () => {
        // Test the health endpoint to verify connection
        const headers = {
          'X-Internal-Secret': config.getInternalSecret(),
        };

        return await httpClient.get<HealthResponse>(
          config.getApiUrl('/v1/external/health'),
          headers
        );
      });

      if (options.format) {
        this.formatOutput(result, options.format);
      } else {
        this.displayTestResult(result);
      }
    } catch (error) {
      this.logger.error('Failed to test connection', error);
      throw error;
    }
  }

  private displayTestResult(result: HealthResponse): void {
    if (result.status === 'healthy') {
      this.logger.success('✅ Connection test successful!');
      this.logger.info(`🏥 API Status: ${result.status}`);
      this.logger.info(`⏰ Uptime: ${Math.round(result.uptime / 60)} minutes`);
      this.logger.info(`🔗 Service: ${result.service}`);
      this.logger.info(`📦 Version: ${result.version}`);
    } else {
      this.logger.warn('⚠️  Connection test completed but API is not healthy');
      this.logger.info('Response:', result);
    }
  }
}
