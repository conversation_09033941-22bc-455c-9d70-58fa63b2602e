import { Logger } from '../core/logger.js';
import { config } from '../core/config.js';
import { CommandOptions } from '../types/index.js';

export abstract class BaseCommand {
  protected logger = new Logger();
  protected config = config.get();

  constructor() {
    // Apply global options
    if (this.config.output.quiet) {
      this.logger.setQuiet(true);
    }
  }

  abstract execute(...args: any[]): Promise<void>;

  protected async withSpinner<T>(message: string, task: () => Promise<T>): Promise<T> {
    // Dynamic import to avoid issues with ESM
    const { default: ora } = await import('ora');
    const spinner = ora(message).start();

    try {
      const result = await task();
      spinner.succeed();
      return result;
    } catch (error) {
      spinner.fail();
      throw error;
    }
  }

  protected formatOutput(data: any, format?: string): void {
    const outputFormat = format || this.config.output.format;

    switch (outputFormat) {
      case 'json':
        this.logger.json(data);
        break;
      case 'yaml':
        // Simple YAML-like output for now
        console.log(this.toYamlString(data));
        break;
      case 'table':
      default:
        if (Array.isArray(data)) {
          this.logger.table(data);
        } else {
          this.logger.info(JSON.stringify(data, null, 2));
        }
        break;
    }
  }

  private toYamlString(obj: any, indent = 0): string {
    const spaces = '  '.repeat(indent);

    if (obj === null) return 'null';
    if (typeof obj === 'string') return obj;
    if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);

    if (Array.isArray(obj)) {
      return obj.map(item => `${spaces}- ${this.toYamlString(item, indent + 1)}`).join('\n');
    }

    if (typeof obj === 'object') {
      return Object.entries(obj)
        .map(([key, value]) => `${spaces}${key}: ${this.toYamlString(value, indent + 1)}`)
        .join('\n');
    }

    return String(obj);
  }

  protected validateOptions(options: CommandOptions): void {
    // Common option validation
    if (options.format && !['table', 'json', 'yaml'].includes(options.format)) {
      throw new Error('Invalid format. Must be one of: table, json, yaml');
    }
  }

  protected getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    const internalSecret = this.config.internal.secret;
    if (internalSecret) {
      headers['X-Internal-Secret'] = internalSecret;
    }

    return headers;
  }
}
