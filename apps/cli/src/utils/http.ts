import https from 'https';
import http from 'http';
import { NetworkError } from '../types/index.js';
import { config } from '../core/config.js';

export interface HttpRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

export class HttpClient {
  private timeout: number;

  constructor(timeout?: number) {
    this.timeout = timeout || config.getTimeout();
  }

  async request<T = any>(url: string, options: HttpRequestOptions = {}): Promise<T> {
    return new Promise((resolve, reject) => {
      const isHttps = url.startsWith('https');
      const client = isHttps ? https : http;

      const requestOptions = {
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'Anter-CLI/1.0.0',
          'Content-Type': 'application/json',
          ...options.headers,
        },
        timeout: this.timeout,
        // For HTTPS requests, handle self-signed certificates in development
        ...(isHttps && {
          rejectUnauthorized: false, // Allow self-signed certificates
        }),
      };

      const req = client.request(url, requestOptions, res => {
        let data = '';

        res.on('data', chunk => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
              const result = data ? JSON.parse(data) : null;
              resolve(result);
            } else {
              const errorMessage = data || `HTTP ${res.statusCode}`;
              reject(new NetworkError(`Request failed: ${errorMessage}`));
            }
          } catch (error) {
            reject(
              new NetworkError(
                `Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`
              )
            );
          }
        });
      });

      req.on('error', error => {
        reject(new NetworkError(`Network error: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new NetworkError(`Request timeout after ${this.timeout}ms`));
      });

      if (options.body) {
        const bodyData =
          typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
        req.write(bodyData);
      }

      req.end();
    });
  }

  async get<T = any>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'GET', ...(headers && { headers }) });
  }

  async post<T = any>(url: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'POST', body, ...(headers && { headers }) });
  }

  async put<T = any>(url: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'PUT', body, ...(headers && { headers }) });
  }

  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(url, { method: 'DELETE', ...(headers && { headers }) });
  }
}

// Singleton instance with default configuration
export const httpClient = new HttpClient();
