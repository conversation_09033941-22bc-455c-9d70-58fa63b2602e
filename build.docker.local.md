# Local Docker Build and Run

## Database

```bash
docker network create mynetwork
docker run -d \
	--name postgres \
    -p 5432:5432 \
	-e POSTGRES_PASSWORD=password \
	-e PGDATA=/var/lib/postgresql/data \
	-v ~/.docker-volumes/postgresql-data:/var/lib/postgresql/data \
    --network mynetwork \
	postgres
```

## Redis

```bash
docker run -d \
	--name redis \
	-p 6379:6379 \
	-v ~/.docker-volumes/redis-data:/data \
	--network mynetwork \
	redis
```

## Agent API

```bash
# Build the API
docker build -t anter-ai -f apps/api/Dockerfile .

# Run the API making sure it's in the same docker network as the database and provide the .env.production.docker.local
docker run -it --rm --name anter-ai -p 8000:8000 --env-file apps/api/.env.production.docker.local --network mynetwork anter-ai
node apps/api/dist/api/src/server.js
```
