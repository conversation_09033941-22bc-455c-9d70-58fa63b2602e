[revive]
ignoreGeneratedHeader = true
severity = "warning"
confidence = 0.8
errorCode = 0
warningCode = 0

rules = ["blank-imports", "context-as-argument", "context-keys-type", "dot-imports", "empty-block", "errorf", "error-naming", "error-return", "error-strings", "exported", "increment-decrement", "indent-error-flow", "package-comments", "range", "receiver-naming", "redefines-builtin-id", "superfluous-else", "time-naming", "unexported-return", "unreachable-code", "unused-parameter", "var-declaration", "var-naming"]

[rule.blank-imports]

[rule.context-as-argument]

[rule.context-keys-type]

[rule.dot-imports]

[rule.empty-block]

[rule.errorf]

[rule.error-naming]

[rule.error-return]

[rule.error-strings]

[rule.exported]

[rule.increment-decrement]

[rule.indent-error-flow]

[rule.package-comments]

[rule.range]

[rule.receiver-naming]

[rule.redefines-builtin-id]

[rule.superfluous-else]
arguments = [""]

[rule.time-naming]

[rule.unexported-return]

[rule.unreachable-code]

[rule.unused-parameter]

[rule.var-declaration]

[rule.var-naming]

