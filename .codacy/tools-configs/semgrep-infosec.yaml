rules:
  # Express.js Security Vulnerabilities
  - id: express.security.rate-limit-missing
    pattern: |
      const express = require('express')
      const app = express()
      ...
      app.listen($PORT)
    message: 'Missing rate limiting middleware - add express-rate-limit'
    severity: WARNING
    languages: [javascript, typescript]

  - id: express.security.cors-misconfiguration
    pattern: |
      app.use(cors({
        origin: true
      }))
    message: 'CORS configuration allows all origins - restrict to specific domains'
    severity: WARNING
    languages: [javascript, typescript]

  - id: express.security.helmet-missing
    pattern: |
      const express = require('express')
      const app = express()
      ...
      app.listen($PORT)
    message: 'Missing Helmet.js security headers - add helmet middleware'
    severity: WARNING
    languages: [javascript, typescript]

  # Authentication & Session Security
  - id: auth.session-insecure-cookie
    pattern: |
      app.use(session({
        secret: $SECRET,
        cookie: {
          secure: false
        }
      }))
    message: 'Session cookie not marked as secure - set secure: true in production'
    severity: WARNING
    languages: [javascript, typescript]

  - id: auth.session-no-httponly
    pattern: |
      app.use(session({
        secret: $SECRET,
        cookie: {
          httpOnly: false
        }
      }))
    message: 'Session cookie not marked as httpOnly - set httpOnly: true'
    severity: WARNING
    languages: [javascript, typescript]

  - id: auth.hardcoded-secret
    pattern: |
      app.use(session({
        secret: 'hardcoded-secret-value'
      }))
    message: 'Hardcoded session secret - use environment variable'
    severity: ERROR
    languages: [javascript, typescript]

  # SQL Injection Prevention
  - id: sql.injection-concatenation
    pattern: |
      $QUERY = "SELECT * FROM users WHERE id = " + $USER_INPUT
    message: 'SQL injection vulnerability - use parameterized queries'
    severity: ERROR
    languages: [javascript, typescript]

  - id: sql.injection-template
    pattern: |
      $QUERY = `SELECT * FROM users WHERE id = ${$USER_INPUT}`
    message: 'SQL injection vulnerability - use parameterized queries'
    severity: ERROR
    languages: [javascript, typescript]

  # XSS Prevention
  - id: xss.innerhtml-usage
    pattern: |
      $ELEMENT.innerHTML = $USER_INPUT
    message: 'XSS vulnerability - use textContent or sanitize input'
    severity: ERROR
    languages: [javascript, typescript]

  - id: xss.eval-usage
    pattern: |
      eval($USER_INPUT)
    message: 'XSS vulnerability - avoid eval with user input'
    severity: ERROR
    languages: [javascript, typescript]

  # Command Injection Prevention
  - id: command.injection-exec
    pattern: |
      exec($USER_INPUT)
    message: 'Command injection vulnerability - validate and sanitize input'
    severity: ERROR
    languages: [javascript, typescript]

  - id: command.injection-spawn
    pattern: |
      spawn($COMMAND, [$USER_INPUT])
    message: 'Command injection vulnerability - validate and sanitize input'
    severity: ERROR
    languages: [javascript, typescript]

  # Path Traversal Prevention
  - id: path.traversal-readfile
    pattern: |
      fs.readFile($USER_INPUT)
    message: 'Path traversal vulnerability - validate file paths'
    severity: ERROR
    languages: [javascript, typescript]

  # JWT Security
  - id: jwt.hardcoded-secret
    pattern: |
      jwt.sign($PAYLOAD, 'hardcoded-secret')
    message: 'Hardcoded JWT secret - use environment variable'
    severity: ERROR
    languages: [javascript, typescript]

  # Environment Variable Security
  - id: env.exposure-log
    pattern: |
      console.log(process.env.$SENSITIVE_VAR)
    message: 'Sensitive environment variable exposure in logs'
    severity: WARNING
    languages: [javascript, typescript]

  - id: env.exposure-response
    pattern: |
      res.send(process.env.$SENSITIVE_VAR)
    message: 'Sensitive environment variable exposure in response'
    severity: ERROR
    languages: [javascript, typescript]

  # API Security
  - id: api.key-exposure
    pattern: |
      res.json({ apiKey: $API_KEY })
    message: 'API key exposure in response - remove sensitive data'
    severity: ERROR
    languages: [javascript, typescript]

  # Crypto Security
  - id: crypto.weak-hash-md5
    pattern: |
      crypto.createHash('md5')
    message: 'Weak hash algorithm (MD5) - use SHA-256 or better'
    severity: ERROR
    languages: [javascript, typescript]

  - id: crypto.weak-cipher-ecb
    pattern: |
      crypto.createCipher('aes-128-ecb', $KEY)
    message: 'Weak cipher mode (ECB) - use CBC or GCM'
    severity: ERROR
    languages: [javascript, typescript]

  # TypeScript Security
  - id: typescript.any-type-usage
    pattern: |
      function $FUNC($PARAM: any) {
        ...
      }
    message: "Using 'any' type reduces type safety - use specific types"
    severity: WARNING
    languages: [typescript]

  - id: typescript.unsafe-assertion
    pattern: |
      const $VAR = $EXPR as $TYPE
    message: 'Unsafe type assertion - validate data structure'
    severity: WARNING
    languages: [typescript]

  # React Security
  - id: react.xss-dangerously-set-innerhtml
    pattern: |
      <div dangerouslySetInnerHTML={{ __html: $USER_INPUT }} />
    message: 'XSS vulnerability - avoid dangerouslySetInnerHTML with user input'
    severity: ERROR
    languages: [javascript, typescript, jsx, tsx]

  - id: react.unsafe-target-blank
    pattern: |
      <a target="_blank" href={$URL}>$TEXT</a>
    message: "Missing rel='noopener noreferrer' for target='_blank'"
    severity: WARNING
    languages: [javascript, typescript, jsx, tsx]

  # LangChain Security
  - id: langchain.hardcoded-api-key
    pattern: |
      const llm = new OpenAI({
        apiKey: 'sk-...'
      })
    message: 'Hardcoded OpenAI API key - use environment variable'
    severity: ERROR
    languages: [javascript, typescript]

  - id: langchain.prompt-injection
    pattern: |
      const prompt = `Answer this: ${$USER_INPUT}`
    message: 'Potential prompt injection - validate and sanitize user input'
    severity: WARNING
    languages: [javascript, typescript]

  # MCP Tools Security
  - id: mcp.token-exposure
    pattern: |
      console.log('MCP Token:', $MCP_TOKEN)
    message: 'MCP token exposure in logs - use secure logging'
    severity: ERROR
    languages: [javascript, typescript]

  # Teams Bot Security
  - id: teams.hardcoded-token
    pattern: |
      const token = 'hardcoded-teams-token'
    message: 'Hardcoded Teams bot token - use environment variable'
    severity: ERROR
    languages: [javascript, typescript]

  # General Security Best Practices
  - id: security.console-log-production
    pattern: |
      console.log($SENSITIVE_DATA)
    message: 'Sensitive data logging in production - use proper logging levels'
    severity: WARNING
    languages: [javascript, typescript]

  - id: security.no-input-validation
    pattern: |
      function $FUNC($USER_INPUT) {
        // No validation
        return processInput($USER_INPUT)
      }
    message: 'Missing input validation - validate all user inputs'
    severity: WARNING
    languages: [javascript, typescript]

  - id: security.weak-random
    pattern: |
      Math.random()
    message: 'Weak random number generation - use crypto.randomBytes()'
    severity: WARNING
    languages: [javascript, typescript]
