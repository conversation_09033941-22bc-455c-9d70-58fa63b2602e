export default [
  {
    files: ['**/*.{js,ts,jsx,tsx}'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
    },
    rules: {
      // Monorepo Import Rules
      'no-restricted-imports': [
        'error',
        {
          patterns: [
            {
              group: ['../../../*'],
              message:
                'Deep import path detected. Consider using barrel exports or shared modules.',
            },
            {
              group: ['/apps/*/src/*'],
              message: 'Cross-app import detected. Use shared packages for common functionality.',
            },
            {
              group: ['/packages/*/src/*'],
              message: "Direct package import. Use the package's public API.",
            },
          ],
        },
      ],

      // Monorepo Path Rules
      'import/no-relative-parent-imports': 'error',
      'import/no-relative-packages': 'error',

      // Monorepo Naming Conventions
      'naming-convention': [
        'error',
        {
          selector: 'variable',
          format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
          leadingUnderscore: 'forbid',
          trailingUnderscore: 'forbid',
        },
        {
          selector: 'function',
          format: ['camelCase', 'PascalCase'],
          leadingUnderscore: 'forbid',
          trailingUnderscore: 'forbid',
        },
        {
          selector: 'class',
          format: ['PascalCase'],
        },
        {
          selector: 'interface',
          format: ['PascalCase'],
          prefix: ['I'],
        },
        {
          selector: 'typeAlias',
          format: ['PascalCase'],
        },
      ],

      // Monorepo File Organization
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            'internal',
            'parent',
            'sibling',
            'index',
            'object',
            'type',
          ],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
        },
      ],

      // Monorepo Dependency Rules
      'import/no-duplicates': 'error',
      'import/no-cycle': 'error',

      // Monorepo Configuration Rules
      'no-process-env': 'warn',
      'no-console': [
        'warn',
        {
          allow: ['warn', 'error'],
        },
      ],

      // Monorepo TypeScript Rules
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/explicit-function-return-type': 'warn',
      '@typescript-eslint/explicit-module-boundary-types': 'warn',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],

      // Monorepo Security Rules
      'security/detect-object-injection': 'error',
      'security/detect-non-literal-regexp': 'error',
      'security/detect-unsafe-regex': 'error',

      // Monorepo Performance Rules
      'no-var': 'error',
      'prefer-const': 'error',
      'no-loop-func': 'error',
      'no-new-func': 'error',

      // Monorepo Code Quality
      complexity: ['warn', 10],
      'max-depth': ['warn', 4],
      'max-lines': ['warn', 300],
      'max-params': ['warn', 5],

      // Monorepo Documentation
      'jsdoc/require-jsdoc': [
        'warn',
        {
          publicOnly: true,
          require: {
            FunctionDeclaration: true,
            MethodDefinition: true,
            ClassDeclaration: true,
          },
        },
      ],

      // Monorepo Testing Rules
      'jest/no-disabled-tests': 'warn',
      'jest/no-focused-tests': 'error',
      'jest/no-identical-title': 'error',
      'jest/prefer-to-have-length': 'warn',
      'jest/valid-expect': 'error',

      // Monorepo React Rules (if applicable)
      'react/prop-types': 'off', // Using TypeScript
      'react/react-in-jsx-scope': 'off', // Not needed in modern React
      'react/jsx-uses-react': 'off',
      'react/jsx-uses-vars': 'error',

      // Monorepo Accessibility
      'jsx-a11y/alt-text': 'error',
      'jsx-a11y/anchor-has-content': 'error',
      'jsx-a11y/anchor-is-valid': 'error',
      'jsx-a11y/aria-props': 'error',
      'jsx-a11y/aria-proptypes': 'error',
      'jsx-a11y/aria-unsupported-elements': 'error',
      'jsx-a11y/role-has-required-aria-props': 'error',
      'jsx-a11y/role-supports-aria-props': 'error',

      // Monorepo Best Practices
      'prefer-template': 'error',
      'template-curly-spacing': 'error',
      'object-shorthand': 'error',
      'prefer-destructuring': [
        'error',
        {
          array: true,
          object: true,
        },
        {
          enforceForRenamedProperties: false,
        },
      ],

      // Monorepo Error Handling
      'no-throw-literal': 'error',
      'prefer-promise-reject-errors': 'error',

      // Monorepo Async/Await
      'require-await': 'error',
      'no-return-await': 'error',

      // Monorepo Immutability
      'no-param-reassign': 'error',
      'prefer-spread': 'error',

      // Monorepo Code Style
      indent: ['error', 2],
      quotes: ['error', 'single'],
      semi: ['error', 'always'],
      'comma-dangle': ['error', 'always-multiline'],
      'no-trailing-spaces': 'error',
      'eol-last': 'error',
    },
  },
  {
    files: ['**/*.test.{js,ts,jsx,tsx}', '**/*.spec.{js,ts,jsx,tsx}'],
    rules: {
      // Test-specific rules
      'jest/expect-expect': 'error',
      'jest/no-test-return-statement': 'error',
      'jest/prefer-called-with': 'warn',
      'jest/prefer-strict-equal': 'warn',
      'jest/require-top-level-describe': 'error',
    },
  },
  {
    files: ['**/packages/**/*.{js,ts,jsx,tsx}'],
    rules: {
      // Package-specific rules
      'import/no-relative-parent-imports': 'error',
      'import/no-relative-packages': 'error',
    },
  },
  {
    files: ['**/apps/**/*.{js,ts,jsx,tsx}'],
    rules: {
      // App-specific rules
      'import/no-relative-parent-imports': 'warn',
    },
  },
];
