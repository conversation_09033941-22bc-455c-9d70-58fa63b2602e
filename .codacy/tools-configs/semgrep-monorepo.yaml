rules:
  # Monorepo Architecture & Structure
  - id: monorepo.circular-dependency
    pattern: |
      // Circular dependency detection
      import { $MODULE } from '../$SAME_MODULE'
    message: 'Monorepo: Potential circular dependency - review import structure'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.deep-import-path
    pattern: |
      import { $MODULE } from '../../../$DEEP_PATH'
    message: 'Monorepo: Deep import path - consider using barrel exports or shared modules'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.duplicate-package-json
    pattern: |
      // Multiple package.json files with same dependencies
      "dependencies": {
        "$PACKAGE": "$VERSION"
      }
    message: 'Monorepo: Duplicate dependencies across packages - consider workspace-level management'
    severity: WARNING
    languages: [json]

  # Package Management & Dependencies
  - id: monorepo.inconsistent-versions
    pattern: |
      "$PACKAGE": "$VERSION1"
      ...
      "$PACKAGE": "$VERSION2"
    message: 'Monorepo: Inconsistent package versions across workspace - align versions'
    severity: ERROR
    languages: [json]

  - id: monorepo.missing-workspace-config
    pattern: |
      // Missing workspace configuration
      {
        "name": "$PACKAGE"
      }
    message: 'Monorepo: Missing workspace configuration - add to root package.json'
    severity: WARNING
    languages: [json]

  - id: monorepo.private-package-exposure
    pattern: |
      export * from '@private-package/$MODULE'
    message: 'Monorepo: Exposing private package internals - use public API only'
    severity: WARNING
    languages: [javascript, typescript]

  # Code Organization & Imports
  - id: monorepo.wrong-import-source
    pattern: |
      import { $MODULE } from '$APP/$PATH'
    message: 'Monorepo: Cross-app import - use shared packages for common functionality'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.missing-barrel-export
    pattern: |
      // Direct file imports instead of barrel exports
      import { $MODULE } from './$FILE'
    message: 'Monorepo: Missing barrel export - use index.ts for cleaner imports'
    severity: INFO
    languages: [javascript, typescript]

  - id: monorepo.absolute-path-import
    pattern: |
      import { $MODULE } from '/$ABSOLUTE_PATH'
    message: 'Monorepo: Absolute path import - use relative or workspace imports'
    severity: WARNING
    languages: [javascript, typescript]

  # Configuration & Environment
  - id: monorepo.duplicate-env-config
    pattern: |
      // Duplicate environment configuration
      $ENV_VAR = $VALUE
    message: 'Monorepo: Duplicate environment configuration - centralize in root'
    severity: WARNING
    languages: [javascript, typescript, shell]

  - id: monorepo.missing-root-config
    pattern: |
      // Missing root-level configuration
      module.exports = {
        // No monorepo-specific config
      }
    message: 'Monorepo: Missing root configuration - add workspace settings'
    severity: WARNING
    languages: [javascript, typescript, json]

  # Testing & Quality
  - id: monorepo.missing-cross-package-tests
    pattern: |
      // No integration tests for cross-package functionality
      describe('$PACKAGE', () => {
        // Only unit tests
      })
    message: 'Monorepo: Missing cross-package integration tests'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.duplicate-test-setup
    pattern: |
      // Duplicate test setup across packages
      beforeEach(() => {
        // Same setup code
      })
    message: 'Monorepo: Duplicate test setup - create shared test utilities'
    severity: WARNING
    languages: [javascript, typescript]

  # Build & Deployment
  - id: monorepo.missing-build-order
    pattern: |
      // Missing dependency order in build scripts
      "build": "tsc"
    message: 'Monorepo: Missing build order - ensure proper dependency resolution'
    severity: WARNING
    languages: [json, javascript, typescript]

  - id: monorepo.duplicate-build-config
    pattern: |
      // Duplicate build configuration
      "scripts": {
        "build": "$BUILD_COMMAND"
      }
    message: 'Monorepo: Duplicate build configuration - use shared build tools'
    severity: WARNING
    languages: [json, javascript, typescript]

  # Documentation & Standards
  - id: monorepo.missing-package-readme
    pattern: |
      // Missing README for package
      // No documentation
    message: 'Monorepo: Missing package README - document package purpose and usage'
    severity: WARNING
    languages: [markdown]

  # Security & Access Control
  - id: monorepo.cross-package-secret-access
    pattern: |
      // Accessing secrets across packages
      process.env.$SECRET
    message: 'Monorepo: Cross-package secret access - use secure configuration management'
    severity: ERROR
    languages: [javascript, typescript]

  - id: monorepo.duplicate-auth-config
    pattern: |
      // Duplicate authentication configuration
      const auth = {
        // Same auth config
      }
    message: 'Monorepo: Duplicate auth configuration - centralize authentication'
    severity: WARNING
    languages: [javascript, typescript]

  # Performance & Optimization
  - id: monorepo.duplicate-utility-functions
    pattern: |
      // Duplicate utility functions across packages
      function $UTILITY($PARAMS) {
        // Same implementation
      }
    message: 'Monorepo: Duplicate utility functions - move to shared packages'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.missing-shared-types
    pattern: |
      // Missing shared type definitions
      interface $INTERFACE {
        // Duplicate type definitions
      }
    message: 'Monorepo: Missing shared types - create common type definitions'
    severity: INFO
    languages: [typescript]

  # API & Integration
  # - id: monorepo.duplicate-api-definitions
  #   pattern: |
  #     // Duplicate API definitions
  #     interface $API_INTERFACE {
  #       // Same API structure
  #     }
  #   message: 'Monorepo: Duplicate API definitions - use shared API contracts'
  #   severity: INFO
  #   languages: [typescript, javascript]

  - id: monorepo.missing-api-versioning
    pattern: |
      // Missing API versioning
      app.use('/api', $ROUTER)
    message: 'Monorepo: Missing API versioning - implement versioned APIs'
    severity: WARNING
    languages: [javascript, typescript]

  # Database & Data Management
  - id: monorepo.duplicate-database-config
    pattern: |
      // Duplicate database configuration
      const dbConfig = {
        // Same database settings
      }
    message: 'Monorepo: Duplicate database configuration - centralize database settings'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.cross-package-data-access
    pattern: |
      // Direct data access across packages
      const data = await $DATABASE.$OPERATION()
    message: 'Monorepo: Cross-package data access - use service layer abstraction'
    severity: WARNING
    languages: [javascript, typescript]

  # Monitoring & Observability
  - id: monorepo.duplicate-logging-config
    pattern: |
      // Duplicate logging configuration
      const logger = $LOGGER_CONFIG
    message: 'Monorepo: Duplicate logging configuration - use shared logging setup'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.missing-monitoring
    pattern: |
      // Missing monitoring setup
      // No health checks or metrics
    message: 'Monorepo: Missing monitoring - implement cross-package observability'
    severity: WARNING
    languages: [javascript, typescript]

  # Error Handling & Resilience
  - id: monorepo.duplicate-error-handling
    pattern: |
      // Duplicate error handling patterns
      try {
        // Same error handling
      } catch ($ERROR) {
        // Same error response
      }
    message: 'Monorepo: Duplicate error handling - create shared error utilities'
    severity: WARNING
    languages: [javascript, typescript]

  - id: monorepo.missing-circuit-breaker
    pattern: |
      // Missing circuit breaker for cross-package calls
      const result = await $EXTERNAL_SERVICE()
    message: 'Monorepo: Missing circuit breaker - implement resilience patterns'
    severity: WARNING
    languages: [javascript, typescript]
