rules:
  # OWASP Top 10 2021 - A01:2021 Broken Access Control
  - id: owasp-top10.a01.broken-access-control
    pattern: |
      // Missing authentication check
      app.get('/admin', (req, res) => {
        res.send(adminData)
      })
    message: 'OWASP A01:2021 - Missing authentication/authorization check'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a01.insecure-direct-object-reference
    pattern: |
      app.get('/user/:id', (req, res) => {
        const user = getUserById(req.params.id)
        res.json(user)
      })
    message: 'OWASP A01:2021 - Insecure Direct Object Reference (IDOR) - validate user permissions'
    severity: ERROR
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A02:2021 Cryptographic Failures
  - id: owasp-top10.a02.weak-hash-algorithm
    pattern: |
      crypto.createHash('md5')
    message: 'OWASP A02:2021 - Weak hash algorithm (MD5) - use SHA-256 or better'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a02.weak-cipher-mode
    pattern: |
      crypto.createCipher('aes-128-ecb', $KEY)
    message: 'OWASP A02:2021 - Weak cipher mode (ECB) - use CBC or GCM'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a02.hardcoded-secret
    pattern: |
      const secret = 'hardcoded-secret-value'
    message: 'OWASP A02:2021 - Hardcoded cryptographic secret - use environment variables'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a02.weak-random
    pattern: |
      Math.random()
    message: 'OWASP A02:2021 - Weak random number generation - use crypto.randomBytes()'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A03:2021 Injection
  - id: owasp-top10.a03.sql-injection
    pattern: |
      $QUERY = "SELECT * FROM users WHERE id = " + $USER_INPUT
    message: 'OWASP A03:2021 - SQL injection vulnerability - use parameterized queries'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a03.sql-injection-template
    pattern: |
      $QUERY = `SELECT * FROM users WHERE id = ${$USER_INPUT}`
    message: 'OWASP A03:2021 - SQL injection vulnerability - use parameterized queries'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a03.command-injection
    pattern: |
      exec($USER_INPUT)
    message: 'OWASP A03:2021 - Command injection vulnerability - validate and sanitize input'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a03.xss-innerhtml
    pattern: |
      $ELEMENT.innerHTML = $USER_INPUT
    message: 'OWASP A03:2021 - XSS vulnerability - use textContent or sanitize input'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a03.xss-eval
    pattern: |
      eval($USER_INPUT)
    message: 'OWASP A03:2021 - XSS vulnerability - avoid eval with user input'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a03.prompt-injection
    pattern: |
      const prompt = `Answer this: ${$USER_INPUT}`
    message: 'OWASP A03:2021 - Prompt injection vulnerability - validate and sanitize user input'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A04:2021 Insecure Design
  - id: owasp-top10.a04.missing-input-validation
    pattern: |
      function $FUNC($USER_INPUT) {
        // No validation
        return processInput($USER_INPUT)
      }
    message: 'OWASP A04:2021 - Missing input validation - validate all user inputs'
    severity: WARNING
    languages: [javascript, typescript]

  - id: owasp-top10.a04.missing-rate-limiting
    pattern: |
      const express = require('express')
      const app = express()
      ...
      app.listen($PORT)
    message: 'OWASP A04:2021 - Missing rate limiting - add express-rate-limit middleware'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A05:2021 Security Misconfiguration
  - id: owasp-top10.a05.cors-misconfiguration
    pattern: |
      app.use(cors({
        origin: true
      }))
    message: 'OWASP A05:2021 - CORS misconfiguration - restrict to specific domains'
    severity: WARNING
    languages: [javascript, typescript]

  - id: owasp-top10.a05.missing-security-headers
    pattern: |
      const express = require('express')
      const app = express()
      ...
      app.listen($PORT)
    message: 'OWASP A05:2021 - Missing security headers - add Helmet.js middleware'
    severity: WARNING
    languages: [javascript, typescript]

  - id: owasp-top10.a05.insecure-session-config
    pattern: |
      app.use(session({
        secret: $SECRET,
        cookie: {
          secure: false
        }
      }))
    message: 'OWASP A05:2021 - Insecure session configuration - set secure: true in production'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A06:2021 Vulnerable and Outdated Components
  - id: owasp-top10.a06.known-vulnerable-package
    pattern: |
      require('vulnerable-package')
    message: 'OWASP A06:2021 - Known vulnerable package - update or replace'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A07:2021 Identification and Authentication Failures
  - id: owasp-top10.a07.hardcoded-credentials
    pattern: |
      const password = 'hardcoded-password'
    message: 'OWASP A07:2021 - Hardcoded credentials - use environment variables'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a07.weak-jwt-secret
    pattern: |
      jwt.sign($PAYLOAD, 'weak-secret')
    message: 'OWASP A07:2021 - Weak JWT secret - use strong, environment-based secret'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a07.missing-mfa
    pattern: |
      // Authentication without MFA
      if (username && password) {
        authenticateUser(username, password)
      }
    message: 'OWASP A07:2021 - Missing Multi-Factor Authentication (MFA)'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A08:2021 Software and Data Integrity Failures
  - id: owasp-top10.a08.untrusted-data-deserialization
    pattern: |
      JSON.parse($UNTRUSTED_DATA)
    message: 'OWASP A08:2021 - Untrusted data deserialization - validate input before parsing'
    severity: WARNING
    languages: [javascript, typescript]

  - id: owasp-top10.a08.insecure-dependency
    pattern: |
      const data = await fetch($UNTRUSTED_URL).then(r => r.json())
    message: 'OWASP A08:2021 - Insecure dependency - validate external data sources'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A09:2021 Security Logging and Monitoring Failures
  - id: owasp-top10.a09.sensitive-data-logging
    pattern: |
      console.log('Password:', $PASSWORD)
    message: 'OWASP A09:2021 - Sensitive data logging - avoid logging credentials'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a09.missing-audit-log
    pattern: |
      // No logging of security events
      authenticateUser(username, password)
    message: 'OWASP A09:2021 - Missing security event logging - log authentication attempts'
    severity: WARNING
    languages: [javascript, typescript]

  # OWASP Top 10 2021 - A10:2021 Server-Side Request Forgery (SSRF)
  - id: owasp-top10.a10.ssrf-fetch
    pattern: |
      fetch($USER_INPUT)
    message: 'OWASP A10:2021 - SSRF vulnerability - validate URLs before fetching'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp-top10.a10.ssrf-axios
    pattern: |
      axios.get($USER_INPUT)
    message: 'OWASP A10:2021 - SSRF vulnerability - validate URLs before making requests'
    severity: ERROR
    languages: [javascript, typescript]

  # Additional OWASP-related security patterns
  - id: owasp.environment-variable-exposure
    pattern: |
      res.send(process.env.$SENSITIVE_VAR)
    message: 'OWASP - Environment variable exposure in response'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp.api-key-exposure
    pattern: |
      res.json({ apiKey: $API_KEY })
    message: 'OWASP - API key exposure in response'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp.path-traversal
    pattern: |
      fs.readFile($USER_INPUT)
    message: 'OWASP - Path traversal vulnerability - validate file paths'
    severity: ERROR
    languages: [javascript, typescript]

  - id: owasp.react-xss
    pattern: |
      <div dangerouslySetInnerHTML={{ __html: $USER_INPUT }} />
    message: 'OWASP - XSS vulnerability in React - avoid dangerouslySetInnerHTML'
    severity: ERROR
    languages: [javascript, typescript, jsx, tsx]

  - id: owasp.react-unsafe-target
    pattern: |
      <a target="_blank" href={$URL}>$TEXT</a>
    message: "OWASP - Missing rel='noopener noreferrer' for target='_blank'"
    severity: WARNING
    languages: [javascript, typescript, jsx, tsx]
