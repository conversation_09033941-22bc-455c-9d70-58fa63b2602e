# Monorepo Best Practices for AskInfoSec API

## Overview

This document outlines the monorepo best practices that are enforced through Codacy's static analysis tools. These practices ensure code quality, maintainability, and consistency across the multi-app structure.

## 🏗️ Architecture & Structure

### Package Organization

```
askinfosec-api/
├── apps/           # Application packages
│   ├── api/        # Main API application
│   ├── agents/     # AI agents application
│   ├── cli/        # Command-line interface
│   └── teams/      # Microsoft Teams bot
├── packages/       # Shared packages
│   ├── domain-model/    # Shared domain models
│   ├── mcp-tools/       # MCP tools package
│   └── shared-services/ # Common services
└── scripts/        # Build and utility scripts
```

### Import Structure Rules

#### ✅ **Recommended**

```typescript
// Use barrel exports
import { UserService } from '@shared-services/user';
import { ApiResponse } from '@domain-model/types';

// Use relative imports within same package
import { config } from '../config';
import { utils } from './utils';
```

#### ❌ **Avoid**

```typescript
// Deep relative imports
import { utils } from '../../../utils';

// Cross-app imports
import { apiConfig } from '/apps/api/src/config';

// Direct package internals
import { internal } from '@shared-services/src/internal';
```

## 📦 Package Management

### Dependency Management

- **Centralized Dependencies**: Use workspace-level dependency management
- **Version Alignment**: Ensure consistent versions across packages
- **Private Packages**: Don't expose internal package APIs

### Workspace Configuration

```json
{
  "name": "askinfosec-api",
  "private": true,
  "workspaces": ["apps/*", "packages/*"],
  "scripts": {
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint"
  }
}
```

## 🔧 Configuration & Environment

### Environment Variables

- **Centralized Config**: Use root-level environment configuration
- **Package-Specific**: Override only when necessary
- **Security**: Never commit sensitive values

### Build Configuration

```json
{
  "scripts": {
    "build": "turbo run build --filter=./packages/*",
    "build:apps": "turbo run build --filter=./apps/*"
  }
}
```

## 🧪 Testing Strategy

### Test Organization

- **Unit Tests**: Within each package
- **Integration Tests**: Cross-package functionality
- **E2E Tests**: Full application workflows

### Shared Test Utilities

```typescript
// packages/shared-services/src/testing/
export const createTestUser = () => ({
  /* ... */
});
export const mockApiResponse = () => ({
  /* ... */
});
```

## 🔒 Security & Access Control

### Cross-Package Security

- **Service Layer**: Use abstraction for cross-package data access
- **Authentication**: Centralize auth configuration
- **Secrets**: Use secure configuration management

### API Security

```typescript
// ✅ Use service abstraction
const userService = new UserService();
const user = await userService.getUser(id);

// ❌ Direct database access across packages
const user = await database.users.findById(id);
```

## 📊 Monitoring & Observability

### Logging Strategy

- **Centralized Logging**: Use shared logging configuration
- **Structured Logs**: Consistent log format across packages
- **Security Events**: Log authentication and authorization events

### Health Checks

```typescript
// Implement health checks for each package
export const healthCheck = async () => {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    dependencies: await checkDependencies(),
  };
};
```

## 🚀 Performance & Optimization

### Code Sharing

- **Utility Functions**: Move to shared packages
- **Type Definitions**: Use common type definitions
- **Constants**: Share configuration constants

### Build Optimization

- **Incremental Builds**: Use Turbo for efficient builds
- **Caching**: Leverage build caching
- **Parallel Execution**: Run tasks in parallel

## 📝 Documentation Standards

### Package Documentation

Each package should include:

- **README.md**: Purpose, usage, and examples
- **API Documentation**: Public API reference
- **Changelog**: Version history and changes

### Code Documentation

```typescript
/**
 * User service for managing user operations
 * @package @shared-services/user
 */
export class UserService {
  /**
   * Retrieves a user by ID
   * @param id - User identifier
   * @returns Promise<User | null>
   */
  async getUser(id: string): Promise<User | null> {
    // Implementation
  }
}
```

## 🔄 Error Handling & Resilience

### Error Patterns

- **Consistent Error Types**: Use shared error definitions
- **Circuit Breakers**: Implement for external service calls
- **Graceful Degradation**: Handle service failures

### Error Handling Example

```typescript
// packages/shared-services/src/errors/
export class MonorepoError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly package: string
  ) {
    super(message);
    this.name = 'MonorepoError';
  }
}
```

## 🎯 Code Quality Standards

### Naming Conventions

- **Variables**: camelCase
- **Functions**: camelCase
- **Classes**: PascalCase
- **Interfaces**: PascalCase with 'I' prefix
- **Constants**: UPPER_SNAKE_CASE

### Import Organization

```typescript
// 1. Built-in modules
import { readFile } from 'fs';
import { join } from 'path';

// 2. External dependencies
import express from 'express';
import { z } from 'zod';

// 3. Internal packages
import { UserService } from '@shared-services/user';
import { ApiResponse } from '@domain-model/types';

// 4. Relative imports
import { config } from '../config';
import { utils } from './utils';
```

## 🔍 Codacy Enforcement

### Semgrep Rules

- **Circular Dependencies**: Detect import cycles
- **Deep Imports**: Flag deep relative paths
- **Cross-Package Access**: Enforce service layer usage
- **Duplicate Code**: Identify code duplication

### ESLint Rules

- **Import Order**: Enforce consistent import organization
- **Naming Conventions**: Ensure consistent naming
- **Code Complexity**: Limit function complexity
- **Security Patterns**: Enforce security best practices

## 🚀 Getting Started

### Setup Commands

```bash
# Install dependencies
pnpm install

# Run linting
pnpm lint

# Run tests
pnpm test

# Build all packages
pnpm build
```

### Development Workflow

1. **Create Feature Branch**: `git checkout -b feature/new-feature`
2. **Make Changes**: Follow monorepo best practices
3. **Run Checks**: `pnpm lint && pnpm test`
4. **Create PR**: Ensure all checks pass
5. **Merge**: After review and approval

## 📋 Checklist

### Before Committing

- [ ] All tests pass
- [ ] Linting passes
- [ ] No circular dependencies
- [ ] Proper import organization
- [ ] Documentation updated
- [ ] Security considerations addressed

### Before Releasing

- [ ] All packages built successfully
- [ ] Integration tests pass
- [ ] Performance benchmarks met
- [ ] Security scan clean
- [ ] Documentation complete

## 🔧 Troubleshooting

### Common Issues

#### Circular Dependencies

```bash
# Check for circular dependencies
pnpm run check-circular

# Fix by using dependency injection or restructuring
```

#### Import Issues

```bash
# Check import paths
pnpm run check-imports

# Use barrel exports to simplify imports
```

#### Build Failures

```bash
# Clean and rebuild
pnpm run clean
pnpm install
pnpm build
```

## 📚 Additional Resources

- [Turbo Documentation](https://turbo.build/repo/docs)
- [Monorepo Best Practices](https://monorepo.tools/)
- [TypeScript Project References](https://www.typescriptlang.org/docs/handbook/project-references.html)
- [ESLint Import Rules](https://github.com/import-js/eslint-plugin-import)
