exclude_paths:
    - '**/node_modules/**'
    - '**/dist/**'
    - '**/build/**'
    - '**/.next/**'
    - '**/coverage/**'
    - '**/test-results/**'
    - '**/logs/**'
    - '**/*.min.js'
    - '**/*.bundle.js'
    - '**/package-lock.json'
    - '**/yarn.lock'
    - '**/pnpm-lock.yaml'
    - '**/.git/**'
    - '**/certs/**'
    - '**/docker-compose*.yml'
    - '**/Dockerfile*'
    - '**/.env*'
    - '**/env.example'
include_paths:
    - apps/**/*.{ts,tsx,js,jsx}
    - packages/**/*.{ts,tsx,js,jsx}
    - '**/*.json'
    - '**/*.yaml'
    - '**/*.yml'
    - '**/*.md'
    - '**/*.dockerfile'
    - '**/*.sh'
runtimes:
    - node@22.2.0
    - python@3.11.11
tools:
    - eslint@8.57.0
    - lizard@1.17.19
    - semgrep@1.78.0
    - trivy@0.59.1
