# OWASP Top 10 2021 Configuration Reference

## Overview

This document explains how OWASP Top 10 2021 security controls are implemented in the Codacy configuration for the AskInfoSec API project.

## Configuration Location

**Primary Configuration**: `.codacy/tools-configs/semgrep.yaml`

- Contains comprehensive OWASP Top 10 2021 rules
- Tailored specifically for TypeScript/JavaScript applications
- Includes both general and project-specific security patterns

## OWASP Top 10 2021 Categories

### A01:2021 – Broken Access Control

**Configuration**: `owasp-top10.a01.*` rules in Semgrep

- **Patterns Detected**:
  - Missing authentication checks
  - Insecure Direct Object References (IDOR)
  - Unauthorized access to resources
- **Examples**:

  ```javascript
  // ❌ Missing auth check
  app.get('/admin', (req, res) => {
    res.send(adminData);
  });

  // ❌ IDOR vulnerability
  app.get('/user/:id', (req, res) => {
    const user = getUserById(req.params.id);
    res.json(user);
  });
  ```

### A02:2021 – Cryptographic Failures

**Configuration**: `owasp-top10.a02.*` rules in Semgrep

- **Patterns Detected**:
  - Weak hash algorithms (MD5)
  - Weak cipher modes (ECB)
  - Hardcoded secrets
  - Weak random number generation
- **Examples**:

  ```javascript
  // ❌ Weak hash
  crypto.createHash('md5');

  // ❌ Weak cipher
  crypto.createCipher('aes-128-ecb', key);

  // ❌ Hardcoded secret
  const secret = 'hardcoded-secret-value';

  // ❌ Weak random
  Math.random();
  ```

### A03:2021 – Injection

**Configuration**: `owasp-top10.a03.*` rules in Semgrep

- **Patterns Detected**:
  - SQL injection
  - XSS (Cross-Site Scripting)
  - Command injection
  - Prompt injection (for AI/LLM applications)
- **Examples**:

  ```javascript
  // ❌ SQL injection
  const query = 'SELECT * FROM users WHERE id = ' + userInput;

  // ❌ XSS
  element.innerHTML = userInput;

  // ❌ Command injection
  exec(userInput);

  // ❌ Prompt injection
  const prompt = `Answer this: ${userInput}`;
  ```

### A04:2021 – Insecure Design

**Configuration**: `owasp-top10.a04.*` rules in Semgrep

- **Patterns Detected**:
  - Missing input validation
  - Missing rate limiting
  - Insecure architectural patterns
- **Examples**:

  ```javascript
  // ❌ Missing input validation
  function processInput(userInput) {
    return processData(userInput); // No validation
  }

  // ❌ Missing rate limiting
  app.get('/api/data', (req, res) => {
    // No rate limiting middleware
  });
  ```

### A05:2021 – Security Misconfiguration

**Configuration**: `owasp-top10.a05.*` rules in Semgrep

- **Patterns Detected**:
  - CORS misconfiguration
  - Missing security headers
  - Insecure session configuration
- **Examples**:

  ```javascript
  // ❌ CORS misconfiguration
  app.use(
    cors({
      origin: true, // Allows all origins
    })
  );

  // ❌ Missing security headers
  const app = express();
  app.listen(3000); // No Helmet.js

  // ❌ Insecure session
  app.use(
    session({
      cookie: { secure: false },
    })
  );
  ```

### A06:2021 – Vulnerable and Outdated Components

**Configuration**: `owasp-top10.a06.*` rules in Semgrep

- **Patterns Detected**:
  - Known vulnerable packages
  - Outdated dependencies
- **Examples**:
  ```javascript
  // ❌ Known vulnerable package
  require('vulnerable-package');
  ```

### A07:2021 – Identification and Authentication Failures

**Configuration**: `owasp-top10.a07.*` rules in Semgrep

- **Patterns Detected**:
  - Hardcoded credentials
  - Weak JWT secrets
  - Missing MFA
- **Examples**:

  ```javascript
  // ❌ Hardcoded credentials
  const password = 'hardcoded-password';

  // ❌ Weak JWT secret
  jwt.sign(payload, 'weak-secret');

  // ❌ Missing MFA
  if (username && password) {
    authenticateUser(username, password);
  }
  ```

### A08:2021 – Software and Data Integrity Failures

**Configuration**: `owasp-top10.a08.*` rules in Semgrep

- **Patterns Detected**:
  - Untrusted data deserialization
  - Insecure dependencies
- **Examples**:

  ```javascript
  // ❌ Untrusted deserialization
  JSON.parse(untrustedData);

  // ❌ Insecure dependency
  const data = await fetch(untrustedUrl).then(r => r.json());
  ```

### A09:2021 – Security Logging and Monitoring Failures

**Configuration**: `owasp-top10.a09.*` rules in Semgrep

- **Patterns Detected**:
  - Sensitive data logging
  - Missing security event logging
- **Examples**:

  ```javascript
  // ❌ Sensitive data logging
  console.log('Password:', password);

  // ❌ Missing audit log
  authenticateUser(username, password); // No logging
  ```

### A10:2021 – Server-Side Request Forgery (SSRF)

**Configuration**: `owasp-top10.a10.*` rules in Semgrep

- **Patterns Detected**:
  - Unvalidated URL fetching
  - SSRF vulnerabilities
- **Examples**:

  ```javascript
  // ❌ SSRF vulnerability
  fetch(userInput);

  // ❌ SSRF with axios
  axios.get(userInput);
  ```

## Additional Security Patterns

Beyond OWASP Top 10, the configuration also includes:

- **Environment variable exposure**
- **API key exposure**
- **Path traversal vulnerabilities**
- **React-specific XSS patterns**
- **LangChain security patterns**
- **Teams bot security patterns**

## Usage

### Running OWASP Top 10 Scans

```bash
# Run Semgrep with OWASP Top 10 rules
.codacy/cli.sh analyze --tool semgrep

# Run full analysis including OWASP Top 10
.codacy/cli.sh analyze
```

### Interpreting Results

- **ERROR**: Critical security vulnerabilities that must be fixed
- **WARNING**: Security issues that should be addressed
- **INFO**: Best practice recommendations

### Customizing Rules

To modify OWASP Top 10 rules:

1. Edit `.codacy/tools-configs/semgrep.yaml`
2. Adjust severity levels as needed
3. Add project-specific patterns
4. Test with `codacy_cli_analyze`

## Compliance

This configuration helps achieve compliance with:

- **OWASP Top 10 2021**
- **NIST Cybersecurity Framework**
- **ISO 27001 standards**
- **Industry best practices for infosec applications**

## Maintenance

### Regular Updates

- Review OWASP Top 10 rules quarterly
- Update patterns based on new vulnerabilities
- Adjust severity levels based on project needs
- Monitor for false positives

### Rule Customization

- Add project-specific security patterns
- Adjust thresholds for your environment
- Create custom rules for unique requirements
- Document any rule modifications
