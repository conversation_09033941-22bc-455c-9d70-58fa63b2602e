# Codacy Repository Commit Guide

## 🎯 **What to Commit to Repository**

### ✅ **Essential Files (MUST COMMIT)**

These files are required for <PERSON><PERSON><PERSON> to work properly:

```
.codacy/
├── codacy.yaml              # Main Codacy configuration
├── cli.sh                   # CLI execution script
├── cli-config.yaml          # CLI configuration
├── README.md                # Setup documentation
├── OWASP_TOP10_REFERENCE.md # OWASP Top 10 documentation
├── MONOREPO_BEST_PRACTICES.md # Monorepo best practices
└── tools-configs/
    ├── semgrep.yaml         # Main Semgrep rules (consolidated)
    ├── eslint.config.mjs    # Main ESLint config (consolidated)
    ├── trivy.yaml           # Trivy configuration
    ├── lizard.yaml          # Lizard configuration
    └── languages-config.yaml # Language definitions
```

### ❌ **Do NOT Commit (Ignored Files)**

These files are generated, temporary, or source files:

```
.codacy/
├── logs/                    # Analysis logs (generated)
├── .trivycache/            # Trivy cache (generated)
├── .gitignore              # This file itself
└── tools-configs/
    ├── semgrep-infosec.yaml    # Source file (merged into semgrep.yaml)
    ├── semgrep-owasp-top10.yaml # Source file (merged into semgrep.yaml)
    ├── semgrep-monorepo.yaml   # Source file (merged into semgrep.yaml)
    ├── eslint-monorepo.config.mjs # Source file (merged into eslint.config.mjs)
    ├── analysis_options.yaml   # Dart-specific (unused in Node.js project)
    ├── ruleset.xml            # PMD ruleset (unused)
    ├── revive.toml            # Go ruleset (unused)
    └── pylint.rc             # Python ruleset (unused)
```

## 🔧 **Git Commands for Proper Setup**

### Add Essential Files:

```bash
git add .codacy/codacy.yaml
git add .codacy/cli.sh
git add .codacy/cli-config.yaml
git add .codacy/README.md
git add .codacy/OWASP_TOP10_REFERENCE.md
git add .codacy/MONOREPO_BEST_PRACTICES.md
git add .codacy/tools-configs/semgrep.yaml
git add .codacy/tools-configs/eslint.config.mjs
git add .codacy/tools-configs/trivy.yaml
git add .codacy/tools-configs/lizard.yaml
git add .codacy/tools-configs/languages-config.yaml
```

### Ignore Generated Files:

```bash
# The .codacy/.gitignore handles this automatically
# But you can also add to root .gitignore:
echo ".codacy/logs/" >> .gitignore
echo ".codacy/.trivycache/" >> .gitignore
echo ".codacy/codacy-cli-v2*" >> .gitignore
```

## 🚀 **CI/CD Integration**

The GitHub Actions workflow (`.github/workflows/codacy-analysis.yml`) will:

1. Download the Codacy CLI
2. Run analysis using the committed configuration files
3. Generate logs in `.codacy/logs/` (not committed)
4. Cache Trivy results in `.trivycache/` (not committed)

## 📊 **File Size Considerations**

- **semgrep.yaml**: ~25KB (consolidated rules)
- **eslint.config.mjs**: ~8KB (consolidated rules)
- **Total committed size**: ~50KB (very reasonable)

## 🔍 **Verification**

To verify your setup is correct:

```bash
# Run local analysis
.codacy/cli.sh analyze

# Check that logs are generated but not committed
ls -la .codacy/logs/
git status .codacy/logs/  # Should show as ignored
```

## 🎯 **Best Practices**

1. **Keep source files separate**: Use source files for development, commit consolidated files
2. **Document changes**: Update README.md when adding new rules
3. **Test locally**: Always test configuration changes before committing
4. **Monitor file sizes**: Keep committed files under reasonable limits
5. **Version control**: Use meaningful commit messages for configuration changes
