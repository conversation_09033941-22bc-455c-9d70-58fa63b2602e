# Codacy Configuration for AskInfoSec API

## Overview

This directory contains the Codacy configuration optimized for the AskInfoSec API project, a TypeScript/JavaScript monorepo focused on information security. The configuration is specifically tailored for Node.js applications with security-focused scanning tools.

## Configuration Files

### `codacy.yaml`

Main configuration file that defines:

- **Runtimes**: Node.js (v22.2.0)
- **Tools**: Security-focused tools (Semgrep, Trivy) and code quality tools (<PERSON>SLint, Lizard)
- **Exclude Paths**: Build artifacts, dependencies, sensitive files
- **Include Paths**: Source code, configuration files, documentation

### `cli-config.yaml`

CLI configuration for local analysis mode.

### `tools-configs/`

Directory containing tool-specific configurations:

#### `eslint.config.mjs`

Comprehensive ESLint configuration with:

- **Security Rules**: Prevents XSS, injection attacks, unsafe eval
- **TypeScript Rules**: Type safety, unsafe assertions, any type usage
- **React Rules**: XSS prevention, secure link handling
- **Best Practices**: Code quality, maintainability

#### `semgrep.yaml`

Comprehensive Semgrep rules including:

- **OWASP Top 10 2021**: All 10 categories with specific patterns
- **Express.js Security**: Rate limiting, CORS, Helmet.js
- **Authentication**: Session security, JWT handling
- **Injection Prevention**: SQL, XSS, Command injection
- **API Security**: Key exposure, environment variables
- **LangChain Security**: API keys, prompt injection
- **Teams Bot Security**: Token handling, permissions

#### `trivy.yaml`

Container and dependency vulnerability scanning:

- **Severity Focus**: High and Critical vulnerabilities only
- **Scan Types**: Vulnerabilities, secrets, configuration
- **Performance**: Optimized exclusions and timeouts

## Security Focus Areas

### 1. OWASP Top 10 2021 Compliance

- **A01:2021 Broken Access Control**: Authentication/authorization checks, IDOR prevention
- **A02:2021 Cryptographic Failures**: Strong algorithms, secure secrets, proper random generation
- **A03:2021 Injection**: SQL injection, XSS, command injection, prompt injection prevention
- **A04:2021 Insecure Design**: Input validation, rate limiting, secure architecture
- **A05:2021 Security Misconfiguration**: CORS, security headers, session configuration
- **A06:2021 Vulnerable Components**: Dependency scanning, package updates
- **A07:2021 Authentication Failures**: MFA, credential management, JWT security
- **A08:2021 Data Integrity**: Secure deserialization, trusted dependencies
- **A09:2021 Logging Failures**: Security event logging, sensitive data protection
- **A10:2021 SSRF**: URL validation, secure external requests

### 2. Authentication & Authorization

- Session management security
- JWT token handling
- API key protection
- Environment variable security

### 2. Input Validation & Sanitization

- SQL injection prevention
- XSS protection
- Command injection prevention
- Path traversal protection

### 3. Monorepo Best Practices

- **Architecture**: Circular dependency detection, deep import prevention
- **Package Management**: Version consistency, workspace configuration
- **Code Organization**: Import structure, barrel exports, naming conventions
- **Security**: Cross-package access control, centralized configuration
- **Performance**: Code sharing, build optimization, duplicate detection
- **Testing**: Cross-package integration tests, shared utilities
- **Documentation**: Package READMEs, API documentation standards

### 4. API Security

- Rate limiting
- CORS configuration
- Security headers (Helmet.js)
- Input validation

### 5. Code Quality

- TypeScript type safety
- Best practices enforcement
- Code maintainability
- Performance considerations

## Usage

### Local Analysis

```bash
# Run full analysis
.codacy/cli.sh analyze

# Run specific tool
.codacy/cli.sh analyze --tool eslint
.codacy/cli.sh analyze --tool semgrep
.codacy/cli.sh analyze --tool trivy
```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: Codacy Analysis
  run: |
    .codacy/cli.sh analyze
    .codacy/cli.sh analyze --tool trivy
```

## Best Practices

### 1. Regular Scanning

- Run Codacy analysis before each commit
- Integrate into CI/CD pipeline
- Review security findings immediately

### 2. Security Rules

- Never commit hardcoded secrets
- Always validate user input
- Use parameterized queries
- Implement proper authentication

### 3. Code Quality

- Use TypeScript strict mode
- Avoid `any` types
- Implement proper error handling
- Follow consistent coding standards

### 4. Dependencies

- Regular dependency updates
- Vulnerability scanning
- License compliance
- Minimal dependency footprint

## Tool-Specific Guidelines

### ESLint

- Focus on security rules for infosec projects
- TypeScript-specific rules for type safety
- React security rules for frontend components

### Semgrep

- Custom rules for project-specific vulnerabilities
- Focus on authentication and API security
- LangChain and MCP-specific security patterns

### Trivy

- Container vulnerability scanning
- Dependency vulnerability detection
- Secret scanning in configuration files

## Monitoring & Reporting

### Metrics to Track

- Security vulnerability count
- Code quality score
- Technical debt
- Test coverage

### Alerts

- Critical security vulnerabilities
- High-severity issues
- Failed builds due to security issues

## Troubleshooting

### Common Issues

1. **Large file exclusions**: Update `codacy.yaml` exclude paths
2. **Tool timeouts**: Adjust timeout settings in tool configs
3. **False positives**: Customize rule severity levels
4. **Performance issues**: Optimize include/exclude paths

### Performance Optimization

- Exclude build artifacts and dependencies
- Use focused tool configurations
- Implement caching strategies
- Parallel tool execution

## Security Compliance

This configuration aligns with:

- OWASP Top 10
- NIST Cybersecurity Framework
- ISO 27001 standards
- Industry best practices for infosec applications

## Maintenance

### Regular Updates

- Update tool versions quarterly
- Review and update security rules monthly
- Monitor for new vulnerability patterns
- Update documentation as needed

### Rule Customization

- Add project-specific security rules
- Adjust severity levels based on context
- Create custom Semgrep patterns
- Optimize for team workflow

## Support

For issues or questions:

1. Check tool documentation
2. Review Codacy community forums
3. Consult security team
4. Update configuration as needed
