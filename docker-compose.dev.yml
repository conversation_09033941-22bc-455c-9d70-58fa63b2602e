version: '3.8'

services:
  redis:
    image: redis:7.2-alpine
    container_name: askinfosec-redis
    ports:
      - '6379:6379'
    restart: unless-stopped

  api:
    container_name: askinfosec-api
    build:
      context: . # monorepo root!
      dockerfile: apps/api/Dockerfile
    ports:
      - '8000:8000'
    env_file:
      - apps/api/.env
    environment:
      - NODE_ENV=development
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      - DATABASE_URL_ROOT_USER=${DATABASE_URL_ROOT_USER}
      - DATABASE_URL_RLS_USER=${DATABASE_URL_RLS_USER}
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_CHAT_MODEL=${OPENAI_CHAT_MODEL:-gpt-4o-mini}
      - OPENAI_CHAT_INSTRUCTIONS=${OPENAI_CHAT_INSTRUCTIONS:-You are a helpful AI assistant specialized in information security and cybersecurity...}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.7}
      - OPENAI_MAX_TOKENS=${OPENAI_MAX_TOKENS:-1000}
      # Authentication Configuration
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - TOKEN_EXPIRY=${TOKEN_EXPIRY:-15m}
      - REFRESH_TOKEN_SECRET=${REFRESH_TOKEN_SECRET}
      - REFRESH_TOKEN_EXPIRY=${REFRESH_TOKEN_EXPIRY:-7d}
      - INTERNAL_API_SECRET=${INTERNAL_API_SECRET}
      # API Configuration
      - BASE_URL=${BASE_URL:-http://localhost:8000}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - PUBLIC_WS_BASE=${PUBLIC_WS_BASE}
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - SKIP_REDIS=${SKIP_REDIS:-true}
      # MCP Configuration
      - MCP_ENABLED=${MCP_ENABLED:-true}
      - MCP_SESSION_TTL=${MCP_SESSION_TTL:-1800}
      - MCP_CONTEXT_TTL=${MCP_CONTEXT_TTL:-3600}
      - MCP_MAX_CONTEXT_SIZE=${MCP_MAX_CONTEXT_SIZE:-10485760}
      - MCP_MAX_SESSIONS_PER_ORG=${MCP_MAX_SESSIONS_PER_ORG:-5}
      - MCP_CLEANUP_INTERVAL=${MCP_CLEANUP_INTERVAL:-300000}
      - MCP_MAX_RETRY_ATTEMPTS=${MCP_MAX_RETRY_ATTEMPTS:-3}
      - MCP_HEALTH_CHECK_INTERVAL=${MCP_HEALTH_CHECK_INTERVAL:-120000}
      # LangChain Configuration
      - LANGCHAIN_ENABLED=${LANGCHAIN_ENABLED:-true}
      - LANGCHAIN_MODEL=${LANGCHAIN_MODEL:-gpt-4o-mini}
      - LANGCHAIN_EMBEDDINGS=${LANGCHAIN_EMBEDDINGS:-text-embedding-3-small}
      - LANGCHAIN_TEMPERATURE=${LANGCHAIN_TEMPERATURE:-0.7}
      - LANGCHAIN_MAX_TOKENS=${LANGCHAIN_MAX_TOKENS:-1000}
      - LANGCHAIN_MAX_RETRIES=${LANGCHAIN_MAX_RETRIES:-3}
      - LANGCHAIN_REQUEST_TIMEOUT=${LANGCHAIN_REQUEST_TIMEOUT:-30000}
      - LANGCHAIN_RATE_LIMIT_RPM=${LANGCHAIN_RATE_LIMIT_RPM:-60}
      - LANGCHAIN_TRACING=${LANGCHAIN_TRACING:-false}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY}
    healthcheck:
      test:
        [
          'CMD',
          'curl',
          '-f',
          '-X',
          'POST',
          '-H',
          'Content-Type: application/json',
          '-H',
          'x-internal-secret: ${INTERNAL_API_SECRET}',
          '-d',
          '{"message":"healthcheck"}',
          'http://localhost:8000/api/v1/external/echo',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
