{"name": "askinfosec-workspace", "version": "0.0.1", "description": "AskInfoSec mono-repo workspace", "private": true, "scripts": {"build": "pnpm install && pnpm -r build", "dev": "pnpm -r dev", "test": "pnpm -r test", "clean": "pnpm -r clean", "clean:all": "pnpm -r clean:all", "format": "prettier --write .", "format:check": "prettier --check .", "generate-certs": "node scripts/generate-certs.mjs", "api:dev": "pnpm --filter @anter/api dev", "api:build": "pnpm --filter @anter/api build", "api:start": "pnpm --filter @anter/api start", "api:test": "pnpm --filter @anter/api test", "agents:build": "pnpm --filter @anter/agents build", "agents:test": "pnpm --filter @anter/agents test", "mcp-tools:build": "pnpm --filter @anter/mcp-tools build", "mcp-tools:test": "pnpm --filter @anter/mcp-tools test", "domain-model:build": "pnpm --filter @anter/domain-model build", "domain-model:test": "pnpm --filter @anter/domain-model test", "shared-services:build": "pnpm --filter @anter/shared-services build", "shared-services:test": "pnpm --filter @anter/shared-services test", "cli:build": "pnpm --filter @anter/cli build", "cli:dev": "pnpm --filter @anter/cli dev", "cli:test": "pnpm --filter @anter/cli test", "cli:link": "pnpm --filter @anter/cli link:global"}, "keywords": ["infosec", "api", "monorepo"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.0.1", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "dependencies": {"@anter/agents": "workspace:^", "build": "^0.1.4", "ioredis": "^5.7.0", "zod": "^3.23.8"}, "pnpm": {"overrides": {"@langchain/core": "^0.3.61", "@langchain/openai": "^0.4.9", "@langchain/langgraph": "^0.3.6", "@langchain/langgraph-supervisor": "^0.0.14", "@langchain/community": "^0.3.47"}}}