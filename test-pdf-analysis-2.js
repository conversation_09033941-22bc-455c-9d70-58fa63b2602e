const { FileTextExtractor } = require('./packages/mcp-tools/dist/services/file-text-extractor');
const { EmbeddingService } = require('./packages/mcp-tools/dist/services/embedding-service');
const fs = require('fs');
const path = require('path');

async function analyzePDF() {
  console.log('🔍 Analyzing PDF content and semantic similarity...\n');

  try {
    // Read the PDF file
    const pdfPath = path.join(
      __dirname,
      'apps/agents/dist/BCID-Apollo-1500-Deductible-PPO-SBC.pdf'
    );
    const pdfBuffer = fs.readFileSync(pdfPath);

    console.log(`📄 PDF file size: ${pdfBuffer.length} bytes`);

    // Extract text content
    console.log('📝 Extracting text from PDF...');
    const extractionResult = await FileTextExtractor.extractText(pdfBuffer, 'pdf');

    console.log(`📊 Extraction Results:`);
    console.log(`  - Characters: ${extractionResult.text.length}`);
    console.log(`  - Pages: ${extractionResult.metadata?.pages || 'unknown'}`);
    console.log(`  - Method: ${extractionResult.metadata?.extractionMethod}`);
    console.log(`  - Warnings: ${extractionResult.metadata?.warnings?.length || 0}\n`);

    // Show first 500 characters of content
    console.log('📄 Content Preview (first 500 chars):');
    console.log('='.repeat(50));
    console.log(extractionResult.text.substring(0, 500));
    console.log('='.repeat(50));
    console.log();

    // Analyze key terms in the document
    const content = extractionResult.text.toLowerCase();
    const keyTerms = [
      'summary of benefits',
      'coverage',
      'benefit',
      'plan',
      'deductible',
      'apollo',
      'bcid',
      'ppo',
      'medical',
      'health',
      'insurance',
    ];

    console.log('🔍 Key Terms Analysis:');
    keyTerms.forEach(term => {
      const count = (content.match(new RegExp(term, 'g')) || []).length;
      console.log(`  - "${term}": ${count} occurrences`);
    });
    console.log();

    // Test semantic similarity
    console.log('🧮 Testing Semantic Similarity...');

    // Initialize embedding service
    const embeddingService = new EmbeddingService();

    // Your query
    const query = 'what is our summary of benefit coverage?';
    console.log(`Query: "${query}"`);

    // Generate embeddings
    console.log('🔄 Generating embeddings...');
    const queryEmbedding = await embeddingService.embedText(query);
    const documentEmbedding = await embeddingService.embedText(extractionResult.text);

    // Calculate cosine similarity
    function cosineSimilarity(vecA, vecB) {
      let dotProduct = 0;
      let normA = 0;
      let normB = 0;

      for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
      }

      return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    const similarity = cosineSimilarity(queryEmbedding, documentEmbedding);
    console.log(`📊 Cosine Similarity Score: ${similarity.toFixed(4)}`);

    // Analyze different chunk sizes
    console.log('\n🔬 Chunk Size Analysis:');
    const chunkSizes = [200, 400, 800];

    for (const chunkSize of chunkSizes) {
      const words = extractionResult.text.split(/\s+/);
      const chunk = words.slice(0, chunkSize).join(' ');

      if (chunk.length > 0) {
        const chunkEmbedding = await embeddingService.embedText(chunk);
        const chunkSimilarity = cosineSimilarity(queryEmbedding, chunkEmbedding);
        console.log(`  - First ${chunkSize} words: ${chunkSimilarity.toFixed(4)}`);
      }
    }

    // Test with query expansions
    console.log('\n🔍 Query Expansion Analysis:');
    const expandedQueries = [
      'what is our summary of benefit coverage?',
      'summary of benefits and coverage information',
      'what are our health plan benefits?',
      'BCID Apollo PPO coverage details',
      'deductible and benefit information',
    ];

    for (const expandedQuery of expandedQueries) {
      const expandedEmbedding = await embeddingService.embedText(expandedQuery);
      const expandedSimilarity = cosineSimilarity(expandedEmbedding, documentEmbedding);
      console.log(`  - "${expandedQuery}": ${expandedSimilarity.toFixed(4)}`);
    }

    console.log('\n✅ Analysis complete!');
  } catch (error) {
    console.error('❌ Error during analysis:', error);
  }
}

// Run the analysis
analyzePDF();
