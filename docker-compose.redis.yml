version: '3.8'

services:
  redis:
    image: redis:7.2-alpine
    container_name: askinfosec-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  redis-insight:
    image: redis/redisinsight:latest
    container_name: askinfosec-redis-insight
    ports:
      - '8001:5540'
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
