# =============================================================================
# ANTER AI - ROOT ENVIRONMENT VARIABLES
# =============================================================================
# This file contains all environment variables used across the entire project
# Copy this file to .env and update the values with your actual configuration

# =============================================================================
# API CONFIGURATION
# =============================================================================
BASE_URL="http://localhost:8000"
PORT=8000
HOST=localhost
USE_HTTPS=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL_ROOT_USER="your_database_root_user_connection_string"
DATABASE_URL_RLS_USER="your_database_rls_user_connection_string"
DATABASE_URL_INTERNAL="your_database_internal_connection_string"
INTERNAL_DB_HAS_RLS=false

# =============================================================================
# AUTHENTICATION
# =============================================================================
JWT_SECRET="your_jwt_secret_key"
REFRESH_TOKEN_SECRET="your_refresh_token_secret_key"
TOKEN_EXPIRY="15m"
REFRESH_TOKEN_EXPIRY="7d"
INTERNAL_API_SECRET="your_internal_api_secret"

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGINS="*"

# =============================================================================
# MCP (Model Context Protocol) CONFIGURATION
# =============================================================================
MCP_ENABLED=true
MCP_SESSION_TTL=1800
MCP_CONTEXT_TTL=3600
MCP_MAX_CONTEXT_SIZE=10485760
MCP_MAX_SESSIONS_PER_ORG=5
MCP_CLEANUP_INTERVAL=300000
MCP_MAX_RETRY_ATTEMPTS=3
MCP_HEALTH_CHECK_INTERVAL=120000

# =============================================================================
# LANGCHAIN CONFIGURATION
# =============================================================================
LANGCHAIN_ENABLED=true
LANGCHAIN_MODEL=gpt-4o-mini
LANGCHAIN_EMBEDDINGS=text-embedding-3-large
LANGCHAIN_TEMPERATURE=0.7
LANGCHAIN_MAX_TOKENS=1000
LANGCHAIN_MAX_RETRIES=3
LANGCHAIN_REQUEST_TIMEOUT=30000
LANGCHAIN_RATE_LIMIT_RPM=60
LANGCHAIN_TRACING=false
FORCE_HARDCODED_PROMPTS=false

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY="your_openai_api_key"
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_CHAT_INSTRUCTIONS="You are a helpful AI assistant specialized in information security and cybersecurity..."
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=1000

# =============================================================================
# INFRASTRUCTURE
# =============================================================================
REDIS_URL=redis://localhost:6379
SKIP_REDIS=false
PUBLIC_WS_BASE=ws://localhost:8000

# =============================================================================
# LANGGRAPH CONFIGURATION
# =============================================================================
ENABLE_LANGGRAPH=false

# =============================================================================
# LANGFUSE CONFIGURATION
# =============================================================================
LANGFUSE_PUBLIC_KEY="your_langfuse_public_key"
LANGFUSE_SECRET_KEY="your_langfuse_secret_key"
LANGFUSE_BASEURL="https://us.cloud.langfuse.com"
LANGFUSE_SAMPLE_RATE="1"
LANGFUSE_TRACING_ENABLED="true"
LANGFUSE_DEFAULT_PROMPT="system-default"

# =============================================================================
# TEAMS BOT CONFIGURATION
# =============================================================================
BOT_ID="your_bot_app_id_from_azure_bot_framework"
BOT_PASSWORD="your_bot_app_password_from_azure_bot_framework"
BOT_ENDPOINT="https://your-domain.com/api/messages"
TEAMS_APP_ID="your_teams_app_id_usually_same_as_bot_id"
TEAMS_APP_BASE_URL="https://your-ngrok-url.ngrok.io"
TEAMS_APP_ENV="development"

# =============================================================================
# ANTER API INTEGRATION (for Teams)
# =============================================================================
ANTER_API_URL="http://localhost:8000"
ANTER_EMAIL="<EMAIL>"
ANTER_ORG_ID="your_organization_id"

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
LOG_LEVEL="debug"
NODE_ENV="development"
DEBUG="anter*"

# Azure Application Insights (Optional)
APPINSIGHTS_INSTRUMENTATIONKEY="your_instrumentation_key"

# =============================================================================
# SSL/TLS CERTIFICATES (Optional for local development)
# =============================================================================
CA_CERT_PATH=""
CLIENT_CERT_PATH=""
CLIENT_KEY_PATH=""
CA_CERT=""
CLIENT_CERT=""
CLIENT_KEY=""

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================
AGENT_NAME="unknown"
AGENT_VERSION="1.0.0"
AGENT_TIMEOUT=30000
AGENT_RETRY_ATTEMPTS=3

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_PROVIDER="memory"
CACHE_TTL=3600000
CACHE_MAX_SIZE=1000

# =============================================================================
# METRICS CONFIGURATION
# =============================================================================
METRICS_ENABLED=false
METRICS_ENDPOINT=""
METRICS_INTERVAL=60000

# =============================================================================
# LOGGING FORMAT
# =============================================================================
LOG_FORMAT="json"
