// Shared MCP contracts for cross-package reuse
// nosemgrep: central shared contracts file; monorepo duplicate/missing-shared-types rules not applicable here

export type DatabaseAdapter = {
  withTenant(orgId: string): Promise<any>;
  execute(query: any): Promise<any>;
};

export type CacheAdapter = {
  get(key: string): Promise<string | null>;
  set(key: string, value: string): Promise<any>;
  setex(key: string, ttl: number, value: string): Promise<any>;
  keys(pattern: string): Promise<string[]>;
  ping(): Promise<string>;
  dbsize(): Promise<number>;
  info(section?: string): Promise<string>;
  sadd(key: string, ...members: string[]): Promise<number>;
  smembers(key: string): Promise<string[]>;
  srem(key: string, ...members: string[]): Promise<number>;
  expire(key: string, seconds: number): Promise<number>;
};

export type LoggerAdapter = {
  info(message: string, ...args: any[]): void;
  error(error: Error | string, message?: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  debug(message: string, ...args: any[]): void;
};

export type MCPToolsDependencies = {
  database: DatabaseAdapter;
  cache: CacheAdapter;
  logger?: LoggerAdapter;
};

export type MCPSession = {
  id: string;
  organization_id: string;
  user_id?: string;
  created_at?: Date;
  last_activity?: Date;
};

export type AsyncTask = {
  id: string;
  session_id: string;
  tool_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  result?: any;
  error?: string;
  current_step?: string;
  estimated_completion?: string;
  metadata?: Record<string, any>;
};

export type TaskStatusResponse = {
  task: AsyncTask;
  can_cancel: boolean;
};

export type TaskMetrics = {
  total_tasks: number;
  active_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  tasks_by_tool: Record<string, number>;
};
