{"name": "@anter/domain-model", "version": "0.0.1", "description": "Shared Drizzle ORM models and TypeScript types for AskInfoSec services.", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "pnpm clean && tsc --build", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"drizzle-orm": "^0.44.5"}, "devDependencies": {"@types/node": "^24.3.3", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "README.md"], "author": "AskInfoSec", "license": "ISC"}