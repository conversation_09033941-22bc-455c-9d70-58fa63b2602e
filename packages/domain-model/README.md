# @anter/domain-model

Shared Drizzle ORM models and TypeScript types for the AskInfoSec platform.

## Purpose

This package centralizes all database schemas (Drizzle ORM) and related domain types so they can be reused by any service in the monorepo (API, agents, background workers, etc.).

## Usage

```ts
import { users } from '@anter/domain-model';
```

## Development

1. Add or update schemas under `src/`.
2. Export them from `src/index.ts`.
3. Run `pnpm -F @anter/domain-model build` to compile.
4. Ensure unit tests pass: `pnpm -F @anter/domain-model test`.
