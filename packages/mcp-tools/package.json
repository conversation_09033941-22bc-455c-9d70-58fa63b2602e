{"name": "@anter/mcp-tools", "version": "0.0.1", "description": "MCP (Model Context Protocol) tools and task management for AskInfoSec platform", "main": "dist/index.js", "types": "dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/types/index.d.ts"}}, "scripts": {"build": "pnpm clean && tsc --build", "dev": "tsc --watch", "clean": "rm -rf dist", "clean:all": "rm -rf dist node_modules", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "peerDependencies": {"drizzle-orm": "^0.44.5"}, "dependencies": {"@anter/shared-services": "workspace:*", "@langchain/openai": "^0.6.11", "@modelcontextprotocol/sdk": "^1.18.0", "ajv": "^8.17.1", "html-to-text": "^9.0.5", "ioredis": "^5.7.0", "langfuse": "^3.38.5", "mammoth": "^1.10.0", "pdf-parse": "^1.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/html-to-text": "^9.0.4", "@types/ioredis": "^5.0.0", "@types/node": "^24.0.1", "@types/pdf-parse": "^1.1.4", "eslint": "^9.0.0", "ioredis-mock": "^8.9.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "README.md"], "keywords": ["mcp", "model-context-protocol", "tools", "database", "analysis", "askinfosec"], "author": "AskInfoSec", "license": "ISC"}