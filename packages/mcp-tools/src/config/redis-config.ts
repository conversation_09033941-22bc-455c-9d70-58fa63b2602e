export interface RedisConfig {
  url: string;
  indexEnabled: boolean;
  searchEnabled: boolean | 'auto';
  embeddingBatchSize: number;
  indexTtlHours: number;
  searchFallbackStrategy: 'in_memory' | 'error';
  connectionOptions?: {
    retryDelayOnFailover?: number;
    maxRetriesPerRequest?: number;
    connectTimeout?: number;
    commandTimeout?: number;
  };

  // Phase 3 - Advanced Features
  rediSearchEnabled: boolean;
  useNativeVectorSearch: boolean;
  multiLevelCacheEnabled: boolean;
  circuitBreakerEnabled: boolean;
  metricsEnabled: boolean;
  l1CacheMaxDocuments: number;
  l1CacheTtlSeconds: number;
  circuitBreakerFailureThreshold: number;
  circuitBreakerRecoveryTimeout: number;
}

/**
 * Parse Redis configuration from environment variables
 */
export function parseRedisConfig(): RedisConfig {
  const config: RedisConfig = {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    indexEnabled: process.env.REDIS_INDEX_ENABLED
      ? process.env.REDIS_INDEX_ENABLED === 'true'
      : true, // Default to true if not set
    searchEnabled: process.env.REDIS_SEARCH_ENABLED
      ? parseSearchEnabled(process.env.REDIS_SEARCH_ENABLED)
      : true, // Default to true if not set
    embeddingBatchSize: parseInt(process.env.EMBEDDING_BATCH_SIZE || '75', 10),
    indexTtlHours: parseInt(process.env.INDEX_TTL_HOURS || '168', 10), // 7 days default
    searchFallbackStrategy: parseSearchFallbackStrategy(process.env.SEARCH_FALLBACK_STRATEGY),
    connectionOptions: {
      retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100', 10),
      maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
      connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '10000', 10),
      commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || '5000', 10),
    },

    rediSearchEnabled: process.env.REDISEARCH_ENABLED === 'true',
    useNativeVectorSearch: process.env.USE_NATIVE_VECTOR_SEARCH !== 'false',
    multiLevelCacheEnabled: process.env.MULTI_LEVEL_CACHE_ENABLED === 'true',
    circuitBreakerEnabled: process.env.CIRCUIT_BREAKER_ENABLED === 'true',
    metricsEnabled: process.env.METRICS_ENABLED !== 'false',
    l1CacheMaxDocuments: parseInt(process.env.L1_CACHE_MAX_DOCUMENTS || '1000', 10),
    l1CacheTtlSeconds: parseInt(process.env.L1_CACHE_TTL_SECONDS || '3600', 10),
    circuitBreakerFailureThreshold: parseInt(
      process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5',
      10
    ),
    circuitBreakerRecoveryTimeout: parseInt(
      process.env.CIRCUIT_BREAKER_RECOVERY_TIMEOUT || '60000',
      10
    ),
  };

  return config;
}

function parseSearchEnabled(value?: string): boolean | 'auto' {
  if (!value) return 'auto';
  if (value === 'auto') return 'auto';
  return value === 'true';
}

function parseSearchFallbackStrategy(value?: string): 'in_memory' | 'error' {
  if (value === 'error') return 'error';
  return 'in_memory';
}

/**
 * Validate Redis configuration
 */
export function validateRedisConfig(config: RedisConfig): void {
  if (!config.url) {
    throw new Error('Redis URL is required');
  }

  if (config.embeddingBatchSize < 1 || config.embeddingBatchSize > 100) {
    throw new Error('Embedding batch size must be between 1 and 100');
  }

  if (config.indexTtlHours < 1 || config.indexTtlHours > 8760) {
    // Max 1 year
    throw new Error('Index TTL must be between 1 and 8760 hours');
  }
}

/**
 * Get Redis configuration with validation
 */
export function getRedisConfig(): RedisConfig {
  const config = parseRedisConfig();
  validateRedisConfig(config);
  return config;
}
