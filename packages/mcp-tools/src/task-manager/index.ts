import { Cache<PERSON>dapter, LoggerAdapter } from '../types';
import { AsyncTask, TaskStatusResponse, TaskMetrics } from '../types/task';

/**
 * Lightweight async-task orchestration layer backed by an abstract `CacheAdapter` (e.g., Redis).
 * Each task is stored as a JSON blob with a TTL so the store automatically evicts stale entries
 * even if `cleanupExpiredTasks` is never called. A separate *session set* keeps track of all task
 * IDs belonging to a user session, enabling quick listing and concurrency control.
 *
 * Key features:
 * • Max-concurrency enforcement per session.
 * • Atomic status transitions with timestamps (`created_at`, `started_at`, `completed_at`).
 * • Best-effort clean-up for dangling tasks and session sets.
 * • Aggregated metrics for observability.
 *
 * All methods are *await*-able and safe for concurrent callers assuming the underlying
 * `CacheAdapter` offers the usual Redis semantics for `setex`, `sadd`, etc.
 */
export class AsyncTaskManager {
  private readonly TASK_TTL = 3600; // 1 hour
  private readonly MAX_CONCURRENT_TASKS = 10;

  /**
   * @param cache   – Concrete implementation that exposes a Redis-like API used for persistence.
   * @param logger  – Optional structured logger; debug/info messages are emitted throughout the
   *                  task lifecycle.
   */
  constructor(
    private cache: CacheAdapter,
    private logger?: LoggerAdapter
  ) {}

  /**
   * Create a **new task** in *pending* state and register it under the provided session.
   *
   * @param sessionId – Logical user/session identifier.
   * @param toolName  – Human-readable tool reference (used later for metrics).
   * @param metadata  – Arbitrary JSON serialisable map persisted alongside the task.
   * @returns Generated `taskId`.
   * @throws Error If the caller already has the maximum number of concurrent tasks.
   */
  async createTask(
    sessionId: string,
    toolName: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    const task: AsyncTask = {
      id: taskId,
      session_id: sessionId,
      tool_name: toolName,
      status: 'pending',
      progress: 0,
      created_at: now,
      updated_at: now,
      metadata,
    };

    // Check concurrent task limit
    const activeTasks = await this.getActiveTasksForSession(sessionId);
    if (activeTasks.length >= this.MAX_CONCURRENT_TASKS) {
      throw new Error(`Maximum concurrent tasks limit reached (${this.MAX_CONCURRENT_TASKS})`);
    }

    const taskKey = this.getTaskKey(sessionId, taskId);
    await this.cache.setex(taskKey, this.TASK_TTL, JSON.stringify(task));

    // Add to session task list
    const sessionTasksKey = this.getSessionTasksKey(sessionId);
    await this.cache.sadd(sessionTasksKey, taskId);
    await this.cache.expire(sessionTasksKey, this.TASK_TTL);

    this.logger?.info(`Created task ${taskId} for session ${sessionId}`);
    return taskId;
  }

  /**
   * Fetch a task by id.
   * @returns The task object or `null` when not found/expired.
   */
  async getTask(sessionId: string, taskId: string): Promise<AsyncTask | null> {
    const taskKey = this.getTaskKey(sessionId, taskId);
    const taskData = await this.cache.get(taskKey);

    if (!taskData) {
      return null;
    }

    return JSON.parse(taskData);
  }

  /**
   * Atomically update selected fields of a task and refresh its TTL.
   *
   * @param updates – Partial task object. `status` changes automatically propagate related
   *                  timestamp fields (`started_at`, `completed_at`).
   * @throws Error If the task does not exist.
   */
  async updateTask(sessionId: string, taskId: string, updates: Partial<AsyncTask>): Promise<void> {
    const task = await this.getTask(sessionId, taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    const updatedTask: AsyncTask = {
      ...task,
      ...updates,
      updated_at: new Date().toISOString(),
    };

    // Set completed_at when status changes to completed or failed
    if (['completed', 'failed', 'cancelled'].includes(updatedTask.status) && !task.completed_at) {
      updatedTask.completed_at = new Date().toISOString();
    }

    // Set started_at when status changes to processing
    if (updatedTask.status === 'processing' && !task.started_at) {
      updatedTask.started_at = new Date().toISOString();
    }

    const taskKey = this.getTaskKey(sessionId, taskId);
    await this.cache.setex(taskKey, this.TASK_TTL, JSON.stringify(updatedTask));

    this.logger?.debug(`Updated task ${taskId}: ${JSON.stringify(updates)}`);
  }

  /**
   * Attempt to cancel a *pending* or *processing* task.
   *
   * @returns `true` if the task moved to *cancelled*; `false` when the task is already finished
   *          or nonexistent.
   */
  async cancelTask(sessionId: string, taskId: string): Promise<boolean> {
    const task = await this.getTask(sessionId, taskId);
    if (!task) {
      return false;
    }

    if (['completed', 'failed', 'cancelled'].includes(task.status)) {
      return false; // Cannot cancel already finished tasks
    }

    await this.updateTask(sessionId, taskId, {
      status: 'cancelled',
      current_step: 'Task cancelled by user',
    });

    this.logger?.info(`Cancelled task ${taskId}`);
    return true;
  }

  /**
   * Convenience helper to return the current task object plus a `can_cancel` boolean understood
   * by API clients.
   */
  async getTaskStatus(sessionId: string, taskId: string): Promise<TaskStatusResponse | null> {
    const task = await this.getTask(sessionId, taskId);
    if (!task) {
      return null;
    }

    const canCancel = !['completed', 'failed', 'cancelled'].includes(task.status);

    return {
      task,
      can_cancel: canCancel,
    };
  }

  /**
   * List **all** tasks for a given session ordered by `created_at` descending.
   */
  async listTasksForSession(sessionId: string): Promise<AsyncTask[]> {
    const sessionTasksKey = this.getSessionTasksKey(sessionId);
    const taskIds = await this.cache.smembers(sessionTasksKey);

    const tasks: AsyncTask[] = [];
    for (const taskId of taskIds) {
      const task = await this.getTask(sessionId, taskId);
      if (task) {
        tasks.push(task);
      }
    }

    // Sort by creation date (newest first)
    return tasks.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  /**
   * Return tasks whose status is either *pending* or *processing*.
   */
  async getActiveTasksForSession(sessionId: string): Promise<AsyncTask[]> {
    const allTasks = await this.listTasksForSession(sessionId);
    return allTasks.filter(task => ['pending', 'processing'].includes(task.status));
  }

  /**
   * Iterate over every session key and prune task IDs whose backing hash expired. Mainly useful
   * when the cache has lost volatile data (e.g., after Redis restart) leaving orphaned IDs.
   *
   * @returns Number of removed stub references.
   */
  async cleanupExpiredTasks(): Promise<number> {
    // This method would be called periodically to clean up expired tasks
    // For now, we rely on cache TTL for automatic cleanup

    const allSessionKeys = await this.cache.keys('mcp:tasks:session:*');
    let cleanedUp = 0;

    for (const sessionKey of allSessionKeys) {
      const sessionId = sessionKey.split(':').pop();
      if (!sessionId) continue;

      const taskIds = await this.cache.smembers(sessionKey);
      for (const taskId of taskIds) {
        const task = await this.getTask(sessionId, taskId);
        if (!task) {
          // Remove from session set if task doesn't exist
          await this.cache.srem(sessionKey, taskId);
          cleanedUp++;
        }
      }
    }

    this.logger?.info(`Cleaned up ${cleanedUp} expired tasks`);
    return cleanedUp;
  }

  /**
   * Aggregate basic statistics across **all sessions** for monitoring dashboards.
   */
  async getTaskMetrics(): Promise<TaskMetrics> {
    const allSessionKeys = await this.cache.keys('mcp:tasks:session:*');
    const metrics: TaskMetrics = {
      total_tasks: 0,
      active_tasks: 0,
      completed_tasks: 0,
      failed_tasks: 0,
      tasks_by_tool: {},
    };

    for (const sessionKey of allSessionKeys) {
      const sessionId = sessionKey.split(':').pop();
      if (!sessionId) continue;

      const tasks = await this.listTasksForSession(sessionId);
      for (const task of tasks) {
        metrics.total_tasks++;

        if (['pending', 'processing'].includes(task.status)) {
          metrics.active_tasks++;
        } else if (task.status === 'completed') {
          metrics.completed_tasks++;
        } else if (task.status === 'failed') {
          metrics.failed_tasks++;
        }

        // Track by tool
        if (!metrics.tasks_by_tool[task.tool_name]) {
          metrics.tasks_by_tool[task.tool_name] = 0;
        }
        metrics.tasks_by_tool[task.tool_name]++;
      }
    }

    return metrics;
  }

  /* ----------------------------- PRIVATE HELPERS ----------------------------- */

  /** Build key for an individual task record. */
  private getTaskKey(sessionId: string, taskId: string): string {
    return `mcp:tasks:${sessionId}:${taskId}`;
  }

  /** Build key for the Redis set storing all taskIds belonging to a session. */
  private getSessionTasksKey(sessionId: string): string {
    return `mcp:tasks:session:${sessionId}`;
  }
}
