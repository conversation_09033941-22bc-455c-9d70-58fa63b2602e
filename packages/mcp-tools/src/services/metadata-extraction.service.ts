/**
 * Metadata Extraction Service
 * Extracts and enriches document metadata
 */
/**
 * Richly typed representation of metadata automatically extracted from text documents. Each
 * top-level section is optional because certain inputs (e.g., code snippets) do not lend
 * themselves to all analysis dimensions. All dates are expressed as native `Date` objects for
 * downstream convenience.
 */
export interface ExtractedMetadata {
  // Document characteristics
  documentStructure?: {
    hasHeaders: boolean;
    hasTables: boolean;
    hasLists: boolean;
    hasCodeBlocks: boolean;
    sectionCount: number;
  };

  // Content metrics
  contentMetrics?: {
    wordCount: number;
    sentenceCount: number;
    paragraphCount: number;
    averageWordsPerSentence: number;
    readingTimeMinutes: number;
  };

  // Dates and temporal information
  temporalData?: {
    creationDate?: Date;
    lastModified?: Date;
    referencedDates: Date[];
    timePeriod?: {
      from?: Date;
      to?: Date;
      description: string;
    };
  };

  // Security-specific metadata
  securityMetadata?: {
    threatLevel: 'low' | 'medium' | 'high' | 'critical';
    containsCredentials: boolean;
    containsPII: boolean;
    hasVulnerabilityReferences: boolean;
    complianceReferences: string[];
    securityKeywords: string[];
  };

  // Technical metadata
  technicalMetadata?: {
    mentionedTechnologies: string[];
    platforms: string[];
    programmingLanguages: string[];
    protocols: string[];
    standards: string[];
  };

  // Language and formatting
  languageData?: {
    primaryLanguage: string;
    confidence: number;
    technicalTermDensity: number;
    formality: 'informal' | 'formal' | 'technical' | 'academic';
  };

  // Quality indicators
  qualityMetrics?: {
    completeness: number; // 0-1 scale
    clarity: number; // 0-1 scale
    structure: number; // 0-1 scale
    credibility: number; // 0-1 scale
  };
}

/**
 * @deprecated Don't use this service.
 */
/**
 * Legacy implementation that performs best-effort inference of structural and semantic metadata
 * such as header usage, temporal coverage, security markers and linguistic quality. Although the
 * class is flagged as *deprecated*, it is still referenced by historical pipelines – therefore
 * we document it thoroughly for maintainers.
 *
 * All public and private methods are side-effect free and can safely run in parallel.
 */
// @ts-ignore
class MetadataExtractionService {
  /**
   * Orchestrate **parallel** extraction of every metadata facet supported by this service.
   * Each sub-routine is executed via `Promise.all` for optimal throughput.
   *
   * @param document – Plain object expected to expose at least a `content` string field and
   *                   optional `metadata` map from upstream systems.
   * @returns Consolidated {@link ExtractedMetadata} structure.
   */
  async extractMetadata(document: any): Promise<ExtractedMetadata> {
    const content = document.content || document.text || '';
    const existingMetadata = document.metadata || {};

    if (!content.trim()) {
      return this.getMinimalMetadata(existingMetadata);
    }

    // Perform parallel extraction of different metadata types
    const [
      documentStructure,
      contentMetrics,
      temporalData,
      securityMetadata,
      technicalMetadata,
      languageData,
      qualityMetrics,
    ] = await Promise.all([
      this.analyzeDocumentStructure(content),
      this.calculateContentMetrics(content),
      this.extractTemporalData(content, existingMetadata),
      this.extractSecurityMetadata(content),
      this.extractTechnicalMetadata(content),
      this.analyzeLanguageCharacteristics(content),
      this.calculateQualityMetrics(content),
    ]);

    return {
      documentStructure,
      contentMetrics,
      temporalData,
      securityMetadata,
      technicalMetadata,
      languageData,
      qualityMetrics,
    };
  }

  /**
   * Analyse the **layout** of a document by inspecting markdown headers, tables, lists and code
   * sections. The approach relies purely on regular expressions – no external parsers – which
   * makes it extremely fast yet surprisingly effective for technical content.
   */
  private async analyzeDocumentStructure(content: string): Promise<{
    hasHeaders: boolean;
    hasTables: boolean;
    hasLists: boolean;
    hasCodeBlocks: boolean;
    sectionCount: number;
  }> {
    // Detect headers (markdown style or numbered sections)
    const headerPatterns = [
      /^#{1,6}\s+.+$/gm, // Markdown headers
      /^\d+\.\s+.+$/gm, // Numbered sections
      /^[A-Z][A-Za-z\s]+:$/gm, // Section headers with colons
    ];
    const hasHeaders = headerPatterns.some(pattern => pattern.test(content));

    // Detect tables
    const tablePatterns = [
      /\|.+\|/g, // Markdown tables
      /\+[-+]+\+/g, // ASCII tables
      /\btable\b.*\bcolumn\b/gi, // Table references
    ];
    const hasTables = tablePatterns.some(pattern => pattern.test(content));

    // Detect lists
    const listPatterns = [
      /^\s*[-*+]\s+/gm, // Unordered lists
      /^\s*\d+\.\s+/gm, // Ordered lists
      /^\s*[a-zA-Z]\.\s+/gm, // Lettered lists
    ];
    const hasLists = listPatterns.some(pattern => pattern.test(content));

    // Detect code blocks
    const codePatterns = [
      /```[\s\S]*?```/g, // Markdown code blocks
      /`[^`]+`/g, // Inline code
      /^\s{4,}.+$/gm, // Indented code blocks
    ];
    const hasCodeBlocks = codePatterns.some(pattern => pattern.test(content));

    // Count sections (rough estimate based on headers and paragraph breaks)
    const headerMatches = content.match(/^#{1,6}\s+.+$|^\d+\.\s+.+$|^[A-Z][A-Za-z\s]+:$/gm) || [];
    const paragraphBreaks = content.split(/\n\s*\n/).length;
    const sectionCount = Math.max(headerMatches.length, Math.ceil(paragraphBreaks / 3));

    return {
      hasHeaders,
      hasTables,
      hasLists,
      hasCodeBlocks,
      sectionCount,
    };
  }

  /**
   * Compute fundamental **text metrics** such as word and sentence counts as well as an estimated
   * reading time. Prior to counting, URLs and code blocks are stripped to avoid skewing the
   * statistics.
   */
  private async calculateContentMetrics(content: string): Promise<{
    wordCount: number;
    sentenceCount: number;
    paragraphCount: number;
    averageWordsPerSentence: number;
    readingTimeMinutes: number;
  }> {
    // Clean content for analysis
    const cleanContent = content
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/`[^`]+`/g, '') // Remove inline code
      .replace(/https?:\/\/[^\s]+/g, ''); // Remove URLs

    const words = cleanContent.split(/\s+/).filter(w => w.trim().length > 0);
    const sentences = cleanContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = cleanContent.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    const wordCount = words.length;
    const sentenceCount = sentences.length;
    const paragraphCount = paragraphs.length;
    const averageWordsPerSentence = sentenceCount > 0 ? wordCount / sentenceCount : 0;

    // Average reading speed: 200 words per minute
    const readingTimeMinutes = Math.ceil(wordCount / 200);

    return {
      wordCount,
      sentenceCount,
      paragraphCount,
      averageWordsPerSentence: Math.round(averageWordsPerSentence * 10) / 10,
      readingTimeMinutes,
    };
  }

  /**
   * Combine explicit dates found in the existing metadata with any temporal expressions detected
   * inside the content (ISO, US, EN formats). Returns a chronologically sorted array plus an
   * optional *time period* summary.
   */
  private async extractTemporalData(
    content: string,
    existingMetadata: any
  ): Promise<{
    creationDate?: Date;
    lastModified?: Date;
    referencedDates: Date[];
    timePeriod?: {
      from?: Date;
      to?: Date;
      description: string;
    };
  }> {
    // Extract creation and modification dates from metadata
    const creationDate = this.parseDate(existingMetadata.createdAt || existingMetadata.created);
    const lastModified = this.parseDate(existingMetadata.updatedAt || existingMetadata.modified);

    // Extract dates mentioned in content
    const datePatterns = [
      /\b\d{4}-\d{2}-\d{2}\b/g, // YYYY-MM-DD
      /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, // MM/DD/YYYY or DD/MM/YYYY
      /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/gi,
      /\b\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4}\b/gi,
    ];

    const referencedDates: Date[] = [];
    datePatterns.forEach(pattern => {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        const date = this.parseDate(match);
        if (date && !isNaN(date.getTime())) {
          referencedDates.push(date);
        }
      });
    });

    // Remove duplicates and sort
    const uniqueDates = [...new Set(referencedDates.map(d => d.getTime()))]
      .map(timestamp => new Date(timestamp))
      .sort((a, b) => a.getTime() - b.getTime());

    // Determine time period
    let timePeriod;
    if (uniqueDates.length > 1) {
      const from = uniqueDates[0];
      const to = uniqueDates[uniqueDates.length - 1];
      const daysDiff = Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24));

      let description = `Covers ${daysDiff} days`;
      if (daysDiff > 365) {
        description = `Covers ${Math.round(daysDiff / 365)} years`;
      } else if (daysDiff > 30) {
        description = `Covers ${Math.round(daysDiff / 30)} months`;
      }

      timePeriod = { from, to, description };
    }

    return {
      creationDate,
      lastModified,
      referencedDates: uniqueDates,
      timePeriod,
    };
  }

  /**
   * Identify potential *security red-flags* such as leaked credentials, PII and vulnerability
   * identifiers (CVE, CWE). Also assigns a heuristic threat level.
   */
  private async extractSecurityMetadata(content: string): Promise<{
    threatLevel: 'low' | 'medium' | 'high' | 'critical';
    containsCredentials: boolean;
    containsPII: boolean;
    hasVulnerabilityReferences: boolean;
    complianceReferences: string[];
    securityKeywords: string[];
  }> {
    const lowerContent = content.toLowerCase();

    // Detect credentials
    const credentialPatterns = [
      /password\s*[:=]\s*\S+/gi,
      /api[_\s]?key\s*[:=]\s*\S+/gi,
      /secret\s*[:=]\s*\S+/gi,
      /token\s*[:=]\s*\S+/gi,
    ];
    const containsCredentials = credentialPatterns.some(pattern => pattern.test(content));

    // Detect PII
    const piiPatterns = [
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN
      /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, // Credit card
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
      /\b\d{3}-\d{3}-\d{4}\b/g, // Phone number
    ];
    const containsPII = piiPatterns.some(pattern => pattern.test(content));

    // Check for vulnerability references
    const vulnPatterns = [
      /cve-\d{4}-\d{4,}/gi,
      /cwe-\d+/gi,
      /\b(critical|high|medium|low)\s+(vulnerability|risk)\b/gi,
    ];
    const hasVulnerabilityReferences = vulnPatterns.some(pattern => pattern.test(content));

    // Extract compliance references
    const complianceKeywords = ['gdpr', 'hipaa', 'sox', 'pci', 'iso', 'nist', 'fedramp', 'fisma'];
    const complianceReferences = complianceKeywords.filter(keyword =>
      lowerContent.includes(keyword)
    );

    // Extract security keywords
    const securityTerms = [
      'security',
      'vulnerability',
      'threat',
      'risk',
      'exploit',
      'malware',
      'breach',
      'attack',
      'encryption',
      'authentication',
      'authorization',
      'firewall',
      'intrusion',
      'incident',
      'forensics',
      'penetration',
    ];
    const securityKeywords = securityTerms.filter(term => lowerContent.includes(term));

    // Calculate threat level
    let threatLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

    if (containsCredentials || hasVulnerabilityReferences) {
      threatLevel = 'high';
    } else if (containsPII || securityKeywords.length > 5) {
      threatLevel = 'medium';
    } else if (securityKeywords.length > 0) {
      threatLevel = 'low';
    }

    // Escalate to critical if multiple high-risk indicators
    if (containsCredentials && hasVulnerabilityReferences && containsPII) {
      threatLevel = 'critical';
    }

    return {
      threatLevel,
      containsCredentials,
      containsPII,
      hasVulnerabilityReferences,
      complianceReferences,
      securityKeywords,
    };
  }

  /**
   * Extract mentions of technologies, platforms, programming languages, protocols and standards
   * using simple keyword matching against curated lists.
   */
  private async extractTechnicalMetadata(content: string): Promise<{
    mentionedTechnologies: string[];
    platforms: string[];
    programmingLanguages: string[];
    protocols: string[];
    standards: string[];
  }> {
    // const lowerContent = content.toLowerCase();

    // Technology categories
    const technologies = [
      'apache',
      'nginx',
      'iis',
      'mysql',
      'postgresql',
      'mongodb',
      'redis',
      'docker',
      'kubernetes',
      'jenkins',
      'git',
      'aws',
      'azure',
      'gcp',
      'splunk',
      'elasticsearch',
      'kibana',
      'grafana',
      'prometheus',
    ];

    const platforms = [
      'windows',
      'linux',
      'macos',
      'android',
      'ios',
      'ubuntu',
      'centos',
      'redhat',
      'debian',
      'fedora',
      'solaris',
      'aix',
    ];

    const programmingLanguages = [
      'javascript',
      'python',
      'java',
      'c++',
      'c#',
      'php',
      'ruby',
      'go',
      'rust',
      'swift',
      'kotlin',
      'typescript',
      'sql',
      'powershell',
      'bash',
    ];

    const protocols = [
      'http',
      'https',
      'ftp',
      'ssh',
      'telnet',
      'smtp',
      'pop3',
      'imap',
      'dns',
      'dhcp',
      'snmp',
      'tcp',
      'udp',
      'icmp',
      'ssl',
      'tls',
    ];

    const standards = [
      'owasp',
      'nist',
      'iso27001',
      'iso27002',
      'cis',
      'sans',
      'mitre',
      'cvss',
      'cve',
      'cwe',
      'pci-dss',
      'gdpr',
      'hipaa',
    ];

    const extractCategory = (category: string[]) =>
      category.filter(item =>
        new RegExp(`\\b${item.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i').test(content)
      );

    return {
      mentionedTechnologies: extractCategory(technologies),
      platforms: extractCategory(platforms),
      programmingLanguages: extractCategory(programmingLanguages),
      protocols: extractCategory(protocols),
      standards: extractCategory(standards),
    };
  }

  /**
   * Lightweight language detection and formality assessment using stop-word statistics and
   * technical term density.
   */
  private async analyzeLanguageCharacteristics(content: string): Promise<{
    primaryLanguage: string;
    confidence: number;
    technicalTermDensity: number;
    formality: 'informal' | 'formal' | 'technical' | 'academic';
  }> {
    // Simple language detection (assuming English for now)
    const primaryLanguage = 'en';
    const confidence = 0.95;

    // Calculate technical term density
    const words = content.split(/\s+/).filter(w => w.trim().length > 0);
    const technicalTerms = [
      'algorithm',
      'protocol',
      'implementation',
      'configuration',
      'architecture',
      'methodology',
      'infrastructure',
      'framework',
      'environment',
      'vulnerability',
      'authentication',
      'authorization',
      'encryption',
      'cryptography',
      'certificate',
    ];

    const technicalTermCount = technicalTerms.filter(term =>
      new RegExp(`\\b${term}\\b`, 'i').test(content)
    ).length;

    const technicalTermDensity = words.length > 0 ? technicalTermCount / words.length : 0;

    // Determine formality
    let formality: 'informal' | 'formal' | 'technical' | 'academic';

    if (technicalTermDensity > 0.05) {
      formality = 'technical';
    } else if (content.includes('abstract') && content.includes('conclusion')) {
      formality = 'academic';
    } else if (/\b(shall|must|will|should)\b/gi.test(content)) {
      formality = 'formal';
    } else {
      formality = 'informal';
    }

    return {
      primaryLanguage,
      confidence,
      technicalTermDensity: Math.round(technicalTermDensity * 1000) / 1000,
      formality,
    };
  }

  /**
   * Aggregate several quality heuristics (completeness, clarity, structure, credibility) into a
   * compact score card between 0 and 1.
   */
  private async calculateQualityMetrics(content: string): Promise<{
    completeness: number;
    clarity: number;
    structure: number;
    credibility: number;
  }> {
    const words = content.split(/\s+/).filter(w => w.trim().length > 0);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

    // Completeness (based on content length and structure)
    let completeness = 0;
    if (words.length > 100) completeness += 0.3;
    if (words.length > 500) completeness += 0.3;
    if (content.includes('conclusion') || content.includes('summary')) completeness += 0.2;
    if (sentences.length > 10) completeness += 0.2;

    // Clarity (based on sentence structure and readability)
    let clarity = 0;
    const avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
    if (avgWordsPerSentence > 10 && avgWordsPerSentence < 25) clarity += 0.4;
    if (!/\?\?\?|TODO|FIXME/i.test(content)) clarity += 0.3;
    if (content.split('\n\n').length > 2) clarity += 0.3; // Has paragraphs

    // Structure (based on document organization)
    let structure = 0;
    if (/^#{1,6}\s+/gm.test(content) || /^\d+\.\s+/gm.test(content)) structure += 0.4;
    if (/^\s*[-*+]\s+/gm.test(content) || /^\s*\d+\.\s+/gm.test(content)) structure += 0.3;
    if (content.split('\n\n').length > 3) structure += 0.3;

    // Credibility (based on references and professional language)
    let credibility = 0;
    if (/https?:\/\/[^\s]+/g.test(content)) credibility += 0.2; // Has links
    if (/\b(source|reference|citation)\b/gi.test(content)) credibility += 0.2;
    if (!/\b(maybe|probably|guess|think)\b/gi.test(content)) credibility += 0.3;
    if (/\b(research|study|analysis|evaluation)\b/gi.test(content)) credibility += 0.3;

    // Normalize to 0-1 range
    completeness = Math.min(completeness, 1);
    clarity = Math.min(clarity, 1);
    structure = Math.min(structure, 1);
    credibility = Math.min(credibility, 1);

    return {
      completeness: Math.round(completeness * 100) / 100,
      clarity: Math.round(clarity * 100) / 100,
      structure: Math.round(structure * 100) / 100,
      credibility: Math.round(credibility * 100) / 100,
    };
  }

  /**
   * Parse dates from multiple string formats (ISO, slash-separated, long-form) into `Date`
   * objects. Invalid parses return `undefined`.
   */
  private parseDate(dateString: any): Date | undefined {
    if (!dateString) return undefined;

    if (dateString instanceof Date) return dateString;

    if (typeof dateString === 'string') {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? undefined : date;
    }

    return undefined;
  }

  /**
   * Generate a *minimal* metadata structure used as a safe fallback when the input content is
   * empty or unparsable.
   */
  private getMinimalMetadata(_existingMetadata: any): ExtractedMetadata {
    return {
      contentMetrics: {
        wordCount: 0,
        sentenceCount: 0,
        paragraphCount: 0,
        averageWordsPerSentence: 0,
        readingTimeMinutes: 0,
      },
      securityMetadata: {
        threatLevel: 'low',
        containsCredentials: false,
        containsPII: false,
        hasVulnerabilityReferences: false,
        complianceReferences: [],
        securityKeywords: [],
      },
      qualityMetrics: {
        completeness: 0,
        clarity: 0,
        structure: 0,
        credibility: 0,
      },
    };
  }
}
