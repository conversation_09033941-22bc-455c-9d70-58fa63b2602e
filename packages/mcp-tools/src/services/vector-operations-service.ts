import { EmbeddingService } from './embedding-service';
import { ProcessedDocument } from './document-processor';
import {
  IEmbeddingIndex,
  EmbeddingIndexFactory,
  EmbeddingIndexFactoryOptions,
} from './embedding-index';
import type { EmbeddingRedisClient as RedisClient } from './embedding-index/shared-types';

export interface VectorSearchOptions {
  organizationId?: string;
  filters?: Record<string, any>;
  topK?: number;
  threshold?: number;
  useEmbeddings?: boolean; // Use actual embeddings vs simple vectors
}

export interface VectorDocument {
  id: string;
  vector: number[];
  content?: string;
  metadata: Record<string, any>;
  organizationId: string;
  timestamp: Date;
}

export interface VectorSearchResult {
  id: string;
  score: number;
  content?: string;
  metadata: Record<string, any>;
}

export class VectorOperationsService {
  private vectorStore = new Map<string, VectorDocument>();
  private organizationIndex = new Map<string, Set<string>>(); // org -> docIds
  private embeddingService?: EmbeddingService;
  private searchCount = 0;

  // New: Optional backing index (Redis or in-memory via EmbeddingIndexFactory)
  private embeddingIndexPromise?: Promise<IEmbeddingIndex | undefined>;
  // @ts-ignore
  private redisClient?: RedisClient;

  constructor(
    embeddingService?: EmbeddingService,
    redisClient?: RedisClient,
    private readonly defaultOrganizationId: string = 'default'
  ) {
    this.embeddingService = embeddingService;
    this.redisClient = redisClient;

    // Lazy-create the embedding index – will resolve to Redis implementation
    // when a real client is supplied and reachable, otherwise falls back to
    // an in-memory index. We do not await here to keep the constructor sync.
    this.embeddingIndexPromise = (async () => {
      if (redisClient) {
        const redisAvailable = await EmbeddingIndexFactory.isRedisAvailable(redisClient);
        if (redisAvailable) {
          const opts: EmbeddingIndexFactoryOptions = {
            organizationId: this.defaultOrganizationId,
            redisClient,
          };
          return await EmbeddingIndexFactory.createIndex(opts);
        }
      }
      // Fallback handled by factory with forceInMemory when no redis
      return undefined;
    })();
  }

  /**
   * Perform a vector-based semantic similarity search over all documents belonging to the
   * provided `organizationId`.
   *
   * Workflow:
   *   1. Increment the internal `searchCount` metric for observability.
   *   2. Produce an embedding (or a deterministic fallback vector) for the input `query` via
   *      `generateQueryVector` – this automatically decides between true model embeddings and the
   *      simple heuristic vector based on the availability of `EmbeddingService` and the
   *      `useEmbeddings` flag.
   *   3. Retrieve the set of candidate document IDs registered under the given organisation. If
   *      none exist, short-circuit and return an empty result list.
   *   4. For each candidate document that matches the optional metadata `filters`, compute the
   *      cosine similarity between the query vector and the stored document vector.
   *   5. Discard any candidate whose similarity is below `threshold` (default 0.1).
   *   6. Sort the remaining candidates in descending similarity order and take the top `topK`
   *      (default 20).
   *   7. Map each candidate into a `VectorSearchResult` object and return the resulting array.
   *
   * Parameters:
   *     @param query           Human-readable search string to locate similar documents for.
   *     @param options         Behaviour-customising object with the following fields:
   *                            • organizationId (string) ‑ tenant scope identifier (default
   *                              "default").
   *                            • filters (Record<string, any>) ‑ key/value pairs that must match a
   *                              document's metadata for it to be considered.
   *                            • topK (number) ‑ maximum number of results to return (default 20).
   *                            • threshold (number) ‑ minimum cosine similarity for a candidate to
   *                              be included (default 0.1).
   *                            • useEmbeddings (boolean) ‑ force the use of real embeddings instead
   *                              of heuristic vectors when an `EmbeddingService` instance is
   *                              present (defaults to `true` if an embedding service was supplied
   *                              to the constructor, otherwise `false`).
   *
   * Returns:
   *     A `Promise<VectorSearchResult[]>` ordered by most similar first. Each element contains the
   *     document `id`, similarity `score`, optional `content`, and stored `metadata`.
   *     If no matching documents exist, an empty array is returned.
   *
   * Error Handling:
   *     Any unexpected failure (e.g., embedding service outage) is caught and logged to console; the
   *     method then gracefully returns an empty array instead of propagating the error upstream.
   */
  async findSimilarDocuments(
    query: string,
    options: VectorSearchOptions = {}
  ): Promise<VectorSearchResult[]> {
    console.log('[VectorOperationsService] Finding similar documents');

    const {
      organizationId = 'default',
      filters = {},
      topK = 3,
      threshold = 0.1,
      useEmbeddings = !!this.embeddingService,
    } = options;

    try {
      this.searchCount++;

      // Prefer embedding index if available
      const embeddingIndex = await this.embeddingIndexPromise;

      // Generate query vector
      const queryVector = await this.generateQueryVector(query, useEmbeddings);

      if (embeddingIndex) {
        const indexResults = await embeddingIndex.searchSimilar(
          queryVector,
          topK,
          threshold,
          filters
        );

        return indexResults.map(r => ({
          id: r.documentId,
          score: r.similarity,
          content: r.content,
          metadata: r.metadata ?? {},
        }));
      }

      // === Legacy in-memory fallback ===

      // Get candidate documents for the organization
      const candidateDocIds = this.organizationIndex.get(organizationId) || new Set();
      if (candidateDocIds.size === 0) {
        console.log(
          '[VectorOperationsService] No vectors in store for organization:',
          organizationId
        );
        return [];
      }

      // Calculate similarities for organization documents
      const similarities: Array<VectorDocument & { similarity: number }> = [];

      for (const docId of candidateDocIds) {
        const vectorDoc = this.vectorStore.get(docId);
        if (vectorDoc && this.matchesFilters(vectorDoc, filters)) {
          const similarity = this.calculateSimilarity(queryVector, vectorDoc.vector);
          if (similarity >= threshold) {
            similarities.push({
              ...vectorDoc,
              similarity,
            });
          }
        }
      }

      const results = similarities.sort((a, b) => b.similarity - a.similarity).slice(0, topK);

      return results.map(item => ({
        id: item.id,
        score: item.similarity,
        content: item.content,
        metadata: item.metadata,
      }));
    } catch (error) {
      console.error('[VectorOperationsService] Vector search failed:', error);
      return [];
    }
  }

  /**
   * Add document vector to the store
   */
  async addDocumentVector(
    documentId: string,
    content: string,
    metadata: Record<string, any> = {},
    organizationId: string = 'default'
  ): Promise<void> {
    try {
      const vector = await this.generateQueryVector(content, !!this.embeddingService);

      // Store via embedding index if available
      const embeddingIndex = await this.embeddingIndexPromise;
      if (embeddingIndex) {
        await embeddingIndex.store(documentId, vector, content, metadata);
      }

      const vectorDoc: VectorDocument = {
        id: documentId,
        vector,
        content,
        metadata,
        organizationId,
        timestamp: new Date(),
      };

      this.vectorStore.set(documentId, vectorDoc);

      // Update organization index
      if (!this.organizationIndex.has(organizationId)) {
        this.organizationIndex.set(organizationId, new Set());
      }
      this.organizationIndex.get(organizationId)!.add(documentId);

      console.log('[VectorOperationsService] Added vector for document:', documentId);
    } catch (error) {
      console.error('[VectorOperationsService] Failed to add document vector:', error);
      throw error;
    }
  }

  /**
   * Add multiple documents from ProcessedDocument array
   */
  async addDocuments(
    documents: ProcessedDocument[],
    organizationId: string = 'default'
  ): Promise<void> {
    const validDocuments = documents.filter(doc => doc.content?.trim());

    if (validDocuments.length === 0) {
      return;
    }

    try {
      const embeddingIndex = await this.embeddingIndexPromise;

      // Use batch processing if we have an embedding service
      if (this.embeddingService) {
        const embeddings = await this.embeddingService.embedTexts(
          validDocuments.map(doc => doc.content)
        );

        // If embedding index available, batch store via index
        if (embeddingIndex) {
          const batch = validDocuments.map((doc, i) => ({
            documentId: doc.id,
            embedding: embeddings[i],
            content: doc.content,
            metadata: doc.metadata || {},
          }));
          await embeddingIndex.batchStore(batch);
        }

        for (let i = 0; i < validDocuments.length; i++) {
          const document = validDocuments[i];
          const embedding = embeddings[i];

          const vectorDoc: VectorDocument = {
            id: document.id,
            vector: embedding,
            content: document.content,
            metadata: document.metadata || {},
            organizationId,
            timestamp: new Date(),
          };

          this.vectorStore.set(document.id, vectorDoc);

          // Update organization index
          if (!this.organizationIndex.has(organizationId)) {
            this.organizationIndex.set(organizationId, new Set());
          }
          this.organizationIndex.get(organizationId)!.add(document.id);
        }
      } else {
        // Fallback to individual processing with simple vectors
        for (const document of validDocuments) {
          if (embeddingIndex) {
            const embedding = await this.generateQueryVector(document.content, true);
            await embeddingIndex.store(
              document.id,
              embedding,
              document.content,
              document.metadata || {}
            );
          }
          await this.addDocumentVector(
            document.id,
            document.content,
            document.metadata || {},
            organizationId
          );
        }
      }

      console.log(
        `[VectorOperationsService] Added ${validDocuments.length} documents to vector store`
      );
    } catch (error) {
      console.error('[VectorOperationsService] Failed to add documents:', error);
      throw error;
    }
  }

  /**
   * Remove document from vector store
   */
  removeDocument(documentId: string, organizationId: string = 'default'): boolean {
    // Attempt to remove from embedding index as well (async, fire & forget)
    void this.embeddingIndexPromise?.then(index => index?.remove(documentId).catch(() => {}));

    const removed = this.vectorStore.delete(documentId);

    // Remove from organization index
    const orgDocs = this.organizationIndex.get(organizationId);
    if (orgDocs) {
      orgDocs.delete(documentId);
      if (orgDocs.size === 0) {
        this.organizationIndex.delete(organizationId);
      }
    }

    return removed;
  }

  /**
   * Clear all vectors for an organization
   */
  clearOrganization(organizationId: string): void {
    void this.embeddingIndexPromise?.then(index => index?.clearOrganization(organizationId));
    const docIds = this.organizationIndex.get(organizationId);
    if (docIds) {
      for (const docId of docIds) {
        this.vectorStore.delete(docId);
      }
      this.organizationIndex.delete(organizationId);
    }
  }

  /**
   * Get vector store statistics
   */
  getStats(): {
    documentCount: number;
    organizationsCount: number;
    vectorDimensions: number;
    searchCount: number;
    hasEmbeddingService: boolean;
    embeddingStats?: any;
  } {
    const firstVector = Array.from(this.vectorStore.values())[0];

    return {
      documentCount: this.vectorStore.size,
      organizationsCount: this.organizationIndex.size,
      vectorDimensions: firstVector?.vector.length || 0,
      searchCount: this.searchCount,
      hasEmbeddingService: !!this.embeddingService,
      embeddingStats: undefined,
    };
  }

  /**
   * Get documents for an organization
   */
  getOrganizationDocuments(organizationId: string): VectorDocument[] {
    const docIds = this.organizationIndex.get(organizationId) || new Set();
    const documents: VectorDocument[] = [];

    for (const docId of docIds) {
      const doc = this.vectorStore.get(docId);
      if (doc) {
        documents.push(doc);
      }
    }

    return documents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private async generateQueryVector(text: string, useEmbeddings: boolean): Promise<number[]> {
    if (useEmbeddings && this.embeddingService) {
      return await this.embeddingService.embedText(text);
    }

    // Fallback to simple vector generation
    return this.createSimpleVector(text);
  }

  private createSimpleVector(text: string): number[] {
    // Create a simple 128-dimensional vector based on text characteristics
    const vector = new Array(128).fill(0);
    const words = text.toLowerCase().split(/\s+/);

    // Use text statistics to create vector
    for (let i = 0; i < words.length && i < 128; i++) {
      const word = words[i];
      if (word.length > 0) {
        vector[i] = (word.charCodeAt(0) - 97) / 26; // Normalize to 0-1
      }
    }

    // Add some randomness based on text length and content
    const textHash = this.simpleHash(text);
    for (let i = 0; i < vector.length; i++) {
      vector[i] += (textHash % (i + 1)) / 1000; // Small random component
    }

    return vector;
  }

  private calculateSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  private matchesFilters(vectorDoc: VectorDocument, filters: Record<string, any>): boolean {
    if (Object.keys(filters).length === 0) {
      return true;
    }

    for (const [key, value] of Object.entries(filters)) {
      if (vectorDoc.metadata[key] !== value) {
        return false;
      }
    }

    return true;
  }

  private simpleHash(text: string): number {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
