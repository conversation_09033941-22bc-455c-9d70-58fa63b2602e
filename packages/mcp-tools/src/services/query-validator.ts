/**
 * SQL **read-only safety** guard used by the data tools to block dangerous write/DDL statements.
 * The validator employs a lightweight whitelist approach: only statements whose first non-comment
 * token is `SELECT` and that do **NOT** contain black-listed keywords (DROP, DELETE, …) are
 * considered safe.
 */
export class QueryValidator {
  private static readonly DANGEROUS_KEYWORDS = [
    'DROP',
    'DELETE',
    'UPDATE',
    'INSERT',
    'ALTER',
    'CREATE',
    'TRUNCATE',
    'GRANT',
    'REVOKE',
    'EXECUTE',
    'EXEC',
    'CALL',
    'MERGE',
    'REPLACE',
  ];

  /**
   * Return `true` when the provided SQL string is deemed safe.
   *
   * @param query – Raw SQL string.
   * @returns Boolean indicating safety.
   */
  static isQuerySafe(query: string): boolean {
    if (!query || typeof query !== 'string') {
      return false;
    }

    const upperQuery = query.toUpperCase().trim();

    // Check if query starts with SELECT (allowing for whitespace and comments)
    const cleanQuery = upperQuery.replace(/^(\s|\/\*.*?\*\/|--.*?\n)*/g, '');
    if (!cleanQuery.startsWith('SELECT')) {
      return false;
    }

    // Check for dangerous keywords
    return !this.DANGEROUS_KEYWORDS.some(keyword => upperQuery.includes(keyword));
  }

  /**
   * Convenience wrapper that throws an `Error` when {@link isQuerySafe} returns `false`.
   *
   * @throws Error If the query contains dangerous operations.
   */
  static validateQuery(query: string): void {
    if (!this.isQuerySafe(query)) {
      throw new Error(
        'Query contains potentially unsafe operations. Only SELECT statements are allowed.'
      );
    }
  }
}
