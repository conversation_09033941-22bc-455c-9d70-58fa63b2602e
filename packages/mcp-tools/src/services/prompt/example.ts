import { PromptManager } from './prompt-manager';
import { InMemoryPromptStorage } from './storage/in-memory-prompt-storage';
import { getDefaultTemplate } from './default-templates';
import { PromptCreateRequest, PromptRenderingContext } from '../../types/prompt';

// Example usage of the prompt management system
async function demonstratePromptManagement() {
  console.log('🚀 Starting Prompt Management System Demo\n');

  // Initialize the prompt manager
  const storage = new InMemoryPromptStorage({
    dataFilePath: '/tmp/demo-prompts.json',
    autoSave: true,
  });

  const promptManager = new PromptManager(
    {
      defaultOrganizationId: 'demo-org',
      defaultProjectId: 'demo-project',
      cacheEnabled: true,
      cacheTTL: 300000, // 5 minutes
      autoIncrementVersion: true,
    },
    storage
  );

  try {
    // 1. Create a prompt from a default template
    console.log('📝 Creating prompt from default template...');
    const template = getDefaultTemplate('traditional-rag');
    if (!template) {
      throw new Error('Default template not found');
    }

    const prompt = await promptManager.createFromTemplate(template, 'demo-org', 'demo-user', {
      name: 'my-rag-prompt',
      tags: ['demo', 'rag'],
      labels: ['demo'],
    });

    console.log(`✅ Created prompt: ${prompt.name} v${prompt.version}`);
    console.log(`   ID: ${prompt.id}`);
    console.log(`   Type: ${prompt.type}`);
    console.log(`   Tags: ${prompt.tags.join(', ')}\n`);

    // 2. Create a custom text prompt
    console.log('📝 Creating custom text prompt...');
    const customRequest: PromptCreateRequest = {
      organizationId: 'demo-org',
      projectId: 'demo-project',
      createdBy: 'demo-user',
      prompt: 'Hello {{name}}, welcome to {{platform}}! Your user ID is {{userId}}.',
      name: 'welcome-message',
      version: 1,
      type: 'text',
      config: {
        temperature: 0.7,
        includeConversationHistory: true,
        responseFormat: 'natural',
      },
      tags: ['welcome', 'custom'],
      labels: ['demo'],
    };

    const customPrompt = await promptManager.createPrompt(customRequest);
    console.log(`✅ Created custom prompt: ${customPrompt.name} v${customPrompt.version}\n`);

    // 3. Render the custom prompt with variables
    console.log('🎨 Rendering prompt with variables...');
    const context: PromptRenderingContext = {
      variables: {
        name: 'Alice',
        platform: 'Anter AI Platform',
        userId: 'user-123',
      },
    };

    const rendered = await promptManager.renderPromptByName('demo-org', 'welcome-message', context);

    console.log('📤 Rendered prompt:');
    console.log(rendered.renderedPrompt);
    console.log(`\n✅ Used variables: ${rendered.usedVariables.join(', ')}`);
    console.log(`✅ Missing variables: ${rendered.missingVariables.join(', ') || 'none'}`);
    console.log(`✅ Warnings: ${rendered.warnings.join(', ') || 'none'}\n`);

    // 4. Search for prompts
    console.log('🔍 Searching for prompts...');
    const searchResults = await promptManager.searchPrompts({
      organizationId: 'demo-org',
      tags: ['demo'],
    });

    console.log(`✅ Found ${searchResults.length} prompts:`);
    searchResults.forEach(p => {
      console.log(`   - ${p.name} v${p.version} (${p.type})`);
    });
    console.log();

    // 5. Get statistics
    console.log('📊 Getting statistics...');
    const stats = await promptManager.getStats();
    console.log(`✅ Total prompts: ${stats.totalPrompts}`);
    console.log(`✅ Total dependencies: ${stats.totalDependencies}`);
    console.log(`✅ Organizations: ${stats.organizations.join(', ')}\n`);

    // 6. Update a prompt
    console.log('✏️  Updating prompt...');
    const updated = await promptManager.updatePrompt(customPrompt.id, {
      tags: ['welcome', 'custom', 'updated'],
      commitMessage: 'Added updated tag',
    });

    if (updated) {
      console.log(`✅ Updated prompt: ${updated.name}`);
      console.log(`   New tags: ${updated.tags.join(', ')}\n`);
    }

    // 7. Create a chat prompt
    console.log('💬 Creating chat prompt...');
    const chatRequest: PromptCreateRequest = {
      organizationId: 'demo-org',
      projectId: 'demo-project',
      createdBy: 'demo-user',
      prompt: [
        { role: 'system', content: 'You are {{assistantName}}, a helpful AI assistant.' },
        { role: 'user', content: 'Hello {{userName}}, how can I help you today?' },
      ],
      name: 'chat-assistant',
      version: 1,
      type: 'chat',
      config: {
        temperature: 0.8,
        includeConversationHistory: true,
        responseFormat: 'natural',
      },
      tags: ['chat', 'assistant'],
      labels: ['demo'],
    };

    const chatPrompt = await promptManager.createPrompt(chatRequest);
    console.log(`✅ Created chat prompt: ${chatPrompt.name} v${chatPrompt.version}\n`);

    // 8. Render chat prompt
    console.log('🎨 Rendering chat prompt...');
    const chatContext: PromptRenderingContext = {
      variables: {
        assistantName: 'Anter Assistant',
        userName: 'Bob',
      },
    };

    const renderedChat = await promptManager.renderPromptByName(
      'demo-org',
      'chat-assistant',
      chatContext
    );

    console.log('📤 Rendered chat prompt:');
    if (Array.isArray(renderedChat.renderedPrompt)) {
      renderedChat.renderedPrompt.forEach((msg, index) => {
        console.log(`   ${index + 1}. [${msg.role}]: ${msg.content}`);
      });
    }
    console.log();

    console.log('🎉 Demo completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Created prompts from templates and custom requests');
    console.log('- Rendered prompts with variable substitution');
    console.log('- Searched and updated prompts');
    console.log('- Demonstrated both text and chat prompt types');
  } catch (error) {
    console.error('❌ Error during demo:', error);
  } finally {
    // Clean up
    await promptManager.close();
  }
}

// Export for use in other modules
export { demonstratePromptManagement };

// Run the demo if this file is executed directly
if (require.main === module) {
  demonstratePromptManagement().catch(console.error);
}
