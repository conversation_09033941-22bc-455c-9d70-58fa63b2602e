import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptValidationResult,
  PromptRenderingContext,
  PromptRenderingResult,
  PromptManagerConfig,
  PromptTemplate,
} from '../../types/prompt';
import { LoggerAdapter } from '../../types';
import { PromptStorage } from './storage/prompt-storage.interface';
import { InMemoryPromptStorage } from './storage/in-memory-prompt-storage';
import { LangfusePromptStorage } from './storage/langfuse-prompt-storage';
import { HybridPromptStorage } from './storage/hybrid-prompt-storage';
import { PromptRenderer } from './prompt-renderer';

export class PromptManager {
  private storage: PromptStorage;
  private renderer: PromptRenderer;
  private config: PromptManagerConfig;
  private logger?: LoggerAdapter;
  private cache: Map<string, { prompt: Prompt; timestamp: number }> = new Map();

  constructor(config: PromptManagerConfig = {}, storage?: PromptStorage, logger?: LoggerAdapter) {
    this.config = {
      defaultOrganizationId: 'default',
      defaultProjectId: 'default',
      cacheEnabled: true,
      cacheTTL: 300000, // 5 minutes
      maxCacheSize: 1000,
      enableVersioning: true,
      autoIncrementVersion: true,
      ...config,
    };

    // Initialize storage based on config
    if (storage) {
      this.storage = storage;
    } else {
      this.storage = this.createStorageFromConfig(config, logger);
    }

    this.renderer = new PromptRenderer(logger);
    this.logger = logger;
  }

  /**
   * Creates a new prompt
   */
  async createPrompt(request: PromptCreateRequest): Promise<Prompt> {
    try {
      // Validate the request
      const validation = await this.validateCreateRequest(request);
      if (!validation.isValid) {
        throw new Error(`Invalid prompt creation request: ${validation.errors.join(', ')}`);
      }

      // Auto-increment version if enabled
      if (this.config.autoIncrementVersion && !request.version) {
        const latest = await this.storage.getLatestPrompt(request.organizationId, request.name);
        request.version = latest ? latest.version + 1 : 1;
      }

      // Create the prompt
      const prompt = await this.storage.createPrompt(request);

      // Clear cache for this prompt name
      this.clearCacheForPrompt(request.organizationId, request.name, request.projectId);

      this.logger?.info(
        `[PromptManager] Created prompt: ${prompt.id} (${prompt.name} v${prompt.version})`
      );
      return prompt;
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to create prompt: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Gets a prompt by ID
   */
  async getPrompt(id: string): Promise<Prompt | null> {
    try {
      return await this.storage.getPrompt(id);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Gets a prompt by name and optionally version
   */
  async getPromptByName(
    organizationId: string,
    name: string,
    version?: number,
    projectId?: string,
    useCache: boolean = true
  ): Promise<Prompt | null> {
    try {
      const cacheKey = `${organizationId}:${projectId || 'no-project'}:${name}:${version || 'latest'}`;

      // Check cache first
      if (useCache && this.config.cacheEnabled) {
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL!) {
          this.logger?.debug(`[PromptManager] Cache hit for ${cacheKey}`);
          return cached.prompt;
        }
      }

      // Get from storage
      const prompt = version
        ? await this.storage.getPromptByName(organizationId, name, version, projectId)
        : await this.storage.getLatestPrompt(organizationId, name, projectId);

      // Cache the result
      if (prompt && this.config.cacheEnabled) {
        this.cache.set(cacheKey, { prompt, timestamp: Date.now() });
        this.cleanupCache();
      }

      return prompt;
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get prompt ${name}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Updates an existing prompt
   */
  async updatePrompt(id: string, request: PromptUpdateRequest): Promise<Prompt | null> {
    try {
      const prompt = await this.storage.getPrompt(id);
      if (!prompt) {
        throw new Error(`Prompt with ID ${id} not found`);
      }

      // Validate the update
      const validation = await this.validateUpdateRequest(request);
      if (!validation.isValid) {
        throw new Error(`Invalid prompt update request: ${validation.errors.join(', ')}`);
      }

      // Update the prompt
      const updatedPrompt = await this.storage.updatePrompt(id, request);

      if (updatedPrompt) {
        // Clear cache for this prompt
        this.clearCacheForPrompt(
          updatedPrompt.organizationId,
          updatedPrompt.name,
          updatedPrompt.projectId
        );
        this.logger?.info(`[PromptManager] Updated prompt: ${id}`);
      }

      return updatedPrompt;
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to update prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Deletes a prompt
   */
  async deletePrompt(id: string): Promise<boolean> {
    try {
      const prompt = await this.storage.getPrompt(id);
      if (!prompt) {
        return false;
      }

      const deleted = await this.storage.deletePrompt(id);

      if (deleted) {
        // Clear cache for this prompt
        this.clearCacheForPrompt(prompt.organizationId, prompt.name, prompt.projectId);
        this.logger?.info(`[PromptManager] Deleted prompt: ${id}`);
      }

      return deleted;
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to delete prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Searches for prompts based on criteria
   */
  async searchPrompts(query: PromptQuery): Promise<Prompt[]> {
    try {
      return await this.storage.searchPrompts(query);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to search prompts: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Gets all prompts for a project
   */
  async getPromptsByProject(organizationId: string, projectId: string): Promise<Prompt[]> {
    try {
      // Filter by organization first, then by project
      const orgPrompts = await this.storage.getPromptsByOrganization(organizationId);
      return orgPrompts.filter(prompt => prompt.projectId === projectId);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get prompts for project ${projectId}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async getPromptsByOrganization(organizationId: string): Promise<Prompt[]> {
    try {
      return await this.storage.getPromptsByOrganization(organizationId);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get prompts for organization ${organizationId}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Gets active prompts
   */
  async getActivePrompts(organizationId?: string, projectId?: string): Promise<Prompt[]> {
    try {
      return await this.storage.getActivePrompts(organizationId, projectId);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get active prompts: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Renders a prompt with variables
   */
  async renderPrompt(
    promptId: string,
    context: PromptRenderingContext
  ): Promise<PromptRenderingResult> {
    try {
      const prompt = await this.getPrompt(promptId);
      if (!prompt) {
        throw new Error(`Prompt with ID ${promptId} not found`);
      }

      return this.renderer.renderPrompt(prompt, context);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to render prompt ${promptId}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Renders a prompt by name
   */
  async renderPromptByName(
    organizationId: string,
    name: string,
    context: PromptRenderingContext,
    version?: number,
    projectId?: string
  ): Promise<PromptRenderingResult> {
    try {
      const prompt = await this.getPromptByName(organizationId, name, version, projectId);
      if (!prompt) {
        const projectInfo = projectId ? `project ${projectId}` : 'organization';
        throw new Error(`Prompt ${name} not found in ${projectInfo} ${organizationId}`);
      }

      return this.renderer.renderPrompt(prompt, context);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to render prompt ${name}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Creates a prompt from a template
   */
  async createFromTemplate(
    template: PromptTemplate,
    organizationId: string,
    createdBy: string,
    overrides: Partial<PromptCreateRequest> = {}
  ): Promise<Prompt> {
    try {
      const request: PromptCreateRequest = {
        organizationId,
        createdBy,
        prompt: template.prompt,
        name: template.name,
        version: 1,
        type: template.type,

        config: template.config,
        tags: template.tags,
        labels: template.labels,
        ...overrides,
      };

      return await this.createPrompt(request);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to create prompt from template: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Validates a prompt
   */
  async validatePrompt(prompt: Prompt): Promise<PromptValidationResult> {
    try {
      return await this.storage.validatePrompt(prompt);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to validate prompt: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Gets prompt statistics
   */
  async getStats(): Promise<{
    totalPrompts: number;
    totalDependencies: number;
    organizations: string[];
  }> {
    try {
      return await this.storage.getStats();
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to get stats: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Clears the cache
   */
  clearCache(): void {
    this.cache.clear();
    this.logger?.info(`[PromptManager] Cache cleared`);
  }

  /**
   * Closes the prompt manager and storage
   */
  async close(): Promise<void> {
    try {
      await this.storage.close();
      this.clearCache();
      this.logger?.info(`[PromptManager] Closed`);
    } catch (error) {
      this.logger?.error(
        `[PromptManager] Failed to close: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  private async validateCreateRequest(
    request: PromptCreateRequest
  ): Promise<PromptValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!request.organizationId?.trim()) {
      errors.push('Organization ID is required');
    }

    if (!request.name?.trim()) {
      errors.push('Prompt name is required');
    }

    if (!request.createdBy?.trim()) {
      errors.push('Created by is required');
    }

    if (!request.prompt) {
      errors.push('Prompt content is required');
    }

    if (!request.type || !['text', 'chat'].includes(request.type)) {
      errors.push('Prompt type must be either "text" or "chat"');
    }

    if (request.version !== undefined && request.version < 1) {
      errors.push('Version must be at least 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private async validateUpdateRequest(
    request: PromptUpdateRequest
  ): Promise<PromptValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (request.prompt !== undefined) {
      if (typeof request.prompt !== 'string' && !Array.isArray(request.prompt)) {
        errors.push('Prompt must be a string or array of messages');
      }
    }

    if (
      request.config?.temperature !== undefined &&
      (request.config.temperature < 0 || request.config.temperature > 2)
    ) {
      warnings.push('Temperature should be between 0 and 2');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private clearCacheForPrompt(organizationId: string, name: string, projectId?: string): void {
    if (!this.config.cacheEnabled) return;

    const keysToDelete: string[] = [];
    const projectPrefix = projectId
      ? `${organizationId}:${projectId}:${name}:`
      : `${organizationId}:no-project:${name}:`;

    for (const key of Array.from(this.cache.keys())) {
      if (key.startsWith(projectPrefix)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  private cleanupCache(): void {
    if (!this.config.cacheEnabled || !this.config.maxCacheSize) return;

    if (this.cache.size > this.config.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toDelete = entries.slice(0, this.cache.size - this.config.maxCacheSize);
      toDelete.forEach(([key]) => this.cache.delete(key));

      this.logger?.debug(`[PromptManager] Cleaned up ${toDelete.length} cache entries`);
    }
  }

  private createStorageFromConfig(
    config: PromptManagerConfig,
    logger?: LoggerAdapter
  ): PromptStorage {
    const storageType = config.storageType || 'memory';

    switch (storageType) {
      case 'langfuse':
        return new LangfusePromptStorage({ logger });
      case 'hybrid':
        const primaryStorage = new LangfusePromptStorage({ logger });
        const fallbackStorage = new InMemoryPromptStorage({ logger });
        return new HybridPromptStorage({
          primaryStorage,
          fallbackStorage,
          logger,
        });
      case 'memory':
      default:
        return new InMemoryPromptStorage({ logger });
    }
  }
}
