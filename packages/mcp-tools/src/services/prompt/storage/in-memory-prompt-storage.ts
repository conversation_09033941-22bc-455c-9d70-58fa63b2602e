import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { randomBytes } from 'crypto';
import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptDependency,
  PromptValidationResult,
  DependencyValidationResult,
  DependencyResolutionResult,
  PromptGraph,
} from '../../../types/prompt';
import { LoggerAdapter } from '../../../types';
import { PromptStorage, StorageConfig } from './prompt-storage.interface';

export interface InMemoryPromptStorageConfig extends StorageConfig {
  dataFilePath?: string;
  autoSave?: boolean;
  autoSaveInterval?: number;
  backupEnabled?: boolean;
  maxBackups?: number;
}

export class InMemoryPromptStorage implements PromptStorage {
  private prompts: Map<string, Prompt> = new Map();
  private dependencies: Map<string, PromptDependency> = new Map();
  private config: InMemoryPromptStorageConfig;
  private logger?: LoggerAdapter;
  private autoSaveTimer?: NodeJS.Timeout;

  constructor(config: InMemoryPromptStorageConfig = {}) {
    this.config = {
      dataFilePath: join(process.cwd(), 'data', 'prompts.json'),
      autoSave: true,
      autoSaveInterval: 30000,
      backupEnabled: true,
      maxBackups: 5,
      ...config,
    };
    this.logger = config.logger;
    this.loadData();
    this.setupAutoSave();
  }

  private setupAutoSave(): void {
    if (this.config.autoSave && this.config.autoSaveInterval) {
      this.autoSaveTimer = setInterval(() => {
        this.saveData();
      }, this.config.autoSaveInterval);
    }
  }

  private loadData(): void {
    try {
      if (existsSync(this.config.dataFilePath!)) {
        const fileContent = readFileSync(this.config.dataFilePath!, 'utf-8');

        // Validate JSON structure before parsing
        if (!fileContent.trim()) {
          this.logger?.warn('[InMemoryPromptStorage] Empty data file, starting with empty storage');
          return;
        }

        const data = JSON.parse(fileContent);

        // Validate data structure
        if (!data || typeof data !== 'object') {
          throw new Error('Invalid data file format');
        }

        if (data.prompts) {
          if (!Array.isArray(data.prompts)) {
            throw new Error('Prompts must be an array');
          }

          this.prompts.clear();
          for (const prompt of data.prompts) {
            if (prompt && typeof prompt === 'object' && prompt.id) {
              prompt.createdAt = new Date(prompt.createdAt);
              prompt.updatedAt = new Date(prompt.updatedAt);
              this.prompts.set(prompt.id, prompt);
            }
          }
        }

        if (data.dependencies) {
          if (!Array.isArray(data.dependencies)) {
            throw new Error('Dependencies must be an array');
          }

          this.dependencies.clear();
          for (const dep of data.dependencies) {
            if (dep && typeof dep === 'object' && dep.id) {
              dep.createdAt = new Date(dep.createdAt);
              dep.updatedAt = new Date(dep.updatedAt);
              this.dependencies.set(dep.id, dep);
            }
          }
        }

        this.logger?.info(
          `[InMemoryPromptStorage] Loaded ${this.prompts.size} prompts and ${this.dependencies.size} dependencies`
        );
      }
    } catch (error) {
      this.logger?.warn(
        `[InMemoryPromptStorage] Failed to load data: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private saveData(): void {
    try {
      const dir = join(this.config.dataFilePath!, '..');
      if (!existsSync(dir)) {
        const fs = require('fs');
        fs.mkdirSync(dir, { recursive: true });
      }

      const data = {
        prompts: Array.from(this.prompts.values()),
        dependencies: Array.from(this.dependencies.values()),
        lastUpdated: new Date().toISOString(),
      };

      writeFileSync(this.config.dataFilePath!, JSON.stringify(data, null, 2));
      this.logger?.debug(`[InMemoryPromptStorage] Data saved successfully`);
    } catch (error) {
      this.logger?.error(
        `[InMemoryPromptStorage] Failed to save data: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private generateId(): string {
    const timestamp = Date.now();
    const randomPart = randomBytes(6).toString('hex');
    return `prompt_${timestamp}_${randomPart}`;
  }

  async createPrompt(request: PromptCreateRequest): Promise<Prompt> {
    const id = this.generateId();
    const now = new Date();

    const prompt: Prompt = {
      id,
      createdAt: now,
      updatedAt: now,
      organizationId: request.organizationId,
      projectId: request.projectId,
      createdBy: request.createdBy,
      prompt: request.prompt,
      name: request.name,
      version: request.version,
      type: request.type,
      config: request.config ?? {},
      tags: request.tags ?? [],
      labels: request.labels ?? [],
      commitMessage: request.commitMessage,
    };

    this.prompts.set(id, prompt);

    if (this.config.autoSave) {
      this.saveData();
    }

    this.logger?.info(
      `[InMemoryPromptStorage] Created prompt: ${id} (${request.name} v${request.version})`
    );
    return prompt;
  }

  async getPrompt(id: string): Promise<Prompt | null> {
    return this.prompts.get(id) || null;
  }

  async getPromptByName(
    organizationId: string,
    name: string,
    version?: number,
    projectId?: string
  ): Promise<Prompt | null> {
    for (const prompt of Array.from(this.prompts.values())) {
      if (
        prompt.organizationId === organizationId &&
        prompt.name === name &&
        (projectId === undefined || prompt.projectId === projectId)
      ) {
        if (version === undefined || prompt.version === version) {
          return prompt;
        }
      }
    }
    return null;
  }

  async getLatestPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    let latest: Prompt | null = null;
    let maxVersion = -1;

    for (const prompt of Array.from(this.prompts.values())) {
      if (
        prompt.organizationId === organizationId &&
        prompt.name === name &&
        prompt.version > maxVersion &&
        (projectId === undefined || prompt.projectId === projectId)
      ) {
        latest = prompt;
        maxVersion = prompt.version;
      }
    }

    return latest;
  }

  async updatePrompt(id: string, request: PromptUpdateRequest): Promise<Prompt | null> {
    const prompt = this.prompts.get(id);
    if (!prompt) {
      return null;
    }

    const updatedPrompt: Prompt = {
      ...prompt,
      ...request,
      updatedAt: new Date(),
    };

    this.prompts.set(id, updatedPrompt);

    if (this.config.autoSave) {
      this.saveData();
    }

    this.logger?.info(`[InMemoryPromptStorage] Updated prompt: ${id}`);
    return updatedPrompt;
  }

  async deletePrompt(id: string): Promise<boolean> {
    // Delete all dependencies first (cascade delete)
    await this.deleteDependenciesForPrompt(id);

    const deleted = this.prompts.delete(id);

    if (deleted && this.config.autoSave) {
      this.saveData();
      this.logger?.info(`[InMemoryPromptStorage] Deleted prompt: ${id}`);
    }

    return deleted;
  }

  async searchPrompts(query: PromptQuery): Promise<Prompt[]> {
    const results: Prompt[] = [];

    for (const prompt of Array.from(this.prompts.values())) {
      let matches = true;

      if (query.organizationId && prompt.organizationId !== query.organizationId) {
        matches = false;
      }

      if (
        query.projectId &&
        (prompt.projectId === undefined || prompt.projectId !== query.projectId)
      ) {
        matches = false;
      }

      if (query.name && prompt.name !== query.name) {
        matches = false;
      }

      if (query.version !== undefined && prompt.version !== query.version) {
        matches = false;
      }

      if (query.type && prompt.type !== query.type) {
        matches = false;
      }

      // Note: isActive is deprecated in favor of labels
      // Use labels like "production" to indicate active status
      if (query.labels && query.labels.length > 0) {
        const hasAllLabels = query.labels.every(label => prompt.labels.includes(label));
        if (!hasAllLabels) {
          matches = false;
        }
      }

      if (query.createdBy && prompt.createdBy !== query.createdBy) {
        matches = false;
      }

      if (query.tags && query.tags.length > 0) {
        const hasAllTags = query.tags.every(tag => prompt.tags.includes(tag));
        if (!hasAllTags) {
          matches = false;
        }
      }

      if (query.labels && query.labels.length > 0) {
        const hasAllLabels = query.labels.every(label => prompt.labels.includes(label));
        if (!hasAllLabels) {
          matches = false;
        }
      }

      if (matches) {
        results.push(prompt);
      }
    }

    return results.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  async getPromptsByProject(projectId: string): Promise<Prompt[]> {
    return Array.from(this.prompts.values())
      .filter(prompt => prompt.projectId === projectId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  async getPromptsByOrganization(organizationId: string): Promise<Prompt[]> {
    return Array.from(this.prompts.values())
      .filter(prompt => prompt.organizationId === organizationId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  async getActivePrompts(organizationId?: string, projectId?: string): Promise<Prompt[]> {
    let prompts = Array.from(this.prompts.values());

    if (organizationId) {
      prompts = prompts.filter(p => p.organizationId === organizationId);
    }

    if (projectId) {
      prompts = prompts.filter(p => p.projectId === projectId);
    }

    // Filter for prompts with "production" label (active prompts)
    return prompts
      .filter(prompt => prompt.labels.includes('production'))
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  /**
   * Get prompts by specific labels
   * @param labels Array of labels to filter by
   * @param organizationId Optional organization filter
   * @param projectId Optional project filter
   */
  async getPromptsByLabels(
    labels: string[],
    organizationId?: string,
    projectId?: string
  ): Promise<Prompt[]> {
    let prompts = Array.from(this.prompts.values());

    if (organizationId) {
      prompts = prompts.filter(p => p.organizationId === organizationId);
    }

    if (projectId) {
      prompts = prompts.filter(p => p.projectId === projectId);
    }

    // Filter for prompts that have all specified labels
    return prompts
      .filter(prompt => labels.every(label => prompt.labels.includes(label)))
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  /**
   * Get the production prompt for a specific name and project
   * @param organizationId Organization ID
   * @param name Prompt name
   * @param projectId Optional project ID
   */
  async getProductionPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    const prompts = await this.getPromptsByLabels(['production'], organizationId, projectId);
    return prompts.find(p => p.name === name) || null;
  }

  async validatePrompt(prompt: Prompt): Promise<PromptValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!prompt.name || typeof prompt.name !== 'string' || !prompt.name.trim()) {
      errors.push('Prompt name is required');
    }

    if (prompt.version < 1) {
      errors.push('Prompt version must be at least 1');
    }

    if (
      !prompt.organizationId ||
      typeof prompt.organizationId !== 'string' ||
      !prompt.organizationId.trim()
    ) {
      errors.push('Organization ID is required');
    }

    if (!prompt.createdBy.trim()) {
      errors.push('Created by is required');
    }

    if (prompt.type === 'chat') {
      if (typeof prompt.prompt === 'string') {
        errors.push('Chat prompts must be an array of messages');
      } else if (!Array.isArray(prompt.prompt)) {
        errors.push('Chat prompt must be an array');
      } else {
        for (const message of prompt.prompt) {
          if (
            !message.role ||
            !['system', 'user', 'assistant', 'function'].includes(message.role)
          ) {
            errors.push('Invalid message role');
          }
          if (!message.content || typeof message.content !== 'string') {
            errors.push('Message content is required and must be a string');
          }
        }
      }
    } else if (prompt.type === 'text') {
      if (typeof prompt.prompt !== 'string') {
        errors.push('Text prompts must be a string');
      } else if (!prompt.prompt.trim()) {
        errors.push('Text prompt content is required');
      }
    }

    if (
      prompt.config.temperature !== undefined &&
      (prompt.config.temperature < 0 || prompt.config.temperature > 2)
    ) {
      warnings.push('Temperature should be between 0 and 2');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  async createDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<PromptDependency> {
    // Validate parent exists
    const parentPrompt = await this.getPrompt(dependency.parentId);
    if (!parentPrompt) {
      throw new Error(`Parent prompt with id ${dependency.parentId} does not exist`);
    }

    // Validate child exists (if childVersion is specified)
    if (dependency.childVersion) {
      const childPrompt = await this.getPromptByName(
        dependency.organizationId,
        dependency.childName,
        dependency.childVersion,
        dependency.projectId
      );
      if (!childPrompt) {
        throw new Error(
          `Child prompt ${dependency.childName} v${dependency.childVersion} does not exist`
        );
      }
    }

    // Check for circular dependencies
    const hasCircular = await this.hasCircularDependency(dependency);
    if (hasCircular) {
      throw new Error(`Circular dependency detected for prompt ${dependency.parentId}`);
    }

    const id = this.generateId();
    const now = new Date();

    const newDependency: PromptDependency = {
      id,
      createdAt: now,
      updatedAt: now,
      ...dependency,
    };

    this.dependencies.set(id, newDependency);

    if (this.config.autoSave) {
      this.saveData();
    }

    this.logger?.info(`[InMemoryPromptStorage] Created dependency: ${id}`);
    return newDependency;
  }

  async getDependencies(parentId: string): Promise<PromptDependency[]> {
    return Array.from(this.dependencies.values()).filter(dep => dep.parentId === parentId);
  }

  async getDependenciesByChild(
    organizationId: string,
    childName: string,
    childVersion?: number,
    childLabel?: string
  ): Promise<PromptDependency[]> {
    return Array.from(this.dependencies.values()).filter(
      dep =>
        dep.organizationId === organizationId &&
        dep.childName === childName &&
        (childVersion === undefined || dep.childVersion === childVersion) &&
        (childLabel === undefined || dep.childLabel === childLabel)
    );
  }

  async deleteDependency(id: string): Promise<boolean> {
    const deleted = this.dependencies.delete(id);

    if (deleted && this.config.autoSave) {
      this.saveData();
      this.logger?.info(`[InMemoryPromptStorage] Deleted dependency: ${id}`);
    }

    return deleted;
  }

  async deleteDependenciesForPrompt(parentId: string): Promise<number> {
    const keysToDelete: string[] = [];
    for (const [id, dep] of Array.from(this.dependencies.entries())) {
      if (dep.parentId === parentId) {
        keysToDelete.push(id);
      }
    }

    keysToDelete.forEach(key => this.dependencies.delete(key));

    if (keysToDelete.length > 0 && this.config.autoSave) {
      this.saveData();
      this.logger?.info(
        `[InMemoryPromptStorage] Deleted ${keysToDelete.length} dependencies for prompt: ${parentId}`
      );
    }

    return keysToDelete.length;
  }

  async getStats(): Promise<{
    totalPrompts: number;
    totalDependencies: number;
    organizations: string[];
  }> {
    const organizations = new Set<string>();
    for (const prompt of Array.from(this.prompts.values())) {
      organizations.add(prompt.organizationId);
    }

    return {
      totalPrompts: this.prompts.size,
      totalDependencies: this.dependencies.size,
      organizations: Array.from(organizations),
    };
  }

  async close(): Promise<void> {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    this.saveData();
    this.logger?.info(`[InMemoryPromptStorage] Storage closed`);
  }

  /**
   * Check for circular dependencies in the dependency graph
   */
  private async hasCircularDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<boolean> {
    // First, find the child prompt that would be added
    const childPrompt = await this.getPromptByName(
      dependency.organizationId,
      dependency.childName,
      dependency.childVersion,
      dependency.projectId
    );

    if (!childPrompt) {
      // If child doesn't exist, no circular dependency possible
      return false;
    }

    // Check if adding this dependency would create a cycle
    // by checking if the child can reach the parent through existing dependencies
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const checkCircular = async (promptId: string): Promise<boolean> => {
      if (recursionStack.has(promptId)) {
        return true; // Circular dependency detected
      }
      if (visited.has(promptId)) {
        return false; // Already visited, no cycle
      }

      visited.add(promptId);
      recursionStack.add(promptId);

      // Check if we've reached the parent (which would create a cycle)
      if (promptId === dependency.parentId) {
        return true;
      }

      // Get all dependencies for this prompt
      const deps = await this.getDependencies(promptId);

      for (const dep of deps) {
        // Find the child prompt
        const depChildPrompt = await this.getPromptByName(
          dep.organizationId,
          dep.childName,
          dep.childVersion,
          dep.projectId
        );

        if (depChildPrompt && (await checkCircular(depChildPrompt.id))) {
          return true;
        }
      }

      recursionStack.delete(promptId);
      return false;
    };

    // Check if the child can reach the parent (which would create a cycle)
    return await checkCircular(childPrompt.id);
  }

  /**
   * Validate a dependency before creation
   */
  async validateDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<DependencyValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if parent exists
    const parentPrompt = await this.getPrompt(dependency.parentId);
    if (!parentPrompt) {
      errors.push(`Parent prompt with id ${dependency.parentId} does not exist`);
    }

    // Check if child exists (if version is specified)
    if (dependency.childVersion) {
      const childPrompt = await this.getPromptByName(
        dependency.organizationId,
        dependency.childName,
        dependency.childVersion,
        dependency.projectId
      );
      if (!childPrompt) {
        errors.push(
          `Child prompt ${dependency.childName} v${dependency.childVersion} does not exist`
        );
      }
    } else {
      // Check if any version of the child exists
      const childPrompts = await this.searchPrompts({
        organizationId: dependency.organizationId,
        projectId: dependency.projectId,
        name: dependency.childName,
      });
      if (childPrompts.length === 0) {
        warnings.push(`No versions found for child prompt ${dependency.childName}`);
      }
    }

    // Check for circular dependencies
    try {
      const hasCircular = await this.hasCircularDependency(dependency);
      if (hasCircular) {
        errors.push(`Circular dependency detected for prompt ${dependency.parentId}`);
      }
    } catch (error) {
      errors.push(`Error checking circular dependencies: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Resolve all dependencies for a prompt
   */
  async resolveDependencies(promptId: string): Promise<DependencyResolutionResult> {
    const resolvedPrompts: Prompt[] = [];
    const unresolvedDependencies: PromptDependency[] = [];
    const circularDependencies: string[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const resolvePrompt = async (id: string): Promise<void> => {
      if (recursionStack.has(id)) {
        circularDependencies.push(id);
        return;
      }
      if (visited.has(id)) {
        return;
      }

      visited.add(id);
      recursionStack.add(id);

      const prompt = await this.getPrompt(id);
      if (prompt) {
        resolvedPrompts.push(prompt);
      }

      const deps = await this.getDependencies(id);
      for (const dep of deps) {
        const childPrompt = await this.getPromptByName(
          dep.organizationId,
          dep.childName,
          dep.childVersion,
          dep.projectId
        );

        if (childPrompt) {
          await resolvePrompt(childPrompt.id);
        } else {
          unresolvedDependencies.push(dep);
        }
      }

      recursionStack.delete(id);
    };

    await resolvePrompt(promptId);

    return {
      resolvedPrompts,
      unresolvedDependencies,
      circularDependencies,
    };
  }

  /**
   * Get dependency graph for visualization
   */
  async getPromptGraph(promptId: string): Promise<PromptGraph> {
    const nodes: Array<{ id: string; name: string; version: number; type: string }> = [];
    const edges: Array<{ from: string; to: string; label: string }> = [];
    const visited = new Set<string>();

    const buildGraph = async (id: string): Promise<void> => {
      if (visited.has(id)) {
        return;
      }
      visited.add(id);

      const prompt = await this.getPrompt(id);
      if (prompt) {
        nodes.push({
          id: prompt.id,
          name: prompt.name,
          version: prompt.version,
          type: prompt.type,
        });

        const deps = await this.getDependencies(id);
        for (const dep of deps) {
          const childPrompt = await this.getPromptByName(
            dep.organizationId,
            dep.childName,
            dep.childVersion,
            dep.projectId
          );

          if (childPrompt) {
            edges.push({
              from: id,
              to: childPrompt.id,
              label: `${dep.childName}${dep.childVersion ? ` v${dep.childVersion}` : ''}`,
            });
            await buildGraph(childPrompt.id);
          }
        }
      }
    };

    await buildGraph(promptId);

    return { nodes, edges };
  }
}
