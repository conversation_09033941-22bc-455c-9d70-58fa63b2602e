import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptDependency,
  PromptValidationResult,
  DependencyValidationResult,
  DependencyResolutionResult,
  PromptGraph,
} from '../../../types/prompt';
import { LoggerAdapter } from '../../../types';

/**
 * Abstract interface for prompt storage implementations
 * This allows seamless switching between different storage backends
 */
export interface PromptStorage {
  // Core CRUD operations
  createPrompt(request: PromptCreateRequest): Promise<Prompt>;
  getPrompt(id: string): Promise<Prompt | null>;
  updatePrompt(id: string, request: PromptUpdateRequest): Promise<Prompt | null>;
  deletePrompt(id: string): Promise<boolean>;

  // Search and query operations
  searchPrompts(query: PromptQuery): Promise<Prompt[]>;
  getPromptByName(
    organizationId: string,
    name: string,
    version?: number,
    projectId?: string
  ): Promise<Prompt | null>;
  getLatestPrompt(organizationId: string, name: string, projectId?: string): Promise<Prompt | null>;
  getPromptsByProject(projectId: string): Promise<Prompt[]>;
  getPromptsByOrganization(organizationId: string): Promise<Prompt[]>;
  getActivePrompts(organizationId?: string, projectId?: string): Promise<Prompt[]>;
  getPromptsByLabels(
    labels: string[],
    organizationId?: string,
    projectId?: string
  ): Promise<Prompt[]>;
  getProductionPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null>;

  // Validation
  validatePrompt(prompt: Prompt): Promise<PromptValidationResult>;

  // Dependency management
  createDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<PromptDependency>;
  getDependencies(parentId: string): Promise<PromptDependency[]>;
  getDependenciesByChild(
    organizationId: string,
    childName: string,
    childVersion?: number,
    childLabel?: string
  ): Promise<PromptDependency[]>;
  deleteDependency(id: string): Promise<boolean>;
  deleteDependenciesForPrompt(parentId: string): Promise<number>;
  validateDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<DependencyValidationResult>;

  // Advanced features
  resolveDependencies(promptId: string): Promise<DependencyResolutionResult>;
  getPromptGraph(promptId: string): Promise<PromptGraph>;

  // Statistics and management
  getStats(): Promise<{ totalPrompts: number; totalDependencies: number; organizations: string[] }>;
  close(): Promise<void>;
}

/**
 * Configuration for storage implementations
 */
export interface StorageConfig {
  logger?: LoggerAdapter;
}

/**
 * Configuration specific to Langfuse storage
 */
export interface LangfuseStorageConfig extends StorageConfig {
  publicKey?: string;
  secretKey?: string;
  baseUrl?: string;
  sampleRate?: number;
  enabled?: boolean;
  requestTimeout?: number;
  flushAt?: number;
  flushInterval?: number;
}

/**
 * Configuration for hybrid storage (runs multiple backends)
 */
export interface HybridStorageConfig extends StorageConfig {
  primaryStorage: PromptStorage;
  fallbackStorage: PromptStorage;
  enableFallback?: boolean;
  syncOnWrite?: boolean;
}
