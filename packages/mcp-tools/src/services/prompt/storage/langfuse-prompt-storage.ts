import { Langfuse } from 'langfuse';
import { randomBytes } from 'crypto';
import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptDependency,
  PromptValidationResult,
  DependencyValidationResult,
  DependencyResolutionResult,
  PromptGraph,
} from '../../../types/prompt';
import { LoggerAdapter } from '../../../types';
import { PromptStorage, LangfuseStorageConfig } from './prompt-storage.interface';

/**
 * Langfuse storage adapter for prompt management
 * Integrates with Langfuse's prompt management API
 */
export class LangfusePromptStorage implements PromptStorage {
  private langfuse: Langfuse;
  private config: LangfuseStorageConfig;
  private logger?: LoggerAdapter;
  private prompts: Map<string, Prompt> = new Map(); // Mock storage for demo

  constructor(config: LangfuseStorageConfig = {}) {
    this.config = {
      publicKey: process.env.LANGFUSE_PUBLIC_KEY,
      secretKey: process.env.LANGFUSE_SECRET_KEY,
      baseUrl: process.env.LANGFUSE_BASEURL || 'https://cloud.langfuse.com',
      sampleRate: parseFloat(process.env.LANGFUSE_SAMPLE_RATE || '1'),
      enabled: process.env.LANGFUSE_TRACING_ENABLED !== 'false',
      requestTimeout: 10000,
      flushAt: 10,
      flushInterval: 5000,
      ...config,
    };

    this.logger = config.logger;

    if (!this.config.publicKey || !this.config.secretKey) {
      throw new Error('Langfuse public key and secret key are required');
    }

    this.langfuse = new Langfuse({
      publicKey: this.config.publicKey,
      secretKey: this.config.secretKey,
      baseUrl: this.config.baseUrl,
      sampleRate: Math.max(0, Math.min(1, this.config.sampleRate || 1)),
      enabled: this.config.enabled,
      requestTimeout: this.config.requestTimeout,
      flushAt: this.config.flushAt,
      flushInterval: this.config.flushInterval,
    });

    this.logger?.info('[LangfusePromptStorage] Initialized with Langfuse API');
  }

  async createPrompt(request: PromptCreateRequest): Promise<Prompt> {
    try {
      // For now, we'll create a mock implementation since Langfuse API has changed
      // In a real implementation, you would use the actual Langfuse API
      const promptId = `langfuse_${Date.now()}_${randomBytes(6).toString('hex')}`;
      const now = new Date();

      const prompt: Prompt = {
        id: promptId,
        createdAt: now,
        updatedAt: now,
        organizationId: request.organizationId,
        projectId: request.projectId,
        createdBy: request.createdBy,
        prompt: request.prompt,
        name: request.name,
        version: request.version,
        type: request.type,
        config: request.config || {},
        tags: request.tags || [],
        labels: request.labels || [],
        commitMessage: request.commitMessage,
      };

      // Store in mock storage
      this.prompts.set(promptId, prompt);

      this.logger?.info(
        `[LangfusePromptStorage] Created prompt: ${prompt.id} (${request.name} v${request.version})`
      );
      return prompt;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to create prompt: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async getPrompt(id: string): Promise<Prompt | null> {
    try {
      return this.prompts.get(id) || null;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to get prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async getPromptByName(
    organizationId: string,
    name: string,
    version?: number,
    projectId?: string
  ): Promise<Prompt | null> {
    try {
      // Mock implementation - search in local storage
      const prompts = Array.from(this.prompts.values()).filter(
        p =>
          p.organizationId === organizationId &&
          p.name === name &&
          (projectId === undefined || p.projectId === projectId)
      );

      if (prompts.length === 0) {
        return null;
      }

      // If version is specified, find exact match
      if (version !== undefined) {
        const exactMatch = prompts.find(p => p.version === version);
        return exactMatch || null;
      }

      // Return the latest version
      const latest = prompts.reduce((latest, current) =>
        current.version > latest.version ? current : latest
      );

      return latest;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to get prompt by name ${name}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async getLatestPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    return this.getPromptByName(organizationId, name, undefined, projectId);
  }

  async updatePrompt(id: string, request: PromptUpdateRequest): Promise<Prompt | null> {
    try {
      const existingPrompt = this.prompts.get(id);
      if (!existingPrompt) {
        return null;
      }

      const updatedPrompt: Prompt = {
        ...existingPrompt,
        ...request,
        updatedAt: new Date(),
      };

      this.prompts.set(id, updatedPrompt);
      this.logger?.info(`[LangfusePromptStorage] Updated prompt: ${id}`);
      return updatedPrompt;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to update prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async deletePrompt(id: string): Promise<boolean> {
    try {
      const deleted = this.prompts.delete(id);
      this.logger?.info(`[LangfusePromptStorage] Deleted prompt: ${id}`);
      return deleted;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to delete prompt ${id}: ${error instanceof Error ? error.message : String(error)}`
      );
      return false;
    }
  }

  async searchPrompts(query: PromptQuery): Promise<Prompt[]> {
    try {
      // For now, we'll use mock storage since Langfuse API has changed
      // In a real implementation, you would use the actual Langfuse API
      const results: Prompt[] = [];

      // Filter prompts from mock storage based on query
      for (const prompt of this.prompts.values()) {
        let matches = true;

        if (query.organizationId && prompt.organizationId !== query.organizationId) {
          matches = false;
        }
        if (query.projectId && prompt.projectId !== query.projectId) {
          matches = false;
        }
        if (query.name && prompt.name !== query.name) {
          matches = false;
        }
        if (query.version !== undefined && prompt.version !== query.version) {
          matches = false;
        }
        if (query.type && prompt.type !== query.type) {
          matches = false;
        }
        if (query.createdBy && prompt.createdBy !== query.createdBy) {
          matches = false;
        }
        if (query.tags && query.tags.length > 0) {
          const hasMatchingTag = query.tags.some(tag => prompt.tags.includes(tag));
          if (!hasMatchingTag) matches = false;
        }
        if (query.labels && query.labels.length > 0) {
          const hasMatchingLabel = query.labels.some(label => prompt.labels.includes(label));
          if (!hasMatchingLabel) matches = false;
        }

        if (matches) {
          results.push(prompt);
        }
      }

      return results;
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to search prompts: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async getPromptsByProject(projectId: string): Promise<Prompt[]> {
    return this.searchPrompts({ projectId });
  }

  async getPromptsByOrganization(organizationId: string): Promise<Prompt[]> {
    return this.searchPrompts({ organizationId });
  }

  async getActivePrompts(organizationId?: string, projectId?: string): Promise<Prompt[]> {
    return this.getPromptsByLabels(['production'], organizationId, projectId);
  }

  async getPromptsByLabels(
    labels: string[],
    organizationId?: string,
    projectId?: string
  ): Promise<Prompt[]> {
    const query: PromptQuery = { labels };
    if (organizationId) query.organizationId = organizationId;
    if (projectId) query.projectId = projectId;

    return this.searchPrompts(query);
  }

  async getProductionPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    const prompts = await this.getPromptsByLabels(['production'], organizationId, projectId);
    return prompts.find(p => p.name === name) || null;
  }

  async validatePrompt(prompt: Prompt): Promise<PromptValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!prompt.name || typeof prompt.name !== 'string' || !prompt.name.trim()) {
      errors.push('Prompt name is required');
    }

    if (prompt.version < 1) {
      errors.push('Prompt version must be at least 1');
    }

    if (
      !prompt.organizationId ||
      typeof prompt.organizationId !== 'string' ||
      !prompt.organizationId.trim()
    ) {
      errors.push('Organization ID is required');
    }

    if (!prompt.createdBy.trim()) {
      errors.push('Created by is required');
    }

    if (prompt.type === 'chat') {
      if (typeof prompt.prompt === 'string') {
        errors.push('Chat prompts must be an array of messages');
      } else if (!Array.isArray(prompt.prompt)) {
        errors.push('Chat prompt must be an array');
      } else {
        for (const message of prompt.prompt) {
          if (
            !message.role ||
            !['system', 'user', 'assistant', 'function'].includes(message.role)
          ) {
            errors.push('Invalid message role');
          }
          if (!message.content || typeof message.content !== 'string') {
            errors.push('Message content is required and must be a string');
          }
        }
      }
    } else if (prompt.type === 'text') {
      if (typeof prompt.prompt !== 'string') {
        errors.push('Text prompts must be a string');
      } else if (!prompt.prompt.trim()) {
        errors.push('Text prompt content is required');
      }
    }

    if (
      prompt.config.temperature !== undefined &&
      (prompt.config.temperature < 0 || prompt.config.temperature > 2)
    ) {
      warnings.push('Temperature should be between 0 and 2');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Dependency management - Langfuse doesn't have native dependency support, so we'll implement a simplified version
  async createDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<PromptDependency> {
    // For now, we'll store dependencies in the prompt's metadata
    // This is a simplified implementation - in production, you might want to use a separate table
    const parentPrompt = await this.getPrompt(dependency.parentId);
    if (!parentPrompt) {
      throw new Error(`Parent prompt with id ${dependency.parentId} does not exist`);
    }

    const newDependency: PromptDependency = {
      id: `dep_${Date.now()}_${randomBytes(6).toString('hex')}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...dependency,
    };

    // Store dependency in parent prompt's metadata
    const metadata = parentPrompt.config.metadata || {};
    const dependencies = metadata.dependencies || [];
    dependencies.push(newDependency);

    await this.updatePrompt(dependency.parentId, {
      config: {
        ...parentPrompt.config,
        metadata: {
          ...metadata,
          dependencies,
        },
      },
    });

    this.logger?.info(`[LangfusePromptStorage] Created dependency: ${newDependency.id}`);
    return newDependency;
  }

  async getDependencies(parentId: string): Promise<PromptDependency[]> {
    const parentPrompt = await this.getPrompt(parentId);
    if (!parentPrompt) {
      return [];
    }

    const metadata = parentPrompt.config.metadata || {};
    return metadata.dependencies || [];
  }

  async getDependenciesByChild(
    organizationId: string,
    childName: string,
    childVersion?: number,
    childLabel?: string
  ): Promise<PromptDependency[]> {
    // This would require scanning all prompts - simplified implementation
    const allPrompts = await this.getPromptsByOrganization(organizationId);
    const dependencies: PromptDependency[] = [];

    for (const prompt of allPrompts) {
      const deps = await this.getDependencies(prompt.id);
      for (const dep of deps) {
        if (
          dep.childName === childName &&
          (childVersion === undefined || dep.childVersion === childVersion) &&
          (childLabel === undefined || dep.childLabel === childLabel)
        ) {
          dependencies.push(dep);
        }
      }
    }

    return dependencies;
  }

  async deleteDependency(id: string): Promise<boolean> {
    // Find and remove dependency from parent prompt
    const allPrompts = await this.searchPrompts({});

    for (const prompt of allPrompts) {
      const deps = await this.getDependencies(prompt.id);
      const depIndex = deps.findIndex(d => d.id === id);

      if (depIndex !== -1) {
        deps.splice(depIndex, 1);
        await this.updatePrompt(prompt.id, {
          config: {
            ...prompt.config,
            metadata: {
              ...prompt.config.metadata,
              dependencies: deps,
            },
          },
        });
        this.logger?.info(`[LangfusePromptStorage] Deleted dependency: ${id}`);
        return true;
      }
    }

    return false;
  }

  async deleteDependenciesForPrompt(parentId: string): Promise<number> {
    const deps = await this.getDependencies(parentId);
    if (deps.length === 0) {
      return 0;
    }

    await this.updatePrompt(parentId, {
      config: {
        metadata: {
          dependencies: [],
        },
      },
    });

    this.logger?.info(
      `[LangfusePromptStorage] Deleted ${deps.length} dependencies for prompt: ${parentId}`
    );
    return deps.length;
  }

  async validateDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<DependencyValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if parent exists
    const parentPrompt = await this.getPrompt(dependency.parentId);
    if (!parentPrompt) {
      errors.push(`Parent prompt with id ${dependency.parentId} does not exist`);
    }

    // Check if child exists (if version is specified)
    if (dependency.childVersion) {
      const childPrompt = await this.getPromptByName(
        dependency.organizationId,
        dependency.childName,
        dependency.childVersion,
        dependency.projectId
      );
      if (!childPrompt) {
        errors.push(
          `Child prompt ${dependency.childName} v${dependency.childVersion} does not exist`
        );
      }
    } else {
      // Check if any version of the child exists
      const childPrompts = await this.searchPrompts({
        organizationId: dependency.organizationId,
        projectId: dependency.projectId,
        name: dependency.childName,
      });
      if (childPrompts.length === 0) {
        warnings.push(`No versions found for child prompt ${dependency.childName}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  async resolveDependencies(promptId: string): Promise<DependencyResolutionResult> {
    const resolvedPrompts: Prompt[] = [];
    const unresolvedDependencies: PromptDependency[] = [];
    const circularDependencies: string[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const resolvePrompt = async (id: string): Promise<void> => {
      if (recursionStack.has(id)) {
        circularDependencies.push(id);
        return;
      }
      if (visited.has(id)) {
        return;
      }

      visited.add(id);
      recursionStack.add(id);

      const prompt = await this.getPrompt(id);
      if (prompt) {
        resolvedPrompts.push(prompt);
      }

      const deps = await this.getDependencies(id);
      for (const dep of deps) {
        const childPrompt = await this.getPromptByName(
          dep.organizationId,
          dep.childName,
          dep.childVersion,
          dep.projectId
        );

        if (childPrompt) {
          await resolvePrompt(childPrompt.id);
        } else {
          unresolvedDependencies.push(dep);
        }
      }

      recursionStack.delete(id);
    };

    await resolvePrompt(promptId);

    return {
      resolvedPrompts,
      unresolvedDependencies,
      circularDependencies,
    };
  }

  async getPromptGraph(promptId: string): Promise<PromptGraph> {
    const nodes: Array<{ id: string; name: string; version: number; type: string }> = [];
    const edges: Array<{ from: string; to: string; label: string }> = [];
    const visited = new Set<string>();

    const buildGraph = async (id: string): Promise<void> => {
      if (visited.has(id)) {
        return;
      }
      visited.add(id);

      const prompt = await this.getPrompt(id);
      if (prompt) {
        nodes.push({
          id: prompt.id,
          name: prompt.name,
          version: prompt.version,
          type: prompt.type,
        });

        const deps = await this.getDependencies(id);
        for (const dep of deps) {
          const childPrompt = await this.getPromptByName(
            dep.organizationId,
            dep.childName,
            dep.childVersion,
            dep.projectId
          );

          if (childPrompt) {
            edges.push({
              from: id,
              to: childPrompt.id,
              label: `${dep.childName}${dep.childVersion ? ` v${dep.childVersion}` : ''}`,
            });
            await buildGraph(childPrompt.id);
          }
        }
      }
    };

    await buildGraph(promptId);

    return { nodes, edges };
  }

  async getStats(): Promise<{
    totalPrompts: number;
    totalDependencies: number;
    organizations: string[];
  }> {
    try {
      const allPrompts = await this.searchPrompts({});
      const organizations = new Set<string>();
      let totalDependencies = 0;

      for (const prompt of allPrompts) {
        organizations.add(prompt.organizationId);
        const deps = await this.getDependencies(prompt.id);
        totalDependencies += deps.length;
      }

      return {
        totalPrompts: allPrompts.length,
        totalDependencies,
        organizations: Array.from(organizations),
      };
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to get stats: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      await this.langfuse.shutdownAsync();
      this.logger?.info(`[LangfusePromptStorage] Storage closed`);
    } catch (error) {
      this.logger?.error(
        `[LangfusePromptStorage] Failed to close: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  // Helper methods
}
