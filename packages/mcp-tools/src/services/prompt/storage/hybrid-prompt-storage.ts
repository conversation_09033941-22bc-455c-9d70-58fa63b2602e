import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptDependency,
  PromptValidationResult,
  DependencyValidationResult,
  DependencyResolutionResult,
  PromptGraph,
} from '../../../types/prompt';
import { LoggerAdapter } from '../../../types';
import { PromptStorage, HybridStorageConfig } from './prompt-storage.interface';

/**
 * Hybrid storage adapter that can run multiple storage backends in parallel
 * Provides fallback capabilities and data synchronization
 */
export class HybridPromptStorage implements PromptStorage {
  private primaryStorage: PromptStorage;
  private fallbackStorage: PromptStorage;
  private config: HybridStorageConfig;
  private logger?: LoggerAdapter;

  constructor(config: HybridStorageConfig) {
    this.primaryStorage = config.primaryStorage;
    this.fallbackStorage = config.fallbackStorage;
    this.config = {
      enableFallback: true,
      syncOnWrite: true,
      ...config,
    };
    this.logger = config.logger;

    this.logger?.info('[HybridPromptStorage] Initialized with primary and fallback storage');
  }

  async createPrompt(request: PromptCreateRequest): Promise<Prompt> {
    try {
      // Try primary storage first
      const prompt = await this.primaryStorage.createPrompt(request);

      // Sync to fallback if enabled
      if (this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.createPrompt(request);
          this.logger?.debug('[HybridPromptStorage] Synced prompt to fallback storage');
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return prompt;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      // Try fallback if enabled
      if (this.config.enableFallback) {
        this.logger?.info('[HybridPromptStorage] Falling back to secondary storage');
        return await this.fallbackStorage.createPrompt(request);
      }

      throw primaryError;
    }
  }

  async getPrompt(id: string): Promise<Prompt | null> {
    try {
      // Try primary storage first
      const prompt = await this.primaryStorage.getPrompt(id);
      if (prompt) {
        return prompt;
      }

      // Try fallback if primary doesn't have it
      if (this.config.enableFallback) {
        return await this.fallbackStorage.getPrompt(id);
      }

      return null;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getPrompt(id);
      }

      throw primaryError;
    }
  }

  async getPromptByName(
    organizationId: string,
    name: string,
    version?: number,
    projectId?: string
  ): Promise<Prompt | null> {
    try {
      const prompt = await this.primaryStorage.getPromptByName(
        organizationId,
        name,
        version,
        projectId
      );
      if (prompt) {
        return prompt;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getPromptByName(organizationId, name, version, projectId);
      }

      return null;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getPromptByName(organizationId, name, version, projectId);
      }

      throw primaryError;
    }
  }

  async getLatestPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    try {
      const prompt = await this.primaryStorage.getLatestPrompt(organizationId, name, projectId);
      if (prompt) {
        return prompt;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getLatestPrompt(organizationId, name, projectId);
      }

      return null;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getLatestPrompt(organizationId, name, projectId);
      }

      throw primaryError;
    }
  }

  async updatePrompt(id: string, request: PromptUpdateRequest): Promise<Prompt | null> {
    try {
      const prompt = await this.primaryStorage.updatePrompt(id, request);

      if (prompt && this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.updatePrompt(id, request);
          this.logger?.debug('[HybridPromptStorage] Synced update to fallback storage');
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync update to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return prompt;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.updatePrompt(id, request);
      }

      throw primaryError;
    }
  }

  async deletePrompt(id: string): Promise<boolean> {
    try {
      const deleted = await this.primaryStorage.deletePrompt(id);

      if (deleted && this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.deletePrompt(id);
          this.logger?.debug('[HybridPromptStorage] Synced deletion to fallback storage');
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync deletion to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return deleted;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.deletePrompt(id);
      }

      throw primaryError;
    }
  }

  async searchPrompts(query: PromptQuery): Promise<Prompt[]> {
    try {
      const prompts = await this.primaryStorage.searchPrompts(query);

      // If primary has results, return them
      if (prompts.length > 0) {
        return prompts;
      }

      // Try fallback if primary has no results
      if (this.config.enableFallback) {
        return await this.fallbackStorage.searchPrompts(query);
      }

      return prompts;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.searchPrompts(query);
      }

      throw primaryError;
    }
  }

  async getPromptsByProject(projectId: string): Promise<Prompt[]> {
    return this.searchPrompts({ projectId });
  }

  async getPromptsByOrganization(organizationId: string): Promise<Prompt[]> {
    return this.searchPrompts({ organizationId });
  }

  async getActivePrompts(organizationId?: string, projectId?: string): Promise<Prompt[]> {
    try {
      const prompts = await this.primaryStorage.getActivePrompts(organizationId, projectId);

      if (prompts.length > 0) {
        return prompts;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getActivePrompts(organizationId, projectId);
      }

      return prompts;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getActivePrompts(organizationId, projectId);
      }

      throw primaryError;
    }
  }

  async getPromptsByLabels(
    labels: string[],
    organizationId?: string,
    projectId?: string
  ): Promise<Prompt[]> {
    const query: PromptQuery = { labels };
    if (organizationId) query.organizationId = organizationId;
    if (projectId) query.projectId = projectId;

    return this.searchPrompts(query);
  }

  async getProductionPrompt(
    organizationId: string,
    name: string,
    projectId?: string
  ): Promise<Prompt | null> {
    try {
      const prompt = await this.primaryStorage.getProductionPrompt(organizationId, name, projectId);
      if (prompt) {
        return prompt;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getProductionPrompt(organizationId, name, projectId);
      }

      return null;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getProductionPrompt(organizationId, name, projectId);
      }

      throw primaryError;
    }
  }

  async validatePrompt(prompt: Prompt): Promise<PromptValidationResult> {
    // Use primary storage for validation
    try {
      return await this.primaryStorage.validatePrompt(prompt);
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.validatePrompt(prompt);
      }

      throw primaryError;
    }
  }

  async createDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<PromptDependency> {
    try {
      const dep = await this.primaryStorage.createDependency(dependency);

      if (this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.createDependency(dependency);
          this.logger?.debug('[HybridPromptStorage] Synced dependency to fallback storage');
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync dependency to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return dep;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.createDependency(dependency);
      }

      throw primaryError;
    }
  }

  async getDependencies(parentId: string): Promise<PromptDependency[]> {
    try {
      const deps = await this.primaryStorage.getDependencies(parentId);
      if (deps.length > 0) {
        return deps;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getDependencies(parentId);
      }

      return deps;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getDependencies(parentId);
      }

      throw primaryError;
    }
  }

  async getDependenciesByChild(
    organizationId: string,
    childName: string,
    childVersion?: number,
    childLabel?: string
  ): Promise<PromptDependency[]> {
    try {
      const deps = await this.primaryStorage.getDependenciesByChild(
        organizationId,
        childName,
        childVersion,
        childLabel
      );
      if (deps.length > 0) {
        return deps;
      }

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getDependenciesByChild(
          organizationId,
          childName,
          childVersion,
          childLabel
        );
      }

      return deps;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getDependenciesByChild(
          organizationId,
          childName,
          childVersion,
          childLabel
        );
      }

      throw primaryError;
    }
  }

  async deleteDependency(id: string): Promise<boolean> {
    try {
      const deleted = await this.primaryStorage.deleteDependency(id);

      if (deleted && this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.deleteDependency(id);
          this.logger?.debug(
            '[HybridPromptStorage] Synced dependency deletion to fallback storage'
          );
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync dependency deletion to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return deleted;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.deleteDependency(id);
      }

      throw primaryError;
    }
  }

  async deleteDependenciesForPrompt(parentId: string): Promise<number> {
    try {
      const count = await this.primaryStorage.deleteDependenciesForPrompt(parentId);

      if (count > 0 && this.config.syncOnWrite) {
        try {
          await this.fallbackStorage.deleteDependenciesForPrompt(parentId);
          this.logger?.debug('[HybridPromptStorage] Synced dependency cleanup to fallback storage');
        } catch (fallbackError) {
          this.logger?.warn(
            `[HybridPromptStorage] Failed to sync dependency cleanup to fallback: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`
          );
        }
      }

      return count;
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.deleteDependenciesForPrompt(parentId);
      }

      throw primaryError;
    }
  }

  async validateDependency(
    dependency: Omit<PromptDependency, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<DependencyValidationResult> {
    try {
      return await this.primaryStorage.validateDependency(dependency);
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.validateDependency(dependency);
      }

      throw primaryError;
    }
  }

  async resolveDependencies(promptId: string): Promise<DependencyResolutionResult> {
    try {
      return await this.primaryStorage.resolveDependencies(promptId);
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.resolveDependencies(promptId);
      }

      throw primaryError;
    }
  }

  async getPromptGraph(promptId: string): Promise<PromptGraph> {
    try {
      return await this.primaryStorage.getPromptGraph(promptId);
    } catch (primaryError) {
      this.logger?.warn(
        `[HybridPromptStorage] Primary storage failed: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      if (this.config.enableFallback) {
        return await this.fallbackStorage.getPromptGraph(promptId);
      }

      throw primaryError;
    }
  }

  async getStats(): Promise<{
    totalPrompts: number;
    totalDependencies: number;
    organizations: string[];
  }> {
    try {
      const primaryStats = await this.primaryStorage.getStats();
      const fallbackStats = await this.fallbackStorage.getStats();

      // Combine stats from both storages
      const combinedOrganizations = new Set([
        ...primaryStats.organizations,
        ...fallbackStats.organizations,
      ]);

      return {
        totalPrompts: primaryStats.totalPrompts + fallbackStats.totalPrompts,
        totalDependencies: primaryStats.totalDependencies + fallbackStats.totalDependencies,
        organizations: Array.from(combinedOrganizations),
      };
    } catch (error) {
      this.logger?.error(
        `[HybridPromptStorage] Failed to get stats: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      await Promise.all([this.primaryStorage.close(), this.fallbackStorage.close()]);
      this.logger?.info('[HybridPromptStorage] Both storages closed');
    } catch (error) {
      this.logger?.error(
        `[HybridPromptStorage] Failed to close: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Get the primary storage instance
   */
  getPrimaryStorage(): PromptStorage {
    return this.primaryStorage;
  }

  /**
   * Get the fallback storage instance
   */
  getFallbackStorage(): PromptStorage {
    return this.fallbackStorage;
  }

  /**
   * Check if fallback is enabled
   */
  isFallbackEnabled(): boolean {
    return this.config.enableFallback || false;
  }

  /**
   * Check if sync on write is enabled
   */
  isSyncOnWriteEnabled(): boolean {
    return this.config.syncOnWrite || false;
  }
}
