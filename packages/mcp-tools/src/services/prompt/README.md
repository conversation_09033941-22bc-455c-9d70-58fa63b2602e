# Prompt Management System

A sophisticated prompt management system designed to handle both text and chat type prompts, with support for versioning, templating, and variable rendering. The system is designed to work with in-memory storage initially and can be easily extended to support database storage.

## Features

- **Multi-type Support**: Handles both text and chat prompts
- **Versioning**: Automatic version management with configurable auto-increment
- **Templating**: Variable substitution with validation
- **Caching**: Configurable in-memory caching with TTL
- **Validation**: Comprehensive prompt validation
- **Search**: Advanced search with multiple criteria
- **Dependencies**: Support for prompt dependencies (future feature)
- **Backup**: Automatic backup and recovery for data files

## Architecture

### Core Components

1. **PromptManager**: Main service orchestrating all prompt operations
2. **InMemoryPromptStorage**: Storage adapter for in-memory/JSON file persistence
3. **PromptRenderer**: Handles variable substitution and prompt rendering
4. **Default Templates**: Pre-built templates for common use cases

### Data Model

The system follows the Prisma schema design:

```typescript
interface Prompt {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
  projectId?: string;
  createdBy: string;
  prompt: string | PromptMessage[]; // text or chat
  name: string;
  version: number;
  type: 'text' | 'chat';
  isActive?: boolean;
  config: PromptConfig;
  tags: string[];
  labels: string[];
  commitMessage?: string;
}
```

## Usage

### Basic Setup

```typescript
import { PromptManager } from './prompt-manager';

const promptManager = new PromptManager({
  defaultOrganizationId: 'my-org',
  defaultProjectId: 'my-project',
  cacheEnabled: true,
  cacheTTL: 300000, // 5 minutes
  autoIncrementVersion: true,
});
```

### Creating Prompts

```typescript
// Text prompt
const textPrompt = await promptManager.createPrompt({
  organizationId: 'my-org',
  projectId: 'my-project',
  createdBy: 'user-123',
  prompt: 'Hello {{name}}, welcome to {{platform}}!',
  name: 'welcome-message',
  version: 1,
  type: 'text',
  config: {
    temperature: 0.7,
    includeConversationHistory: true,
  },
  tags: ['welcome', 'custom'],
  labels: ['production'],
});

// Chat prompt
const chatPrompt = await promptManager.createPrompt({
  organizationId: 'my-org',
  createdBy: 'user-123',
  prompt: [
    { role: 'system', content: 'You are {{assistantName}}' },
    { role: 'user', content: 'Hello {{userName}}!' },
  ],
  name: 'chat-assistant',
  version: 1,
  type: 'chat',
});
```

### Creating from Templates

```typescript
import { getDefaultTemplate } from './default-templates';

const template = getDefaultTemplate('traditional-rag');
if (template) {
  const prompt = await promptManager.createFromTemplate(template, 'my-org', 'user-123', {
    name: 'my-rag-prompt',
    tags: ['custom', 'rag'],
  });
}
```

### Rendering Prompts

```typescript
// Render by ID
const result = await promptManager.renderPrompt(promptId, {
  variables: {
    name: 'Alice',
    platform: 'Anter AI',
  },
});

// Render by name
const result = await promptManager.renderPromptByName('my-org', 'welcome-message', {
  variables: {
    name: 'Bob',
    platform: 'Anter AI',
  },
});

console.log(result.renderedPrompt);
console.log('Used variables:', result.usedVariables);
console.log('Missing variables:', result.missingVariables);
```

### Searching Prompts

```typescript
// Search by organization
const prompts = await promptManager.searchPrompts({
  organizationId: 'my-org',
});

// Search by type and tags
const textPrompts = await promptManager.searchPrompts({
  type: 'text',
  tags: ['welcome'],
});

// Search active prompts
const activePrompts = await promptManager.getActivePrompts('my-org', 'my-project');
```

### Updating Prompts

```typescript
const updated = await promptManager.updatePrompt(promptId, {
  prompt: 'Updated prompt content',
  tags: ['updated', 'custom'],
  commitMessage: 'Updated welcome message',
});
```

### Managing Dependencies

```typescript
// Create dependency
const dependency = await storage.createDependency({
  organizationId: 'my-org',
  parentId: 'parent-prompt-id',
  childName: 'child-prompt',
  childLabel: 'child-label',
  childVersion: 1,
});

// Get dependencies
const dependencies = await storage.getDependencies('parent-prompt-id');
```

## Default Templates

The system includes several pre-built templates:

- **traditional-rag**: Traditional RAG style for natural responses
- **analytical**: Focused analysis style for structured insights
- **conversational**: Friendly conversational style
- **chat-completion**: Multi-turn chat completion
- **code-generation**: Specialized for code generation
- **security-analysis**: Specialized for security analysis
- **documentation-generation**: Specialized for technical documentation

## Configuration

### PromptManagerConfig

```typescript
interface PromptManagerConfig {
  defaultOrganizationId?: string;
  defaultProjectId?: string;
  cacheEnabled?: boolean;
  cacheTTL?: number;
  maxCacheSize?: number;
  enableVersioning?: boolean;
  autoIncrementVersion?: boolean;
}
```

### InMemoryPromptStorageConfig

```typescript
interface InMemoryPromptStorageConfig {
  dataFilePath?: string;
  autoSave?: boolean;
  autoSaveInterval?: number;
  backupEnabled?: boolean;
  maxBackups?: number;
}
```

## Variable Rendering

The system supports variable substitution using the `{{variableName}}` syntax:

```typescript
// Template
const template = 'Hello {{name}}, welcome to {{platform}}!';

// Context
const context = {
  variables: {
    name: 'Alice',
    platform: 'Anter AI',
  },
};

// Result
// "Hello Alice, welcome to Anter AI!"
```

### Supported Variable Types

- **Strings**: Direct substitution
- **Numbers**: Converted to string
- **Booleans**: Converted to string
- **Objects**: JSON stringified
- **Null/Undefined**: Empty string with warning

## Validation

The system provides comprehensive validation:

```typescript
// Validate prompt creation
const validation = await promptManager.validatePrompt(prompt);
if (!validation.isValid) {
  console.log('Errors:', validation.errors);
  console.log('Warnings:', validation.warnings);
}

// Validate rendering context
const contextValidation = renderer.validateContext(prompt, context);
if (!contextValidation.isValid) {
  console.log('Missing variables:', contextValidation.missingVariables);
}
```

## Caching

The system includes configurable caching:

```typescript
const promptManager = new PromptManager({
  cacheEnabled: true,
  cacheTTL: 300000, // 5 minutes
  maxCacheSize: 1000,
});

// Cache is automatically used for getPromptByName calls
const prompt = await promptManager.getPromptByName('org', 'name', 1, 'project', true);

// Clear cache manually if needed
promptManager.clearCache();
```

## Error Handling

The system provides comprehensive error handling:

```typescript
try {
  const prompt = await promptManager.createPrompt(request);
} catch (error) {
  if (error.message.includes('Invalid prompt creation request')) {
    // Handle validation errors
  } else if (error.message.includes('not found')) {
    // Handle not found errors
  } else {
    // Handle other errors
  }
}
```

## Testing

The system includes comprehensive test coverage:

```bash
# Run all prompt-related tests
npm test -- --testPathPattern="prompt"

# Run specific test files
npm test -- --testPathPattern="prompt-manager.test.ts"
npm test -- --testPathPattern="prompt-renderer.test.ts"
npm test -- --testPathPattern="in-memory-prompt-storage.test.ts"
```

## Example Usage

See `example.ts` for a complete demonstration of the prompt management system.

## Future Enhancements

- Database storage adapters (PostgreSQL, MongoDB)
- Advanced dependency management
- Prompt analytics and metrics
- Collaborative editing
- Version control integration
- Advanced search with full-text search
- Prompt performance tracking
- A/B testing support
