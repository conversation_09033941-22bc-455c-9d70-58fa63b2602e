import {
  Prompt,
  PromptMessage,
  PromptRenderingContext,
  PromptRenderingResult,
} from '../../types/prompt';
import { LoggerAdapter } from '../../types';

export class PromptRenderer {
  private logger?: LoggerAdapter;

  constructor(logger?: LoggerAdapter) {
    this.logger = logger;
  }

  /**
   * Renders a prompt by replacing variables with actual values
   */
  renderPrompt(prompt: Prompt, context: PromptRenderingContext): PromptRenderingResult {
    const usedVariables: string[] = [];
    const missingVariables: string[] = [];
    const warnings: string[] = [];

    try {
      if (prompt.type === 'text') {
        const renderedText = this.renderTextPrompt(
          prompt.prompt as string,
          context,
          usedVariables,
          missingVariables,
          warnings
        );

        return {
          renderedPrompt: renderedText,
          usedVariables,
          missingVariables,
          warnings,
        };
      } else if (prompt.type === 'chat') {
        const renderedMessages = this.renderChatPrompt(
          prompt.prompt as PromptMessage[],
          context,
          usedVariables,
          missingVariables,
          warnings
        );

        return {
          renderedPrompt: renderedMessages,
          usedVariables,
          missingVariables,
          warnings,
        };
      } else {
        throw new Error(`Unsupported prompt type: ${prompt.type}`);
      }
    } catch (error) {
      this.logger?.error(
        `[PromptRenderer] Error rendering prompt: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  private renderTextPrompt(
    text: string,
    context: PromptRenderingContext,
    usedVariables: string[],
    missingVariables: string[],
    warnings: string[]
  ): string {
    return this.replaceVariables(text, context, usedVariables, missingVariables, warnings);
  }

  private renderChatPrompt(
    messages: PromptMessage[],
    context: PromptRenderingContext,
    usedVariables: string[],
    missingVariables: string[],
    warnings: string[]
  ): PromptMessage[] {
    return messages.map(message => ({
      ...message,
      content: this.replaceVariables(
        message.content,
        context,
        usedVariables,
        missingVariables,
        warnings
      ),
    }));
  }

  private replaceVariables(
    text: string,
    context: PromptRenderingContext,
    usedVariables: string[],
    missingVariables: string[],
    warnings: string[]
  ): string {
    // Match variables in the format {{variableName}}
    const variableRegex = /\{\{([^}]+)\}\}/g;
    let result = text;
    const foundVariables = new Set<string>();

    result = result.replace(variableRegex, (match, variableName) => {
      const trimmedVarName = variableName.trim();
      foundVariables.add(trimmedVarName);

      if (context.variables.hasOwnProperty(trimmedVarName)) {
        const value = context.variables[trimmedVarName];

        // Handle different types of values
        if (typeof value === 'string') {
          return value;
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          return String(value);
        } else if (value === null || value === undefined) {
          warnings.push(`Variable '${trimmedVarName}' is null or undefined`);
          return '';
        } else if (typeof value === 'object') {
          try {
            return JSON.stringify(value);
          } catch (error) {
            warnings.push(`Failed to stringify object for variable '${trimmedVarName}'`);
            return '[Object]';
          }
        } else {
          return String(value);
        }
      } else {
        missingVariables.push(trimmedVarName);
        return match; // Keep the original placeholder
      }
    });

    // Add found variables to usedVariables
    foundVariables.forEach(variable => {
      if (!missingVariables.includes(variable)) {
        usedVariables.push(variable);
      }
    });

    return result;
  }

  /**
   * Extracts all variables from a prompt template
   */
  extractVariables(prompt: Prompt): string[] {
    const variables = new Set<string>();
    const variableRegex = /\{\{([^}]+)\}\}/g;

    if (prompt.type === 'text') {
      const text = prompt.prompt as string;
      let match;
      while ((match = variableRegex.exec(text)) !== null) {
        variables.add(match[1].trim());
      }
    } else if (prompt.type === 'chat') {
      const messages = prompt.prompt as PromptMessage[];
      for (const message of messages) {
        let match;
        while ((match = variableRegex.exec(message.content)) !== null) {
          variables.add(match[1].trim());
        }
      }
    }

    return Array.from(variables);
  }

  /**
   * Validates that all required variables are provided in the context
   */
  validateContext(
    prompt: Prompt,
    context: PromptRenderingContext
  ): {
    isValid: boolean;
    missingVariables: string[];
    extraVariables: string[];
  } {
    const requiredVariables = this.extractVariables(prompt);
    const providedVariables = Object.keys(context.variables);

    const missingVariables = requiredVariables.filter(
      variable => !providedVariables.includes(variable)
    );

    const extraVariables = providedVariables.filter(
      variable => !requiredVariables.includes(variable)
    );

    return {
      isValid: missingVariables.length === 0,
      missingVariables,
      extraVariables,
    };
  }

  /**
   * Creates a template preview with sample values
   */
  createPreview(prompt: Prompt, sampleValues: Record<string, any> = {}): string {
    const variables = this.extractVariables(prompt);
    const previewContext: PromptRenderingContext = {
      variables: {},
    };

    // Fill in sample values for missing variables
    for (const variable of variables) {
      if (sampleValues.hasOwnProperty(variable)) {
        previewContext.variables[variable] = sampleValues[variable];
      } else {
        // Generate a sample value based on the variable name
        previewContext.variables[variable] = this.generateSampleValue(variable);
      }
    }

    try {
      const result = this.renderPrompt(prompt, previewContext);
      return typeof result.renderedPrompt === 'string'
        ? result.renderedPrompt
        : JSON.stringify(result.renderedPrompt, null, 2);
    } catch (error) {
      return `Error creating preview: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  private generateSampleValue(variableName: string): any {
    const lowerName = variableName.toLowerCase();

    if (lowerName.includes('name') || lowerName.includes('title')) {
      return `Sample ${variableName}`;
    } else if (lowerName.includes('email')) {
      return '<EMAIL>';
    } else if (lowerName.includes('url') || lowerName.includes('link')) {
      return 'https://example.com';
    } else if (lowerName.includes('date')) {
      return new Date().toISOString().split('T')[0];
    } else if (lowerName.includes('number') || lowerName.includes('count')) {
      return 42;
    } else if (lowerName.includes('list') || lowerName.includes('array')) {
      return ['item1', 'item2', 'item3'];
    } else if (lowerName.includes('object') || lowerName.includes('data')) {
      return { key: 'value', nested: { data: 'example' } };
    } else {
      return `[${variableName}]`;
    }
  }
}
