# Langfuse Integration for Prompt Management

This document describes the complete Langfuse integration for the prompt management system, including how to use the existing default prompt (configurable via `LANGFUSE_DEFAULT_PROMPT` environment variable) and configure different storage backends.

## Overview

The prompt management system now supports multiple storage backends:

- **In-Memory Storage**: JSON file-based storage (default)
- **Langfuse Storage**: Enterprise-grade prompt management via Langfuse API
- **Hybrid Storage**: Combines Langfuse and in-memory storage with fallback capabilities

## Quick Start

### 1. Environment Configuration

Set up your Langfuse environment variables:

```bash
# Required
LANGFUSE_PUBLIC_KEY="pk-lf-..."
LANGFUSE_SECRET_KEY="sk-lf-..."

# Optional
LANGFUSE_BASEURL="https://cloud.langfuse.com"  # or self-hosted URL
LANGFUSE_SAMPLE_RATE="1.0"
LANGFUSE_TRACING_ENABLED="true"
```

### 2. Basic Usage

```typescript
import { PromptManager } from '@anter/mcp-tools';

// Use Langfuse as primary storage
const promptManager = new PromptManager({
  defaultOrganizationId: 'my-org',
  defaultProjectId: 'my-project',
  storageType: 'langfuse',
  cacheEnabled: true,
  cacheTTL: 300000, // 5 minutes
  autoIncrementVersion: true,
});

// Create a prompt
const prompt = await promptManager.createPrompt({
  organizationId: 'my-org',
  projectId: 'my-project',
  createdBy: 'user-123',
  prompt: 'Hello {{name}}, welcome to {{platform}}!',
  name: 'welcome-message',
  version: 1,
  type: 'text',
  config: {
    temperature: 0.7,
    includeConversationHistory: true,
  },
  tags: ['welcome', 'custom'],
  labels: ['production'],
});

// Render the prompt
const rendered = await promptManager.renderPromptByName('my-org', 'welcome-message', {
  variables: {
    name: 'Alice',
    platform: 'Anter AI',
  },
});

console.log(rendered.renderedPrompt);
```

## Storage Types

### 1. In-Memory Storage (Default)

```typescript
import { InMemoryPromptStorage } from '@anter/mcp-tools';

const storage = new InMemoryPromptStorage({
  dataFilePath: '/path/to/prompts.json',
  autoSave: true,
  autoSaveInterval: 30000,
  backupEnabled: true,
  maxBackups: 5,
  logger: console,
});

const promptManager = new PromptManager(
  {
    storageType: 'memory',
  },
  storage
);
```

### 2. Langfuse Storage

```typescript
import { LangfusePromptStorage } from '@anter/mcp-tools';

const storage = new LangfusePromptStorage({
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  baseUrl: process.env.LANGFUSE_BASEURL,
  sampleRate: 1.0,
  enabled: true,
  requestTimeout: 10000,
  flushAt: 10,
  flushInterval: 5000,
  logger: console,
});

const promptManager = new PromptManager(
  {
    storageType: 'langfuse',
  },
  storage
);
```

### 3. Hybrid Storage

```typescript
import {
  LangfusePromptStorage,
  InMemoryPromptStorage,
  HybridPromptStorage,
} from '@anter/mcp-tools';

const primaryStorage = new LangfusePromptStorage({ logger: console });
const fallbackStorage = new InMemoryPromptStorage({ logger: console });

const hybridStorage = new HybridPromptStorage({
  primaryStorage,
  fallbackStorage,
  enableFallback: true,
  syncOnWrite: true,
  logger: console,
});

const promptManager = new PromptManager(
  {
    storageType: 'hybrid',
  },
  hybridStorage
);
```

## Using the Default Prompt

The default prompt is configurable via the `LANGFUSE_DEFAULT_PROMPT` environment variable (defaults to `system-default`). Here's how to use it:

### 1. Retrieve the Prompt

```typescript
const promptManager = new PromptManager({
  storageType: 'langfuse',
  defaultOrganizationId: 'your-org',
});

// Get the default prompt
const defaultPromptName = process.env.LANGFUSE_DEFAULT_PROMPT || 'system-default';
const systemPrompt = await promptManager.getPromptByName('your-org', defaultPromptName);

if (systemPrompt) {
  console.log(`Found default prompt: ${systemPrompt.name} v${systemPrompt.version}`);
  console.log(`ID: ${systemPrompt.id}`);
  console.log(`Type: ${systemPrompt.type}`);
  console.log(`Labels: ${systemPrompt.labels.join(', ')}`);
}
```

### 2. Render the Prompt

```typescript
// Render the default prompt with variables
const rendered = await promptManager.renderPrompt(systemPrompt.id, {
  variables: {
    // Add variables that the default prompt expects
    userInput: 'Hello, how can you help me today?',
    context: 'User is asking for general assistance',
    // Add any other variables your prompt template uses
  },
});

console.log('Rendered prompt:', rendered.renderedPrompt);
console.log('Used variables:', rendered.usedVariables);
console.log('Missing variables:', rendered.missingVariables);
```

### 3. Update the Prompt

```typescript
// Update the default prompt
const updated = await promptManager.updatePrompt(systemPrompt.id, {
  tags: [...systemPrompt.tags, 'updated'],
  commitMessage: 'Updated via API integration',
});

if (updated) {
  console.log('Successfully updated default prompt');
}
```

## Advanced Features

### 1. Template-Based Prompt Creation

```typescript
import { getDefaultTemplate } from '@anter/mcp-tools';

const template = getDefaultTemplate('traditional-rag');
if (template) {
  const prompt = await promptManager.createFromTemplate(template, 'my-org', 'user-123', {
    name: 'my-rag-prompt',
    tags: ['custom', 'rag'],
    labels: ['production'],
  });
}
```

### 2. Dependency Management

```typescript
// Create a dependency between prompts
const dependency = await storage.createDependency({
  organizationId: 'my-org',
  parentId: parentPrompt.id,
  childName: 'child-prompt',
  childVersion: 1,
});

// Get all dependencies for a prompt
const dependencies = await storage.getDependencies(parentPrompt.id);

// Resolve the full dependency chain
const resolution = await storage.resolveDependencies(parentPrompt.id);
console.log('Resolved prompts:', resolution.resolvedPrompts.length);
console.log('Unresolved dependencies:', resolution.unresolvedDependencies.length);
```

### 3. Search and Filtering

```typescript
// Search by organization
const orgPrompts = await promptManager.searchPrompts({
  organizationId: 'my-org',
});

// Search by type and tags
const textPrompts = await promptManager.searchPrompts({
  type: 'text',
  tags: ['welcome'],
});

// Get production prompts
const productionPrompts = await promptManager.getActivePrompts('my-org', 'my-project');

// Get prompts by specific labels
const latestPrompts = await storage.getPromptsByLabels(['latest'], 'my-org');
```

### 4. Statistics and Monitoring

```typescript
// Get storage statistics
const stats = await promptManager.getStats();
console.log(`Total prompts: ${stats.totalPrompts}`);
console.log(`Total dependencies: ${stats.totalDependencies}`);
console.log(`Organizations: ${stats.organizations.join(', ')}`);

// Get dependency graph for visualization
const graph = await storage.getPromptGraph(promptId);
console.log('Graph nodes:', graph.nodes.length);
console.log('Graph edges:', graph.edges.length);
```

## Migration Strategy

### 1. Gradual Migration

```typescript
// Start with hybrid storage for safe migration
const promptManager = new PromptManager({
  storageType: 'hybrid',
  defaultOrganizationId: 'my-org',
});

// This will use Langfuse as primary and in-memory as fallback
// All writes go to both storages, reads try Langfuse first
```

### 2. Data Migration

```typescript
// Migrate existing prompts from in-memory to Langfuse
const inMemoryStorage = new InMemoryPromptStorage();
const langfuseStorage = new LangfusePromptStorage();

const existingPrompts = await inMemoryStorage.getPromptsByOrganization('my-org');

for (const prompt of existingPrompts) {
  await langfuseStorage.createPrompt({
    organizationId: prompt.organizationId,
    projectId: prompt.projectId,
    createdBy: prompt.createdBy,
    prompt: prompt.prompt,
    name: prompt.name,
    version: prompt.version,
    type: prompt.type,
    config: prompt.config,
    tags: prompt.tags,
    labels: prompt.labels,
    commitMessage: 'Migrated from in-memory storage',
  });
}
```

### 3. Fallback Configuration

```typescript
// Configure fallback behavior
const hybridStorage = new HybridPromptStorage({
  primaryStorage: new LangfusePromptStorage(),
  fallbackStorage: new InMemoryPromptStorage(),
  enableFallback: true, // Use fallback on primary failure
  syncOnWrite: true, // Write to both storages
  logger: console,
});

// Check fallback status
console.log('Fallback enabled:', hybridStorage.isFallbackEnabled());
console.log('Sync on write enabled:', hybridStorage.isSyncOnWriteEnabled());
```

## Error Handling

### 1. Storage Failures

```typescript
try {
  const prompt = await promptManager.createPrompt(request);
} catch (error) {
  if (error.message.includes('Langfuse')) {
    // Handle Langfuse-specific errors
    console.error('Langfuse storage error:', error);
  } else if (error.message.includes('not found')) {
    // Handle not found errors
    console.error('Prompt not found:', error);
  } else {
    // Handle other errors
    console.error('Unexpected error:', error);
  }
}
```

### 2. Fallback Behavior

```typescript
// With hybrid storage, failures automatically fall back
const promptManager = new PromptManager({
  storageType: 'hybrid',
});

// If Langfuse fails, it will automatically try in-memory storage
const defaultPromptName = process.env.LANGFUSE_DEFAULT_PROMPT || 'system-default';
const prompt = await promptManager.getPromptByName('my-org', defaultPromptName);
```

## Best Practices

### 1. Environment Configuration

- Use environment variables for sensitive configuration
- Set appropriate timeouts and retry limits
- Enable logging for debugging

### 2. Caching Strategy

```typescript
const promptManager = new PromptManager({
  cacheEnabled: true,
  cacheTTL: 300000, // 5 minutes
  maxCacheSize: 1000,
});
```

### 3. Label Management

```typescript
// Use labels for environment management
await promptManager.updatePrompt(promptId, {
  labels: ['production', 'latest'],
});

// Use labels for state management
await promptManager.updatePrompt(promptId, {
  labels: ['deprecated'],
});
```

### 4. Version Management

```typescript
// Enable auto-increment versioning
const promptManager = new PromptManager({
  autoIncrementVersion: true,
});

// Create new version automatically
const newVersion = await promptManager.createPrompt({
  ...request,
  version: undefined, // Will be auto-incremented
});
```

## Troubleshooting

### 1. Common Issues

**Langfuse Connection Failed**

- Check environment variables
- Verify network connectivity
- Check Langfuse service status

**Prompt Not Found**

- Verify organization and project IDs
- Check prompt name spelling
- Ensure prompt exists in Langfuse

**Dependency Issues**

- Check for circular dependencies
- Verify parent/child prompt existence
- Validate dependency configuration

### 2. Debugging

```typescript
// Enable detailed logging
const promptManager = new PromptManager(
  {
    storageType: 'langfuse',
  },
  undefined,
  console
);

// Check storage status
const stats = await promptManager.getStats();
console.log('Storage stats:', stats);
```

## Performance Considerations

### 1. Caching

- Enable caching for frequently accessed prompts
- Set appropriate TTL based on update frequency
- Monitor cache hit rates

### 2. Batch Operations

- Use batch operations when possible
- Minimize API calls to Langfuse
- Consider local caching for read-heavy workloads

### 3. Network Optimization

- Set appropriate timeouts
- Use connection pooling
- Monitor network latency

## Conclusion

The Langfuse integration provides enterprise-grade prompt management with:

- ✅ Full compatibility with existing Langfuse prompts (like the default prompt)
- ✅ Multiple storage backends with fallback capabilities
- ✅ Comprehensive dependency management
- ✅ Advanced search and filtering
- ✅ Template-based prompt creation
- ✅ Version control and labeling
- ✅ Performance optimization and caching

This integration allows you to seamlessly migrate from in-memory storage to Langfuse while maintaining backward compatibility and providing robust fallback mechanisms.
