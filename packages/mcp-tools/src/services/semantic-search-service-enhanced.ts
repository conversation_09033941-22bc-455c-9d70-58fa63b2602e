import { EmbeddingService } from './embedding-service';
import { ProcessedDocument } from './document-processor';
import { ContentHashService } from './content-hash.service';
import {
  IEmbeddingIndex,
  EmbeddingIndexFactory,
  EmbeddingIndexFactoryOptions,
} from './embedding-index';
import { TextChunkingService, ChunkingOptions } from './text-chunking-service';

export interface SearchResultEnhanced {
  documentId: string;
  content: string;
  /** Final score after all ranking steps */
  score: number;
  /** Raw cosine similarity before any re-ranking */
  rawSimilarity: number;
  /** Score after re-ranking but before threshold filter */
  rerankScore?: number;
  /** Whether the result passed the similarity threshold filter */
  passedFilter: boolean;
  /** Marked when no results pass threshold; top-1/top-2 are allowed through */
  lowConfidence?: boolean;
  metadata?: Record<string, any>;
  highlights?: string[];
}

export interface SearchOptionsEnhanced {
  topK?: number;
  threshold?: number;
  includeMetadata?: boolean;
  includeHighlights?: boolean;
  organizationId?: string;
  filters?: Record<string, any>;
  rerank?: boolean;
  useRedis?: boolean;
  redisClient?: any;
  executor?: string;
  logger?: any;
  cacheAdapter?: any; // Add cache adapter for persistent hash storage
}

// Legacy interfaces from the original implementation (compatibility)
export interface SemanticSearchOptions {
  organizationId?: string;
  userId?: string;
  filters?: any;
  maxResults?: number;
  threshold?: number;
  includeMetadata?: boolean;
}

export interface ContextualSearchOptions extends SemanticSearchOptions {
  context?: {
    previousQueries?: string[];
    userPreferences?: Record<string, any>;
    sessionContext?: Record<string, any>;
  };
}

export interface SearchResult {
  id: string;
  title: string;
  content?: string;
  score: number;
  metadata: Record<string, any>;
  embedding?: number[];
}

// Current pipeline interfaces (compatibility)
export interface SemanticSearchPipelineArgs {
  query: string;
  organizationId: string;
  userId?: string | null;
  documents?: any[] | null;
  topK?: number;
}

export interface SemanticSearchPipelineResult {
  success: boolean;
  searchResults: any[];
  documents: any[];
  count: number;
  message: string;
  averageScore: number;
}

/**
 * Enhanced semantic search service that delegates to IEmbeddingIndex
 * for storage and retrieval, providing automatic Redis/in-memory fallback.
 *
 * This service now includes all functionality from the original agents' semantic search service
 * for full backward compatibility.
 */
export class SemanticSearchServiceEnhanced {
  private embeddingService: EmbeddingService;
  private contentHashService: ContentHashService;
  private textChunkingService: TextChunkingService;
  private indexCache = new Map<string, IEmbeddingIndex>();
  private searchCount = 0;
  private logger?: any;
  private embeddingCache = new Map<string, number[]>();
  private searchCache = new Map<string, SearchResult[]>();
  private redisClient?: any;

  constructor(
    embeddingService?: EmbeddingService,
    contentHashService?: ContentHashService,
    logger?: any,
    cacheAdapter?: any,
    redisClient?: any
  ) {
    this.embeddingService = embeddingService || new EmbeddingService();
    this.contentHashService = contentHashService || new ContentHashService();
    this.textChunkingService = new TextChunkingService();
    this.logger = logger;

    // Set cache adapter for persistent hash storage if provided
    if (cacheAdapter && this.contentHashService.setCacheAdapter) {
      this.contentHashService.setCacheAdapter(cacheAdapter);
    }

    if (redisClient) {
      this.redisClient = redisClient;
    }
  }

  /**
   * Get or create an embedding index for the specified organization
   */
  private async getEmbeddingIndex(
    organizationId: string,
    options?: SearchOptionsEnhanced
  ): Promise<IEmbeddingIndex> {
    const cacheKey = `${organizationId}-${options?.useRedis ? 'redis' : 'auto'}`;

    if (this.indexCache.has(cacheKey)) {
      return this.indexCache.get(cacheKey)!;
    }

    const factoryOptions: EmbeddingIndexFactoryOptions = {
      organizationId,
      redisClient: options?.redisClient || this.redisClient,
      forceInMemory: options?.useRedis === false,
    };

    const index = await EmbeddingIndexFactory.createIndex(factoryOptions);
    this.indexCache.set(cacheKey, index);

    return index;
  }

  /**
   * Index a single document for semantic search
   */
  async indexDocument(
    document: ProcessedDocument,
    organizationId: string = 'default',
    options?: SearchOptionsEnhanced
  ): Promise<void> {
    if (!document.content?.trim()) {
      throw new Error('Document content cannot be empty');
    }

    try {
      // Generate embedding for the document
      const embedding = await this.embeddingService.embedText(document.content);

      // Get the appropriate index
      const index = await this.getEmbeddingIndex(organizationId, options);

      // Store in index
      await index.store(document.id, embedding, document.content, document.metadata);
    } catch (error) {
      throw new Error(
        `Failed to index document ${document.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Batch-index documents using optimized embedding generation and storage
   */
  async indexDocuments(
    documents: ProcessedDocument[],
    organizationId: string = 'default',
    options?: SearchOptionsEnhanced
  ): Promise<void> {
    const validDocuments = documents.filter(doc => doc.content?.trim());

    if (validDocuments.length === 0) {
      if (this.logger) {
        this.logger.warn(
          `[SemanticSearch] No valid documents to index - all documents have empty content`,
          {
            organizationId,
            totalDocuments: documents.length,
            validDocuments: 0,
          }
        );
      }
      return;
    }

    if (this.logger) {
      this.logger.info(`[SemanticSearch] Starting batch indexing`, {
        organizationId,
        totalDocuments: documents.length,
        validDocuments: validDocuments.length,
        documentIds: validDocuments.map(doc => doc.id),
      });
    }

    try {
      // Generate embeddings in batch for better performance
      const texts = validDocuments.map(doc => doc.content);
      const embeddings = await this.embeddingService.embedTexts(texts);

      if (this.logger) {
        this.logger.info(`[SemanticSearch] Generated embeddings`, {
          organizationId,
          embeddingCount: embeddings.length,
        });
      }

      // Get the appropriate index
      const index = await this.getEmbeddingIndex(organizationId, options);

      // Prepare documents for batch storage
      const documentsToStore = validDocuments.map((doc, i) => ({
        documentId: doc.id,
        embedding: embeddings[i],
        content: doc.content,
        metadata: doc.metadata || {},
      }));

      if (this.logger) {
        this.logger.info(`[SemanticSearch] Storing documents in index`, {
          organizationId,
          documentsToStore: documentsToStore.length,
          documentIds: documentsToStore.map(doc => doc.documentId),
        });
      }

      // Batch store in index
      await index.batchStore(documentsToStore);

      if (this.logger) {
        this.logger.info(`[SemanticSearch] Successfully indexed documents`, {
          organizationId,
          indexedCount: documentsToStore.length,
          documentIds: documentsToStore.map(doc => doc.documentId),
        });
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error(`[SemanticSearch] Failed to index documents`, {
          organizationId,
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        });
      }
      throw new Error(
        `Failed to index documents: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Incremental indexing - only index documents that have changed content
   * Enhanced with text chunking for large documents
   */
  async indexDocumentsIncremental(
    documents: ProcessedDocument[],
    organizationId: string = 'default',
    options?: SearchOptionsEnhanced & ChunkingOptions
  ): Promise<{
    indexed: number;
    skipped: number;
    changedDocuments: string[];
    totalChunks: number;
    chunkedDocuments: string[];
  }> {
    const validDocuments = documents.filter(doc => doc.content?.trim());

    if (validDocuments.length === 0) {
      return { indexed: 0, skipped: 0, changedDocuments: [], totalChunks: 0, chunkedDocuments: [] };
    }

    if (this.logger) {
      this.logger.info(`[SemanticSearch] Starting incremental indexing`, {
        organizationId,
        totalDocuments: validDocuments.length,
        documentIds: validDocuments.map(doc => doc.id),
      });
    }

    try {
      // Load persistent hashes to prevent unnecessary re-embedding
      if (this.contentHashService.loadPersistentHashes) {
        await this.contentHashService.loadPersistentHashes(organizationId);
        if (this.logger) {
          this.logger.info(`[SemanticSearch] Loaded persistent hashes for organization`, {
            organizationId,
          });
        }
      }

      // Filter documents that have changed content
      const documentsToReindex = this.contentHashService.getDocumentsToReindex(
        validDocuments.map(doc => ({
          id: doc.id,
          content: doc.content,
          metadata: doc.metadata,
        }))
      );

      if (documentsToReindex.length === 0) {
        if (this.logger) {
          this.logger.info(`[SemanticSearch] No documents need reindexing - all up to date`, {
            organizationId,
            totalDocuments: validDocuments.length,
          });
        }
        return {
          indexed: 0,
          skipped: validDocuments.length,
          changedDocuments: [],
          totalChunks: 0,
          chunkedDocuments: [],
        };
      }

      if (this.logger) {
        this.logger.info(`[SemanticSearch] Documents to reindex`, {
          organizationId,
          documentsToReindex: documentsToReindex.length,
          documentIds: documentsToReindex.map(doc => doc.id),
        });
      }

      // Get the appropriate index
      const index = await this.getEmbeddingIndex(organizationId, options);

      // Process documents with chunking
      let totalChunks = 0;
      const chunkedDocuments: string[] = [];
      const allChunksToStore: Array<{
        documentId: string;
        embedding: number[];
        content: string;
        metadata?: Record<string, any>;
      }> = [];

      // Map back to ProcessedDocument format for chunking
      const documentsToProcess = documentsToReindex.map(doc => {
        const originalDoc = validDocuments.find(original => original.id === doc.id);
        return {
          id: doc.id,
          name: originalDoc?.name || doc.id,
          content: doc.content,
          metadata: doc.metadata,
        } as ProcessedDocument;
      });

      for (const doc of documentsToProcess) {
        // Always chunk documents to respect model limits and ensure consistent storage keys
        const chunkingStats = this.textChunkingService.getChunkingStats(doc.content, options);

        if (this.logger) {
          this.logger.info(`[SemanticSearch] Document chunking stats`, {
            organizationId,
            documentId: doc.id,
            estimatedTokens: chunkingStats.estimatedTokens,
            needsChunking: chunkingStats.needsChunking,
            estimatedChunks: chunkingStats.estimatedChunks,
          });
        }

        const chunkedDoc = await this.textChunkingService.chunkDocument(doc, options);

        if (this.logger) {
          this.logger.info(`[SemanticSearch] Document chunked`, {
            organizationId,
            documentId: doc.id,
            chunks: chunkedDoc.chunks.length,
          });
        }

        // Generate embeddings for each chunk (single or multiple)
        for (let i = 0; i < chunkedDoc.chunks.length; i++) {
          const chunk = chunkedDoc.chunks[i];
          const embedding = await this.embeddingService.embedText(chunk.content);

          // Generate proper Redis key for the chunk
          const chunkKey = this.textChunkingService.generateChunkKey(
            organizationId,
            doc.id,
            chunk.chunkNumber
          );

          allChunksToStore.push({
            documentId: chunkKey, // Use the properly formatted Redis key
            embedding,
            content: chunk.content,
            metadata: {
              ...chunk.metadata,
              isChunked: true,
              originalDocumentId: doc.id,
              chunkIndex: i,
              totalChunks: chunkedDoc.chunks.length,
              organizationId,
            },
          });

          totalChunks += 1;
        }

        chunkedDocuments.push(doc.id);
      }

      // Batch store all chunks in index
      if (allChunksToStore.length > 0) {
        if (this.logger) {
          this.logger.info(`[SemanticSearch] Storing chunks in index`, {
            organizationId,
            totalChunksToStore: allChunksToStore.length,
            chunkIds: allChunksToStore.map(chunk => chunk.documentId),
          });
        }

        await index.batchStore(allChunksToStore);
      }

      // Save persistent hashes to prevent future unnecessary re-embedding
      if (this.contentHashService.savePersistentHashes) {
        await this.contentHashService.savePersistentHashes(organizationId);
        if (this.logger) {
          this.logger.info(`[SemanticSearch] Saved persistent hashes for organization`, {
            organizationId,
          });
        }
      }

      if (this.logger) {
        this.logger.info(`[SemanticSearch] Incremental indexing completed`, {
          organizationId,
          indexed: documentsToReindex.length,
          skipped: validDocuments.length - documentsToReindex.length,
          totalChunks,
          chunkedDocuments,
        });
      }

      return {
        indexed: documentsToReindex.length,
        skipped: validDocuments.length - documentsToReindex.length,
        changedDocuments: documentsToReindex.map(doc => doc.id),
        totalChunks,
        chunkedDocuments,
      };
    } catch (error) {
      if (this.logger) {
        this.logger.error(error as Error, `[SemanticSearch] Incremental indexing failed`, {
          organizationId,
          totalDocuments: validDocuments.length,
        });
      }
      throw new Error(
        `Failed to incrementally index documents: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Execute semantic search using the two-phase approach:
   * 1. Search in embedding index for similar documents/chunks
   * 2. Optionally re-rank results using additional heuristics
   * Enhanced to handle chunked documents and aggregate results
   */
  async search(
    query: string,
    options: SearchOptionsEnhanced = {}
  ): Promise<SearchResultEnhanced[]> {
    if (!query?.trim()) {
      return [];
    }

    const {
      topK = 2,
      threshold = 0.3,
      includeMetadata = true,
      includeHighlights = false,
      organizationId,
      filters,
      rerank = false,
      // @ts-ignore
      executor = 'unknown',
      // @ts-ignore
      logger,
    } = options;

    if (!organizationId) {
      throw new Error('Organization ID is required');
    }

    // Use the logger from options if available, otherwise fall back to instance logger
    const searchLogger = logger || this.logger;

    if (searchLogger) {
      searchLogger.info(`[SemanticSearch] Search called`, {
        query,
        organizationId: options.organizationId,
      });
    }

    this.searchCount++;

    try {
      // Generate query embedding
      const queryEmbedding = await this.embeddingService.embedText(query);

      // Get the appropriate index
      const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);

      // Search in index (this will return both regular documents and chunks)
      const indexResults = await embeddingIndex.searchSimilar(
        queryEmbedding,
        topK,
        threshold,
        filters
      );

      if (searchLogger) {
        searchLogger.info(`[SemanticSearch] Raw index results`, {
          organizationId,
          rawResultsCount: indexResults.length,
          resultIds: indexResults.map(r => r.documentId),
        });
      }

      // Process results and handle chunked documents
      const processedResults = await this.processSearchResults(
        indexResults,
        organizationId,
        embeddingIndex,
        options
      );

      // Convert to SearchResult format
      let results: SearchResultEnhanced[] = processedResults.map(result => ({
        documentId: result.documentId,
        content: result.content,
        score: result.similarity,
        rawSimilarity: result.similarity,
        rerankScore: undefined,
        passedFilter: result.similarity >= threshold,
        metadata: includeMetadata ? result.metadata : undefined,
        highlights: includeHighlights ? this.generateHighlights(query, result.content) : undefined,
      }));

      // Optional re-ranking
      if (rerank) {
        results = await this.rerankResults(query, results);
      }

      // Update passedFilter flag after potential rerank
      results = results.map(r => ({
        ...r,
        passedFilter: r.score >= threshold,
      }));

      // Soft fallback: if no result meets threshold, flag top-1/top-2 as low-confidence
      const anyPassed = results.some(r => r.passedFilter);
      if (!anyPassed && results.length > 0) {
        const allowN = Math.min(2, results.length);
        for (let i = 0; i < allowN; i++) {
          results[i] = { ...results[i], lowConfidence: true };
        }
      }

      // Limit to topK results
      results = results.slice(0, topK);

      if (searchLogger) {
        searchLogger.info(`[SemanticSearch] Final search results`, {
          organizationId,
          resultsCount: results.length,
          results: results.map(r => ({
            documentId: r.documentId,
            previewContent: r.content.slice(0, 100),
            score: r.score,
            rawSimilarity: r.rawSimilarity,
            rerankScore: r.rerankScore,
            isChunked: r.metadata?.isChunked,
            chunkNumber: r.metadata?.chunkNumber,
          })),
        });
      }

      return results;
    } catch (error) {
      if (searchLogger) {
        searchLogger.error(error, '[SemanticSearchServiceEnhanced] Search failed:');
      }
      return [];
    }
  }

  /**
   * Process search results to handle chunked documents and aggregate content
   */
  private async processSearchResults(
    indexResults: Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>,
    _organizationId: string,
    _embeddingIndex: IEmbeddingIndex,
    _options: SearchOptionsEnhanced
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    const processedResults: Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }> = [];

    // Group results by original document ID
    const documentGroups = new Map<
      string,
      Array<{
        documentId: string;
        similarity: number;
        content: string;
        metadata?: Record<string, any>;
      }>
    >();

    for (const result of indexResults) {
      const metadata = result.metadata || {};

      if (metadata.isChunked && metadata.originalDocumentId) {
        // This is a chunk, group by original document ID
        const originalDocId = metadata.originalDocumentId;
        if (!documentGroups.has(originalDocId)) {
          documentGroups.set(originalDocId, []);
        }
        documentGroups.get(originalDocId)!.push(result);
      } else {
        // This is a regular document, add directly
        processedResults.push(result);
      }
    }

    // Process chunked documents
    for (const [originalDocId, chunks] of documentGroups) {
      if (chunks.length === 0) continue;

      // Sort chunks by chunk number
      const sortedChunks = chunks.sort((a, b) => {
        const aNum = a.metadata?.chunkNumber || 0;
        const bNum = b.metadata?.chunkNumber || 0;
        return aNum - bNum;
      });

      // Calculate aggregated similarity score (weighted average)
      const totalSimilarity = chunks.reduce((sum, chunk) => sum + chunk.similarity, 0);
      const avgSimilarity = totalSimilarity / chunks.length;

      // Reconstruct full content from chunks
      const fullContent = sortedChunks.map(c => c.content).join('\n\n');

      // Create aggregated result
      const aggregatedResult = {
        documentId: originalDocId,
        similarity: avgSimilarity,
        content: fullContent,
        metadata: {
          ...sortedChunks[0].metadata,
          isChunked: true,
          totalChunks: sortedChunks.length,
          chunkSimilarities: sortedChunks.map(chunk => ({
            chunkNumber: chunk.metadata?.chunkNumber,
            similarity: chunk.similarity,
          })),
          aggregatedFromChunks: true,
        },
      };

      processedResults.push(aggregatedResult);
    }

    // Sort by similarity score
    processedResults.sort((a, b) => b.similarity - a.similarity);

    return processedResults;
  }

  /**
   * Find documents similar to a given document
   */
  async findSimilarDocuments(
    documentId: string,
    options: SearchOptionsEnhanced = {}
  ): Promise<SearchResultEnhanced[]> {
    const organizationId = options.organizationId || 'default';

    try {
      // Get the appropriate index
      const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);

      // Retrieve the target document
      const targetDoc = await embeddingIndex.retrieve(documentId);
      if (!targetDoc) {
        return [];
      }

      // Search for similar documents using the target document's embedding
      const indexResults = await embeddingIndex.searchSimilar(
        targetDoc.embedding,
        options.topK || 3,
        options.threshold || 0.0,
        options.filters
      );

      // Filter out the target document itself
      const filteredResults = indexResults.filter(result => result.documentId !== documentId);

      // Convert to SearchResult format
      return filteredResults.map(result => ({
        documentId: result.documentId,
        content: result.content,
        score: result.similarity,
        rawSimilarity: result.similarity,
        rerankScore: undefined,
        passedFilter: true,
        metadata: options.includeMetadata ? result.metadata : undefined,
        highlights: options.includeHighlights
          ? this.generateHighlights(targetDoc.content, result.content)
          : undefined,
      }));
    } catch (error) {
      this.logger?.error(error, '[SemanticSearchServiceEnhanced] Similar document search failed:');
      return [];
    }
  }

  /**
   * Remove a document from the index
   */
  async removeDocument(
    documentId: string,
    organizationId: string = 'default',
    options?: SearchOptionsEnhanced
  ): Promise<boolean> {
    try {
      const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);
      return await embeddingIndex.remove(documentId);
    } catch (error) {
      this.logger?.error(error, '[SemanticSearchServiceEnhanced] Remove document failed:');
      return false;
    }
  }

  /**
   * Clear all documents for an organization
   */
  async clearOrganization(organizationId: string, options?: SearchOptionsEnhanced): Promise<void> {
    try {
      const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);
      await embeddingIndex.clearOrganization(organizationId);

      // Clear from cache
      this.indexCache.delete(`${organizationId}-${options?.useRedis ? 'redis' : 'auto'}`);
    } catch (error) {
      this.logger?.error(error, '[SemanticSearchServiceEnhanced] Clear organization failed:');
    }
  }

  /**
   * Get comprehensive statistics
   */
  async getStats(organizationId?: string, options?: SearchOptionsEnhanced) {
    const stats = {
      searchCount: this.searchCount,
      cachedIndexes: this.indexCache.size,
      indexStats: {} as Record<string, any>,
    };

    if (organizationId) {
      try {
        const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);
        stats.indexStats[organizationId] = await embeddingIndex.getStats();
      } catch (error) {
        this.logger?.error(error, '[SemanticSearchServiceEnhanced] Get stats failed:');
      }
    }

    return stats;
  }

  /**
   * Health check for the service and underlying indexes
   */
  async healthCheck(organizationId?: string, options?: SearchOptionsEnhanced): Promise<boolean> {
    try {
      if (organizationId) {
        const embeddingIndex = await this.getEmbeddingIndex(organizationId, options);
        return await embeddingIndex.healthCheck();
      }

      // Check all cached indexes
      for (const embeddingIndex of this.indexCache.values()) {
        const isHealthy = await embeddingIndex.healthCheck();
        if (!isHealthy) {
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger?.error(error, '[SemanticSearchServiceEnhanced] Health check failed:');
      return false;
    }
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    this.indexCache.clear();
    this.embeddingService.clearCache();
    this.embeddingCache.clear();
    this.searchCache.clear();
    EmbeddingIndexFactory.clearCache();
  }

  // ========================================
  // Compatibility methods from agents' semantic search service
  // ========================================

  /**
   * Find relevant documents using semantic search
   * (Compatibility method from agents' semantic search service)
   */
  async findRelevantDocs(
    query: string,
    documents: ProcessedDocument[],
    topK: number = 3
  ): Promise<any[]> {
    if (!documents || documents.length === 0) {
      return [];
    }

    if (!query || query.trim().length === 0) {
      return [];
    }

    try {
      // Generate query embedding
      const queryEmbedding = await this.embeddingService.generateQueryEmbedding(query);

      // Get or generate document embeddings
      const docEmbeddings = await this.embeddingService.getDocumentEmbeddings(documents);

      // Calculate similarities and rank documents
      const rankedDocs = documents
        .map(doc => {
          const docEmbedding = docEmbeddings.get(doc.id);
          if (!docEmbedding) {
            return null;
          }

          const similarityScore = this.embeddingService.cosineSimilarity(
            queryEmbedding,
            docEmbedding
          );

          return {
            ...doc,
            similarityScore,
            rank: 0, // Will be set after sorting
          };
        })
        .filter((doc): doc is any => doc !== null)
        .sort((a, b) => b.similarityScore - a.similarityScore) // Sort by similarity descending
        .slice(0, topK) // Take top-k
        .map((doc, index) => ({
          ...doc,
          rank: index + 1,
        }));

      return rankedDocs;
    } catch (error) {
      // Fallback: return first topK documents
      return documents.slice(0, topK).map((doc, index) => ({
        ...doc,
        similarityScore: 0,
        rank: index + 1,
      }));
    }
  }

  /**
   * End-to-end semantic search pipeline used by AskAIAgent traditional RAG and SearchAgent tool.
   * (Compatibility method from agents' semantic search service)
   */
  async semanticSearchPipeline(
    args: SemanticSearchPipelineArgs
  ): Promise<SemanticSearchPipelineResult> {
    const { query, organizationId, documents: docsInput, topK = 3 } = args;

    let docs: any[] = [];

    if (docsInput && Array.isArray(docsInput) && docsInput.length > 0) {
      docs = docsInput;
    } else {
      // For compatibility, we'll use a mock document service
      // In a real implementation, this would call the actual document service
      docs = [];
    }

    if (docs.length === 0) {
      return {
        success: false,
        searchResults: [],
        documents: [],
        count: 0,
        message: 'No documents available for semantic search',
        averageScore: 0,
      };
    }

    // Document indexing is now handled externally via /v1/external/embeddings/update
    // We assume embeddings are already up-to-date and go straight to semantic search
    let searchResults = await this.search(query, {
      organizationId,
      topK,
      useRedis: true,
    });

    const avgScore =
      searchResults.length > 0
        ? searchResults.reduce((sum, r) => sum + (r.score || 0), 0) / searchResults.length
        : 0;

    let relevanceMessage = '';
    if (searchResults.length > 0) {
      if (avgScore > 0.8) relevanceMessage = 'High relevance matches found';
      else if (avgScore > 0.6) relevanceMessage = 'Moderate relevance matches found';
      else relevanceMessage = 'Low relevance matches found';
    }

    return {
      success: true,
      searchResults,
      documents: docs,
      count: searchResults.length,
      message: `Found ${searchResults.length} relevant results. ${relevanceMessage}`,
      averageScore: avgScore,
    };
  }

  /**
   * Execute a semantic search for `query` across the organisation's document
   * corpus using cosine similarity between the query embedding and every
   * document embedding.
   * (Compatibility method from agents' semantic search service)
   */
  async semanticSearch(query: string, options: SemanticSearchOptions): Promise<SearchResult[]> {
    try {
      // Generate query embedding
      const queryEmbedding = await this.generateEmbedding(query);

      // Get all documents for the organization
      const documents = await this.getAllDocuments(options.organizationId, options.userId);

      if (documents.length === 0) {
        return [];
      }

      // Calculate semantic similarity for each document
      const results = await this.calculateSemanticSimilarity(queryEmbedding, documents, options);

      // Filter by threshold and sort by relevance
      const filteredResults = results
        .filter(result => result.score >= (options.threshold || 0.1))
        .sort((a, b) => b.score - a.score)
        .slice(0, options.maxResults || 20);

      return filteredResults;
    } catch (error) {
      return [];
    }
  }

  /**
   * Perform an advanced search that takes the user's session context into
   * account. The method first runs a standard semantic search and afterwards
   * boosts scores based on user preferences and historical queries.
   * (Compatibility method from agents' semantic search service)
   */
  async contextualSearch(query: string, options: ContextualSearchOptions): Promise<SearchResult[]> {
    // First perform basic semantic search
    const baseResults = await this.semanticSearch(query, options);

    if (!options.context) {
      return baseResults;
    }

    // Enhance results with contextual scoring
    const contextualResults = await this.enhanceWithContext(baseResults, options.context);

    // Re-rank based on contextual relevance
    return contextualResults.sort((a, b) => b.score - a.score).slice(0, options.maxResults || 20);
  }

  /**
   * Retrieve documents similar to the one identified by `documentId` using the
   * document's own content as the search query.
   * (Compatibility method from agents' semantic search service)
   */
  async findSimilarDocumentsLegacy(
    documentId: string,
    options: SemanticSearchOptions
  ): Promise<SearchResult[]> {
    try {
      // Get the target document
      const targetDoc = await this.getDocumentById(documentId, options.organizationId);
      if (!targetDoc) {
        return [];
      }

      // Use document content as query
      const query = targetDoc.content || targetDoc.title || '';
      if (!query) {
        return [];
      }

      // Perform semantic search excluding the target document
      const results = await this.semanticSearch(query, options);
      return results.filter(result => result.id !== documentId);
    } catch (error) {
      return [];
    }
  }

  /**
   * Convenience helper that limits the semantic search to a single `category`
   * defined in the document metadata.
   * (Compatibility method from agents' semantic search service)
   */
  async searchByCategory(
    query: string,
    category: string,
    options: SemanticSearchOptions
  ): Promise<SearchResult[]> {
    const enhancedOptions = {
      ...options,
      filters: {
        ...options.filters,
        category,
      },
    };

    return this.semanticSearch(query, enhancedOptions);
  }

  /**
   * Execute multiple queries in parallel and combine their results, boosting
   * documents that appear in more than one individual result set.
   * (Compatibility method from agents' semantic search service)
   */
  async multiQuerySearch(
    queries: string[],
    options: SemanticSearchOptions
  ): Promise<{
    combinedResults: SearchResult[];
    individualResults: Record<string, SearchResult[]>;
  }> {
    // Execute all queries in parallel
    const searchPromises = queries.map(query =>
      this.semanticSearch(query, options).then(results => ({ query, results }))
    );

    const searchResults = await Promise.all(searchPromises);

    // Combine results with score boosting for documents that match multiple queries
    const combinedResultsMap = new Map<string, SearchResult>();
    const individualResults: Record<string, SearchResult[]> = {};

    searchResults.forEach(({ query, results }) => {
      individualResults[query] = results;

      results.forEach(result => {
        if (combinedResultsMap.has(result.id)) {
          // Boost score for documents that match multiple queries
          const existing = combinedResultsMap.get(result.id)!;
          existing.score = Math.min(existing.score + result.score * 0.3, 1.0);
          existing.metadata.matchedQueries = (existing.metadata.matchedQueries || 1) + 1;
        } else {
          combinedResultsMap.set(result.id, {
            ...result,
            metadata: {
              ...result.metadata,
              matchedQueries: 1,
            },
          });
        }
      });
    });

    const combinedResults = Array.from(combinedResultsMap.values()).sort(
      (a, b) => b.score - a.score
    );

    return {
      combinedResults,
      individualResults,
    };
  }

  /**
   * Return basic statistics about the current cache sizes so that callers can
   * decide when to evict or persist data.
   * (Compatibility method from agents' semantic search service)
   */
  getCacheStats(): {
    embeddingCacheSize: number;
    searchCacheSize: number;
  } {
    return {
      embeddingCacheSize: this.embeddingCache.size,
      searchCacheSize: this.searchCache.size,
    };
  }

  // ========================================
  // Private helper methods from agents' semantic search service
  // ========================================

  private async generateEmbedding(text: string): Promise<number[]> {
    // Check cache first
    if (this.embeddingCache.has(text)) {
      return this.embeddingCache.get(text)!;
    }

    try {
      // Use the embedding service
      const vector = await this.embeddingService.embedText(text);

      // Cache the result
      this.embeddingCache.set(text, vector);

      return vector;
    } catch (error) {
      // Return a default vector if embedding fails
      return new Array(384).fill(0);
    }
  }

  private async getAllDocuments(_organizationId?: string, _userId?: string): Promise<any[]> {
    try {
      // This would typically call the MCP tools to get documents
      // For now, return a mock dataset
      return [
        {
          id: 'doc1',
          title: 'Security Policy Document',
          content:
            'This document outlines the security policies and procedures for our organization...',
          metadata: { type: 'policy', category: 'security' },
        },
        {
          id: 'doc2',
          title: 'Vulnerability Assessment Report',
          content:
            'Comprehensive vulnerability assessment of network infrastructure and applications...',
          metadata: { type: 'report', category: 'assessment' },
        },
        // Add more mock documents as needed
      ];
    } catch (error) {
      return [];
    }
  }

  private async getDocumentById(id: string, organizationId?: string): Promise<any | null> {
    const documents = await this.getAllDocuments(organizationId);
    return documents.find(doc => doc.id === id) || null;
  }

  private async calculateSemanticSimilarity(
    queryEmbedding: number[],
    documents: any[],
    _options: SemanticSearchOptions
  ): Promise<SearchResult[]> {
    const results = [];

    for (const doc of documents) {
      try {
        // Generate embedding for document
        const docText = `${doc.title} ${doc.content || ''}`;
        const docEmbedding = await this.generateEmbedding(docText);

        // Calculate cosine similarity
        const similarity = this.cosineSimilarity(queryEmbedding, docEmbedding);

        results.push({
          id: doc.id,
          title: doc.title,
          content: doc.content,
          score: similarity,
          metadata: doc.metadata || {},
          embedding: docEmbedding,
        });
      } catch (error) {
        // Skip failed documents
      }
    }

    return results;
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    if (denominator === 0) return 0;

    return dotProduct / denominator;
  }

  private async enhanceWithContext(results: SearchResult[], context: any): Promise<SearchResult[]> {
    return results.map(result => {
      let contextBoost = 0;

      // Boost based on user preferences
      if (context.userPreferences) {
        const preferences = context.userPreferences;

        // Category preferences
        if (preferences.preferredCategories?.includes(result.metadata.category)) {
          contextBoost += 0.1;
        }

        // Document type preferences
        if (preferences.preferredDocumentTypes?.includes(result.metadata.type)) {
          contextBoost += 0.1;
        }

        // Recency preference
        if (preferences.preferRecent && result.metadata.createdAt) {
          const docAge = Date.now() - new Date(result.metadata.createdAt).getTime();
          const daysSinceCreation = docAge / (1000 * 60 * 60 * 24);
          if (daysSinceCreation < 30) {
            contextBoost += 0.05;
          }
        }
      }

      // Boost based on previous queries
      if (context.previousQueries?.length) {
        const queryTerms = context.previousQueries.join(' ').toLowerCase().split(/\s+/);
        const docText = `${result.title} ${result.content || ''}`.toLowerCase();

        const matchingTerms = queryTerms.filter((term: string) => docText.includes(term));
        contextBoost += (matchingTerms.length / queryTerms.length) * 0.1;
      }

      // Apply context boost
      const enhancedScore = Math.min(result.score + contextBoost, 1.0);

      return {
        ...result,
        score: enhancedScore,
        metadata: {
          ...result.metadata,
          contextBoost,
        },
      };
    });
  }

  // Private helper methods

  private generateHighlights(query: string, content: string): string[] {
    const highlights: string[] = [];
    const queryTerms = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();

    for (const term of queryTerms) {
      const index = contentLower.indexOf(term);
      if (index !== -1) {
        const start = Math.max(0, index - 50);
        const end = Math.min(content.length, index + term.length + 50);
        const highlight = content.substring(start, end);
        highlights.push(highlight);
      }
    }

    return highlights.slice(0, 3); // Return up to 3 highlights
  }

  private async rerankResults(
    query: string,
    results: SearchResultEnhanced[]
  ): Promise<SearchResultEnhanced[]> {
    // Simple re-ranking based on exact term matches
    const queryTerms = query.toLowerCase().split(/\s+/);

    const reranked = results
      .map(result => {
        const contentLower = result.content.toLowerCase();
        let boost = 0;

        for (const term of queryTerms) {
          const termCount = (contentLower.match(new RegExp(term, 'g')) || []).length;
          boost += termCount * 0.1;
        }

        const newScore = Math.min(result.score + boost, 1.0);

        return {
          ...result,
          score: newScore,
          rerankScore: newScore,
        };
      })
      .sort((a, b) => b.score - a.score);

    return reranked;
  }
}
