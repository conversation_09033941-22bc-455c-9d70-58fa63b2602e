import type { EmbeddingRedisClient as RedisClient } from './embedding-index/shared-types';

export interface KeyStructureAlert {
  timestamp: number;
  severity: 'warning' | 'error' | 'critical';
  message: string;
  details: {
    incorrectKeys: string[];
    organizationId: string;
    totalIncorrectKeys: number;
    totalCorrectKeys: number;
  };
}

export interface MonitoringConfig {
  checkIntervalMs: number;
  alertThreshold: number;
  organizations: string[];
  enableRealTimeAlerts: boolean;
}

export class RedisKeyMonitor {
  private redis: RedisClient;
  private config: MonitoringConfig;
  private isMonitoring: boolean = false;
  private checkInterval?: NodeJS.Timeout;
  private alertCallbacks: Array<(alert: KeyStructureAlert) => void> = [];

  constructor(redis: RedisClient, config: MonitoringConfig) {
    this.redis = redis;
    this.config = config;
  }

  /**
   * Start monitoring Redis key structure
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.warn('[RedisKeyMonitor] Monitoring already started');
      return;
    }

    console.log('[RedisKeyMonitor] Starting Redis key structure monitoring');
    this.isMonitoring = true;

    // Perform initial check
    await this.performKeyStructureCheck();

    // Set up periodic checks
    this.checkInterval = setInterval(async () => {
      await this.performKeyStructureCheck();
    }, this.config.checkIntervalMs);

    console.log(
      `[RedisKeyMonitor] Monitoring started with ${this.config.checkIntervalMs}ms interval`
    );
  }

  /**
   * Stop monitoring Redis key structure
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('[RedisKeyMonitor] Stopping Redis key structure monitoring');
    this.isMonitoring = false;

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
    }
  }

  /**
   * Add alert callback
   */
  onAlert(callback: (alert: KeyStructureAlert) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * Remove alert callback
   */
  removeAlertCallback(callback: (alert: KeyStructureAlert) => void): void {
    const index = this.alertCallbacks.indexOf(callback);
    if (index > -1) {
      this.alertCallbacks.splice(index, 1);
    }
  }

  /**
   * Perform a key structure check
   */
  async performKeyStructureCheck(): Promise<KeyStructureAlert[]> {
    const alerts: KeyStructureAlert[] = [];

    for (const organizationId of this.config.organizations) {
      try {
        const alert = await this.checkOrganizationKeys(organizationId);
        if (alert) {
          alerts.push(alert);
        }
      } catch (error) {
        console.error(`[RedisKeyMonitor] Error checking organization ${organizationId}:`, error);

        // Create error alert
        alerts.push({
          timestamp: Date.now(),
          severity: 'error',
          message: `Failed to check key structure for organization ${organizationId}`,
          details: {
            incorrectKeys: [],
            organizationId,
            totalIncorrectKeys: 0,
            totalCorrectKeys: 0,
          },
        });
      }
    }

    // Trigger alerts if enabled
    if (this.config.enableRealTimeAlerts) {
      for (const alert of alerts) {
        this.triggerAlert(alert);
      }
    }

    return alerts;
  }

  /**
   * Check key structure for a specific organization
   */
  private async checkOrganizationKeys(organizationId: string): Promise<KeyStructureAlert | null> {
    console.log(`[RedisKeyMonitor] Checking key structure for organization: ${organizationId}`);

    // Find all document keys for this organization
    const allDocKeys = await this.redis.keys('doc-*::*');
    const allEmbeddingKeys = await this.redis.keys(
      `embeddings:org-${organizationId.replace(/^org-/, '')}:*`
    );

    // Filter incorrect keys (doc-*::* that don't start with embeddings:)
    const incorrectKeys = allDocKeys.filter(key => !key.startsWith('embeddings:'));

    // Count correct keys for this organization
    const correctKeys = allEmbeddingKeys.filter(key =>
      key.includes(`org-${organizationId.replace(/^org-/, '')}`)
    );

    const totalIncorrectKeys = incorrectKeys.length;
    const totalCorrectKeys = correctKeys.length;

    console.log(`[RedisKeyMonitor] Organization ${organizationId} results:`, {
      totalIncorrectKeys,
      totalCorrectKeys,
      incorrectKeys: incorrectKeys.slice(0, 5), // Log first 5 for debugging
    });

    // Determine if we should create an alert
    if (totalIncorrectKeys > 0) {
      const severity = this.determineSeverity(totalIncorrectKeys, totalCorrectKeys);

      const alert: KeyStructureAlert = {
        timestamp: Date.now(),
        severity,
        message: this.generateAlertMessage(severity, totalIncorrectKeys, totalCorrectKeys),
        details: {
          incorrectKeys,
          organizationId,
          totalIncorrectKeys,
          totalCorrectKeys,
        },
      };

      return alert;
    }

    return null;
  }

  /**
   * Determine alert severity based on key counts
   */
  private determineSeverity(
    incorrectKeys: number,
    correctKeys: number
  ): 'warning' | 'error' | 'critical' {
    const totalKeys = incorrectKeys + correctKeys;
    const incorrectPercentage = totalKeys > 0 ? (incorrectKeys / totalKeys) * 100 : 0;

    if (incorrectKeys >= this.config.alertThreshold) {
      return 'critical';
    } else if (incorrectPercentage > 10) {
      return 'error';
    } else {
      return 'warning';
    }
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(
    severity: string,
    incorrectKeys: number,
    correctKeys: number
  ): string {
    const totalKeys = incorrectKeys + correctKeys;
    const percentage = totalKeys > 0 ? ((incorrectKeys / totalKeys) * 100).toFixed(1) : '0';

    switch (severity) {
      case 'critical':
        return `CRITICAL: ${incorrectKeys} Redis keys have incorrect structure (${percentage}% of total keys)`;
      case 'error':
        return `ERROR: ${incorrectKeys} Redis keys have incorrect structure (${percentage}% of total keys)`;
      case 'warning':
        return `WARNING: ${incorrectKeys} Redis keys have incorrect structure (${percentage}% of total keys)`;
      default:
        return `ALERT: ${incorrectKeys} Redis keys have incorrect structure`;
    }
  }

  /**
   * Trigger alert callbacks
   */
  private triggerAlert(alert: KeyStructureAlert): void {
    console.log(`[RedisKeyMonitor] Triggering ${alert.severity} alert:`, alert.message);

    for (const callback of this.alertCallbacks) {
      try {
        callback(alert);
      } catch (error) {
        console.error('[RedisKeyMonitor] Error in alert callback:', error);
      }
    }
  }

  /**
   * Get monitoring status
   */
  getStatus(): {
    isMonitoring: boolean;
    config: MonitoringConfig;
    lastCheck?: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      config: this.config,
    };
  }

  /**
   * Get statistics for all organizations
   */
  async getStatistics(): Promise<{
    organizations: Record<
      string,
      {
        totalKeys: number;
        correctKeys: number;
        incorrectKeys: number;
        incorrectPercentage: number;
      }
    >;
    summary: {
      totalOrganizations: number;
      totalKeys: number;
      totalCorrectKeys: number;
      totalIncorrectKeys: number;
      overallIncorrectPercentage: number;
    };
  }> {
    const organizations: Record<string, any> = {};
    let totalKeys = 0;
    let totalCorrectKeys = 0;
    let totalIncorrectKeys = 0;

    for (const organizationId of this.config.organizations) {
      try {
        const allDocKeys = await this.redis.keys('doc-*::*');
        const allEmbeddingKeys = await this.redis.keys(
          `embeddings:org-${organizationId.replace(/^org-/, '')}:*`
        );

        const incorrectKeys = allDocKeys.filter(key => !key.startsWith('embeddings:'));
        const correctKeys = allEmbeddingKeys.filter(key =>
          key.includes(`org-${organizationId.replace(/^org-/, '')}`)
        );

        const orgTotalKeys = incorrectKeys.length + correctKeys.length;
        const incorrectPercentage =
          orgTotalKeys > 0 ? (incorrectKeys.length / orgTotalKeys) * 100 : 0;

        organizations[organizationId] = {
          totalKeys: orgTotalKeys,
          correctKeys: correctKeys.length,
          incorrectKeys: incorrectKeys.length,
          incorrectPercentage: Math.round(incorrectPercentage * 100) / 100,
        };

        totalKeys += orgTotalKeys;
        totalCorrectKeys += correctKeys.length;
        totalIncorrectKeys += incorrectKeys.length;
      } catch (error) {
        console.error(`[RedisKeyMonitor] Error getting statistics for ${organizationId}:`, error);
        organizations[organizationId] = {
          totalKeys: 0,
          correctKeys: 0,
          incorrectKeys: 0,
          incorrectPercentage: 0,
        };
      }
    }

    const overallIncorrectPercentage = totalKeys > 0 ? (totalIncorrectKeys / totalKeys) * 100 : 0;

    return {
      organizations,
      summary: {
        totalOrganizations: this.config.organizations.length,
        totalKeys,
        totalCorrectKeys,
        totalIncorrectKeys,
        overallIncorrectPercentage: Math.round(overallIncorrectPercentage * 100) / 100,
      },
    };
  }

  /**
   * Health check for the monitor
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    message: string;
    details?: any;
  }> {
    try {
      // Test Redis connection
      await this.redis.ping();

      // Check if monitoring is active
      if (!this.isMonitoring) {
        return {
          status: 'unhealthy',
          message: 'Monitoring is not active',
        };
      }

      // Perform a quick key structure check
      const alerts = await this.performKeyStructureCheck();
      const hasCriticalAlerts = alerts.some(alert => alert.severity === 'critical');

      if (hasCriticalAlerts) {
        return {
          status: 'unhealthy',
          message: 'Critical key structure issues detected',
          details: {
            alertCount: alerts.length,
            criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
          },
        };
      }

      return {
        status: 'healthy',
        message: 'Redis key monitor is healthy',
        details: {
          alertCount: alerts.length,
          isMonitoring: this.isMonitoring,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: {
          error: error instanceof Error ? error.stack : undefined,
        },
      };
    }
  }
}
