// Type declaration for html-to-text module
declare module 'html-to-text' {
  export function convert(html: string, options?: any): string;
}

import { ProcessedDocument } from './document-processor';
import { convert } from 'html-to-text';

export interface DocumentAnalysisResult {
  documentId: string;
  contentType: 'text' | 'html' | 'markdown' | 'code' | 'mixed';
  wordCount: number;
  characterCount: number;
  paragraphCount: number;
  sentenceCount: number;
  readabilityScore?: number;
  keyTopics?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
  language?: string;
  quality: 'high' | 'medium' | 'low';
  metadata: Record<string, any>;
}

export interface ContentClassification {
  category: string;
  confidence: number;
  subcategories?: string[];
  tags?: string[];
}

export interface MetadataExtractionResult {
  title?: string;
  author?: string;
  createdDate?: Date;
  modifiedDate?: Date;
  keywords?: string[];
  description?: string;
  category?: string;
  fileSize?: number;
  pageCount?: number;
  customFields?: Record<string, any>;
}

export interface SanitizationOptions {
  removeHtml?: boolean;
  removeMarkdown?: boolean;
  normalizeWhitespace?: boolean;
  removeUrls?: boolean;
  removeEmails?: boolean;
  preserveLineBreaks?: boolean;
  maxLength?: number;
}

/**
 * Creates a new DocumentAnalyzerService.
 *
 * The class is largely stateless and can be reused across your application. It maintains
 * a single internal counter (`analysisCount`) that is incremented every time
 * {@link analyzeDocument} is executed. No external services are required – all algorithms
 * rely on synchronous string processing or the lightweight `html-to-text` conversion.
 *
 * @example
 * ```ts
 * import { DocumentAnalyzerService } from "./document-analyzer-service";
 * const analyzer = new DocumentAnalyzerService();
 * const result = await analyzer.analyzeDocument(processedDoc);
 * console.log(result.wordCount);
 * ```
 */
export class DocumentAnalyzerService {
  private analysisCount = 0;

  /**
   * Instantiate the service.
   *
   * Currently no arguments are required – further dependencies (e.g. language models)
   * could be injected here in the future. The only side-effect is initialising the
   * internal `analysisCount` metric to **0**.
   */
  constructor() {}

  /**
   * Perform a **comprehensive analysis** of a {@link ProcessedDocument}. The routine derives a
   * wide range of linguistic and structural statistics such as word count, readability score
   * and sentiment, then packages the results together with basic file metadata in a
   * {@link DocumentAnalysisResult} object.
   *
   * Algorithmic overview:
   * 1. Detect dominant content type (plain-text / HTML / markdown / code) by heuristic regex.
   * 2. Compute token-based metrics – words, characters, paragraphs and sentences.
   * 3. Estimate readability using the [Flesch Reading-Ease](https://en.wikipedia.org/wiki/Flesch%E2%80%93Kincaid_readability_tests) formula.
   * 4. Extract keyword frequency to surface *keyTopics*.
   * 5. Apply a naive polarity counter for *sentiment*.
   * 6. Run lightweight language detection and a quality heuristic that rewards structure and
   *    lexical diversity.
   *
   * @param document – The `ProcessedDocument` previously produced by the document-processor
   *                  pipeline. Its `content` field **must** be a non-empty UTF-8 string.
   * @returns A populated {@link DocumentAnalysisResult} instance.
   * @throws Error If the document has empty content.
   *
   * @example
   * ```ts
   * const analysis = await analyzer.analyzeDocument(doc);
   * console.log(`Readability: ${analysis.readabilityScore}`);
   * ```
   */
  async analyzeDocument(document: ProcessedDocument): Promise<DocumentAnalysisResult> {
    if (!document.content?.trim()) {
      throw new Error('Document content cannot be empty');
    }

    this.analysisCount++;

    const content = document.content;
    const contentType = this.detectContentType(content);
    const wordCount = this.countWords(content);
    const characterCount = content.length;
    const paragraphCount = this.countParagraphs(content);
    const sentenceCount = this.countSentences(content);
    const readabilityScore = this.calculateReadabilityScore(content);
    const keyTopics = this.extractKeyTopics(content);
    const sentiment = this.analyzeSentiment(content);
    const language = this.detectLanguage(content);
    const quality = this.assessQuality(content, wordCount, sentenceCount);

    return {
      documentId: document.id,
      contentType,
      wordCount,
      characterCount,
      paragraphCount,
      sentenceCount,
      readabilityScore,
      keyTopics,
      sentiment,
      language,
      quality,
      metadata: {
        ...document.metadata,
        documentType: document.documentType,
        fileType: document.fileType,
        createdAt: document.createdAt,
        updatedAt: document.updatedAt,
        analysisTimestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Classify an **arbitrary text snippet** into a coarse content category *(security, technical,
   * documentation, policy, incident, general)* by counting occurrences of predefined keyword
   * sets. The function returns the winning category alongside a confidence score and basic
   * tag/sub-category enrichment.
   *
   * @param content – Raw content string (UTF-8).
   * @returns A {@link ContentClassification} structure describing the assigned category.
   * @example
   * ```ts
   * const { category, confidence } = analyzer.classifyContent(text);
   * ```
   */
  classifyContent(content: string): ContentClassification {
    const contentLower = content.toLowerCase();
    const words = contentLower.split(/\s+/);

    // Define classification patterns
    const categories = {
      security: {
        keywords: [
          'security',
          'vulnerability',
          'threat',
          'attack',
          'malware',
          'encryption',
          'firewall',
          'breach',
          'hack',
          'cyber',
        ],
        weight: 0,
      },
      technical: {
        keywords: [
          'api',
          'database',
          'server',
          'code',
          'programming',
          'software',
          'development',
          'framework',
          'library',
        ],
        weight: 0,
      },
      documentation: {
        keywords: [
          'guide',
          'manual',
          'instructions',
          'tutorial',
          'documentation',
          'readme',
          'how-to',
          'specification',
        ],
        weight: 0,
      },
      policy: {
        keywords: [
          'policy',
          'procedure',
          'compliance',
          'regulation',
          'governance',
          'standard',
          'requirement',
          'guideline',
        ],
        weight: 0,
      },
      incident: {
        keywords: [
          'incident',
          'issue',
          'problem',
          'error',
          'failure',
          'outage',
          'alert',
          'emergency',
        ],
        weight: 0,
      },
    };

    // Calculate weights based on keyword frequency
    for (const word of words) {
      for (const [, config] of Object.entries(categories)) {
        if (config.keywords.includes(word)) {
          config.weight += 1;
        }
      }
    }

    // Find category with highest weight
    let bestCategory = 'general';
    let bestWeight = 0;
    let confidence = 0;

    for (const [category, config] of Object.entries(categories)) {
      if (config.weight > bestWeight) {
        bestWeight = config.weight;
        bestCategory = category;
      }
    }

    // Calculate confidence based on keyword density
    confidence = Math.min(1.0, bestWeight / Math.max(1, words.length / 100));

    const subcategories = this.extractSubcategories(content, bestCategory);
    const tags = this.extractTags(content);

    return {
      category: bestCategory,
      confidence,
      subcategories,
      tags,
    };
  }

  /**
   * Extract metadata such as *title*, *author*, *keywords* and file size from a processed
   * document. Multiple heuristics are applied (e.g. first markdown heading for title).
   *
   * @param document – A {@link ProcessedDocument} whose `content` field is analysed.
   * @returns A {@link MetadataExtractionResult} with best-effort fields filled in.
   */
  extractMetadata(document: ProcessedDocument): MetadataExtractionResult {
    const content = document.content;
    const metadata: MetadataExtractionResult = {};

    // Extract title (first line or heading)
    metadata.title = this.extractTitle(content) || document.name;

    // Extract dates from content
    const dates = this.extractDates(content);
    if (dates.length > 0) {
      metadata.createdDate = dates[0];
      if (dates.length > 1) {
        metadata.modifiedDate = dates[dates.length - 1];
      }
    }

    // Use document timestamps as fallback
    if (!metadata.createdDate && document.createdAt) {
      metadata.createdDate = new Date(document.createdAt);
    }
    if (!metadata.modifiedDate && document.updatedAt) {
      metadata.modifiedDate = new Date(document.updatedAt);
    }

    // Extract keywords
    metadata.keywords = this.extractKeywords(content);

    // Extract description (first paragraph or summary)
    metadata.description = this.extractDescription(content);

    // Extract author from content patterns
    metadata.author = this.extractAuthor(content);

    // Calculate file size
    metadata.fileSize = Buffer.byteLength(content, 'utf8');

    // Estimate page count (assuming ~250 words per page)
    const wordCount = this.countWords(content);
    metadata.pageCount = Math.ceil(wordCount / 250);

    // Set category based on document type or content analysis
    metadata.category = document.documentType || this.classifyContent(content).category;

    return metadata;
  }

  /**
   * Sanitize arbitrary content **in-memory** – stripping HTML, Markdown, URLs, e-mails and
   * normalising whitespace while optionally truncating excessively long input. Uses `html-to-text`
   * under the hood for robust HTML parsing.
   *
   * @param content – The raw string to sanitise.
   * @param options – Fine-grained {@link SanitizationOptions} controlling the cleaning steps.
   * @returns Clean plain-text string suitable for downstream NLP.
   *
   * @example
   * ```ts
   * const safe = analyzer.sanitizeContent(rawHtml, { removeUrls: true });
   * ```
   */
  sanitizeContent(content: string, options: SanitizationOptions = {}): string {
    const {
      removeHtml = true,
      removeMarkdown = false,
      normalizeWhitespace = true,
      removeUrls = false,
      removeEmails = false,
      preserveLineBreaks = true,
      maxLength,
    } = options;

    let sanitized = content;

    // Remove HTML tags and convert to plain text
    if (removeHtml) {
      sanitized = convert(sanitized, {
        wordwrap: false,
        preserveNewlines: preserveLineBreaks,
        selectors: [
          { selector: 'img', format: 'skip' },
          { selector: 'script', format: 'skip' },
          { selector: 'style', format: 'skip' },
        ],
      });
    }

    // Remove markdown formatting
    if (removeMarkdown) {
      sanitized = this.removeMarkdownFormatting(sanitized);
    }

    // Remove URLs
    if (removeUrls) {
      sanitized = sanitized.replace(/https?:\/\/[^\s]+/g, '');
    }

    // Remove email addresses
    if (removeEmails) {
      sanitized = sanitized.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '');
    }

    // Normalize whitespace
    if (normalizeWhitespace) {
      sanitized = sanitized.replace(/\s+/g, ' ').trim();
      if (preserveLineBreaks) {
        sanitized = sanitized.replace(/\. /g, '.\n');
      }
    }

    // Truncate if needed
    if (maxLength && sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength).trim() + '...';
    }

    return sanitized;
  }

  /**
   * Get **runtime statistics** for this service instance.
   *
   * @returns Object with a single `analysisCount` field.
   */
  getStats() {
    return {
      analysisCount: this.analysisCount,
    };
  }

  // Private helper methods

  /**
   * Detect dominant content type by applying regular expressions for HTML tags, markdown
   * syntax and common source-code tokens.
   *
   * @param content – Text to inspect.
   * @returns One of `'text' | 'html' | 'markdown' | 'code' | 'mixed'`.
   */
  private detectContentType(content: string): 'text' | 'html' | 'markdown' | 'code' | 'mixed' {
    const htmlPattern = /<[^>]+>/;
    const markdownPattern = /#{1,6}\s|`{3}|\*{1,2}[^*]+\*{1,2}|\[.+\]\(.+\)/;
    const codePattern = /function\s+\w+|class\s+\w+|import\s+|export\s+|const\s+\w+\s*=/;

    const hasHtml = htmlPattern.test(content);
    const hasMarkdown = markdownPattern.test(content);
    const hasCode = codePattern.test(content);

    if (hasHtml && (hasMarkdown || hasCode)) return 'mixed';
    if (hasHtml) return 'html';
    if (hasMarkdown) return 'markdown';
    if (hasCode) return 'code';
    return 'text';
  }

  /** Calculate whitespace-trimmed word count. */
  private countWords(content: string): number {
    return content
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0).length;
  }

  /** Count paragraphs separated by blank lines. */
  private countParagraphs(content: string): number {
    return content.split(/\n\s*\n/).filter(para => para.trim().length > 0).length;
  }

  /** Count sentences by splitting on `.`, `!`, `?`. */
  private countSentences(content: string): number {
    return content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
  }

  /**
   * Approximate readability via Flesch Reading-Ease.
   * @see https://en.wikipedia.org/wiki/Flesch%E2%80%93Kincaid_readability_tests
   */
  private calculateReadabilityScore(content: string): number {
    const words = this.countWords(content);
    const sentences = this.countSentences(content);
    const syllables = this.countSyllables(content);

    if (sentences === 0 || words === 0) return 0;

    // Flesch Reading Ease Score
    const score = 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words);
    return Math.max(0, Math.min(100, score));
  }

  /** Naive syllable estimator based on vowel clusters. */
  private countSyllables(content: string): number {
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    let syllableCount = 0;

    for (const word of words) {
      const vowels = word.match(/[aeiouy]+/g);
      syllableCount += vowels ? vowels.length : 1;
    }

    return syllableCount;
  }

  /** Return the 10 most frequent words ≥4 chars as key topics. */
  private extractKeyTopics(content: string): string[] {
    const words = content.toLowerCase().match(/\b\w{4,}\b/g) || [];

    const frequency: Record<string, number> = {};
    for (const word of words) {
      frequency[word] = (frequency[word] || 0) + 1;
    }

    return Object.entries(frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /** Simple polarity counter over curated positive/negative lexicons. */
  private analyzeSentiment(content: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = [
      'good',
      'great',
      'excellent',
      'success',
      'effective',
      'secure',
      'safe',
      'reliable',
    ];
    const negativeWords = [
      'bad',
      'poor',
      'failure',
      'error',
      'vulnerable',
      'risk',
      'threat',
      'dangerous',
    ];

    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    for (const word of words) {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    }

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /** Lightweight English detector based on stop-word ratio. */
  private detectLanguage(content: string): string {
    // Simple language detection based on common words
    const englishWords = [
      'the',
      'and',
      'for',
      'are',
      'but',
      'not',
      'you',
      'all',
      'can',
      'had',
      'her',
      'was',
      'one',
      'our',
      'out',
      'day',
      'get',
      'has',
      'him',
      'his',
      'how',
      'man',
      'new',
      'now',
      'old',
      'see',
      'two',
      'way',
      'who',
      'boy',
      'did',
      'its',
      'let',
      'put',
      'say',
      'she',
      'too',
      'use',
    ];

    const words = content.toLowerCase().split(/\s+/);
    const englishMatches = words.filter(word => englishWords.includes(word)).length;
    const englishRatio = englishMatches / Math.max(1, words.length);

    return englishRatio > 0.1 ? 'en' : 'unknown';
  }

  /**
   * Multi-factor quality heuristic rewarding length, structure and vocabulary diversity.
   */
  private assessQuality(
    content: string,
    wordCount: number,
    sentenceCount: number
  ): 'high' | 'medium' | 'low' {
    const avgWordsPerSentence = sentenceCount > 0 ? wordCount / sentenceCount : 0;
    const hasStructure = /\n\s*\n/.test(content); // Has paragraphs
    const hasVariety = new Set(content.toLowerCase().split(/\s+/)).size / wordCount;

    let qualityScore = 0;

    // Length criteria
    if (wordCount > 100) qualityScore += 1;
    if (wordCount > 500) qualityScore += 1;

    // Structure criteria
    if (hasStructure) qualityScore += 1;
    if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 25) qualityScore += 1;

    // Vocabulary diversity
    if (hasVariety > 0.5) qualityScore += 1;

    if (qualityScore >= 4) return 'high';
    if (qualityScore >= 2) return 'medium';
    return 'low';
  }

  /** Determine sub-categories from keyword presence. */
  private extractSubcategories(content: string, category: string): string[] {
    const subcategoryMap: Record<string, string[]> = {
      security: ['vulnerability-assessment', 'incident-response', 'compliance', 'risk-management'],
      technical: ['api-documentation', 'system-architecture', 'troubleshooting', 'configuration'],
      documentation: ['user-guide', 'admin-guide', 'api-reference', 'tutorial'],
      policy: ['security-policy', 'access-control', 'data-governance', 'compliance-framework'],
      incident: ['security-incident', 'system-outage', 'data-breach', 'service-disruption'],
    };

    const subcategories = subcategoryMap[category] || [];
    const contentLower = content.toLowerCase();

    return subcategories.filter(sub => sub.split('-').some(word => contentLower.includes(word)));
  }

  /** Extract predefined tags (e.g., confidential, draft). */
  private extractTags(content: string): string[] {
    const commonTags = [
      'urgent',
      'confidential',
      'public',
      'internal',
      'draft',
      'final',
      'review',
      'approved',
    ];
    const contentLower = content.toLowerCase();

    return commonTags.filter(tag => contentLower.includes(tag));
  }

  /** Heuristic title discovery – first markdown header or short first line. */
  private extractTitle(content: string): string | undefined {
    // Try to find title in various formats
    const lines = content.split('\n');

    // Look for markdown heading
    for (const line of lines.slice(0, 5)) {
      const trimmed = line.trim();
      if (trimmed.startsWith('#')) {
        return trimmed.replace(/^#+\s*/, '').trim();
      }
      if (trimmed.length > 0 && trimmed.length < 100) {
        return trimmed;
      }
    }

    return undefined;
  }

  /** Parse and chronologically sort date literals found in text. */
  private extractDates(content: string): Date[] {
    const datePattern = /\b\d{4}-\d{2}-\d{2}\b|\b\d{1,2}\/\d{1,2}\/\d{4}\b|\b\w+ \d{1,2}, \d{4}\b/g;
    const matches = content.match(datePattern) || [];

    return matches
      .map(match => new Date(match))
      .filter(date => !isNaN(date.getTime()))
      .sort((a, b) => a.getTime() - b.getTime());
  }

  /** Keyword extraction ignoring common English stop-words. */
  private extractKeywords(content: string): string[] {
    const words = content.toLowerCase().match(/\b\w{3,}\b/g) || [];

    const stopWords = new Set([
      'the',
      'and',
      'for',
      'are',
      'but',
      'not',
      'you',
      'all',
      'can',
      'had',
      'her',
      'was',
      'one',
      'our',
      'out',
      'day',
      'get',
      'has',
      'him',
      'his',
      'how',
      'man',
      'new',
      'now',
      'old',
      'see',
      'two',
      'way',
      'who',
      'boy',
      'did',
      'its',
      'let',
      'put',
      'say',
      'she',
      'too',
      'use',
    ]);

    const frequency: Record<string, number> = {};
    for (const word of words) {
      if (!stopWords.has(word)) {
        frequency[word] = (frequency[word] || 0) + 1;
      }
    }

    return Object.entries(frequency)
      .filter(([, count]) => count > 1)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 15)
      .map(([word]) => word);
  }

  /** Grab the first descriptive paragraph between 50-300 chars. */
  private extractDescription(content: string): string | undefined {
    const paragraphs = content.split(/\n\s*\n/);
    for (const para of paragraphs.slice(0, 3)) {
      const trimmed = para.trim();
      if (trimmed.length > 50 && trimmed.length < 300) {
        return trimmed;
      }
    }
    return undefined;
  }

  /** Attempt to find an author line via regex patterns. */
  private extractAuthor(content: string): string | undefined {
    const authorPatterns = [
      /author:\s*([^\n]+)/i,
      /by\s+([A-Za-z\s]+)/i,
      /created\s+by\s+([A-Za-z\s]+)/i,
    ];

    for (const pattern of authorPatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  /** Strip markdown formatting tokens while keeping plain text. */
  private removeMarkdownFormatting(content: string): string {
    return content
      .replace(/#{1,6}\s/g, '') // Remove headings
      .replace(/\*{1,2}([^*]+)\*{1,2}/g, '$1') // Remove bold/italic
      .replace(/`([^`]+)`/g, '$1') // Remove inline code
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1'); // Remove images, keep alt text
  }
}
