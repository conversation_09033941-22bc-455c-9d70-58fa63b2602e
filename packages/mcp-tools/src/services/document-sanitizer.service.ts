import { convert as htmlToText } from 'html-to-text';

/**
 * Utility responsible for converting raw HTML / mixed-format strings into **clean plain text**.
 * It leverages the battle-tested `html-to-text` library but also provides multiple fallbacks so
 * that it works in constrained serverless environments where native HTML parsers might be
 * missing.
 *
 * All operations are *pure* – no external state is mutated. Therefore the service can be safely
 * shared between requests.
 *
 * @example
 * ```ts
 * const sanitizer = new DocumentSanitizerService();
 * const plain = sanitizer.sanitize(rawHtml);
 * ```
 */
export class DocumentSanitizerService {
  /**
   * Convert an arbitrary string to plain text.
   *
   * Processing pipeline:
   * 1. Quick binary detection – bail out early if the string contains NUL bytes or an excessive
   *    amount of non-printable characters.
   * 2. Run `html-to-text` with a minimal selector config to drop scripts/styles while preserving
   *    semantic blocks.
   * 3. Post-process the output to normalise whitespace.
   * 4. If the third-party parser fails (malformed HTML or library not available) fall back to a
   *    lightweight regex-based scrubber.
   *
   * @param rawContent – Raw document string potentially containing HTML markup.
   * @returns Sanitised plain-text representation. Returns an **empty string** for binary payloads
   *          or when sanitisation fails.
   */
  sanitize(rawContent: string): string {
    if (!rawContent || typeof rawContent !== 'string') {
      return '';
    }

    // Check for binary content or non-UTF-8 artifacts
    if (this.isBinaryContent(rawContent)) {
      return '';
    }

    try {
      // Use html-to-text to convert HTML to clean plain text
      const cleanText = htmlToText(rawContent, {
        wordwrap: false, // Don't add line breaks
        selectors: [
          // Remove script and style content completely
          { selector: 'script', format: 'skip' },
          { selector: 'style', format: 'skip' },
          // Convert common elements to plain text with minimal formatting
          { selector: 'h1', format: 'block' },
          { selector: 'h2', format: 'block' },
          { selector: 'h3', format: 'block' },
          { selector: 'h4', format: 'block' },
          { selector: 'h5', format: 'block' },
          { selector: 'h6', format: 'block' },
          { selector: 'p', format: 'block' },
          { selector: 'div', format: 'block' },
          { selector: 'br', format: 'lineBreak' },
          { selector: 'li', format: 'block' },
          // Skip buttons and other interactive elements that don't contain useful content
          { selector: 'button', format: 'skip' },
          { selector: 'input', format: 'skip' },
          { selector: 'select', format: 'skip' },
          { selector: 'textarea', format: 'skip' },
        ],
      });

      // Clean up the result
      return this.postProcessText(cleanText);
    } catch (error) {
      // Fallback to regex-based cleaning if html-to-text fails
      console.warn('[DocumentSanitizerService] html-to-text failed, using fallback:', error);
      return this.fallbackSanitize(rawContent);
    }
  }

  /**
   * Heuristic check to quickly discard binary blobs (e.g. images erroneously decoded as UTF-8)
   * before more expensive parsing is attempted.
   *
   * @param content – String to inspect.
   * @returns `true` if the content appears binary.
   */
  private isBinaryContent(content: string): boolean {
    // Check for null bytes or other binary indicators
    if (content.includes('\0') || content.includes('�')) {
      return true;
    }

    // Check for very high ratio of non-printable characters
    const printableChars = content.match(/[\x20-\x7E\s]/g) || [];
    const printableRatio = printableChars.length / content.length;

    return printableRatio < 0.7; // If less than 70% printable, likely binary
  }

  /**
   * Normalise whitespace by removing Windows line endings, collapsing blank lines and trimming
   * trailing spaces.
   *
   * @param text – Plain text.
   * @returns Cleaned text.
   */
  private postProcessText(text: string): string {
    return (
      text
        // Normalize Windows line endings
        .replace(/\r/g, '')
        // Collapse 3+ blank lines to exactly 2 (paragraph separator)
        .replace(/\n{3,}/g, '\n\n')
        // Trim spaces at line starts/ends
        .split('\n')
        .map(line => line.trim())
        .join('\n')
        // Collapse multiple spaces within a line
        .replace(/ {2,}/g, ' ')
        .trim()
    );
  }

  /**
   * **Fallback** sanitisation routine used when the `html-to-text` dependency is unavailable or
   * throws. Relies solely on regular expressions therefore it is less accurate but has zero
   * external dependencies.
   *
   * @param rawContent – Raw HTML string.
   * @returns Sanitised plain text.
   */
  private fallbackSanitize(rawContent: string): string {
    try {
      let cleaned = rawContent
        // Remove script and style blocks completely
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        // Remove HTML comments
        .replace(/<!--[\s\S]*?-->/g, '')
        // Remove all HTML tags
        .replace(/<[^>]*>/g, ' ')
        // Decode common HTML entities
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&apos;/g, "'");

      return this.postProcessText(cleaned);
    } catch (error) {
      console.error('[DocumentSanitizerService] Fallback sanitization failed:', error);
      return '';
    }
  }
}
