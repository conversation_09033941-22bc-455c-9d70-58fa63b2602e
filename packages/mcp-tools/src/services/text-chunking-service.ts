import { ProcessedDocument } from './document-processor';

export interface TextChunk {
  id: string;
  content: string;
  chunkNumber: number;
  totalChunks: number;
  documentId: string;
  metadata?: Record<string, any>;
  startToken?: number;
  endToken?: number;
}

export interface ChunkingOptions {
  maxTokens?: number;
  overlapTokens?: number;
  preserveParagraphs?: boolean;
  preserveSentences?: boolean;
  minChunkSize?: number;
}

export interface ChunkedDocument {
  documentId: string;
  chunks: TextChunk[];
  totalChunks: number;
  originalContent: string;
  metadata?: Record<string, any>;
}

/**
 * Service for intelligently chunking large text documents while preserving semantic coherence.
 * Handles token limits for embedding models and provides overlap strategies.
 */
export class TextChunkingService {
  private readonly DEFAULT_MAX_TOKENS = 8000; // Conservative limit for text-embedding-3-large
  private readonly DEFAULT_OVERLAP_TOKENS = 200;
  private readonly MIN_CHUNK_SIZE = 100; // Minimum tokens per chunk
  private readonly TOKENS_PER_CHAR = 0.25; // Rough estimate for token calculation

  /**
   * Chunk a single document into multiple chunks
   */
  async chunkDocument(
    document: ProcessedDocument,
    options: ChunkingOptions = {}
  ): Promise<ChunkedDocument> {
    const {
      maxTokens = this.DEFAULT_MAX_TOKENS,
      overlapTokens = this.DEFAULT_OVERLAP_TOKENS,
      preserveParagraphs = true,
      preserveSentences = true,
      minChunkSize = this.MIN_CHUNK_SIZE,
    } = options;

    if (!document.content?.trim()) {
      return {
        documentId: document.id,
        chunks: [],
        totalChunks: 0,
        originalContent: document.content || '',
        metadata: document.metadata,
      };
    }

    // Normalize escaped newlines for JSON-like payloads to improve paragraph detection
    let content = document.content.trim();
    const looksLikeEscapedJson = /\n/.test(content) && /\"|\n|\t/.test(content);
    if (looksLikeEscapedJson) {
      try {
        // If it's a JSON string containing escaped newlines, unescape common sequences
        content = content.replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\r/g, '\r');
      } catch (_e) {
        // Best-effort normalization; ignore failures
      }
    }
    const estimatedTokens = this.estimateTokenCount(content);

    // If content fits within token limit, return as single chunk
    if (estimatedTokens <= maxTokens) {
      return {
        documentId: document.id,
        chunks: [
          {
            id: `${document.id}::1`,
            content,
            chunkNumber: 1,
            totalChunks: 1,
            documentId: document.id,
            metadata: document.metadata,
            startToken: 0,
            endToken: estimatedTokens,
          },
        ],
        totalChunks: 1,
        originalContent: content,
        metadata: document.metadata,
      };
    }

    // Split content into chunks
    const chunks = this.splitContentIntoTokens(
      content,
      maxTokens,
      overlapTokens,
      preserveParagraphs,
      preserveSentences,
      minChunkSize
    );

    // Create chunk objects
    const textChunks: TextChunk[] = chunks.map((chunk, index) => ({
      id: `${document.id}::${index + 1}`,
      content: chunk.content,
      chunkNumber: index + 1,
      totalChunks: chunks.length,
      documentId: document.id,
      metadata: {
        ...document.metadata,
        chunkNumber: index + 1,
        totalChunks: chunks.length,
        isChunked: true,
      },
      startToken: chunk.startToken,
      endToken: chunk.endToken,
    }));

    return {
      documentId: document.id,
      chunks: textChunks,
      totalChunks: chunks.length,
      originalContent: content,
      metadata: document.metadata,
    };
  }

  /**
   * Chunk multiple documents
   */
  async chunkDocuments(
    documents: ProcessedDocument[],
    options: ChunkingOptions = {}
  ): Promise<ChunkedDocument[]> {
    const results = await Promise.all(documents.map(doc => this.chunkDocument(doc, options)));
    return results;
  }

  /**
   * Reconstruct original document from chunks
   */
  reconstructDocument(chunks: TextChunk[]): string {
    if (chunks.length === 0) return '';

    // Sort chunks by chunk number
    const sortedChunks = chunks.sort((a, b) => a.chunkNumber - b.chunkNumber);

    // For overlapping chunks, we need to handle overlap removal
    let reconstructed = '';
    let lastEndToken = 0;

    for (const chunk of sortedChunks) {
      if (chunk.startToken !== undefined && chunk.endToken !== undefined) {
        // If there's overlap, we need to calculate how much to skip
        const overlapSize = Math.max(0, lastEndToken - chunk.startToken);
        const contentToAdd = chunk.content.slice(overlapSize);
        reconstructed += contentToAdd;
        lastEndToken = chunk.endToken;
      } else {
        // Fallback: just concatenate content
        reconstructed += chunk.content;
      }
    }

    return reconstructed;
  }

  /**
   * Estimate token count for a text string
   */
  estimateTokenCount(text: string): number {
    if (!text) return 0;

    // Rough estimation: 1 token ≈ 4 characters for English text
    // This is a conservative estimate for text-embedding-3-large
    return Math.ceil(text.length * this.TOKENS_PER_CHAR);
  }

  /**
   * Split content into token-based chunks while preserving semantic boundaries
   */
  private splitContentIntoTokens(
    content: string,
    maxTokens: number,
    overlapTokens: number,
    preserveParagraphs: boolean,
    _preserveSentences: boolean,
    minChunkSize: number
  ): Array<{ content: string; startToken: number; endToken: number }> {
    const chunks: Array<{ content: string; startToken: number; endToken: number }> = [];
    let currentPosition = 0;
    let currentTokenCount = 0;
    let chunkStartToken = 0;

    // Split content into paragraphs first
    const paragraphs = preserveParagraphs ? this.splitIntoParagraphs(content) : [content];

    for (const paragraph of paragraphs) {
      const paragraphTokens = this.estimateTokenCount(paragraph);

      // If paragraph fits in current chunk
      if (currentTokenCount + paragraphTokens <= maxTokens) {
        currentTokenCount += paragraphTokens;
        currentPosition += paragraph.length;
      } else {
        // Paragraph doesn't fit, need to split
        if (currentTokenCount > 0) {
          // Save current chunk
          const chunkContent = content.substring(
            this.findTokenPosition(content, chunkStartToken),
            this.findTokenPosition(content, currentTokenCount)
          );

          if (this.estimateTokenCount(chunkContent) >= minChunkSize) {
            chunks.push({
              content: chunkContent,
              startToken: chunkStartToken,
              endToken: currentTokenCount,
            });
          }

          // Start new chunk with overlap
          const overlapStart = Math.max(0, currentTokenCount - overlapTokens);
          chunkStartToken = overlapStart;
          currentTokenCount = paragraphTokens;
          currentPosition += paragraph.length;
        } else {
          // Single paragraph is too large, split by sentences
          const sentenceChunks = this.splitParagraphIntoSentences(
            paragraph,
            maxTokens,
            overlapTokens,
            minChunkSize
          );

          for (const sentenceChunk of sentenceChunks) {
            chunks.push(sentenceChunk);
          }

          currentTokenCount = 0;
          chunkStartToken = 0;
        }
      }
    }

    // Add final chunk if there's remaining content
    if (currentTokenCount > 0) {
      const chunkContent = content.substring(
        this.findTokenPosition(content, chunkStartToken),
        this.findTokenPosition(content, currentTokenCount)
      );

      if (this.estimateTokenCount(chunkContent) >= minChunkSize) {
        chunks.push({
          content: chunkContent,
          startToken: chunkStartToken,
          endToken: currentTokenCount,
        });
      }
    }

    return chunks;
  }

  /**
   * Split content into paragraphs
   */
  private splitIntoParagraphs(content: string): string[] {
    return content
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0);
  }

  /**
   * Split a large paragraph into sentence-based chunks
   */
  private splitParagraphIntoSentences(
    paragraph: string,
    maxTokens: number,
    overlapTokens: number,
    minChunkSize: number
  ): Array<{ content: string; startToken: number; endToken: number }> {
    const sentences = this.splitIntoSentences(paragraph);
    const chunks: Array<{ content: string; startToken: number; endToken: number }> = [];
    let currentChunk = '';
    let currentTokenCount = 0;
    let chunkStartToken = 0;

    for (const sentence of sentences) {
      const sentenceTokens = this.estimateTokenCount(sentence);

      if (currentTokenCount + sentenceTokens <= maxTokens) {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
        currentTokenCount += sentenceTokens;
      } else {
        // Save current chunk
        if (currentTokenCount >= minChunkSize) {
          chunks.push({
            content: currentChunk,
            startToken: chunkStartToken,
            endToken: currentTokenCount,
          });
        }

        // Start new chunk with overlap
        const overlapStart = Math.max(0, currentTokenCount - overlapTokens);
        chunkStartToken = overlapStart;
        currentChunk = sentence;
        currentTokenCount = sentenceTokens;
      }
    }

    // Add final chunk
    if (currentTokenCount >= minChunkSize) {
      chunks.push({
        content: currentChunk,
        startToken: chunkStartToken,
        endToken: currentTokenCount,
      });
    }

    return chunks;
  }

  /**
   * Split text into sentences while preserving punctuation
   */
  private splitIntoSentences(text: string): string[] {
    // Split by sentence endings, but be careful with abbreviations
    const sentenceRegex = /[^.!?]+[.!?]+/g;
    const matches = text.match(sentenceRegex);

    if (!matches) {
      return [text];
    }

    return matches.map(s => s.trim()).filter(s => s.length > 0);
  }

  /**
   * Find the character position corresponding to a token count
   */
  private findTokenPosition(text: string, tokenCount: number): number {
    // Rough estimation: convert tokens back to characters
    const estimatedChars = Math.floor(tokenCount / this.TOKENS_PER_CHAR);
    return Math.min(estimatedChars, text.length);
  }

  /**
   * Generate Redis key for a chunk
   * Handles cases where organizationId and documentId already contain their type prefixes
   */
  generateChunkKey(organizationId: string, documentId: string, chunkNumber: number): string {
    // Remove existing prefixes if they exist to avoid duplication
    const cleanOrgId = organizationId.replace(/^org-/, '');
    const cleanDocId = documentId.replace(/^doc-/, '');

    return `embeddings:org-${cleanOrgId}:doc-${cleanDocId}::${chunkNumber}`;
  }

  /**
   * Parse chunk key to extract components
   */
  parseChunkKey(key: string): {
    organizationId: string;
    documentId: string;
    chunkNumber: number;
  } | null {
    const match = key.match(/embeddings:org-([^:]+):doc-([^:]+)::(\d+)/);
    if (!match) return null;

    return {
      organizationId: `org-${match[1]}`, // Add prefix back for consistency
      documentId: `doc-${match[2]}`, // Add prefix back for consistency
      chunkNumber: parseInt(match[3], 10),
    };
  }

  /**
   * Generate pattern to find all chunks for a document
   * Handles cases where organizationId and documentId already contain their type prefixes
   */
  generateDocumentChunkPattern(organizationId: string, documentId: string): string {
    // Remove existing prefixes if they exist to avoid duplication
    const cleanOrgId = organizationId.replace(/^org-/, '');
    const cleanDocId = documentId.replace(/^doc-/, '');

    return `embeddings:org-${cleanOrgId}:doc-${cleanDocId}::*`;
  }

  /**
   * Check if a document needs chunking based on token count
   */
  needsChunking(content: string, maxTokens: number = this.DEFAULT_MAX_TOKENS): boolean {
    return this.estimateTokenCount(content) > maxTokens;
  }

  /**
   * Check if a document ID is already a chunk (contains ::)
   */
  isAlreadyChunked(documentId: string): boolean {
    return documentId.includes('::');
  }

  /**
   * Extract original document ID from a chunk ID
   */
  extractOriginalDocumentId(chunkId: string): string | null {
    const match = chunkId.match(/^(.+)::\d+$/);
    return match ? match[1] : null;
  }

  /**
   * Get chunking statistics for a document
   */
  getChunkingStats(
    content: string,
    options: ChunkingOptions = {}
  ): {
    estimatedTokens: number;
    needsChunking: boolean;
    estimatedChunks: number;
    maxTokens: number;
  } {
    const maxTokens = options.maxTokens || this.DEFAULT_MAX_TOKENS;
    const estimatedTokens = this.estimateTokenCount(content);
    const needsChunking = estimatedTokens > maxTokens;
    const estimatedChunks = needsChunking ? Math.ceil(estimatedTokens / maxTokens) : 1;

    return {
      estimatedTokens,
      needsChunking,
      estimatedChunks,
      maxTokens,
    };
  }
}
