import { createHash } from 'crypto';
import { CacheAdapter } from '../types';

export interface HashedDocument {
  id: string;
  contentHash: string;
  lastModified: Date;
  metadata?: Record<string, any>;
}

export interface ContentHashOptions {
  algorithm?: 'md5' | 'sha1' | 'sha256';
  includeMetadata?: boolean;
  cacheAdapter?: CacheAdapter;
  redisKeyPrefix?: string;
  redisTtlSeconds?: number;
}

/**
 * Service for generating and managing content hashes to enable
 * incremental document updates and change detection.
 */
export class ContentHashService {
  private hashCache = new Map<string, string>();
  private algorithm: string;
  private includeMetadata: boolean;
  private cacheAdapter?: CacheAdapter;
  private redisKeyPrefix: string;
  private redisTtlSeconds: number;

  constructor(options: ContentHashOptions = {}) {
    this.algorithm = options.algorithm || 'sha256';
    this.includeMetadata = options.includeMetadata || false;
    this.cacheAdapter = options.cacheAdapter;
    this.redisKeyPrefix = options.redisKeyPrefix || 'content_hashes:';
    this.redisTtlSeconds = options.redisTtlSeconds || 604800; // 7 days default (168 hours)
  }

  /**
   * Set cache adapter for persistent hash storage
   */
  setCacheAdapter(cacheAdapter: CacheAdapter): void {
    this.cacheAdapter = cacheAdapter;
  }

  /**
   * Load persistent hashes from cache for an organization
   */
  async loadPersistentHashes(orgId: string): Promise<void> {
    if (!this.cacheAdapter) {
      return;
    }

    try {
      const key = `${this.redisKeyPrefix}${orgId}`;
      const hashData = await this.cacheAdapter.get(key);
      if (hashData) {
        // Validate that the data is a valid JSON object before parsing
        const trimmedData = hashData.trim();
        if (!trimmedData.startsWith('{') || !trimmedData.endsWith('}')) {
          console.warn('[ContentHashService] Invalid hash data format, skipping load');
          return;
        }

        const hashes = JSON.parse(trimmedData);
        if (typeof hashes === 'object' && hashes !== null) {
          this.loadHashCache(hashes);
        }
      }
    } catch (error) {
      console.warn('[ContentHashService] Failed to load persistent hashes:', error);
    }
  }

  /**
   * Save persistent hashes to cache for an organization
   */
  async savePersistentHashes(orgId: string): Promise<void> {
    if (!this.cacheAdapter) {
      return;
    }

    try {
      const key = `${this.redisKeyPrefix}${orgId}`;
      const hashData = this.exportHashCache();
      await this.cacheAdapter.setex(key, this.redisTtlSeconds, JSON.stringify(hashData));
    } catch (error) {
      console.warn('[ContentHashService] Failed to save persistent hashes:', error);
    }
  }

  /**
   * Clear persistent hashes for an organization
   */
  async clearPersistentHashes(orgId: string): Promise<void> {
    if (!this.cacheAdapter) {
      return;
    }

    try {
      const key = `${this.redisKeyPrefix}${orgId}`;
      // Note: CacheAdapter doesn't have del method, so we'll set with 1 second TTL
      await this.cacheAdapter.setex(key, 1, '');
    } catch (error) {
      console.warn('[ContentHashService] Failed to clear persistent hashes:', error);
    }
  }

  /**
   * Generate a hash for document content
   */
  generateHash(content: string, metadata?: Record<string, any>): string {
    let hashInput = content;

    if (this.includeMetadata && metadata) {
      // Include sorted metadata in hash to ensure consistency
      const sortedMetadata = Object.keys(metadata)
        .sort()
        .reduce(
          (acc, key) => {
            acc[key] = metadata[key];
            return acc;
          },
          {} as Record<string, any>
        );

      hashInput += JSON.stringify(sortedMetadata);
    }

    return createHash(this.algorithm).update(hashInput, 'utf8').digest('hex');
  }

  /**
   * Check if document content has changed by comparing hashes
   */
  hasContentChanged(documentId: string, content: string, metadata?: Record<string, any>): boolean {
    const newHash = this.generateHash(content, metadata);
    const cachedHash = this.hashCache.get(documentId);

    if (!cachedHash) {
      // No cached hash, consider it changed
      this.hashCache.set(documentId, newHash);
      return true;
    }

    if (cachedHash !== newHash) {
      // Content has changed
      this.hashCache.set(documentId, newHash);
      return true;
    }

    // Content hasn't changed
    return false;
  }

  /**
   * Update the cached hash for a document
   */
  updateHash(documentId: string, content: string, metadata?: Record<string, any>): string {
    const hash = this.generateHash(content, metadata);
    this.hashCache.set(documentId, hash);
    return hash;
  }

  /**
   * Get the cached hash for a document
   */
  getCachedHash(documentId: string): string | undefined {
    return this.hashCache.get(documentId);
  }

  /**
   * Remove a document from the hash cache
   */
  removeFromCache(documentId: string): boolean {
    return this.hashCache.delete(documentId);
  }

  /**
   * Clear the entire hash cache
   */
  clearCache(): void {
    this.hashCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    cachedDocuments: number;
    algorithm: string;
    includeMetadata: boolean;
  } {
    return {
      cachedDocuments: this.hashCache.size,
      algorithm: this.algorithm,
      includeMetadata: this.includeMetadata,
    };
  }

  /**
   * Filter documents that have changed content
   */
  filterChangedDocuments(
    documents: Array<{
      id: string;
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Array<{
    id: string;
    content: string;
    metadata?: Record<string, any>;
    contentHash: string;
    hasChanged: boolean;
  }> {
    return documents.map(doc => {
      const contentHash = this.generateHash(doc.content, doc.metadata);
      const hasChanged = this.hasContentChanged(doc.id, doc.content, doc.metadata);

      return {
        ...doc,
        contentHash,
        hasChanged,
      };
    });
  }

  /**
   * Get documents that need to be reindexed
   */
  getDocumentsToReindex(
    documents: Array<{
      id: string;
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Array<{
    id: string;
    content: string;
    metadata?: Record<string, any>;
    contentHash: string;
  }> {
    const filtered = this.filterChangedDocuments(documents);
    return filtered
      .filter(doc => doc.hasChanged)
      .map(doc => ({
        id: doc.id,
        content: doc.content,
        metadata: doc.metadata,
        contentHash: doc.contentHash,
      }));
  }

  /**
   * Batch update hashes for multiple documents
   */
  batchUpdateHashes(
    documents: Array<{
      id: string;
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Map<string, string> {
    const hashMap = new Map<string, string>();

    for (const doc of documents) {
      const hash = this.updateHash(doc.id, doc.content, doc.metadata);
      hashMap.set(doc.id, hash);
    }

    return hashMap;
  }

  /**
   * Load hash cache from external source (e.g., database, file)
   */
  loadHashCache(hashData: Record<string, string>): void {
    this.hashCache.clear();
    for (const [documentId, hash] of Object.entries(hashData)) {
      this.hashCache.set(documentId, hash);
    }
  }

  /**
   * Export hash cache for persistence
   */
  exportHashCache(): Record<string, string> {
    const exported: Record<string, string> = {};
    for (const [documentId, hash] of this.hashCache.entries()) {
      exported[documentId] = hash;
    }
    return exported;
  }
}
