/**
 * Atomic sample of a numeric measurement captured by the {@link MetricsTrackerService}.
 *
 * All timestamps are recorded in **UTC**. The optional `tags` map allows multi-dimensional
 * slicing (e.g., { region: "us-east-1", db: "primary" }). Arbitrary `metadata` may be attached
 * by callers but is not interpreted by the tracker.
 */
export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

/**
 * Aggregated statistics over a time window produced by
 * {@link MetricsTrackerService.getAggregatedMetrics}.
 */
export interface AggregatedMetric {
  name: string;
  count: number;
  sum: number;
  average: number;
  min: number;
  max: number;
  latest: number;
  period: string;
  tags?: Record<string, string>;
}

/**
 * Declarative filter object used by query methods such as {@link getMetrics}.
 */
export interface MetricsFilter {
  name?: string;
  tags?: Record<string, string>;
  startTime?: Date;
  endTime?: Date;
  limit?: number;
}

/**
 * Structured event emitted by {@link recordPerformance}. Captures high-level operation timing
 * along with success information and optional error messages.
 */
export interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

/**
 * Lightweight **in-process** metrics accumulator suitable for unit tests, local development or
 * micro-services where a full Prometheus/StatsD stack would be overkill. Designed for low memory
 * usage with automatic retention limits and periodic clean-up.
 */
export class MetricsTrackerService {
  private metrics: Map<string, MetricData[]> = new Map();
  private performanceMetrics: PerformanceMetric[] = [];
  private readonly MAX_METRICS_PER_NAME = 1000;
  private readonly MAX_PERFORMANCE_METRICS = 5000;
  private readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 1 hour
  private cleanupTimer?: NodeJS.Timeout;

  /**
   * Instantiate a tracker and start the background clean-up timer.
   *
   * The timer purges data older than 24h every hour to avoid unbounded growth.
   */
  constructor() {
    this.startCleanupTimer();
  }

  /**
   * Record an **arbitrary numeric value** against a metric name.
   *
   * @param name – Logical metric identifier.
   * @param value – Measurement value.
   * @param tags – Optional tag map for dimensional queries.
   * @param metadata – Opaque metadata object stored verbatim.
   */
  recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>,
    metadata?: Record<string, any>
  ): void {
    const metric: MetricData = {
      name,
      value,
      timestamp: new Date(),
      tags,
      metadata,
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricList = this.metrics.get(name)!;
    metricList.push(metric);

    // Keep only the most recent metrics
    if (metricList.length > this.MAX_METRICS_PER_NAME) {
      metricList.splice(0, metricList.length - this.MAX_METRICS_PER_NAME);
    }
  }

  /** Increment a monotonically increasing counter. */
  incrementCounter(name: string, increment: number = 1, tags?: Record<string, string>): void {
    this.recordMetric(name, increment, tags);
  }

  /** Record the *current* value of a resource as a gauge. */
  recordGauge(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric(`${name}.gauge`, value, tags);
  }

  /** Store a duration measurement (milliseconds). */
  recordTiming(name: string, durationMs: number, tags?: Record<string, string>): void {
    this.recordMetric(`${name}.timing`, durationMs, tags);
  }

  /**
   * Capture a rich performance event and automatically mirror it into the generic timing stream.
   */
  recordPerformance(
    operation: string,
    duration: number,
    success: boolean,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): void {
    const performanceMetric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      success,
      errorMessage,
      metadata,
    };

    this.performanceMetrics.push(performanceMetric);

    // Keep only the most recent performance metrics
    if (this.performanceMetrics.length > this.MAX_PERFORMANCE_METRICS) {
      this.performanceMetrics.splice(
        0,
        this.performanceMetrics.length - this.MAX_PERFORMANCE_METRICS
      );
    }

    // Also record as regular metrics
    this.recordTiming(`performance.${operation}`, duration, {
      success: success.toString(),
      ...metadata,
    });
  }

  /**
   * Return **raw samples** that match the provided filter – useful for ad-hoc inspection.
   */
  getMetrics(filter?: MetricsFilter): MetricData[] {
    let allMetrics: MetricData[] = [];

    // Collect all metrics
    for (const [metricName, metricList] of this.metrics.entries()) {
      if (!filter?.name || metricName === filter.name) {
        allMetrics.push(...metricList);
      }
    }

    // Apply filters
    if (filter) {
      allMetrics = this.applyFilters(allMetrics, filter);
    }

    // Sort by timestamp (most recent first)
    allMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply limit
    if (filter?.limit) {
      allMetrics = allMetrics.slice(0, filter.limit);
    }

    return allMetrics;
  }

  /**
   * Compute min/avg/max statistics grouped by the specified time *period*.
   */
  getAggregatedMetrics(
    name: string,
    period: 'hour' | 'day' | 'week' = 'hour',
    tags?: Record<string, string>
  ): AggregatedMetric[] {
    const metricList = this.metrics.get(name) || [];

    // Filter by tags if provided
    const filteredMetrics = tags
      ? metricList.filter(metric => this.matchesTags(metric.tags || {}, tags))
      : metricList;

    if (filteredMetrics.length === 0) {
      return [];
    }

    // Group by time periods
    const groups = this.groupByTimePeriod(filteredMetrics, period);

    return Object.entries(groups).map(([periodKey, metrics]) => {
      const values = metrics.map(m => m.value);
      const sum = values.reduce((a, b) => a + b, 0);

      return {
        name,
        count: metrics.length,
        sum,
        average: sum / metrics.length,
        min: Math.min(...values),
        max: Math.max(...values),
        latest: metrics[metrics.length - 1].value,
        period: periodKey,
        tags,
      };
    });
  }

  /** Summarise performance events and return both the summary and individual records. */
  getPerformanceMetrics(
    operation?: string,
    startTime?: Date,
    endTime?: Date
  ): {
    summary: {
      totalOperations: number;
      successfulOperations: number;
      failedOperations: number;
      averageDuration: number;
      minDuration: number;
      maxDuration: number;
      successRate: number;
    };
    operations: PerformanceMetric[];
  } {
    let filteredMetrics = this.performanceMetrics;

    // Filter by operation
    if (operation) {
      filteredMetrics = filteredMetrics.filter(m => m.operation === operation);
    }

    // Filter by time range
    if (startTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= startTime);
    }
    if (endTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp <= endTime);
    }

    if (filteredMetrics.length === 0) {
      return {
        summary: {
          totalOperations: 0,
          successfulOperations: 0,
          failedOperations: 0,
          averageDuration: 0,
          minDuration: 0,
          maxDuration: 0,
          successRate: 0,
        },
        operations: [],
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const successfulOps = filteredMetrics.filter(m => m.success).length;
    const failedOps = filteredMetrics.length - successfulOps;

    return {
      summary: {
        totalOperations: filteredMetrics.length,
        successfulOperations: successfulOps,
        failedOperations: failedOps,
        averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        successRate: successfulOps / filteredMetrics.length,
      },
      operations: filteredMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()),
    };
  }

  /** Top-N helper to fetch the highest metric values within an optional recent period. */
  getTopMetrics(name: string, limit: number = 10, period?: 'hour' | 'day' | 'week'): MetricData[] {
    const metricList = this.metrics.get(name) || [];

    let filteredMetrics = metricList;

    // Filter by time period if specified
    if (period) {
      const now = new Date();
      const periodMs = this.getPeriodMs(period);
      const cutoff = new Date(now.getTime() - periodMs);
      filteredMetrics = metricList.filter(m => m.timestamp >= cutoff);
    }

    return filteredMetrics.sort((a, b) => b.value - a.value).slice(0, limit);
  }

  /** Return all known metric names sorted alphabetically. */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys()).sort();
  }

  /** Produce a reverse-index of tag keys to all encountered values. */
  getTagsInfo(): Record<string, Set<string>> {
    const tagInfo: Record<string, Set<string>> = {};

    for (const metricList of this.metrics.values()) {
      for (const metric of metricList) {
        if (metric.tags) {
          for (const [key, value] of Object.entries(metric.tags)) {
            if (!tagInfo[key]) {
              tagInfo[key] = new Set();
            }
            tagInfo[key].add(value);
          }
        }
      }
    }

    return tagInfo;
  }

  /** Delete samples older than the provided `Date`. Returns number of entries removed. */
  clearOldMetrics(olderThan: Date): number {
    let clearedCount = 0;

    for (const [name, metricList] of this.metrics.entries()) {
      const originalLength = metricList.length;
      const filtered = metricList.filter(m => m.timestamp > olderThan);
      this.metrics.set(name, filtered);
      clearedCount += originalLength - filtered.length;
    }

    // Clear old performance metrics
    const originalPerfLength = this.performanceMetrics.length;
    this.performanceMetrics = this.performanceMetrics.filter(m => m.timestamp > olderThan);
    clearedCount += originalPerfLength - this.performanceMetrics.length;

    return clearedCount;
  }

  /** Reset tracker state completely. */
  clearAllMetrics(): void {
    this.metrics.clear();
    this.performanceMetrics = [];
  }

  /** Runtime statistics including approximate memory footprint. */
  getStats() {
    const totalMetrics = Array.from(this.metrics.values()).reduce(
      (sum, list) => sum + list.length,
      0
    );

    return {
      totalMetrics,
      uniqueMetricNames: this.metrics.size,
      performanceMetrics: this.performanceMetrics.length,
      memoryUsage: this.estimateMemoryUsage(),
    };
  }

  /** Wrap any async function and automatically record its execution time & outcome. */
  timing<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();

    return fn()
      .then(result => {
        const duration = Date.now() - startTime;
        this.recordPerformance(operation, duration, true);
        return result;
      })
      .catch(error => {
        const duration = Date.now() - startTime;
        this.recordPerformance(operation, duration, false, error.message);
        throw error;
      });
  }

  /** Stop cleanup timer and wipe memory – call during application shutdown. */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clearAllMetrics();
  }

  /**
   * Update RAG-specific metrics with exponential moving average for success rate
   */
  updateRAGMetrics(
    success: boolean,
    executionTime: number,
    docsRetrieved: number,
    semanticMatches: number,
    tags?: Record<string, string>
  ): void {
    // Record base metrics
    this.recordTiming('rag.execution_time', executionTime, tags);
    this.incrementCounter('rag.total_requests', 1, tags);
    this.recordGauge('rag.docs_retrieved', docsRetrieved, tags);
    this.recordGauge('rag.semantic_matches', semanticMatches, tags);

    // Update success rate with exponential moving average
    const successRateKey = 'rag.success_rate';
    const currentMetrics = this.getMetrics({ name: successRateKey, tags });
    const alpha = 0.1;

    let currentSuccessRate = 0;
    if (currentMetrics.length > 0) {
      currentSuccessRate = currentMetrics[0].value;
    }

    const newSuccessRate = currentSuccessRate * (1 - alpha) + (success ? 1 : 0) * alpha;
    this.recordGauge(successRateKey, newSuccessRate, tags);

    // Record performance event
    this.recordPerformance('rag_execution', executionTime, success, undefined, {
      docsRetrieved,
      semanticMatches,
      ...tags,
    });
  }

  /**
   * Update database query metrics with average rows returned
   */
  updateDatabaseMetrics(
    success: boolean,
    executionTime: number,
    rowCount: number,
    tags?: Record<string, string>
  ): void {
    // Record base metrics
    this.recordTiming('db.execution_time', executionTime, tags);
    this.incrementCounter('db.total_queries', 1, tags);
    this.recordGauge('db.rows_returned', rowCount, tags);

    // Update average rows returned (only for successful queries)
    if (success && rowCount > 0) {
      const avgRowsKey = 'db.average_rows_returned';
      const currentMetrics = this.getMetrics({ name: avgRowsKey, tags });

      let currentAvg = 0;
      let count = 1;
      if (currentMetrics.length > 0) {
        currentAvg = currentMetrics[0].value;
        count = Math.min(currentMetrics[0].metadata?.count || 1, 100);
      }

      const newAvg = (currentAvg * (count - 1) + rowCount) / count;
      this.recordMetric(avgRowsKey, newAvg, tags, { count });
    }

    // Record performance event
    this.recordPerformance('database_query', executionTime, success, undefined, {
      rowCount,
      ...tags,
    });
  }

  /**
   * Get comprehensive metrics summary for RAG operations
   */
  getRAGMetricsSummary(tags?: Record<string, string>): {
    totalRequests: number;
    successRate: number;
    averageExecutionTime: number;
    averageDocsRetrieved: number;
    averageSemanticMatches: number;
    lastExecution: Date | null;
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const totalRequests = this.getMetrics({
      name: 'rag.total_requests',
      tags,
      startTime: oneHourAgo,
    }).reduce((sum, m) => sum + m.value, 0);

    const successRateMetrics = this.getMetrics({ name: 'rag.success_rate', tags });
    const successRate = successRateMetrics.length > 0 ? successRateMetrics[0].value : 0;

    const executionTimes = this.getMetrics({
      name: 'rag.execution_time',
      tags,
      startTime: oneHourAgo,
    });
    const averageExecutionTime =
      executionTimes.length > 0
        ? executionTimes.reduce((sum, m) => sum + m.value, 0) / executionTimes.length
        : 0;

    const docsRetrieved = this.getMetrics({
      name: 'rag.docs_retrieved',
      tags,
      startTime: oneHourAgo,
    });
    const averageDocsRetrieved =
      docsRetrieved.length > 0
        ? docsRetrieved.reduce((sum, m) => sum + m.value, 0) / docsRetrieved.length
        : 0;

    const semanticMatches = this.getMetrics({
      name: 'rag.semantic_matches',
      tags,
      startTime: oneHourAgo,
    });
    const averageSemanticMatches =
      semanticMatches.length > 0
        ? semanticMatches.reduce((sum, m) => sum + m.value, 0) / semanticMatches.length
        : 0;

    const lastExecution =
      this.getMetrics({ name: 'rag.total_requests', tags, limit: 1 })[0]?.timestamp || null;

    return {
      totalRequests,
      successRate,
      averageExecutionTime,
      averageDocsRetrieved,
      averageSemanticMatches,
      lastExecution,
    };
  }

  /* ----------------------------- PRIVATE HELPERS ------------------------------ */

  /** Apply tag/time filtering to a metric array. */
  private applyFilters(metrics: MetricData[], filter: MetricsFilter): MetricData[] {
    let filtered = metrics;

    if (filter.tags) {
      filtered = filtered.filter(metric => this.matchesTags(metric.tags || {}, filter.tags!));
    }

    if (filter.startTime) {
      filtered = filtered.filter(metric => metric.timestamp >= filter.startTime!);
    }

    if (filter.endTime) {
      filtered = filtered.filter(metric => metric.timestamp <= filter.endTime!);
    }

    return filtered;
  }

  /** Strict tag equality check used by filters. */
  private matchesTags(
    metricTags: Record<string, string>,
    filterTags: Record<string, string>
  ): boolean {
    for (const [key, value] of Object.entries(filterTags)) {
      if (metricTags[key] !== value) {
        return false;
      }
    }
    return true;
  }

  /** Group samples by rounded time period (hour/day/week). */
  private groupByTimePeriod(
    metrics: MetricData[],
    period: 'hour' | 'day' | 'week'
  ): Record<string, MetricData[]> {
    const groups: Record<string, MetricData[]> = {};

    for (const metric of metrics) {
      const key = this.getTimePeriodKey(metric.timestamp, period);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(metric);
    }

    return groups;
  }

  /** Derive string key for a timestamp-period combination. */
  private getTimePeriodKey(timestamp: Date, period: 'hour' | 'day' | 'week'): string {
    const date = new Date(timestamp);

    switch (period) {
      case 'hour':
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}-${date.getHours()}`;
      case 'day':
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        return `${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}`;
      default:
        return timestamp.toISOString();
    }
  }

  /** Convert period label to milliseconds. */
  private getPeriodMs(period: 'hour' | 'day' | 'week'): number {
    switch (period) {
      case 'hour':
        return 60 * 60 * 1000;
      case 'day':
        return 24 * 60 * 60 * 1000;
      case 'week':
        return 7 * 24 * 60 * 60 * 1000;
      default:
        return 60 * 60 * 1000;
    }
  }

  /** Rough memory usage estimation based on sample counts. */
  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    let size = 0;

    for (const metricList of this.metrics.values()) {
      size += metricList.length * 200; // Estimated 200 bytes per metric
    }

    size += this.performanceMetrics.length * 150; // Estimated 150 bytes per performance metric

    return size;
  }

  /** Schedule the hourly cleanup job. */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      this.clearOldMetrics(cutoff);
    }, this.CLEANUP_INTERVAL_MS);
  }
}
