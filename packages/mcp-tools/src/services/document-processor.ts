import { FileTextExtractor } from './file-text-extractor';

/**
 * Processed document shape used by the vector store, search index and API layer.
 *
 * Values come from heterogeneous sources (database rows, uploaded files, etc.) therefore many
 * fields are optional and stored as either ISO-8601 strings or native `Date`s.
 *
 * Side-effect-free – this interface is purely declarative.
 */
export interface ProcessedDocument {
  id: string;
  name: string;
  content: string;
  documentType?: string;
  fileType?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  metadata?: Record<string, any>;
}

/**
 * Strategy interface that allows callers of {@link DocumentProcessor} to plug-in arbitrary
 * sanitisation logic – for instance HTML stripping or PII redaction.
 */
export interface DocumentSanitizer {
  sanitize(content: string): string;
}

/**
 * High-level utility that turns raw, often noisy data records (database rows, binary uploads)
 * into clean, **chunked** {@link ProcessedDocument}s ready for embedding and search.
 *
 * The processor runs three main phases:
 * 1. **Extraction** – decode text from either the `content` field or (for binary files) the
 *    `buffer_file` field using {@link FileTextExtractor}.
 * 2. **Sanitisation** – optional cleaning performed by a caller-supplied
 *    {@link DocumentSanitizer} implementation.
 * 3. **Chunking** – split long documents into ~400-word fragments for better retrieval.
 *
 * @example
 * ```ts
 * const processor = new DocumentProcessor(new DocumentSanitizerService());
 * const chunks = await processor.processDocumentContent(rawRow);
 * chunks.forEach(c => console.log(c.id, c.content.substring(0, 80)));
 * ```
 */
export class DocumentProcessor {
  /**
   * Construct a processor instance.
   *
   * @param sanitizer – Optional {@link DocumentSanitizer} used to post-process raw text. If not
   *                    provided no sanitisation is performed.
   */
  constructor(private sanitizer?: DocumentSanitizer) {}

  /**
   * Transform an arbitrary *raw* document row into one or more semantic chunks suitable for
   * vector indexing.
   *
   * @param rawDoc – A loosely-typed object – must at least provide an `id` property. Expected
   *                 keys: `content` (string) or `buffer_file` (binary), `fileType|file_type`,
   *                 `name`, `createdAt`, `updatedAt`.
   * @returns Array whose elements are either populated {@link ProcessedDocument}s **or** `null`
   *          placeholders for rows that were discarded (e.g. too little text). The array always
   *          contains at least one element so consumers can maintain positional mapping.
   * @throws Error never – extraction failures are swallowed and result in empty chunks instead.
   */
  async processDocumentContent(rawDoc: any): Promise<(ProcessedDocument | null)[]> {
    if (!rawDoc || !rawDoc.id) {
      return [null];
    }

    console.log(`[DocumentProcessor] Processing document ${rawDoc.id}`, {
      hasContent: !!rawDoc.content,
      hasBufferFile: !!rawDoc.buffer_file,
      fileType: rawDoc.fileType || rawDoc.file_type,
      name: rawDoc.name,
    });

    // Exclude images and other non-text documents
    const inputFileType = (
      rawDoc.fileType ||
      rawDoc.file_type ||
      this.inferFileType(rawDoc.name)
    )?.toLowerCase();
    const isImageType = (t?: string) =>
      !!t &&
      (t.startsWith('image/') ||
        ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp', 'tiff'].includes(t));
    const isExtractableTextType = (t?: string) =>
      !!t &&
      (t.startsWith('text/') ||
        ['application/json', 'application/xml', 'text/xml', 'text/html', 'text/csv'].includes(t) ||
        ['pdf', 'docx', 'xlsx', 'xls', 'txt', 'md', 'html', 'json', 'csv', 'xml'].includes(t));

    if (isImageType(inputFileType)) {
      console.log(`[DocumentProcessor] Skipping non-text document type: ${inputFileType}`);
      return [null];
    }

    // Extract content from various fields
    let rawContent = '';

    // 1. Prefer textual content fields
    if (rawDoc.content && typeof rawDoc.content === 'string') {
      // If content is base64 and type is extractable text, decode and extract via FileTextExtractor
      const base64Pattern = /^[A-Za-z0-9+/\r\n]*={0,2}$/;
      const looksBase64 = base64Pattern.test(rawDoc.content) && rawDoc.content.length > 100;
      if (looksBase64 && isExtractableTextType(inputFileType)) {
        try {
          const buffer = Buffer.from(rawDoc.content, 'base64');
          const fileExtension = this.mimeTypeToExtension(inputFileType || 'text/plain');
          if (FileTextExtractor.isSupportedFileType(fileExtension)) {
            const extractionResult = await FileTextExtractor.extractText(buffer, fileExtension);
            rawContent = extractionResult.text;
          }
        } catch (_) {
          // Fallback: try to decode as UTF-8 string
          rawContent = Buffer.from(rawDoc.content, 'base64').toString('utf-8');
        }
      } else {
        // Plain text path
        rawContent = rawDoc.content;
        // If JSON-like with escaped newlines, unescape for readability
        if (inputFileType && ['application/json', 'json'].includes(inputFileType)) {
          rawContent = rawContent.replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\r/g, '\r');
        }
      }
      console.log(
        `[DocumentProcessor] Using text content from content field (length: ${rawContent.length})`
      );
    }

    // 2. If no text found but we have a binary buffer (e.g., PDF), attempt extraction
    if (!rawContent && rawDoc.buffer_file) {
      console.log(`[DocumentProcessor] Attempting text extraction from buffer_file`);
      try {
        const buffer = this.normalizeToBuffer(rawDoc.buffer_file);
        if (buffer) {
          const fileType =
            (
              rawDoc.fileType ||
              rawDoc.file_type ||
              this.inferFileType(rawDoc.name)
            )?.toLowerCase() || 'application/pdf';

          // Convert MIME type to file extension for FileTextExtractor
          const fileExtension = this.mimeTypeToExtension(fileType);

          if (FileTextExtractor.isSupportedFileType(fileExtension)) {
            console.log(
              `[DocumentProcessor] Direct extraction for file type: ${fileType} (extension: ${fileExtension})`
            );
            const extractionResult = await FileTextExtractor.extractText(buffer, fileExtension);
            rawContent = extractionResult.text;
            console.log(
              `[DocumentProcessor] Direct extraction successful (length: ${rawContent.length})`
            );
          } else {
            console.log(
              `[DocumentProcessor] File type ${fileType} (extension: ${fileExtension}) not supported for extraction`
            );
          }
        } else {
          console.log(`[DocumentProcessor] Failed to normalize buffer, trying direct extraction`);
          // Try direct extraction as a fallback
          try {
            const fileType =
              (
                rawDoc.fileType ||
                rawDoc.file_type ||
                this.inferFileType(rawDoc.name)
              )?.toLowerCase() || 'application/pdf';

            // Convert MIME type to file extension for FileTextExtractor
            const fileExtension = this.mimeTypeToExtension(fileType);

            if (FileTextExtractor.isSupportedFileType(fileExtension)) {
              console.log(
                `[DocumentProcessor] Direct extraction for file type: ${fileType} (extension: ${fileExtension})`
              );
              const extractionResult = await FileTextExtractor.extractText(
                rawDoc.buffer_file,
                fileExtension
              );
              rawContent = extractionResult.text;
              console.log(
                `[DocumentProcessor] Direct extraction successful (length: ${rawContent.length})`
              );
            } else {
              console.log(
                `[DocumentProcessor] File type ${fileType} (extension: ${fileExtension}) not supported for extraction`
              );
            }
          } catch (directError) {
            console.log(`[DocumentProcessor] Direct extraction failed:`, directError);
            // Silent fallback - extraction failed
          }
        }
      } catch (extractionError) {
        console.log(`[DocumentProcessor] Text extraction failed:`, extractionError);
        // Silent fallback - extraction failed
      }
    }

    // Sanitize the content to remove HTML tags and convert to clean plain text
    const content = this.sanitizer ? this.sanitizer.sanitize(rawContent) : rawContent;

    console.log(`[DocumentProcessor] Final content length: ${content?.length || 0}`);

    // Skip documents without meaningful content
    if (!content || content.trim().length < 20) {
      console.log(`[DocumentProcessor] Document ${rawDoc.id} has insufficient content, skipping`);
      return [null];
    }

    // Return single document - chunking will be handled later by TextChunkingService
    return [
      {
        id: rawDoc.id, // Pure document ID without chunking suffix
        name: rawDoc.name || `Document ${rawDoc.id}`,
        content: content.trim(),
        documentType: rawDoc.documentType || rawDoc.document_type,
        fileType: rawDoc.fileType || rawDoc.file_type,
        createdAt: rawDoc.createdAt || rawDoc.created_at,
        updatedAt: rawDoc.updatedAt || rawDoc.updated_at,
      },
    ];
  }

  /**
   * Produce an API-friendly representation of a document including human-oriented *previews* and
   * safe metadata. Binary files are transparently converted to text (where possible) via
   * {@link FileTextExtractor}.
   *
   * @param row – Database row / structured object containing at minimum: `id`, `name` and either
   *              `content` or `buffer_file`.
   * @returns Normalised object with preview fields. Binary buffers are retained in memory but
   *          **never** returned raw – only base64 placeholders and length metadata are exposed.
   */
  async processDocumentForAPI(row: Record<string, any>): Promise<Record<string, any>> {
    const processed = {
      id: row.id,
      name: row.name,
      documentType: row.document_type,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      categoryId: row.category_id,
      accessLevel: row.access_level,
      fileType: row.file_type,
      hasContent: !!row.content,
      hasBufferFile: !!row.buffer_file,
      fullContent: null as string | null,
      contentPreview: null as string | null,
      contentType: 'text/plain',
      contentEncoding: null as string | null,
    };

    // Handle text content from internal platform documents
    if (row.content && typeof row.content === 'string') {
      // Expose full content for internal documents
      processed.fullContent = row.content;
      processed.contentPreview =
        row.content.length > 1000 ? row.content.substring(0, 1000) + '...' : row.content;
    } else if (row.buffer_file && Buffer.isBuffer(row.buffer_file)) {
      // Check if this is likely a binary file based on file_type
      const isBinaryFile =
        row.file_type &&
        !['txt', 'md', 'html', 'json', 'csv', 'xml'].includes(row.file_type.toLowerCase());

      console.log(
        `[DEBUG] processDocumentForAPI: Is binary file: ${isBinaryFile} (file_type: ${row.file_type})`
      );

      if (isBinaryFile) {
        // Try to extract text from binary files using specialized extractors
        console.log(
          `[DEBUG] processDocumentForAPI: Attempting text extraction from binary file type: ${row.file_type}`
        );

        try {
          if (FileTextExtractor.isSupportedFileType(row.file_type)) {
            const extractedText = await FileTextExtractor.extractText(
              row.buffer_file,
              row.file_type,
              { maxSizeBytes: 50 * 1024 * 1024 } // 50MB limit for binary files
            );

            if (extractedText.text && extractedText.text.trim().length > 0) {
              processed.fullContent = extractedText.text;
              processed.contentPreview =
                extractedText.text.length > 1000
                  ? extractedText.text.substring(0, 1000) + '...'
                  : extractedText.text;

              console.log(
                `[DEBUG] processDocumentForAPI: Successfully extracted ${extractedText.text.length} characters from ${row.file_type} file using ${extractedText.metadata?.extractionMethod}`
              );
            } else {
              throw new Error('No text content extracted');
            }
          } else {
            throw new Error(`File type ${row.file_type} not supported for extraction`);
          }
        } catch (extractionError) {
          console.log(
            `[DEBUG] processDocumentForAPI: Text extraction failed for ${row.file_type}: ${extractionError instanceof Error ? extractionError.message : 'Unknown error'}`
          );

          // Fallback to metadata placeholder
          processed.contentPreview = `[Binary file: ${row.file_type}]`;
          processed.contentEncoding = 'base64';
        }

        // Set appropriate content type
        const fileTypeToContentType: Record<string, string> = {
          pdf: 'application/pdf',
          docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          png: 'image/png',
          jpg: 'image/jpeg',
          jpeg: 'image/jpeg',
        };

        processed.contentType =
          fileTypeToContentType[row.file_type?.toLowerCase()] || 'application/octet-stream';
      } else {
        // For text files, convert buffer to full string content
        console.log(`[DEBUG] processDocumentForAPI: Converting buffer to text for non-binary file`);
        const textContent = row.buffer_file.toString('utf-8');
        // Expose full content for text uploads
        processed.fullContent = textContent;
        processed.contentPreview =
          textContent.length > 1000 ? textContent.substring(0, 1000) + '...' : textContent;
        console.log(
          `[DEBUG] processDocumentForAPI: Set fullContent from buffer (length: ${processed.fullContent.length})`
        );
      }
    } else {
      console.log(
        `[DEBUG] processDocumentForAPI: No usable content found - content type: ${typeof row.content}, buffer_file type: ${typeof row.buffer_file}`
      );
    }

    console.log(
      `[DEBUG] processDocumentForAPI: Final processed document ${row.id} has fullContent: ${!!processed.fullContent} (length: ${processed.fullContent?.length || 0}), contentPreview: ${!!processed.contentPreview} (length: ${processed.contentPreview?.length || 0})`
    );
    return processed;
  }

  /**
   * Convert MIME type to file extension for FileTextExtractor compatibility
   */
  private mimeTypeToExtension(mimeType: string): string {
    const mimeToExt: Record<string, string> = {
      'application/pdf': 'pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
      'application/vnd.ms-excel': 'xls',
      'text/plain': 'txt',
      'text/markdown': 'md',
      'text/html': 'html',
      'application/json': 'json',
      'text/csv': 'csv',
      'application/xml': 'xml',
      'text/xml': 'xml',
    };

    return mimeToExt[mimeType.toLowerCase()] || 'pdf'; // Default to pdf if unknown
  }

  /**
   * Infer a file extension from a given filename.
   *
   * @param fileName – Source filename.
   * @returns Lower-case extension without the leading dot or *undefined* when unknown.
   */
  private inferFileType(fileName: string | undefined): string | undefined {
    if (!fileName) return undefined;
    const match = /\.([a-zA-Z0-9]+)$/.exec(fileName);
    return match ? match[1].toLowerCase() : undefined;
  }

  /**
   * Best-effort conversion of user-supplied `buffer_file` payloads into a Node.js `Buffer`.
   * Handles several common serialisation formats produced by browsers, databases and APIs.
   *
   * @param input – Anything resembling binary data (Buffer, base64 string, JSON-serialised
   *                Buffer, array of bytes, hex string, ArrayBuffer, etc.).
   * @returns A `Buffer` on success or `null` if conversion failed.
   */
  private normalizeToBuffer(input: any): Buffer | null {
    if (!input) {
      return null;
    }

    if (Buffer.isBuffer(input)) {
      return input as Buffer;
    }

    // Base64 string
    if (typeof input === 'string') {
      try {
        return Buffer.from(input, 'base64');
      } catch (_) {
        return null;
      }
    }

    // Typical "{ type: 'Buffer', data: [...] }" from JSON.stringify(Buffer)
    if (typeof input === 'object' && input !== null && input.type === 'Buffer' && input.data) {
      if (Array.isArray(input.data)) {
        return Buffer.from(input.data);
      }

      // Handle case where data is an object with numeric keys (e.g., {'0': 255, '1': 0, ...})
      if (typeof input.data === 'object') {
        const orderedNumKeys = Object.keys(input.data).sort((a, b) => Number(a) - Number(b));
        const byteVals: number[] = orderedNumKeys.map(k => Number(input.data[k]));
        if (byteVals.every(v => !isNaN(v) && v >= 0 && v <= 255)) {
          return Buffer.from(byteVals);
        }
      }
    }

    // Plain array of numbers (e.g. deserialised bytea)
    if (Array.isArray(input) && input.every(v => typeof v === 'number')) {
      return Buffer.from(input);
    }

    if (typeof input === 'object' && Object.keys(input).every(k => !isNaN(Number(k)))) {
      const orderedKeys = Object.keys(input).sort((a, b) => Number(a) - Number(b));
      const byteValues: number[] = orderedKeys.map(k => {
        const val = input[k];
        return typeof val === 'number' ? val : Number(val);
      });
      if (byteValues.every(v => !isNaN(v) && v >= 0 && v <= 255)) {
        return Buffer.from(byteValues);
      }
    }

    // Additional PostgreSQL bytea patterns
    // Handle PostgreSQL bytea as hex string (e.g., "\\x504b030414...")
    if (typeof input === 'string' && input.startsWith('\\x')) {
      try {
        const hexString = input.slice(2); // Remove \x prefix
        return Buffer.from(hexString, 'hex');
      } catch (error) {
        return null;
      }
    }

    // Handle Uint8Array or similar typed arrays
    if (
      input &&
      typeof input === 'object' &&
      input.constructor &&
      input.constructor.name === 'Uint8Array'
    ) {
      return Buffer.from(input);
    }

    // Handle ArrayBuffer
    if (input instanceof ArrayBuffer) {
      return Buffer.from(input);
    }

    return null;
  }
}
