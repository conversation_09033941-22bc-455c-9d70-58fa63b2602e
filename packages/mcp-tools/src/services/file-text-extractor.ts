// File text extraction utilities

/**
 * Helper utilities for **safe, memory-efficient** extraction of plain-text from a variety of
 * common document formats (PDF, DOCX, spreadsheets, markdown, JSON, …). Heavy native
 * dependencies such as `pdf-parse`, `mammoth` and `xlsx` are **lazy-loaded** at first use so
 * that importing this module has minimal impact on cold-start latency.
 *
 * Public API:
 *   • {@link FileTextExtractor} – static methods returning structured {@link ExtractionResult}s.
 *   • {@link extractText} – lightweight wrapper that returns the cleaned text only.
 */

// Conditional imports to avoid loading heavy dependencies if not needed
let pdfParse: any;
let mammoth: any;
let xlsx: any;

// Lazy loading of dependencies
/**
 * Dynamically import `pdf-parse` the first time a PDF extraction is requested.
 *
 * @returns The default export of the `pdf-parse` module.
 */
async function loadPdfParse() {
  if (!pdfParse) {
    pdfParse = (await import('pdf-parse')).default;
  }
  return pdfParse;
}

/**
 * Lazily load the `mammoth` DOCX parsing library.
 */
async function loadMammoth() {
  if (!mammoth) {
    mammoth = await import('mammoth');
  }
  return mammoth;
}

/**
 * Lazy-load the `xlsx` package to handle spreadsheet files.
 */
async function loadXlsx() {
  if (!xlsx) {
    xlsx = await import('xlsx');
  }
  return xlsx;
}

export interface ExtractionResult {
  text: string;
  metadata?: {
    pages?: number;
    words?: number;
    chars?: number;
    extractionMethod: string;
    warnings?: string[];
  };
}

export interface ExtractionOptions {
  maxSizeBytes?: number;
  timeout?: number;
  encoding?: string;
}

const DEFAULT_OPTIONS: Required<ExtractionOptions> = {
  maxSizeBytes: 10 * 1024 * 1024, // 10 MB
  timeout: 30000, // 30 seconds
  encoding: 'utf-8',
};

/**
 * Extract text content from various file formats
 */
export class FileTextExtractor {
  private static readonly SUPPORTED_EXTENSIONS = new Set([
    'pdf',
    'docx',
    'xlsx',
    'xls',
    'txt',
    'md',
    'html',
    'json',
    'csv',
    'xml',
  ]);

  /**
   * Determine whether the supplied `fileType` is currently supported.
   *
   * @param fileType – File extension (e.g., `pdf`, `docx`). Case-insensitive.
   * @returns `true` if the extractor has an implementation for the given type.
   */
  static isSupportedFileType(fileType: string): boolean {
    return this.SUPPORTED_EXTENSIONS.has(fileType.toLowerCase());
  }

  /**
   * High-level convenience method that turns a **Buffer** into an {@link ExtractionResult} in a
   * single call. The function enforces sensible timeouts and maximum file sizes.
   *
   * @param buffer  – Raw file data.
   * @param fileType – Lower- or mixed-case extension hint (without dot).
   * @param options – Optional {@link ExtractionOptions} to tweak limits.
   * @returns Structured extraction outcome including warnings and character/word counts.
   * @throws Error  On unsupported file types, oversized buffers or internal parser failures.
   */
  static async extractText(
    buffer: Buffer,
    fileType: string,
    options: ExtractionOptions = {}
  ): Promise<ExtractionResult> {
    const opts = { ...DEFAULT_OPTIONS, ...options };

    // Validate buffer size
    if (buffer.length > opts.maxSizeBytes) {
      throw new Error(
        `File size ${buffer.length} bytes exceeds maximum allowed size of ${opts.maxSizeBytes} bytes`
      );
    }

    // Validate file type
    const normalizedType = fileType.toLowerCase();
    if (!this.isSupportedFileType(normalizedType)) {
      throw new Error(`Unsupported file type: ${fileType}`);
    }

    // Set timeout for extraction
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Text extraction timeout')), opts.timeout);
    });

    try {
      const extractionPromise = this.performExtraction(buffer, normalizedType, opts);
      const result = await Promise.race([extractionPromise, timeoutPromise]);

      // Add metadata
      result.metadata = {
        ...result.metadata,
        chars: result.text.length,
        words: result.text.split(/\s+/).filter(word => word.length > 0).length,
        extractionMethod: result.metadata?.extractionMethod || 'unknown',
      };

      return result;
    } catch (error) {
      throw new Error(
        `Failed to extract text from ${fileType} file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Internal dispatcher that routes the extraction to the appropriate format-specific helper.
   * All thrown errors are propagated to the caller for unified handling.
   *
   * @privateRemarks The method is kept `private` to avoid accidental direct usage that would
   * bypass timeout safety implemented in {@link extractText}.
   */
  private static async performExtraction(
    buffer: Buffer,
    fileType: string,
    options: Required<ExtractionOptions>
  ): Promise<ExtractionResult> {
    switch (fileType) {
      case 'pdf':
        return this.extractFromPdf(buffer);

      case 'docx':
        return this.extractFromDocx(buffer);

      case 'xlsx':
      case 'xls':
        return this.extractFromXlsx(buffer);

      case 'txt':
      case 'md':
      case 'html':
      case 'json':
      case 'csv':
      case 'xml':
        return this.extractFromTextFile(buffer, fileType, options.encoding);

      default:
        throw new Error(`Extraction not implemented for file type: ${fileType}`);
    }
  }

  /** Extract text from PDF files via `pdf-parse`. */
  private static async extractFromPdf(buffer: Buffer): Promise<ExtractionResult> {
    try {
      const pdfParseLib = await loadPdfParse();
      const data = await pdfParseLib(buffer);

      return {
        text: data.text || '',
        metadata: {
          pages: data.numpages || 0,
          extractionMethod: 'pdf-parse',
          warnings: data.text ? [] : ['No text content found in PDF'],
        },
      };
    } catch (error) {
      throw new Error(
        `PDF extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /** Extract raw text from Office DOCX documents using `mammoth`. */
  private static async extractFromDocx(buffer: Buffer): Promise<ExtractionResult> {
    try {
      const mammothLib = await loadMammoth();
      const result = await mammothLib.extractRawText({ buffer });

      return {
        text: result.value || '',
        metadata: {
          extractionMethod: 'mammoth',
          warnings: result.messages?.map((msg: any) => msg.message) || [],
        },
      };
    } catch (error) {
      throw new Error(
        `DOCX extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /** Iterate over all worksheets in an Excel file and concatenate their CSV representations. */
  private static async extractFromXlsx(buffer: Buffer): Promise<ExtractionResult> {
    try {
      const xlsxLib = await loadXlsx();
      const workbook = xlsxLib.read(buffer, { type: 'buffer' });

      const allSheetTexts: string[] = [];
      const warnings: string[] = [];

      workbook.SheetNames.forEach((sheetName: string) => {
        try {
          const worksheet = workbook.Sheets[sheetName];
          const csvData = xlsxLib.utils.sheet_to_csv(worksheet);

          if (csvData.trim()) {
            allSheetTexts.push(`=== Sheet: ${sheetName} ===\n${csvData}`);
          } else {
            warnings.push(`Sheet "${sheetName}" appears to be empty`);
          }
        } catch (sheetError) {
          warnings.push(
            `Failed to process sheet "${sheetName}": ${sheetError instanceof Error ? sheetError.message : 'Unknown error'}`
          );
        }
      });

      return {
        text: allSheetTexts.join('\n\n'),
        metadata: {
          extractionMethod: 'xlsx',
          warnings: warnings.length > 0 ? warnings : undefined,
        },
      };
    } catch (error) {
      throw new Error(
        `Excel extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /** Treat plain-text-like formats (Markdown, CSV, JSON, …) by simple buffer decoding. */
  private static async extractFromTextFile(
    buffer: Buffer,
    _fileType: string,
    encoding: string
  ): Promise<ExtractionResult> {
    try {
      const text = buffer.toString(encoding as BufferEncoding);

      return {
        text,
        metadata: {
          extractionMethod: 'buffer-to-string',
        },
      };
    } catch (error) {
      throw new Error(
        `Text file extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Remove control characters and normalise whitespace in the already extracted text.
   */
  static sanitizeText(text: string): string {
    // Remove control characters except newlines, tabs, and carriage returns
    const sanitized = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, ' ');

    // Normalize whitespace
    return sanitized.replace(/\s+/g, ' ').trim();
  }

  /**
   * Quick helper to derive basic statistics from an {@link ExtractionResult}.
   */
  static getExtractionStats(result: ExtractionResult): {
    textLength: number;
    wordCount: number;
    extractionMethod: string;
    hasWarnings: boolean;
  } {
    return {
      textLength: result.text.length,
      wordCount: result.metadata?.words || 0,
      extractionMethod: result.metadata?.extractionMethod || 'unknown',
      hasWarnings: (result.metadata?.warnings?.length || 0) > 0,
    };
  }
}

/**
 * Shorthand wrapper around {@link FileTextExtractor.extractText} that returns *only* the cleaned
 * text for callers that do not care about metadata.
 */
export async function extractText(
  buffer: Buffer,
  fileType: string,
  options?: ExtractionOptions
): Promise<string> {
  const result = await FileTextExtractor.extractText(buffer, fileType, options);
  return FileTextExtractor.sanitizeText(result.text);
}
