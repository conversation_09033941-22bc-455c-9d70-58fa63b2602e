export interface IEmbeddingIndex {
  /**
   * Store a document embedding in the index
   */
  store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void>;

  /**
   * Retrieve a document embedding from the index
   */
  retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null>;

  /**
   * Search for similar documents using vector similarity
   */
  searchSimilar(
    queryEmbedding: number[],
    topK?: number,
    threshold?: number,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  >;

  /**
   * Remove a document from the index
   */
  remove(documentId: string): Promise<boolean>;

  /**
   * Clear all documents for an organization
   */
  clearOrganization(organizationId: string): Promise<void>;

  /**
   * Get index statistics
   */
  getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }>;

  /**
   * Batch store multiple documents
   */
  batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void>;

  /**
   * Health check for the index
   */
  healthCheck(): Promise<boolean>;

  /**
   * Check if a document is already stored in the index
   */
  hasDocument(documentId: string): Promise<boolean>;

  /**
   * List all document IDs that are currently indexed for the organization
   */
  getIndexedDocumentIds(): Promise<string[]>;
}

export interface EmbeddingIndexOptions {
  organizationId: string;
  ttlHours?: number;
  batchSize?: number;
}

export { RedisEmbeddingIndex } from './redis-embedding-index';
export { InMemoryEmbeddingIndex } from './in-memory-embedding-index';
export { EmbeddingIndexFactory, EmbeddingIndexFactoryOptions } from './embedding-index-factory';

// Phase 3 - Advanced Features
export {
  RediSearchDetector,
  RediSearchInfo,
  VectorIndexConfig,
  VectorIndexInfo,
} from './redis-search-detector';
export {
  RediSearchEmbeddingIndex,
  RediSearchEmbeddingIndexOptions,
} from './redis-search-embedding-index';
export {
  MultiLevelCache,
  MultiLevelCacheOptions,
  CacheStats,
  CacheLevel,
} from './multi-level-cache';
export {
  CircuitBreaker,
  CircuitBreakerFactory,
  CircuitBreakerState,
  CircuitBreakerConfig,
  CircuitBreakerStats,
  FallbackStrategy,
} from './circuit-breaker';
export {
  MetricsService,
  metricsService,
  PrometheusMetric,
  SearchMetrics,
  RedisMetrics,
  EmbeddingMetrics,
  HealthStatus,
} from './metrics-service';
