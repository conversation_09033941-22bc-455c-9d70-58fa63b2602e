// Note: Imports available for future use
// import { CircuitBreakerStats, CircuitBreakerState } from './circuit-breaker';
// import { CacheStats } from './multi-level-cache';

export interface PrometheusMetric {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  help: string;
  labels?: Record<string, string>;
  value: number;
  timestamp?: number;
}

export interface SearchMetrics {
  totalSearches: number;
  successfulSearches: number;
  failedSearches: number;
  avgSearchDuration: number;
  p95SearchDuration: number;
  indexHitRate: number;
  fallbackRate: number;
}

export interface RedisMetrics {
  connectionStatus: 'connected' | 'disconnected' | 'error';
  operationsTotal: number;
  operationErrors: number;
  memoryUsage: number;
  keyspaceSize: number;
  slowlogEntries: number;
}

export interface EmbeddingMetrics {
  apiCallsTotal: number;
  apiCallErrors: number;
  batchEfficiency: number;
  avgBatchSize: number;
  embeddingCacheHits: number;
}

export interface HealthStatus {
  healthy: boolean;
  component: string;
  status: 'up' | 'down' | 'degraded';
  lastCheck: Date;
  details?: Record<string, any>;
  responseTime?: number;
}

/**
 * Metrics collection and health monitoring service for Redis vector index
 */
export class MetricsService {
  private searchMetrics: SearchMetrics;
  private healthStatuses: Map<string, HealthStatus> = new Map();
  private searchDurations: number[] = [];

  constructor() {
    this.searchMetrics = {
      totalSearches: 0,
      successfulSearches: 0,
      failedSearches: 0,
      avgSearchDuration: 0,
      p95SearchDuration: 0,
      indexHitRate: 0,
      fallbackRate: 0,
    };
  }

  /**
   * Record a search operation
   */
  recordSearch(
    duration: number,
    success: boolean,
    _hitIndex: boolean,
    _usedFallback: boolean,
    _organizationId: string
  ): void {
    this.searchMetrics.totalSearches++;

    if (success) {
      this.searchMetrics.successfulSearches++;
    } else {
      this.searchMetrics.failedSearches++;
    }

    // Track search durations for percentile calculation
    this.searchDurations.push(duration);
    if (this.searchDurations.length > 1000) {
      this.searchDurations = this.searchDurations.slice(-1000);
    }

    // Update averages
    this.updateSearchMetrics();
  }

  /**
   * Export metrics in Prometheus format
   */
  exportPrometheusFormat(): string {
    let output = '';

    // Search metrics
    output += `# HELP search_duration_seconds End-to-end search latency\n`;
    output += `# TYPE search_duration_seconds histogram\n`;
    output += `search_duration_seconds ${this.searchMetrics.avgSearchDuration / 1000}\n`;

    output += `# HELP search_index_hit_rate Percentage of searches served from Redis index\n`;
    output += `# TYPE search_index_hit_rate gauge\n`;
    output += `search_index_hit_rate ${this.searchMetrics.indexHitRate}\n`;

    output += `# HELP fallback_rate Share of requests handled by fallback\n`;
    output += `# TYPE fallback_rate gauge\n`;
    output += `fallback_rate ${this.searchMetrics.fallbackRate}\n`;

    return output;
  }

  /**
   * Get health status for all components
   */
  getHealthStatus(): Record<string, HealthStatus> {
    const status: Record<string, HealthStatus> = {};

    for (const [component, healthStatus] of this.healthStatuses) {
      status[component] = { ...healthStatus };
    }

    return status;
  }

  /**
   * Update health status for a component
   */
  updateHealthStatus(
    component: string,
    healthy: boolean,
    details?: Record<string, any>,
    responseTime?: number
  ): void {
    const status: HealthStatus = {
      healthy,
      component,
      status: healthy ? 'up' : 'down',
      lastCheck: new Date(),
      details,
      responseTime,
    };

    this.healthStatuses.set(component, status);
  }

  /**
   * Update search metrics calculations
   */
  private updateSearchMetrics(): void {
    if (this.searchDurations.length === 0) {
      return;
    }

    // Calculate average
    this.searchMetrics.avgSearchDuration =
      this.searchDurations.reduce((sum, duration) => sum + duration, 0) /
      this.searchDurations.length;

    // Calculate P95
    const sorted = [...this.searchDurations].sort((a, b) => a - b);
    const p95Index = Math.floor(sorted.length * 0.95);
    this.searchMetrics.p95SearchDuration = sorted[p95Index] || 0;
  }
}

/**
 * Global metrics service instance
 */
export const metricsService = new MetricsService();
