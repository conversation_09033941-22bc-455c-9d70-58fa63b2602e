import { IEmbeddingIndex, EmbeddingIndexOptions } from './index';
import { RedisEmbeddingIndex } from './redis-embedding-index';
import type { EmbeddingRedisClient as RediSearchRedisClient } from './shared-types';
import { InMemoryEmbeddingIndex } from './in-memory-embedding-index';
import { getRedisConfig } from '../../config/redis-config';

export interface EmbeddingIndexFactoryOptions {
  organizationId: string;
  redisClient?: RediSearchRedisClient;
  forceInMemory?: boolean;
  ttlHours?: number;
}

/**
 * Factory for creating embedding index instances with automatic fallback
 * from Redis to in-memory based on configuration and availability.
 */
export class EmbeddingIndexFactory {
  private static redisConfig = getRedisConfig();
  private static indexCache = new Map<string, IEmbeddingIndex>();

  /**
   * Create or retrieve a cached embedding index instance
   */
  static async createIndex(options: EmbeddingIndexFactoryOptions): Promise<IEmbeddingIndex> {
    if (!options.organizationId) {
      throw new Error('organizationId is required for embedding index isolation');
    }
    const cacheKey = `${options.organizationId}-${options.forceInMemory ? 'memory' : 'auto'}`;

    // Return cached instance if available
    if (this.indexCache.has(cacheKey)) {
      return this.indexCache.get(cacheKey)!;
    }

    const indexOptions: EmbeddingIndexOptions = {
      organizationId: options.organizationId,
      ttlHours: options.ttlHours || this.redisConfig.indexTtlHours,
      batchSize: this.redisConfig.embeddingBatchSize,
    };

    let embeddingIndex: IEmbeddingIndex;

    // Force in-memory if requested
    if (options.forceInMemory) {
      console.log(`[EmbeddingIndexFactory] Creating in-memory index (forced)`, {
        organizationId: options.organizationId,
        cacheKey,
      });
      embeddingIndex = new InMemoryEmbeddingIndex(indexOptions);
    } else {
      console.log('this.redisConfig.indexEnabled', this.redisConfig.indexEnabled);
      console.log('options.redisClient is defined', !!options.redisClient);
      // Try Redis first if enabled
      if (this.redisConfig.indexEnabled && options.redisClient) {
        // Check if this is a mock Redis client
        const isMockClient = this.isMockRedis(options.redisClient);

        if (isMockClient) {
          console.log(`[EmbeddingIndexFactory] Detected mock Redis client, using in-memory index`, {
            organizationId: options.organizationId,
            cacheKey,
          });
          embeddingIndex = this.createFallbackIndex(indexOptions);
        } else {
          console.log(`[EmbeddingIndexFactory] You are using live Redis instance.`, {
            organizationId: options.organizationId,
            hasRedisClient: !!options.redisClient,
            redisConfigEnabled: this.redisConfig.indexEnabled,
            cacheKey,
          });

          try {
            const redisIndex = new RedisEmbeddingIndex(options.redisClient, indexOptions);

            // Test Redis connection
            const isHealthy = await redisIndex.healthCheck();
            if (isHealthy) {
              console.log(`[EmbeddingIndexFactory] Redis health check passed.`, {
                organizationId: options.organizationId,
                cacheKey,
              });
              embeddingIndex = redisIndex;
            } else {
              console.warn(
                `[EmbeddingIndexFactory] Redis health check failed, falling back to in-memory`,
                {
                  organizationId: options.organizationId,
                  cacheKey,
                }
              );
              embeddingIndex = this.createFallbackIndex(indexOptions);
            }
          } catch (error) {
            console.warn(
              `[EmbeddingIndexFactory] Failed to create Redis embedding index, falling back to in-memory:`,
              error
            );
            embeddingIndex = this.createFallbackIndex(indexOptions);
          }
        }
      } else {
        // Redis not enabled or client not available
        console.log(`[EmbeddingIndexFactory] Creating fallback index (Redis not available)`, {
          organizationId: options.organizationId,
          redisConfigEnabled: this.redisConfig.indexEnabled,
          hasRedisClient: !!options.redisClient,
          cacheKey,
        });
        embeddingIndex = this.createFallbackIndex(indexOptions);
      }
    }

    // Cache the instance
    this.indexCache.set(cacheKey, embeddingIndex);
    return embeddingIndex;
  }

  /**
   * Create a fallback index based on configuration
   */
  private static createFallbackIndex(options: EmbeddingIndexOptions): IEmbeddingIndex {
    if (this.redisConfig.searchFallbackStrategy === 'error') {
      throw new Error('Redis embedding index is not available and fallback is disabled');
    }

    return new InMemoryEmbeddingIndex(options);
  }

  /**
   * Clear all cached index instances
   */
  static clearCache(): void {
    this.indexCache.clear();
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): {
    cachedInstances: number;
    instanceKeys: string[];
  } {
    return {
      cachedInstances: this.indexCache.size,
      instanceKeys: Array.from(this.indexCache.keys()),
    };
  }

  /**
   * Check if Redis is available and configured
   */
  static async isRedisAvailable(redisClient?: RediSearchRedisClient): Promise<boolean> {
    if (!this.redisConfig.indexEnabled || !redisClient) {
      return false;
    }

    // Check if this is a mock Redis client
    if (this.isMockRedis(redisClient)) {
      return false;
    }

    try {
      const result = await redisClient.ping();
      return result === 'PONG';
    } catch (error) {
      return false;
    }
  }

  /**
   * Type-safe check for mocked Redis client
   */
  private static isMockRedis(client: unknown): boolean {
    if (typeof client !== 'object' || client === null) return false;
    const value = (client as Record<string, unknown>)['isRedisMocked'];
    return value === true;
  }

  /**
   * Get current Redis configuration
   */
  static getConfig() {
    return { ...this.redisConfig };
  }
}
