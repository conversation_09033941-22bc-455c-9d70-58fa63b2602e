import { IEmbeddingIndex, EmbeddingIndexOptions } from './index';
import { InMemoryEmbeddingIndex } from './in-memory-embedding-index';

export interface CacheLevel {
  name: string;
  maxSize?: number;
  ttlSeconds?: number;
  evictionPolicy?: 'LRU' | 'LFU' | 'FIFO';
}

export interface MultiLevelCacheOptions extends EmbeddingIndexOptions {
  l1Cache: CacheLevel & {
    maxSizeMB?: number;
    maxDocuments?: number;
  };
  l2Cache: CacheLevel;
  cacheStrategy?: 'write-through' | 'write-back' | 'write-around';
  enableStats?: boolean;
}

export interface CacheStats {
  l1: {
    hits: number;
    misses: number;
    documents: number;
    memoryUsageMB: number;
    hitRate: number;
  };
  l2: {
    hits: number;
    misses: number;
    documents: number;
    hitRate: number;
  };
  overall: {
    totalRequests: number;
    hitRate: number;
    avgResponseTime: number;
  };
}

/**
 * Multi-level cache implementation with L1 (memory) and L2 (Redis) caching
 */
export class MultiLevelCache implements IEmbeddingIndex {
  private l1Cache: InMemoryEmbeddingIndex;
  private l2Cache: IEmbeddingIndex;
  private options: MultiLevelCacheOptions;
  private stats: CacheStats;
  private requestTimes: number[] = [];

  constructor(l2Cache: IEmbeddingIndex, options: MultiLevelCacheOptions) {
    this.l2Cache = l2Cache;
    this.options = {
      cacheStrategy: 'write-through',
      enableStats: true,
      ...options,
    };

    // Initialize L1 cache (in-memory)
    this.l1Cache = new InMemoryEmbeddingIndex({
      organizationId: options.organizationId,
      ttlHours: options.l1Cache.ttlSeconds ? options.l1Cache.ttlSeconds / 3600 : undefined,
      batchSize: options.batchSize,
    });

    // Initialize stats
    this.stats = {
      l1: { hits: 0, misses: 0, documents: 0, memoryUsageMB: 0, hitRate: 0 },
      l2: { hits: 0, misses: 0, documents: 0, hitRate: 0 },
      overall: { totalRequests: 0, hitRate: 0, avgResponseTime: 0 },
    };
  }

  /**
   * Store a document in both cache levels
   */
  async store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const startTime = Date.now();

    try {
      // Strategy-based writing
      switch (this.options.cacheStrategy) {
        case 'write-through':
          // Write to both L1 and L2
          await Promise.all([
            this.l1Cache.store(documentId, embedding, content, metadata),
            this.l2Cache.store(documentId, embedding, content, metadata),
          ]);
          break;

        case 'write-back':
          // Write to L1 immediately, L2 later (lazy)
          await this.l1Cache.store(documentId, embedding, content, metadata);
          // Schedule L2 write (in production, this would use a job queue)
          setImmediate(() => {
            this.l2Cache.store(documentId, embedding, content, metadata).catch(error => {
              console.error('L2 write-back failed:', error);
            });
          });
          break;

        case 'write-around':
          // Only write to L2, L1 is populated on read
          await this.l2Cache.store(documentId, embedding, content, metadata);
          break;
      }

      this.updateStats('store', Date.now() - startTime);
    } catch (error) {
      console.error('Failed to store document:', error);
      throw error;
    }
  }

  /**
   * Retrieve a document from cache hierarchy
   */
  async retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null> {
    const startTime = Date.now();
    this.stats.overall.totalRequests++;

    try {
      // Try L1 cache first
      let document = await this.l1Cache.retrieve(documentId);

      if (document) {
        this.stats.l1.hits++;
        this.updateStats('retrieve', Date.now() - startTime);
        return document;
      }

      this.stats.l1.misses++;

      // Try L2 cache
      document = await this.l2Cache.retrieve(documentId);

      if (document) {
        this.stats.l2.hits++;

        // Promote to L1 cache
        await this.l1Cache.store(
          documentId,
          document.embedding,
          document.content,
          document.metadata
        );

        this.updateStats('retrieve', Date.now() - startTime);
        return document;
      }

      this.stats.l2.misses++;
      this.updateStats('retrieve', Date.now() - startTime);
      return null;
    } catch (error) {
      console.error('Failed to retrieve document:', error);
      this.updateStats('retrieve', Date.now() - startTime);
      return null;
    }
  }

  /**
   * Search for similar documents across cache levels
   */
  async searchSimilar(
    queryEmbedding: number[],
    topK: number = 3,
    threshold: number = 0.0,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    const startTime = Date.now();
    this.stats.overall.totalRequests++;

    try {
      // First try L1 cache for quick results
      const l1Results = await this.l1Cache.searchSimilar(queryEmbedding, topK, threshold, filters);

      // If L1 has sufficient high-quality results, return them
      if (l1Results.length >= Math.min(topK, 5) && l1Results[0]?.similarity > 0.8) {
        this.stats.l1.hits++;
        this.updateStats('search', Date.now() - startTime);
        return l1Results.slice(0, topK);
      }

      this.stats.l1.misses++;

      // Otherwise, search L2 cache for comprehensive results
      const l2Results = await this.l2Cache.searchSimilar(queryEmbedding, topK, threshold, filters);

      if (l2Results.length > 0) {
        this.stats.l2.hits++;

        // Promote top results to L1 cache
        const topResults = l2Results.slice(0, Math.min(3, l2Results.length));
        for (const result of topResults) {
          try {
            await this.l1Cache.store(
              result.documentId,
              queryEmbedding, // Use query embedding as approximation
              result.content,
              result.metadata
            );
          } catch (error) {
            console.warn('Failed to promote result to L1:', error);
          }
        }
      } else {
        this.stats.l2.misses++;
      }

      this.updateStats('search', Date.now() - startTime);
      return l2Results;
    } catch (error) {
      console.error('Failed to search documents:', error);
      this.updateStats('search', Date.now() - startTime);
      return [];
    }
  }

  /**
   * Remove a document from both cache levels
   */
  async remove(documentId: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      const [l1Removed, l2Removed] = await Promise.all([
        this.l1Cache.remove(documentId),
        this.l2Cache.remove(documentId),
      ]);

      this.updateStats('remove', Date.now() - startTime);
      return l1Removed || l2Removed;
    } catch (error) {
      console.error('Failed to remove document:', error);
      this.updateStats('remove', Date.now() - startTime);
      return false;
    }
  }

  /**
   * Clear all documents for the organization
   */
  async clearOrganization(organizationId: string): Promise<void> {
    const startTime = Date.now();

    try {
      await Promise.all([
        this.l1Cache.clearOrganization(organizationId),
        this.l2Cache.clearOrganization(organizationId),
      ]);

      // Reset stats
      this.stats.l1 = { hits: 0, misses: 0, documents: 0, memoryUsageMB: 0, hitRate: 0 };
      this.stats.l2 = { hits: 0, misses: 0, documents: 0, hitRate: 0 };

      this.updateStats('clear', Date.now() - startTime);
    } catch (error) {
      console.error('Failed to clear organization:', error);
      throw error;
    }
  }

  /**
   * Get combined statistics from both cache levels
   */
  async getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }> {
    try {
      const [l1Stats, l2Stats] = await Promise.all([
        this.l1Cache.getStats(),
        this.l2Cache.getStats(),
      ]);

      const totalDocs = Math.max(l1Stats.totalDocuments, l2Stats.totalDocuments);

      return {
        totalDocuments: totalDocs,
        organizationCounts: {
          ...l1Stats.organizationCounts,
          ...l2Stats.organizationCounts,
        },
        memoryUsage: l2Stats.memoryUsage,
      };
    } catch (error) {
      console.error('Failed to get stats:', error);
      return {
        totalDocuments: 0,
        organizationCounts: {},
      };
    }
  }

  /**
   * Batch store documents efficiently across cache levels
   */
  async batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    const startTime = Date.now();

    try {
      switch (this.options.cacheStrategy) {
        case 'write-through':
          await Promise.all([
            this.l1Cache.batchStore(documents),
            this.l2Cache.batchStore(documents),
          ]);
          break;

        case 'write-back':
          await this.l1Cache.batchStore(documents);
          setImmediate(() => {
            this.l2Cache.batchStore(documents).catch(error => {
              console.error('L2 batch write-back failed:', error);
            });
          });
          break;

        case 'write-around':
          await this.l2Cache.batchStore(documents);
          break;
      }

      this.updateStats('batchStore', Date.now() - startTime);
    } catch (error) {
      console.error('Failed to batch store documents:', error);
      throw error;
    }
  }

  /**
   * Health check for both cache levels
   */
  async healthCheck(): Promise<boolean> {
    try {
      const l2Healthy = await this.l2Cache.healthCheck();
      const l1Healthy = await this.l1Cache.healthCheck();
      return l2Healthy && l1Healthy;
    } catch (error) {
      console.error('Cache health check failed:', error);
      return false;
    }
  }

  /**
   * Check if a document exists in either cache level (L1 first)
   */
  async hasDocument(documentId: string): Promise<boolean> {
    if (await this.l1Cache.hasDocument(documentId)) return true;
    return this.l2Cache.hasDocument(documentId);
  }

  /**
   * Get the union of document IDs from both cache levels
   */
  async getIndexedDocumentIds(): Promise<string[]> {
    const [l1Ids, l2Ids] = await Promise.all([
      this.l1Cache.getIndexedDocumentIds(),
      this.l2Cache.getIndexedDocumentIds(),
    ]);
    // Merge unique ids
    return Array.from(new Set([...l1Ids, ...l2Ids]));
  }

  /**
   * Get detailed cache statistics
   */
  getCacheStats(): CacheStats {
    // Update hit rates
    const l1Total = this.stats.l1.hits + this.stats.l1.misses;
    const l2Total = this.stats.l2.hits + this.stats.l2.misses;
    const overallTotal = this.stats.overall.totalRequests;

    this.stats.l1.hitRate = l1Total > 0 ? this.stats.l1.hits / l1Total : 0;
    this.stats.l2.hitRate = l2Total > 0 ? this.stats.l2.hits / l2Total : 0;
    this.stats.overall.hitRate =
      overallTotal > 0 ? (this.stats.l1.hits + this.stats.l2.hits) / overallTotal : 0;

    return { ...this.stats };
  }

  /**
   * Invalidate L1 cache (useful for testing or manual cache management)
   */
  async invalidateL1Cache(): Promise<void> {
    try {
      await this.l1Cache.clearOrganization(this.options.organizationId);
      this.stats.l1 = { hits: 0, misses: 0, documents: 0, memoryUsageMB: 0, hitRate: 0 };
    } catch (error) {
      console.error('Failed to invalidate L1 cache:', error);
    }
  }

  /**
   * Warm up L1 cache with frequently accessed documents
   */
  async warmupL1Cache(documentIds: string[]): Promise<void> {
    try {
      const warmupPromises = documentIds.map(async docId => {
        const document = await this.l2Cache.retrieve(docId);
        if (document) {
          await this.l1Cache.store(docId, document.embedding, document.content, document.metadata);
        }
      });

      await Promise.all(warmupPromises);
      console.log(`Warmed up L1 cache with ${documentIds.length} documents`);
    } catch (error) {
      console.error('Failed to warm up L1 cache:', error);
    }
  }

  /**
   * Update internal statistics
   */
  private updateStats(_operation: string, responseTime: number): void {
    if (this.options.enableStats) {
      this.requestTimes.push(responseTime);

      // Keep only last 1000 response times for rolling average
      if (this.requestTimes.length > 1000) {
        this.requestTimes = this.requestTimes.slice(-1000);
      }

      this.stats.overall.avgResponseTime =
        this.requestTimes.reduce((sum, time) => sum + time, 0) / this.requestTimes.length;
    }
  }
}
