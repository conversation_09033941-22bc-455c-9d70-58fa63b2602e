// Shared types for embedding index Redis interactions to avoid duplication across implementations

export interface EmbeddingRedisClient {
  // Hash operations
  hset(key: string, field: string, value: string): Promise<number>;
  // Overload for bulk hash set
  hset(key: string, data: Record<string, string | number>): Promise<number>;
  hget(key: string, field: string): Promise<string | null>;
  hgetall(key: string): Promise<Record<string, string>>;
  hdel(key: string, field: string): Promise<number>;

  // Key operations
  del(key: string): Promise<number>;
  keys(pattern: string): Promise<string[]>;
  expire(key: string, seconds: number): Promise<number>;
  ping(): Promise<string>;
  pipeline(): any;
  exec(): Promise<any>;

  // Set operations for indexed document tracking
  sadd(key: string, member: string): Promise<number>;
  srem(key: string, member: string): Promise<number>;
  smembers(key: string): Promise<string[]>;
  scard(key: string): Promise<number>;
}

export interface EmbeddingStoredDocument {
  documentId: string;
  embedding: number[];
  content: string;
  metadata?: Record<string, any>;
  timestamp: number;
  organizationId: string;
}
