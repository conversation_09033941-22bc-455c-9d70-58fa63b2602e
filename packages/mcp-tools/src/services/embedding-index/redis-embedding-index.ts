import { IEmbeddingIndex, EmbeddingIndexOptions } from './index';
import { EmbeddingRedisClient, EmbeddingStoredDocument } from './shared-types';

type RedisClient = EmbeddingRedisClient;

type StoredDocument = EmbeddingStoredDocument;

function isStoredDocument(value: unknown): value is StoredDocument {
  if (typeof value !== 'object' || value === null) return false;
  const obj: any = value;
  return (
    typeof obj.documentId === 'string' &&
    Array.isArray(obj.embedding) &&
    typeof obj.content === 'string' &&
    typeof obj.timestamp === 'number' &&
    typeof obj.organizationId === 'string'
  );
}

function isMockRedis(client: unknown): client is { isRedisMocked: boolean } {
  return !!client && typeof (client as any).isRedisMocked === 'boolean';
}

/**
 * Redis-based embedding index implementation with basic CRUD operations
 * and brute-force vector similarity search.
 */
export class RedisEmbeddingIndex implements IEmbeddingIndex {
  private redis: RedisClient;
  private options: EmbeddingIndexOptions;
  private keyPrefix: string;
  private docSetKey: string;

  constructor(redis: RedisClient, options: EmbeddingIndexOptions) {
    this.redis = redis;
    this.options = options;
    // Ensure consistent key format with TextChunkingService
    const orgId = options.organizationId.startsWith('org-')
      ? options.organizationId
      : `org-${options.organizationId}`;
    this.keyPrefix = `embeddings:${orgId}`;
    this.docSetKey = `embeddings:${orgId}:docs`;

    // Check if this is a mock Redis client
    if (isMockRedis(redis) && (redis as any).isRedisMocked === true) {
      console.error(
        `[RedisEmbeddingIndex] WARNING: Using MOCK Redis client! Documents will NOT be persisted to Redis.`
      );
      console.error(
        `[RedisEmbeddingIndex] To fix this, ensure Redis is running and SKIP_REDIS is not set to 'true'`
      );
    }
  }

  /**
   * Store a document embedding in Redis
   * Enhanced to support chunked document keys
   */
  async store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    // Use the documentId directly as the key if it's already a full Redis key
    const key = documentId.startsWith('embeddings:')
      ? documentId // Already a full Redis key like "embeddings:org-xxx:doc-yyy::1"
      : `${this.keyPrefix}:${documentId}`; // Regular document key

    const document: StoredDocument = {
      documentId,
      embedding,
      content,
      metadata,
      timestamp: Date.now(),
      organizationId: this.options.organizationId,
    };

    await this.redis.hset(key, 'data', JSON.stringify(document));

    // Track document ID in organization set (if supported)
    if (typeof this.redis.sadd === 'function') {
      try {
        await this.redis.sadd(this.docSetKey, documentId);
      } catch (e) {
        // Non-fatal: continue without set tracking
      }
    }

    // Set TTL if specified
    if (this.options.ttlHours) {
      await this.redis.expire(key, this.options.ttlHours * 3600);
    }
  }

  /**
   * Retrieve a document embedding from Redis
   * Enhanced to support chunked document keys
   */
  async retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null> {
    // Use the documentId directly as the key if it's already a full Redis key
    const key = documentId.startsWith('embeddings:')
      ? documentId // Already a full Redis key like "embeddings:org-xxx:doc-yyy::1"
      : `${this.keyPrefix}:${documentId}`; // Regular document key

    const data = await this.redis.hget(key, 'data');

    if (!data) {
      return null;
    }

    try {
      // nosemgrep: validated JSON parse by strict type guard below
      const parsed: unknown = JSON.parse(data);
      if (!isStoredDocument(parsed)) {
        return null;
      }
      return {
        embedding: parsed.embedding,
        content: parsed.content,
        metadata: parsed.metadata,
      };
    } catch (error) {
      console.error(`Failed to parse document ${documentId}:`, error);
      return null;
    }
  }

  /**
   * Search for similar documents using brute-force cosine similarity
   */
  async searchSimilar(
    queryEmbedding: number[],
    topK: number = 2,
    threshold: number = 0.45,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    // Prefer set tracking for document discovery if available
    let docIds: string[] = [];
    if (typeof this.redis.smembers === 'function') {
      try {
        docIds = await this.redis.smembers(this.docSetKey);
      } catch {
        docIds = [];
      }
    }

    // Fallback to KEYS scan if set is unavailable or empty
    let allKeys: string[] = [];
    if (docIds.length > 0) {
      allKeys = docIds.map(id => (id.startsWith('embeddings:') ? id : `${this.keyPrefix}:${id}`));
    } else {
      const pattern = `${this.keyPrefix}:*`;
      const chunkPattern = `embeddings:org-${this.options.organizationId}:*`;
      const keys = await this.redis.keys(pattern);
      const chunkKeys = await this.redis.keys(chunkPattern);
      allKeys = [...new Set([...keys, ...chunkKeys])];
    }

    console.log('[RedisEmbeddingIndex] searchSimilar -> allKeys:', allKeys);

    if (allKeys.length === 0) {
      return [];
    }

    // Retrieve all documents
    const documents: StoredDocument[] = [];
    for (const key of allKeys) {
      const data = await this.redis.hget(key, 'data');
      if (data) {
        try {
          // nosemgrep: validated JSON parse by strict type guard below
          const parsed: unknown = JSON.parse(data);
          if (!isStoredDocument(parsed)) {
            continue;
          }

          // Apply filters if provided
          if (filters && !this.matchesFilters(parsed, filters)) {
            continue;
          }

          documents.push(parsed);
          console.log(
            '[RedisEmbeddingIndex] searchSimilar -> documents to return:',
            documents.length
          );
        } catch (error) {
          console.error(`Failed to parse document from key ${key}:`, error);
        }
      }
    }

    // Calculate similarities
    const similarities = documents
      .filter(doc => Array.isArray(doc.embedding) && doc.embedding.length === queryEmbedding.length)
      .map(doc => ({
        documentId: doc.documentId,
        similarity: this.calculateCosineSimilarity(queryEmbedding, doc.embedding),
        content: doc.content,
        metadata: doc.metadata,
      }))
      .sort((a, b) => b.similarity - a.similarity);

    const result = similarities
      .filter(result => result.similarity >= threshold)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);

    console.log(
      '[RedisEmbeddingIndex] searchSimilar -> result (length: ' + result.length + '):',
      result.length > 0
        ? result.map(r => {
            return {
              documentId: r.documentId,
              similarity: r.similarity,
              content: r.content.substring(0, 100),
            };
          })
        : '- it does not mean no similarity. It means no similarity >= threshold. We return top 1 even if not met the threshold.'
    );

    // We return top 1 even if not met the threshold.
    // This is because we want to return at least one result.
    if (result.length === 0) {
      return similarities.slice(0, 1);
    }

    return result;
  }

  /**
   * Remove a document from the index
   */
  async remove(documentId: string): Promise<boolean> {
    const key = `${this.keyPrefix}:${documentId}`;
    const pipeline = this.redis.pipeline();
    pipeline.del(key);
    if (typeof this.redis.srem === 'function') {
      pipeline.srem(this.docSetKey, documentId);
    }
    const results = await pipeline.exec();
    const deleted = results && results[0] && results[0][1] > 0;
    return !!deleted;
  }

  /**
   * Clear all documents for an organization
   */
  async clearOrganization(organizationId: string): Promise<void> {
    // If set tracking is available, delete based on set membership
    if (typeof this.redis.smembers === 'function') {
      try {
        const ids = await this.redis.smembers(`embeddings:${organizationId}:docs`);
        if (ids.length > 0) {
          const p = this.redis.pipeline();
          for (const id of ids) {
            p.del(`embeddings:${organizationId}:${id}`);
          }
          p.del(`embeddings:${organizationId}:docs`);
          await p.exec();
          return;
        }
      } catch {
        // fall back to KEYS scan
      }
    }

    const pattern = `embeddings:${organizationId}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await Promise.all(keys.map(key => this.redis.del(key)));
    }
  }

  /**
   * Get index statistics
   */
  async getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }> {
    if (typeof this.redis.scard === 'function') {
      try {
        const count = await this.redis.scard(this.docSetKey);
        return {
          totalDocuments: count,
          organizationCounts: {
            [this.options.organizationId]: count,
          },
        };
      } catch {
        // fall back to KEYS
      }
    }

    const pattern = `${this.keyPrefix}:*`;
    const keys = await this.redis.keys(pattern);
    return {
      totalDocuments: keys.length,
      organizationCounts: {
        [this.options.organizationId]: keys.length,
      },
    };
  }

  /**
   * Batch store multiple documents using Redis pipeline
   */
  async batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    console.log(`[RedisEmbeddingIndex] Starting batch store for ${documents.length} documents`, {
      organizationId: this.options.organizationId,
      keyPrefix: this.keyPrefix,
      documentIds: documents.map(doc => doc.documentId),
      expectedKeys: documents.map(doc => `${this.keyPrefix}:${doc.documentId}`),
    });

    const pipeline = this.redis.pipeline();

    for (const doc of documents) {
      // Use the documentId directly as the key if it's already a full Redis key
      const key = doc.documentId.startsWith('embeddings:')
        ? doc.documentId // Already a full Redis key like "embeddings:org-xxx:doc-yyy::1"
        : `${this.keyPrefix}:${doc.documentId}`; // Regular document key

      const storedDoc: StoredDocument = {
        documentId: doc.documentId,
        embedding: doc.embedding,
        content: doc.content,
        metadata: doc.metadata,
        timestamp: Date.now(),
        organizationId: this.options.organizationId,
      };

      console.log(`[RedisEmbeddingIndex] Storing document with key: ${key}`, {
        documentId: doc.documentId,
        contentLength: doc.content.length,
        embeddingLength: doc.embedding.length,
        isChunked: doc.documentId.includes('::'),
        isFullRedisKey: doc.documentId.startsWith('embeddings:'),
      });

      pipeline.hset(key, 'data', JSON.stringify(storedDoc));
      if (typeof this.redis.sadd === 'function') {
        pipeline.sadd(this.docSetKey, doc.documentId);
      }

      if (this.options.ttlHours) {
        pipeline.expire(key, this.options.ttlHours * 3600);
      }
    }

    try {
      const results = await pipeline.exec();
      console.log(`[RedisEmbeddingIndex] Batch store completed successfully`, {
        organizationId: this.options.organizationId,
        documentsStored: documents.length,
        pipelineResults: results,
      });
    } catch (error) {
      console.error(`[RedisEmbeddingIndex] Batch store failed`, {
        organizationId: this.options.organizationId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Health check for Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }

  /**
   * Check if a document exists in the index
   */
  async hasDocument(documentId: string): Promise<boolean> {
    const key = `${this.keyPrefix}:${documentId}`;
    const data = await this.redis.hget(key, 'data');
    return data !== null;
  }

  /**
   * Get all indexed document IDs for the organization
   */
  async getIndexedDocumentIds(): Promise<string[]> {
    // Prefer set tracking
    if (typeof this.redis.smembers === 'function') {
      try {
        const ids = await this.redis.smembers(this.docSetKey);
        if (ids.length > 0) return ids;
      } catch {
        // ignore and fallback
      }
    }

    const pattern = `${this.keyPrefix}:*`;
    const keys = await this.redis.keys(pattern);
    return keys.map(k => k.substring(this.keyPrefix.length + 1));
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  /**
   * Check if document matches the provided filters
   */
  private matchesFilters(document: StoredDocument, filters: Record<string, any>): boolean {
    if (!document.metadata) {
      return Object.keys(filters).length === 0;
    }

    return Object.entries(filters).every(([key, value]) => {
      return document.metadata![key] === value;
    });
  }
}
