import { IEmbeddingIndex } from './index';

export enum CircuitBreakerState {
  CLOSED = 'closed', // Normal operation
  OPEN = 'open', // Circuit is open, requests fail fast
  HALF_OPEN = 'half_open', // Testing if service is back
}

export interface CircuitBreakerConfig {
  failureThreshold: number; // Number of failures to trigger opening
  recoveryTimeout: number; // Time in ms before attempting recovery
  monitoringWindow: number; // Time window for failure tracking
  successThreshold: number; // Successes needed to close circuit in half-open
  maxRetries: number; // Max retries before giving up
}

export interface FallbackStrategy {
  name: string;
  priority: number;
  description: string;
}

export interface CircuitBreakerStats {
  state: CircuitBreakerState;
  failures: number;
  successes: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  totalRequests: number;
  failureRate: number;
  uptime: number;
}

/**
 * Circuit breaker implementation with fallback strategies
 */
export class CircuitBreaker implements IEmbeddingIndex {
  private primaryIndex: IEmbeddingIndex;
  private fallbackIndexes: Array<{ index: IEmbeddingIndex; strategy: FallbackStrategy }>;
  private config: CircuitBreakerConfig;
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures: number = 0;
  private successes: number = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private totalRequests: number = 0;
  private halfOpenSuccesses: number = 0;
  private createdAt: Date = new Date();

  constructor(
    primaryIndex: IEmbeddingIndex,
    fallbackIndexes: Array<{ index: IEmbeddingIndex; strategy: FallbackStrategy }> = [],
    config: Partial<CircuitBreakerConfig> = {}
  ) {
    this.primaryIndex = primaryIndex;
    this.fallbackIndexes = fallbackIndexes.sort(
      (a, b) => a.strategy.priority - b.strategy.priority
    );

    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 60 seconds
      monitoringWindow: 300000, // 5 minutes
      successThreshold: 3,
      maxRetries: 2,
      ...config,
    };
  }

  /**
   * Store a document with circuit breaker protection
   */
  async store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.executeWithCircuitBreaker(
      'store',
      () => this.primaryIndex.store(documentId, embedding, content, metadata),
      fallbackIndex => fallbackIndex.store(documentId, embedding, content, metadata)
    );
  }

  /**
   * Retrieve a document with circuit breaker protection
   */
  async retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null> {
    return this.executeWithCircuitBreaker(
      'retrieve',
      () => this.primaryIndex.retrieve(documentId),
      fallbackIndex => fallbackIndex.retrieve(documentId)
    );
  }

  /**
   * Search for similar documents with circuit breaker protection
   */
  async searchSimilar(
    queryEmbedding: number[],
    topK: number = 3,
    threshold: number = 0.0,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    return this.executeWithCircuitBreaker(
      'searchSimilar',
      () => this.primaryIndex.searchSimilar(queryEmbedding, topK, threshold, filters),
      fallbackIndex => fallbackIndex.searchSimilar(queryEmbedding, topK, threshold, filters)
    );
  }

  /**
   * Remove a document with circuit breaker protection
   */
  async remove(documentId: string): Promise<boolean> {
    return this.executeWithCircuitBreaker(
      'remove',
      () => this.primaryIndex.remove(documentId),
      fallbackIndex => fallbackIndex.remove(documentId)
    );
  }

  /**
   * Clear organization documents with circuit breaker protection
   */
  async clearOrganization(organizationId: string): Promise<void> {
    return this.executeWithCircuitBreaker(
      'clearOrganization',
      () => this.primaryIndex.clearOrganization(organizationId),
      fallbackIndex => fallbackIndex.clearOrganization(organizationId)
    );
  }

  /**
   * Get statistics with circuit breaker protection
   */
  async getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }> {
    return this.executeWithCircuitBreaker(
      'getStats',
      () => this.primaryIndex.getStats(),
      fallbackIndex => fallbackIndex.getStats()
    );
  }

  /**
   * Check if a document exists within the index hierarchy
   */
  async hasDocument(documentId: string): Promise<boolean> {
    return this.executeWithCircuitBreaker(
      'hasDocument',
      () => (this.primaryIndex as any).hasDocument?.(documentId),
      fallbackIndex => (fallbackIndex as any).hasDocument?.(documentId)
    );
  }

  /**
   * Retrieve IDs of all indexed documents (primary then fallback)
   */
  async getIndexedDocumentIds(): Promise<string[]> {
    return this.executeWithCircuitBreaker(
      'getIndexedDocumentIds',
      () => (this.primaryIndex as any).getIndexedDocumentIds?.(),
      fallbackIndex => (fallbackIndex as any).getIndexedDocumentIds?.()
    );
  }

  /**
   * Batch store with circuit breaker protection
   */
  async batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    return this.executeWithCircuitBreaker(
      'batchStore',
      () => this.primaryIndex.batchStore(documents),
      fallbackIndex => fallbackIndex.batchStore(documents)
    );
  }

  /**
   * Health check with circuit breaker protection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const primaryHealthy = await this.primaryIndex.healthCheck();

      if (primaryHealthy && this.state === CircuitBreakerState.OPEN) {
        // Primary is healthy, try to close circuit
        this.attemptCircuitClose();
      }

      return primaryHealthy || this.fallbackIndexes.length > 0;
    } catch (error) {
      console.error('Health check failed:', error);
      return this.fallbackIndexes.length > 0;
    }
  }

  /**
   * Get circuit breaker statistics
   */
  getCircuitBreakerStats(): CircuitBreakerStats {
    const uptime = Date.now() - this.createdAt.getTime();
    const failureRate = this.totalRequests > 0 ? this.failures / this.totalRequests : 0;

    return {
      state: this.state,
      failures: this.failures,
      successes: this.successes,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      totalRequests: this.totalRequests,
      failureRate,
      uptime,
    };
  }

  /**
   * Manually open the circuit breaker
   */
  openCircuit(): void {
    this.state = CircuitBreakerState.OPEN;
    console.warn('Circuit breaker manually opened');
  }

  /**
   * Manually close the circuit breaker
   */
  closeCircuit(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failures = 0;
    this.halfOpenSuccesses = 0;
    console.log('Circuit breaker manually closed');
  }

  /**
   * Reset circuit breaker statistics
   */
  resetStats(): void {
    this.failures = 0;
    this.successes = 0;
    this.totalRequests = 0;
    this.halfOpenSuccesses = 0;
    this.lastFailureTime = undefined;
    this.lastSuccessTime = undefined;
    this.createdAt = new Date();
  }

  /**
   * Execute an operation with circuit breaker logic
   */
  private async executeWithCircuitBreaker<T>(
    operation: string,
    primaryOperation: () => Promise<T>,
    fallbackOperation: (index: IEmbeddingIndex) => Promise<T>
  ): Promise<T> {
    this.totalRequests++;

    // Check if circuit is open
    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptRecovery()) {
        this.state = CircuitBreakerState.HALF_OPEN;
        console.log('Circuit breaker attempting recovery (half-open)');
      } else {
        return this.executeFallback(operation, fallbackOperation);
      }
    }

    // Try primary operation
    try {
      const result = await primaryOperation();
      this.recordSuccess();
      return result;
    } catch (error) {
      console.error(`Primary ${operation} failed:`, error);
      this.recordFailure();

      // Try fallback strategies
      return this.executeFallback(operation, fallbackOperation);
    }
  }

  /**
   * Execute fallback strategies in priority order
   */
  private async executeFallback<T>(
    operation: string,
    fallbackOperation: (index: IEmbeddingIndex) => Promise<T>
  ): Promise<T> {
    if (this.fallbackIndexes.length === 0) {
      throw new Error(`${operation} failed: no fallback available and primary service is down`);
    }

    let lastError: Error | undefined;

    for (const { index, strategy } of this.fallbackIndexes) {
      try {
        console.log(`Attempting ${operation} with fallback strategy: ${strategy.name}`);
        const result = await fallbackOperation(index);
        console.log(`${operation} succeeded with fallback: ${strategy.name}`);
        return result;
      } catch (error) {
        console.error(`Fallback ${strategy.name} failed for ${operation}:`, error);
        lastError = error as Error;
      }
    }

    throw new Error(`All fallback strategies failed for ${operation}: ${lastError?.message}`);
  }

  /**
   * Record a successful operation
   */
  private recordSuccess(): void {
    this.successes++;
    this.lastSuccessTime = new Date();

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.halfOpenSuccesses++;
      if (this.halfOpenSuccesses >= this.config.successThreshold) {
        this.state = CircuitBreakerState.CLOSED;
        this.failures = 0;
        this.halfOpenSuccesses = 0;
        console.log('Circuit breaker closed - service recovered');
      }
    }
  }

  /**
   * Record a failed operation
   */
  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = new Date();

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      // Failure during half-open, go back to open
      this.state = CircuitBreakerState.OPEN;
      this.halfOpenSuccesses = 0;
      console.warn('Circuit breaker opened - service still failing');
    } else if (this.state === CircuitBreakerState.CLOSED) {
      // Check if we should open the circuit
      if (this.failures >= this.config.failureThreshold) {
        this.state = CircuitBreakerState.OPEN;
        console.warn(
          `Circuit breaker opened - failure threshold reached (${this.failures} failures)`
        );
      }
    }
  }

  /**
   * Check if we should attempt recovery from open state
   */
  private shouldAttemptRecovery(): boolean {
    if (!this.lastFailureTime) {
      return false;
    }

    const timeSinceLastFailure = Date.now() - this.lastFailureTime.getTime();
    return timeSinceLastFailure >= this.config.recoveryTimeout;
  }

  /**
   * Attempt to close the circuit if conditions are met
   */
  private attemptCircuitClose(): void {
    if (this.state === CircuitBreakerState.OPEN) {
      this.state = CircuitBreakerState.HALF_OPEN;
      this.halfOpenSuccesses = 0;
      console.log('Circuit breaker attempting recovery (half-open)');
    }
  }
}

/**
 * Factory for creating circuit breakers with common fallback strategies
 */
export class CircuitBreakerFactory {
  /**
   * Create a circuit breaker with in-memory fallback
   */
  static createWithInMemoryFallback(
    primaryIndex: IEmbeddingIndex,
    inMemoryIndex: IEmbeddingIndex,
    config?: Partial<CircuitBreakerConfig>
  ): CircuitBreaker {
    const fallbackStrategies = [
      {
        index: inMemoryIndex,
        strategy: {
          name: 'in-memory',
          priority: 1,
          description: 'Local in-memory cache fallback',
        },
      },
    ];

    return new CircuitBreaker(primaryIndex, fallbackStrategies, config);
  }

  /**
   * Create a circuit breaker with multiple fallback levels
   */
  static createWithMultipleFallbacks(
    primaryIndex: IEmbeddingIndex,
    fallbacks: Array<{
      index: IEmbeddingIndex;
      name: string;
      priority: number;
      description: string;
    }>,
    config?: Partial<CircuitBreakerConfig>
  ): CircuitBreaker {
    const fallbackStrategies = fallbacks.map(fallback => ({
      index: fallback.index,
      strategy: {
        name: fallback.name,
        priority: fallback.priority,
        description: fallback.description,
      },
    }));

    return new CircuitBreaker(primaryIndex, fallbackStrategies, config);
  }
}
