import { IEmbeddingIndex, EmbeddingIndexOptions } from './index';
import { EmbeddingRedisClient } from './shared-types';

type RedisClient = EmbeddingRedisClient;
import { RediSearchDetector, VectorIndexConfig, RediSearchInfo } from './redis-search-detector';

export interface RediSearchEmbeddingIndexOptions extends EmbeddingIndexOptions {
  vectorDimensions: number;
  distanceMetric?: 'COSINE' | 'L2' | 'IP';
  useNativeSearch?: boolean; // Auto-detect by default
}

/**
 * Enhanced Redis embedding index with RediSearch native vector search support
 */
export class RediSearchEmbeddingIndex implements IEmbeddingIndex {
  private redis: RedisClient;
  private options: RediSearchEmbeddingIndexOptions;
  private detector: RediSearchDetector;
  private rediSearchInfo?: RediSearchInfo;
  private indexName: string;
  private keyPrefix: string;

  constructor(redis: RedisClient, options: RediSearchEmbeddingIndexOptions) {
    this.redis = redis;
    this.options = {
      distanceMetric: 'COSINE',
      useNativeSearch: true,
      ...options,
    };
    this.detector = new RediSearchDetector(redis);
    this.indexName = `embed_search_${this.options.organizationId}`;
    this.keyPrefix = `embed:${this.options.organizationId}:doc:`;
  }

  /**
   * Initialize the index and detect RediSearch capabilities
   */
  async initialize(): Promise<void> {
    this.rediSearchInfo = await this.detector.detectRediSearch();

    if (
      this.rediSearchInfo.available &&
      this.rediSearchInfo.vectorSearchSupported &&
      this.options.useNativeSearch
    ) {
      await this.ensureVectorIndex();
      console.log(
        `RediSearch vector index initialized for organization ${this.options.organizationId}`
      );
    } else {
      console.log(`Using fallback Redis search for organization ${this.options.organizationId}`);
    }
  }

  /**
   * Store a document embedding with optional RediSearch indexing
   */
  async store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const key = `${this.keyPrefix}${documentId}`;
    const timestamp = Date.now();

    try {
      // Prepare document data
      const docData: Record<string, string | number> = {
        documentId,
        content,
        timestamp,
        organizationId: this.options.organizationId,
      };

      if (metadata) {
        docData.metadata = JSON.stringify(metadata);
      }

      // Add embedding for RediSearch
      if (this.canUseNativeSearch()) {
        // Convert embedding to binary format for RediSearch
        const embeddingBuffer = this.serializeEmbedding(embedding);
        docData.embedding = embeddingBuffer.toString('binary');
      }

      // Store in Redis using pipeline for atomicity
      const pipeline = this.redis.pipeline();

      // Store document data
      pipeline.hset(key, docData);

      // Set TTL if configured
      if (this.options.ttlHours) {
        pipeline.expire(key, this.options.ttlHours * 3600);
      }

      // Add to organization document set
      pipeline.sadd(`embed:${this.options.organizationId}:docs`, documentId);

      await pipeline.exec();

      console.log(
        `Stored document ${documentId} with ${this.canUseNativeSearch() ? 'native' : 'fallback'} indexing`
      );
    } catch (error) {
      throw new Error(
        `Failed to store document ${documentId}: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Retrieve a document by ID
   */
  async retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null> {
    try {
      const key = `${this.keyPrefix}${documentId}`;
      const data = await this.redis.hgetall(key);

      if (!data || Object.keys(data).length === 0) {
        return null;
      }

      const result: {
        embedding: number[];
        content: string;
        metadata?: Record<string, any>;
      } = {
        embedding: [],
        content: data.content || '',
      };

      // Deserialize embedding if available
      if (data.embedding) {
        try {
          if (this.canUseNativeSearch()) {
            // Binary format from RediSearch
            const buffer = Buffer.from(data.embedding, 'binary');
            result.embedding = this.deserializeEmbedding(buffer);
          } else {
            // JSON format fallback
            result.embedding = JSON.parse(data.embedding);
          }
        } catch (error) {
          console.warn(`Failed to parse embedding for document ${documentId}:`, error);
          result.embedding = [];
        }
      }

      if (data.metadata) {
        try {
          result.metadata = JSON.parse(data.metadata);
        } catch (error) {
          console.warn(`Failed to parse metadata for document ${documentId}:`, error);
        }
      }

      return result;
    } catch (error) {
      console.error(`Failed to retrieve document ${documentId}:`, error);
      return null;
    }
  }

  /**
   * Search for similar documents using native RediSearch or fallback
   */
  async searchSimilar(
    queryEmbedding: number[],
    topK: number = 3,
    threshold: number = 0.0,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    try {
      if (this.canUseNativeSearch()) {
        return await this.nativeVectorSearch(queryEmbedding, topK, threshold, filters);
      } else {
        return await this.fallbackSearch(queryEmbedding, topK, threshold, filters);
      }
    } catch (error) {
      console.error('Search failed, falling back to brute force:', error);
      return await this.fallbackSearch(queryEmbedding, topK, threshold, filters);
    }
  }

  /**
   * Remove a document from the index
   */
  async remove(documentId: string): Promise<boolean> {
    try {
      const key = `${this.keyPrefix}${documentId}`;

      const pipeline = this.redis.pipeline();
      pipeline.del(key);
      pipeline.srem(`embed:${this.options.organizationId}:docs`, documentId);

      const results = await pipeline.exec();
      const deleted = results && results[0] && results[0][1] > 0;

      if (deleted) {
        console.log(`Removed document ${documentId} from index`);
      }

      return !!deleted;
    } catch (error) {
      console.error(`Failed to remove document ${documentId}:`, error);
      return false;
    }
  }

  /**
   * Clear all documents for the organization
   */
  async clearOrganization(): Promise<void> {
    try {
      // Get all document IDs
      const documentIds = await this.redis.smembers(`embed:${this.options.organizationId}:docs`);

      if (documentIds.length === 0) {
        return;
      }

      // Remove all documents in batch
      const pipeline = this.redis.pipeline();

      for (const documentId of documentIds) {
        pipeline.del(`${this.keyPrefix}${documentId}`);
      }

      // Clear the document set
      pipeline.del(`embed:${this.options.organizationId}:docs`);

      await pipeline.exec();

      console.log(
        `Cleared ${documentIds.length} documents for organization ${this.options.organizationId}`
      );
    } catch (error) {
      console.error(`Failed to clear organization ${this.options.organizationId}:`, error);
      throw error;
    }
  }

  /**
   * Get index statistics
   */
  async getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }> {
    try {
      const documentCount = await this.redis.scard(`embed:${this.options.organizationId}:docs`);

      return {
        totalDocuments: documentCount,
        organizationCounts: {
          [this.options.organizationId]: documentCount,
        },
      };
    } catch (error) {
      console.error('Failed to get stats:', error);
      return {
        totalDocuments: 0,
        organizationCounts: {},
      };
    }
  }

  /**
   * Batch store documents for efficient indexing
   */
  async batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    if (documents.length === 0) {
      return;
    }

    try {
      const pipeline = this.redis.pipeline();
      const timestamp = Date.now();

      for (const doc of documents) {
        const key = `${this.keyPrefix}${doc.documentId}`;

        const docData: Record<string, string | number> = {
          documentId: doc.documentId,
          content: doc.content,
          timestamp,
          organizationId: this.options.organizationId,
        };

        if (doc.metadata) {
          docData.metadata = JSON.stringify(doc.metadata);
        }

        // Add embedding for RediSearch
        if (this.canUseNativeSearch()) {
          const embeddingBuffer = this.serializeEmbedding(doc.embedding);
          docData.embedding = embeddingBuffer.toString('binary');
        }

        pipeline.hset(key, docData);

        // Set TTL if configured
        if (this.options.ttlHours) {
          pipeline.expire(key, this.options.ttlHours * 3600);
        }

        // Add to organization document set
        pipeline.sadd(`embed:${this.options.organizationId}:docs`, doc.documentId);
      }

      await pipeline.exec();

      console.log(
        `Batch stored ${documents.length} documents with ${this.canUseNativeSearch() ? 'native' : 'fallback'} indexing`
      );
    } catch (error) {
      throw new Error(
        `Failed to batch store documents: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Health check for the index
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Test Redis connectivity
      const pingResult = await this.redis.ping();
      return pingResult === 'PONG';
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Check if a document exists in the index
   */
  async hasDocument(documentId: string): Promise<boolean> {
    const key = `${this.keyPrefix}${documentId}`;
    const exists = await this.redis.hget(key, 'content');
    return exists !== null;
  }

  /**
   * Get all indexed document IDs for the organization
   */
  async getIndexedDocumentIds(): Promise<string[]> {
    return await this.redis.smembers(`embed:${this.options.organizationId}:docs`);
  }

  /**
   * Check if native search can be used
   */
  private canUseNativeSearch(): boolean {
    return !!(
      this.options.useNativeSearch &&
      this.rediSearchInfo?.available &&
      this.rediSearchInfo?.vectorSearchSupported
    );
  }

  /**
   * Ensure vector index exists for RediSearch
   */
  private async ensureVectorIndex(): Promise<void> {
    try {
      const indexExists = await this.detector.indexExists(this.indexName);

      if (!indexExists) {
        const config: VectorIndexConfig = {
          organizationId: this.options.organizationId,
          vectorDimensions: this.options.vectorDimensions,
          distanceMetric: this.options.distanceMetric || 'COSINE',
          indexName: this.indexName,
          keyPrefix: this.keyPrefix,
        };

        await this.detector.createVectorIndex(config);
      }
    } catch (error) {
      console.error('Failed to ensure vector index:', error);
      // Don't throw here - fall back to non-native search
    }
  }

  /**
   * Perform native vector search using RediSearch
   */
  private async nativeVectorSearch(
    queryEmbedding: number[],
    topK: number,
    threshold: number,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    try {
      const searchFilters = {
        organizationId: this.options.organizationId,
        ...filters,
      };

      const results = await this.detector.vectorSearch(
        this.indexName,
        queryEmbedding,
        topK,
        searchFilters
      );

      // Filter by threshold
      return results.filter(result => result.similarity >= threshold);
    } catch (error) {
      console.error('Native vector search failed:', error);
      throw error;
    }
  }

  /**
   * Fallback search using brute force similarity
   */
  private async fallbackSearch(
    _queryEmbedding: number[],
    _topK: number,
    _threshold: number,
    _filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    try {
      // Get all document IDs for this organization
      const documentIds = await this.redis.smembers(`embed:${this.options.organizationId}:docs`);

      if (documentIds.length === 0) {
        return [];
      }

      const results: Array<{
        documentId: string;
        similarity: number;
        content: string;
        metadata?: Record<string, any>;
      }> = [];

      // Since we don't store embeddings in fallback mode, we can't do similarity search
      // This would require the embeddings to be stored separately or computed on demand
      console.warn('Fallback search not fully implemented - requires embedding storage strategy');

      return results;
    } catch (error) {
      console.error('Fallback search failed:', error);
      return [];
    }
  }

  /**
   * Serialize embedding to binary format for RediSearch
   */
  private serializeEmbedding(embedding: number[]): Buffer {
    const buffer = Buffer.allocUnsafe(embedding.length * 4);
    for (let i = 0; i < embedding.length; i++) {
      buffer.writeFloatLE(embedding[i], i * 4);
    }
    return buffer;
  }

  /**
   * Deserialize embedding from binary format
   */
  private deserializeEmbedding(buffer: Buffer): number[] {
    const embedding: number[] = [];
    for (let i = 0; i < buffer.length; i += 4) {
      embedding.push(buffer.readFloatLE(i));
    }
    return embedding;
  }
}
