import type { EmbeddingRedisClient as RedisClient } from './shared-types';

export interface RediSearchInfo {
  available: boolean;
  version?: string;
  vectorSearchSupported: boolean;
  maxVectorDimensions?: number;
}

export interface VectorIndexConfig {
  organizationId: string;
  vectorDimensions: number;
  distanceMetric: 'COSINE' | 'L2' | 'IP';
  indexName: string;
  keyPrefix: string;
}

export interface VectorIndexInfo {
  name: string;
  keyPrefix: string;
  vectorDimensions: number;
  distanceMetric: string;
  documentCount: number;
  created: Date;
}

/**
 * Service for detecting RediSearch availability and managing vector indexes
 */
export class RediSearchDetector {
  private redis: RedisClient;
  private cachedInfo?: RediSearchInfo;
  private cacheExpiry?: number;

  constructor(redis: RedisClient) {
    this.redis = redis;
  }

  /**
   * Detect if RediSearch module is available and supports vector search
   */
  async detectRediSearch(): Promise<RediSearchInfo> {
    // Return cached result if still valid (cache for 5 minutes)
    if (this.cachedInfo && this.cacheExpiry && Date.now() < this.cacheExpiry) {
      return this.cachedInfo;
    }

    try {
      // Try to get module list
      const modules = await this.getModuleList();
      const searchModule = modules.find(m => m.name.toLowerCase().includes('search'));

      if (!searchModule) {
        this.cachedInfo = {
          available: false,
          vectorSearchSupported: false,
        };
      } else {
        // Check if vector search is supported by trying FT.INFO on a non-existent index
        const vectorSupported = await this.checkVectorSearchSupport();

        this.cachedInfo = {
          available: true,
          version: searchModule.version,
          vectorSearchSupported: vectorSupported,
          maxVectorDimensions: vectorSupported ? 32768 : undefined, // RediSearch limit
        };
      }

      this.cacheExpiry = Date.now() + 5 * 60 * 1000; // 5 minutes
      return this.cachedInfo;
    } catch (error) {
      console.error('Failed to detect RediSearch:', error);
      return {
        available: false,
        vectorSearchSupported: false,
      };
    }
  }

  /**
   * Create a vector index for an organization
   */
  async createVectorIndex(config: VectorIndexConfig): Promise<void> {
    const rediSearchInfo = await this.detectRediSearch();

    if (!rediSearchInfo.available || !rediSearchInfo.vectorSearchSupported) {
      throw new Error('RediSearch with vector support is not available');
    }

    if (config.vectorDimensions > (rediSearchInfo.maxVectorDimensions || 32768)) {
      throw new Error(
        `Vector dimensions ${config.vectorDimensions} exceed maximum ${rediSearchInfo.maxVectorDimensions}`
      );
    }

    try {
      // Check if index already exists
      const indexExists = await this.indexExists(config.indexName);
      if (indexExists) {
        console.log(`Vector index ${config.indexName} already exists`);
        return;
      }

      // Create vector index using FT.CREATE command
      const command = [
        'FT.CREATE',
        config.indexName,
        'ON',
        'HASH',
        'PREFIX',
        '1',
        config.keyPrefix,
        'SCHEMA',
        'documentId',
        'TEXT',
        'content',
        'TEXT',
        'metadata',
        'TEXT',
        'timestamp',
        'NUMERIC',
        'organizationId',
        'TAG',
        'embedding',
        'VECTOR',
        'HNSW',
        '6',
        'TYPE',
        'FLOAT32',
        'DIM',
        config.vectorDimensions.toString(),
        'DISTANCE_METRIC',
        config.distanceMetric,
      ];

      await this.executeRediSearchCommand(command);
      console.log(
        `Created vector index ${config.indexName} for organization ${config.organizationId}`
      );
    } catch (error) {
      throw new Error(
        `Failed to create vector index: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Delete a vector index
   */
  async deleteVectorIndex(indexName: string): Promise<void> {
    try {
      await this.executeRediSearchCommand(['FT.DROPINDEX', indexName]);
      console.log(`Deleted vector index ${indexName}`);
    } catch (error) {
      // Index might not exist, which is fine
      console.warn(`Failed to delete vector index ${indexName}:`, error);
    }
  }

  /**
   * Get information about existing vector indexes
   */
  async getVectorIndexes(): Promise<VectorIndexInfo[]> {
    try {
      const indexList = await this.executeRediSearchCommand(['FT._LIST']);
      const indexes: VectorIndexInfo[] = [];

      for (const indexName of indexList) {
        try {
          const indexInfo = await this.executeRediSearchCommand(['FT.INFO', indexName]);
          const info = this.parseIndexInfo(indexInfo, indexName);
          if (info) {
            indexes.push(info);
          }
        } catch (error) {
          console.warn(`Failed to get info for index ${indexName}:`, error);
        }
      }

      return indexes;
    } catch (error) {
      console.error('Failed to get vector indexes:', error);
      return [];
    }
  }

  /**
   * Check if a specific index exists
   */
  async indexExists(indexName: string): Promise<boolean> {
    try {
      await this.executeRediSearchCommand(['FT.INFO', indexName]);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Perform vector search using RediSearch
   */
  async vectorSearch(
    indexName: string,
    queryVector: number[],
    topK: number = 3,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    try {
      // Build query with vector search and optional filters
      let query = `*=>[KNN ${topK} @embedding $BLOB AS distance]`;

      if (filters) {
        const filterClauses = Object.entries(filters).map(([key, value]) => {
          if (key === 'organizationId') {
            return `@organizationId:{${value}}`;
          }
          return `@${key}:${value}`;
        });

        if (filterClauses.length > 0) {
          query = `(${filterClauses.join(' ')}) => [KNN ${topK} @embedding $BLOB AS distance]`;
        }
      }

      // Convert vector to binary format
      const vectorBuffer = Buffer.allocUnsafe(queryVector.length * 4);
      for (let i = 0; i < queryVector.length; i++) {
        vectorBuffer.writeFloatLE(queryVector[i], i * 4);
      }

      const searchCommand = [
        'FT.SEARCH',
        indexName,
        query,
        'PARAMS',
        '2',
        'BLOB',
        vectorBuffer.toString('binary'),
        'SORTBY',
        'distance',
        'RETURN',
        '5',
        'documentId',
        'content',
        'metadata',
        'distance',
      ];

      const results = await this.executeRediSearchCommand(searchCommand);
      return this.parseSearchResults(results);
    } catch (error) {
      console.error('Vector search failed:', error);
      return [];
    }
  }

  /**
   * Get RediSearch module information
   */
  private async getModuleList(): Promise<Array<{ name: string; version: string }>> {
    try {
      const modules = await this.executeRediSearchCommand(['MODULE', 'LIST']);
      const result: Array<{ name: string; version: string }> = [];

      // MODULE LIST returns: [name1, [field1, value1, field2, value2], name2, ...]
      for (let i = 0; i < modules.length; i += 2) {
        const name = modules[i];
        const fields = modules[i + 1] || [];
        let version = 'unknown';

        // Find version in fields array
        for (let j = 0; j < fields.length; j += 2) {
          if (fields[j] === 'ver') {
            version = fields[j + 1];
            break;
          }
        }

        result.push({ name, version });
      }

      return result;
    } catch (error) {
      console.error('Failed to get module list:', error);
      return [];
    }
  }

  /**
   * Check if vector search is supported
   */
  private async checkVectorSearchSupport(): Promise<boolean> {
    try {
      // Try to create a temporary vector field in a schema to test support
      await this.executeRediSearchCommand([
        'FT.CREATE',
        'test_vector_support',
        'ON',
        'HASH',
        'PREFIX',
        '1',
        'test:vector:',
        'SCHEMA',
        'test_vector',
        'VECTOR',
        'HNSW',
        '6',
        'TYPE',
        'FLOAT32',
        'DIM',
        '128',
        'DISTANCE_METRIC',
        'COSINE',
      ]);

      // Clean up test index
      await this.executeRediSearchCommand(['FT.DROPINDEX', 'test_vector_support']);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Execute a RediSearch command with proper error handling
   */
  private async executeRediSearchCommand(command: string[]): Promise<any> {
    try {
      // Note: This assumes the Redis client has a generic command execution method
      // In practice, this might need to be adapted based on the actual Redis client interface
      if ('sendCommand' in this.redis) {
        return await (this.redis as any).sendCommand(command);
      } else {
        throw new Error('Redis client does not support raw command execution');
      }
    } catch (error) {
      console.error(`RediSearch command failed: ${command.join(' ')}`, error);
      throw error;
    }
  }

  /**
   * Parse index information from FT.INFO response
   */
  private parseIndexInfo(info: any[], indexName: string): VectorIndexInfo | null {
    try {
      const result: Partial<VectorIndexInfo> = {
        name: indexName,
        created: new Date(),
      };

      // Parse the info array (key-value pairs)
      for (let i = 0; i < info.length; i += 2) {
        const key = info[i];
        const value = info[i + 1];

        switch (key) {
          case 'index_definition':
            if (Array.isArray(value)) {
              for (let j = 0; j < value.length; j += 2) {
                if (value[j] === 'prefixes' && Array.isArray(value[j + 1])) {
                  result.keyPrefix = value[j + 1][0];
                }
              }
            }
            break;
          case 'num_docs':
            result.documentCount = parseInt(value);
            break;
          case 'attributes':
            if (Array.isArray(value)) {
              // Find vector field info
              for (const attr of value) {
                if (Array.isArray(attr) && attr.includes('VECTOR')) {
                  const dimIndex = attr.indexOf('DIM');
                  const metricIndex = attr.indexOf('DISTANCE_METRIC');

                  if (dimIndex >= 0 && dimIndex + 1 < attr.length) {
                    result.vectorDimensions = parseInt(attr[dimIndex + 1]);
                  }
                  if (metricIndex >= 0 && metricIndex + 1 < attr.length) {
                    result.distanceMetric = attr[metricIndex + 1];
                  }
                }
              }
            }
            break;
        }
      }

      // Only return if we have essential information
      if (result.keyPrefix && result.vectorDimensions && result.documentCount !== undefined) {
        return result as VectorIndexInfo;
      }

      return null;
    } catch (error) {
      console.error('Failed to parse index info:', error);
      return null;
    }
  }

  /**
   * Parse search results from FT.SEARCH response
   */
  private parseSearchResults(results: any[]): Array<{
    documentId: string;
    similarity: number;
    content: string;
    metadata?: Record<string, any>;
  }> {
    try {
      if (!Array.isArray(results) || results.length < 1) {
        return [];
      }

      const searchResults: Array<{
        documentId: string;
        similarity: number;
        content: string;
        metadata?: Record<string, any>;
      }> = [];

      // Results format: [count, doc1_key, [field1, value1, field2, value2], doc2_key, ...]
      for (let i = 1; i < results.length; i += 2) {
        const fields = results[i + 1] || [];

        let documentId = '';
        let content = '';
        let metadata: Record<string, any> | undefined;
        let distance = 1.0;

        // Parse fields
        for (let j = 0; j < fields.length; j += 2) {
          const fieldName = fields[j];
          const fieldValue = fields[j + 1];

          switch (fieldName) {
            case 'documentId':
              documentId = fieldValue;
              break;
            case 'content':
              content = fieldValue;
              break;
            case 'metadata':
              try {
                metadata = JSON.parse(fieldValue);
              } catch {
                metadata = { raw: fieldValue };
              }
              break;
            case 'distance':
              distance = parseFloat(fieldValue);
              break;
          }
        }

        if (documentId && content) {
          searchResults.push({
            documentId,
            content,
            metadata,
            similarity: Math.max(0, 1 - distance), // Convert distance to similarity
          });
        }
      }

      return searchResults;
    } catch (error) {
      console.error('Failed to parse search results:', error);
      return [];
    }
  }
}
