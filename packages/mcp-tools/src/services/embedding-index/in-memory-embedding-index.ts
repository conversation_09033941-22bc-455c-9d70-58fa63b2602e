import { IEmbeddingIndex, EmbeddingIndexOptions } from './index';

interface StoredDocument {
  documentId: string;
  embedding: number[];
  content: string;
  metadata?: Record<string, any>;
  timestamp: number;
  organizationId: string;
}

/**
 * In-memory embedding index implementation that serves as a fallback
 * when Redis is not available. This wraps the current map-based approach.
 */
export class InMemoryEmbeddingIndex implements IEmbeddingIndex {
  private documents = new Map<string, StoredDocument>();
  private options: EmbeddingIndexOptions;

  constructor(options: EmbeddingIndexOptions) {
    this.options = options;
  }

  /**
   * Store a document embedding in memory
   */
  async store(
    documentId: string,
    embedding: number[],
    content: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const document: StoredDocument = {
      documentId,
      embedding,
      content,
      metadata,
      timestamp: Date.now(),
      organizationId: this.options.organizationId,
    };

    this.documents.set(documentId, document);
  }

  /**
   * Retrieve a document embedding from memory
   */
  async retrieve(documentId: string): Promise<{
    embedding: number[];
    content: string;
    metadata?: Record<string, any>;
  } | null> {
    const document = this.documents.get(documentId);

    if (!document) {
      return null;
    }

    // Check TTL if specified
    if (this.options.ttlHours) {
      const ageHours = (Date.now() - document.timestamp) / (1000 * 60 * 60);
      if (ageHours > this.options.ttlHours) {
        this.documents.delete(documentId);
        return null;
      }
    }

    return {
      embedding: document.embedding,
      content: document.content,
      metadata: document.metadata,
    };
  }

  /**
   * Search for similar documents using brute-force cosine similarity
   */
  async searchSimilar(
    queryEmbedding: number[],
    topK: number = 3,
    threshold: number = 0.0,
    filters?: Record<string, any>
  ): Promise<
    Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }>
  > {
    const results: Array<{
      documentId: string;
      similarity: number;
      content: string;
      metadata?: Record<string, any>;
    }> = [];

    for (const [documentId, document] of this.documents.entries()) {
      // Check TTL if specified
      if (this.options.ttlHours) {
        const ageHours = (Date.now() - document.timestamp) / (1000 * 60 * 60);
        if (ageHours > this.options.ttlHours) {
          this.documents.delete(documentId);
          continue;
        }
      }

      // Filter by organization
      if (document.organizationId !== this.options.organizationId) {
        continue;
      }

      // Apply filters if provided
      if (filters && !this.matchesFilters(document, filters)) {
        continue;
      }

      const similarity = this.calculateCosineSimilarity(queryEmbedding, document.embedding);

      if (similarity >= threshold) {
        results.push({
          documentId: document.documentId,
          similarity,
          content: document.content,
          metadata: document.metadata,
        });
      }
    }

    // Sort by similarity and return top-K
    return results.sort((a, b) => b.similarity - a.similarity).slice(0, topK);
  }

  /**
   * Remove a document from the index
   */
  async remove(documentId: string): Promise<boolean> {
    return this.documents.delete(documentId);
  }

  /**
   * Clear all documents for an organization
   */
  async clearOrganization(organizationId: string): Promise<void> {
    const keysToDelete: string[] = [];

    for (const [documentId, document] of this.documents.entries()) {
      if (document.organizationId === organizationId) {
        keysToDelete.push(documentId);
      }
    }

    for (const key of keysToDelete) {
      this.documents.delete(key);
    }
  }

  /**
   * Get index statistics
   */
  async getStats(): Promise<{
    totalDocuments: number;
    organizationCounts: Record<string, number>;
    memoryUsage?: number;
  }> {
    const organizationCounts: Record<string, number> = {};
    let totalDocuments = 0;

    for (const document of this.documents.values()) {
      totalDocuments++;
      organizationCounts[document.organizationId] =
        (organizationCounts[document.organizationId] || 0) + 1;
    }

    // Estimate memory usage (rough calculation)
    const memoryUsage = this.documents.size * 1024; // Rough estimate in bytes

    return {
      totalDocuments,
      organizationCounts,
      memoryUsage,
    };
  }

  /**
   * Batch store multiple documents
   */
  async batchStore(
    documents: Array<{
      documentId: string;
      embedding: number[];
      content: string;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    const timestamp = Date.now();

    for (const doc of documents) {
      const storedDoc: StoredDocument = {
        documentId: doc.documentId,
        embedding: doc.embedding,
        content: doc.content,
        metadata: doc.metadata,
        timestamp,
        organizationId: this.options.organizationId,
      };

      this.documents.set(doc.documentId, storedDoc);
    }
  }

  /**
   * Health check for in-memory index (always healthy)
   */
  async healthCheck(): Promise<boolean> {
    return true;
  }

  /**
   * Check if a document exists in the index
   */
  async hasDocument(documentId: string): Promise<boolean> {
    return this.documents.has(documentId);
  }

  /**
   * Get all indexed document IDs for the organization
   */
  async getIndexedDocumentIds(): Promise<string[]> {
    const ids: string[] = [];
    for (const [id, doc] of this.documents.entries()) {
      if (doc.organizationId === this.options.organizationId) {
        ids.push(id);
      }
    }
    return ids;
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  /**
   * Check if document matches the provided filters
   */
  private matchesFilters(document: StoredDocument, filters: Record<string, any>): boolean {
    if (!document.metadata) {
      return Object.keys(filters).length === 0;
    }

    return Object.entries(filters).every(([key, value]) => {
      return document.metadata![key] === value;
    });
  }

  /**
   * Clear expired documents based on TTL
   */
  private cleanupExpired(): void {
    if (!this.options.ttlHours) {
      return;
    }

    const now = Date.now();
    const maxAge = this.options.ttlHours * 60 * 60 * 1000;

    for (const [documentId, document] of this.documents.entries()) {
      if (now - document.timestamp > maxAge) {
        this.documents.delete(documentId);
      }
    }
  }

  /**
   * Periodic cleanup of expired documents
   */
  startPeriodicCleanup(): void {
    if (!this.options.ttlHours) {
      return;
    }

    // Run cleanup every hour
    setInterval(
      () => {
        this.cleanupExpired();
      },
      60 * 60 * 1000
    );
  }
}
