import { ProcessedDocument } from './document-processor';

const CACHE_VERSION = 2; // Increment to invalidate all caches

export interface CachedData {
  version: number;
  documents: ProcessedDocument[];
}

export interface DocumentMetadata {
  id: string;
  updatedAt: string;
}

export interface CacheReconciliationResult {
  docsToKeep: ProcessedDocument[];
  idsToFetch: string[];
}

export interface DocumentCacheResult {
  documents: ProcessedDocument[];
  source: 'memory' | 'redis' | 'database';
  cacheStats: {
    docsFromMemory: number;
    docsFromRedis: number;
    docsFromDatabase: number;
  };
}

export interface DocumentCacheOptions {
  redisKeyPrefix?: string;
  ttlSeconds?: number;
  cacheVersion?: number;
}

export interface DocumentFetcher {
  fetchDocumentIds(orgId: string, userId?: string, traceId?: string): Promise<DocumentMetadata[]>;
  fetchDocumentsByIds(
    orgId: string,
    userId: string | undefined,
    ids: string[],
    traceId?: string
  ): Promise<ProcessedDocument[]>;
}

export interface RedisClient {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, mode: string, ttl: number): Promise<any>;
  del(key: string): Promise<number>;
}

/**
 * Generic document cache service that can be used across different applications.
 * Provides in-memory and Redis caching with intelligent cache reconciliation.
 */
export class DocumentCacheService {
  private readonly REDIS_KEY_PREFIX: string;
  private readonly TTL_SECONDS: number;
  private readonly CACHE_VERSION: number;
  private memoryCache = new Map<string, CachedData>();
  private redisClient?: RedisClient;
  private documentFetcher: DocumentFetcher;
  private logger?: any;

  constructor(
    documentFetcher: DocumentFetcher,
    redisClient?: RedisClient,
    options: DocumentCacheOptions = {}
  ) {
    this.documentFetcher = documentFetcher;
    this.redisClient = redisClient;
    this.REDIS_KEY_PREFIX = options.redisKeyPrefix || 'doc_cache:';
    this.TTL_SECONDS = options.ttlSeconds || 604800; // 7 days default (168 hours)
    this.CACHE_VERSION = options.cacheVersion || CACHE_VERSION;
  }

  /**
   * Set logger for this service
   */
  setLogger(logger: any): void {
    this.logger = logger;
  }

  /**
   * Get documents for an organization with intelligent caching
   */
  async getDocuments(
    orgId: string,
    userId?: string,
    traceId?: string
  ): Promise<DocumentCacheResult> {
    this.logger?.info('[DocumentCacheService] getDocuments called', { orgId, userId, traceId });

    // 1. In-memory cache with database check
    this.logger?.info('[DocumentCacheService] Checking memory cache');
    const memCache = this.memoryCache.get(orgId);
    if (memCache && memCache.version === this.CACHE_VERSION) {
      this.logger?.info(
        '[DocumentCacheService] Memory cache hit, checking database for new documents'
      );
      return this.handleMemoryCacheHit(memCache, orgId, userId, traceId);
    }
    this.logger?.info('[DocumentCacheService] Memory cache miss or version mismatch');

    // 2. Redis cache
    this.logger?.info('[DocumentCacheService] Checking Redis cache');
    const redisCache = await this.loadFromRedis(orgId);
    this.logger?.info('[DocumentCacheService] Redis cache result', {
      hit: !!redisCache,
    });

    // 3. Fetch all doc IDs and timestamps from DB
    this.logger?.info('[DocumentCacheService] About to call documentFetcher.fetchDocumentIds');
    let dbDocsMeta: DocumentMetadata[];
    try {
      dbDocsMeta = await this.documentFetcher.fetchDocumentIds(orgId, userId, traceId);
      this.logger?.info('[DocumentCacheService] fetchDocumentIds returned', {
        documentCount: dbDocsMeta.length,
      });
    } catch (error) {
      this.logger?.error('[DocumentCacheService] fetchDocumentIds failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }

    const { docsToKeep, idsToFetch } = this.reconcileCache(redisCache?.documents ?? [], dbDocsMeta);
    this.logger?.info('[DocumentCacheService] Cache reconciliation', {
      docsToKeep: docsToKeep.length,
      idsToFetch: idsToFetch.length,
    });

    let newDocs: ProcessedDocument[] = [];
    if (idsToFetch.length > 0) {
      this.logger?.info(
        '[DocumentCacheService] About to call documentFetcher.fetchDocumentsByIds',
        {
          documentCount: idsToFetch.length,
        }
      );
      newDocs = await this.documentFetcher.fetchDocumentsByIds(orgId, userId, idsToFetch, traceId);
      this.logger?.info('[DocumentCacheService] fetchDocumentsByIds returned', {
        documentCount: newDocs.length,
      });
    }

    const finalDocs = [...docsToKeep, ...newDocs];
    this.logger?.info('[DocumentCacheService] Final documents count', {
      documentCount: finalDocs.length,
    });

    const cacheData: CachedData = { version: this.CACHE_VERSION, documents: finalDocs };

    await this.saveToRedis(orgId, cacheData);
    this.memoryCache.set(orgId, cacheData);

    // Determine source based on what we actually fetched
    let source: 'memory' | 'redis' | 'database' = 'database';
    if (redisCache && docsToKeep.length > 0 && newDocs.length === 0) {
      source = 'redis';
    } else if (newDocs.length > 0) {
      source = 'database';
    }

    return {
      documents: finalDocs,
      source,
      cacheStats: {
        docsFromMemory: 0,
        docsFromRedis: docsToKeep.length,
        docsFromDatabase: newDocs.length,
      },
    };
  }

  /**
   * Handle memory cache hit with database check for new documents
   */
  private async handleMemoryCacheHit(
    memCache: CachedData,
    orgId: string,
    userId?: string,
    traceId?: string
  ): Promise<DocumentCacheResult> {
    this.logger?.info(
      '[DocumentCacheService] Memory cache hit, checking database for new documents'
    );

    // CRITICAL: Always check database for new documents
    let dbDocsMeta: DocumentMetadata[];
    try {
      dbDocsMeta = await this.documentFetcher.fetchDocumentIds(orgId, userId, traceId);
      this.logger?.info('[DocumentCacheService] fetchDocumentIds returned', {
        documentCount: dbDocsMeta.length,
      });
    } catch (error) {
      this.logger?.error('[DocumentCacheService] fetchDocumentIds failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }

    const { docsToKeep, idsToFetch } = this.reconcileCache(memCache.documents, dbDocsMeta);
    this.logger?.info('[DocumentCacheService] Cache reconciliation', {
      docsToKeep: docsToKeep.length,
      idsToFetch: idsToFetch.length,
    });

    if (idsToFetch.length === 0) {
      // No new documents, return from memory cache
      this.logger?.info(
        '[DocumentCacheService] No new documents detected, returning from memory cache',
        {
          documentCount: memCache.documents.length,
        }
      );
      return {
        documents: memCache.documents,
        source: 'memory',
        cacheStats: {
          docsFromMemory: memCache.documents.length,
          docsFromRedis: 0,
          docsFromDatabase: 0,
        },
      };
    } else {
      // New documents detected, fetch and update cache
      this.logger?.info('[DocumentCacheService] New documents detected, fetching from database', {
        documentCount: idsToFetch.length,
      });

      let newDocs: ProcessedDocument[] = [];
      try {
        newDocs = await this.documentFetcher.fetchDocumentsByIds(
          orgId,
          userId,
          idsToFetch,
          traceId
        );
        this.logger?.info('[DocumentCacheService] fetchDocumentsByIds returned', {
          documentCount: newDocs.length,
        });
      } catch (error) {
        this.logger?.error('[DocumentCacheService] fetchDocumentsByIds failed', {
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }

      const finalDocs = [...docsToKeep, ...newDocs];
      this.logger?.info('[DocumentCacheService] Final documents count after update', {
        documentCount: finalDocs.length,
      });

      // Update cache
      const cacheData: CachedData = { version: this.CACHE_VERSION, documents: finalDocs };
      await this.saveToRedis(orgId, cacheData);
      this.memoryCache.set(orgId, cacheData);

      return {
        documents: finalDocs,
        source: 'database', // Trigger re-indexing
        cacheStats: {
          docsFromMemory: 0,
          docsFromRedis: docsToKeep.length,
          docsFromDatabase: newDocs.length,
        },
      };
    }
  }

  /**
   * Clear cache for a specific organization
   */
  clearCache(orgId: string): void {
    this.memoryCache.delete(orgId);
    if (this.redisClient) {
      const key = `${this.REDIS_KEY_PREFIX}${orgId}`;
      this.redisClient.del?.(key).catch(error => {
        console.warn('[DocumentCacheService] Failed to clear Redis cache:', error);
      });
    }
  }

  /**
   * Clear all caches
   */
  clearAllCaches(): void {
    this.memoryCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    memoryCacheSize: number;
    memoryCacheKeys: string[];
    redisAvailable: boolean;
  } {
    return {
      memoryCacheSize: this.memoryCache.size,
      memoryCacheKeys: Array.from(this.memoryCache.keys()),
      redisAvailable: !!this.redisClient,
    };
  }

  /**
   * Reconcile cached documents with database metadata
   */
  private reconcileCache(
    cachedDocs: ProcessedDocument[],
    dbMeta: DocumentMetadata[]
  ): CacheReconciliationResult {
    const dbMetaMap = new Map(dbMeta.map(d => [d.id, d.updatedAt]));
    const cachedDocsMap = new Map(cachedDocs.map(d => [d.id, d]));
    const docsToKeep: ProcessedDocument[] = [];
    const idsToFetch: string[] = [];

    // Identify documents to keep or refetch
    for (const [id, dbTimestamp] of dbMetaMap.entries()) {
      const cachedDoc = cachedDocsMap.get(id);
      if (cachedDoc && cachedDoc.updatedAt && dbTimestamp) {
        // Doc exists in cache, check if it's up-to-date
        const cacheTimestamp = new Date(cachedDoc.updatedAt).getTime();
        const dbTimestampMs = new Date(dbTimestamp).getTime();
        if (cacheTimestamp >= dbTimestampMs) {
          docsToKeep.push(cachedDoc);
        } else {
          idsToFetch.push(id); // Stale, needs refetching
        }
      } else {
        idsToFetch.push(id); // New document or missing timestamps
      }
    }

    return { docsToKeep, idsToFetch };
  }

  /**
   * Load cache data from Redis
   */
  private async loadFromRedis(orgId: string): Promise<CachedData | undefined> {
    if (!this.redisClient) return undefined;
    try {
      const key = `${this.REDIS_KEY_PREFIX}${orgId}`;
      const data = await this.redisClient.get(key);
      if (!data) return undefined;
      const cache: CachedData = JSON.parse(data);
      if (cache.version !== this.CACHE_VERSION) {
        this.logger?.warn('[DocumentCacheService] Cache version mismatch, invalidating.', {
          orgId,
        });
        return undefined;
      }
      return Array.isArray(cache.documents) ? cache : undefined;
    } catch (error) {
      this.logger?.warn('[DocumentCacheService] Failed to load cache from Redis:', error);
      return undefined;
    }
  }

  /**
   * Save cache data to Redis
   */
  private async saveToRedis(orgId: string, data: CachedData): Promise<void> {
    if (!this.redisClient) return;
    try {
      const key = `${this.REDIS_KEY_PREFIX}${orgId}`;
      await this.redisClient.set(key, JSON.stringify(data), 'EX', this.TTL_SECONDS);
    } catch (error) {
      this.logger?.warn('[DocumentCacheService] Failed to save cache to Redis:', error);
    }
  }
}
