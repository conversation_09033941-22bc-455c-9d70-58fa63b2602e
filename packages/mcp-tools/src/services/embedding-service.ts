import { OpenAIEmbeddings } from '@langchain/openai';
import { ProcessedDocument } from './document-processor';

export interface EmbeddingCache {
  embeddings: Map<string, number[]>; // documentId -> embedding vector
  timestamp: Date;
  organizationId: string;
}

export interface EmbeddingOptions {
  model?: string;
  dimensions?: number;
  organizationId?: string;
  useCache?: boolean;
}

/**
 * Service wrapper around LangChain's {@link OpenAIEmbeddings} that provides
 * organisation-aware embedding generation with a simple in-memory caching layer and
 * basic analytics. The class is **stateful** – each instance keeps track of invocation counts and
 * an LRU-style cache keyed by `organizationId` to minimise token usage.
 *
 * Usage example:
 * ```ts
 * const service = new EmbeddingService({ organizationId: 'acme' });
 * const vector = await service.embedText("Hello world");
 * ```
 */
export class EmbeddingService {
  private embeddings: OpenAIEmbeddings;
  private embeddingCache: Map<string, EmbeddingCache> = new Map();
  private readonly CACHE_TTL_MS = 30 * 60 * 1000; // 30 minutes
  private embeddingCallCount = 0;
  private organizationId: string;

  /**
   * Create a new `EmbeddingService`.
   *
   * @param options – Optional {@link EmbeddingOptions} to control model selection, vector
   *                  dimensions, organisation scoping and cache behaviour.
   *
   * *Side-effects*: Initialises an underlying {@link OpenAIEmbeddings} client which may perform
   * network I/O during token validation.
   */
  constructor(options: EmbeddingOptions = {}) {
    this.organizationId = options.organizationId || 'default';

    // Initialize OpenAI embeddings with environment configuration
    this.embeddings = new OpenAIEmbeddings({
      model: options.model || process.env.LANGCHAIN_EMBEDDINGS || 'text-embedding-3-large',
      dimensions: options.dimensions || 1536,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Generate an embedding for a **single** UTF-8 string.
   *
   * @param text – Non-empty string to embed.
   * @returns Numerical vector (float array).
   * @throws Error If `text` is empty or the OpenAI request fails.
   *
   * @example
   * ```ts
   * const vec = await service.embedText("secure enclave");
   * ```
   */
  async embedText(text: string): Promise<number[]> {
    if (!text?.trim()) {
      throw new Error('Text cannot be empty');
    }

    // Safety guard: avoid exceeding model context window by trimming overly long inputs
    // Approximate token count using same heuristic as chunker (1 token ≈ 4 chars)
    const estimatedTokens = Math.ceil(text.length * 0.25);
    const SAFETY_TOKEN_LIMIT = 7500; // keep under 8192 model max for text-embedding-3-large
    if (estimatedTokens > SAFETY_TOKEN_LIMIT) {
      // Reuse existing helper to create a safe-sized text with title weighting and truncation
      // As we don't have a document name here, pass a neutral title
      text = this.prepareTextForEmbedding(text, 'Document');
    }

    const cacheKey = this.generateCacheKey(text);
    const cached = this.getCachedEmbedding(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      this.embeddingCallCount++;
      const embedding = await this.embeddings.embedQuery(text);
      this.setCachedEmbedding(cacheKey, embedding);
      return embedding;
    } catch (error) {
      throw new Error(
        `Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Batch embed multiple strings using the more economical `embedDocuments` endpoint provided by
   * LangChain/OpenAI.
   *
   * @param texts – Array of strings. Falsy / whitespace-only entries are filtered out.
   * @returns Array of vectors in the **same order** as the filtered `texts` input.
   * @throws Error On upstream API failure.
   */
  async embedTexts(texts: string[]): Promise<number[][]> {
    if (!texts?.length) {
      return [];
    }

    const validTexts = texts.filter(text => text?.trim());
    if (validTexts.length === 0) {
      return [];
    }

    try {
      this.embeddingCallCount += validTexts.length;
      return await this.embeddings.embedDocuments(validTexts);
    } catch (error) {
      throw new Error(
        `Failed to generate batch embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Convenience helper that processes an array of {@link ProcessedDocument}s one-by-one. It is
   * intentionally implemented serially to keep memory footprint low and to allow per-document
   * error logging.
   *
   * @param documents – Pre-sanitised documents with `content` and unique `id`.
   * @returns Array pairing each `documentId` with its embedding; documents with empty content are
   *          skipped.
   */
  async embedDocuments(
    documents: ProcessedDocument[]
  ): Promise<Array<{ documentId: string; embedding: number[] }>> {
    const results: Array<{ documentId: string; embedding: number[] }> = [];

    for (const doc of documents) {
      if (!doc.content?.trim()) {
        continue;
      }

      try {
        const embedding = await this.embedText(doc.content);
        results.push({
          documentId: doc.id,
          embedding,
        });
      } catch (error) {
        console.warn(`Failed to embed document ${doc.id}:`, error);
      }
    }

    return results;
  }

  /**
   * Generate embeddings for an array of text strings
   * (Compatibility method from agents' embedding service)
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    return this.embedTexts(texts);
  }

  /**
   * Generate embedding for a single query
   * (Compatibility method from agents' embedding service)
   */
  async generateQueryEmbedding(query: string): Promise<number[]> {
    return this.embedText(query);
  }

  /**
   * Get or generate embeddings for documents with caching
   * (Compatibility method from agents' embedding service)
   */
  async getDocumentEmbeddings(documents: ProcessedDocument[]): Promise<Map<string, number[]>> {
    const embeddingMap = new Map<string, number[]>();

    // Get cached embeddings
    const cachedEmbeddings = this.getCachedEmbeddings(this.organizationId);

    // Identify documents needing new embeddings
    const docsNeedingEmbeddings: ProcessedDocument[] = [];

    for (const doc of documents) {
      const cachedEmbedding = cachedEmbeddings?.get(doc.id);
      if (cachedEmbedding) {
        embeddingMap.set(doc.id, cachedEmbedding);
      } else {
        docsNeedingEmbeddings.push(doc);
      }
    }

    // Generate embeddings for new documents
    if (docsNeedingEmbeddings.length > 0) {
      try {
        const textsToEmbed = docsNeedingEmbeddings.map(doc =>
          this.prepareTextForEmbedding(doc.content, doc.name)
        );

        const newEmbeddings = await this.embedTexts(textsToEmbed);

        // Map new embeddings to document IDs
        const newEmbeddingMap = new Map<string, number[]>();
        docsNeedingEmbeddings.forEach((doc, index) => {
          if (newEmbeddings[index]) {
            embeddingMap.set(doc.id, newEmbeddings[index]);
            newEmbeddingMap.set(doc.id, newEmbeddings[index]);
          }
        });

        // Update cache with new embeddings
        this.updateEmbeddingCache(this.organizationId, newEmbeddingMap);
      } catch (error) {
        console.error('Failed to generate document embeddings:', error);
        // Continue with only cached embeddings if available
      }
    }

    return embeddingMap;
  }

  /**
   * Calculate cosine similarity between two vectors
   * (Compatibility method from agents' embedding service)
   */
  cosineSimilarity(a: number[], b: number[]): number {
    return this.calculateSimilarity(a, b);
  }

  /**
   * Prepare document text for embedding by combining content with metadata
   * (Compatibility method from agents' embedding service)
   */
  prepareTextForEmbedding(content: string, name: string): string {
    // Combine document name and content for better semantic understanding
    const titleWeight = `Title: ${name}\n\n`;
    const maxContentLength = 8000; // Leave room for title and stay under token limits

    let processedContent = content;
    if (content.length > maxContentLength) {
      // Take first part and last part to preserve context
      const halfLength = Math.floor(maxContentLength / 2);
      processedContent =
        content.substring(0, halfLength) +
        '\n... [content truncated] ...\n' +
        content.substring(content.length - halfLength);
    }

    return titleWeight + processedContent;
  }

  /**
   * Compute **cosine similarity** between two embeddings.
   *
   * @param embedding1 – First vector.
   * @param embedding2 – Second vector (must be same dimensionality).
   * @returns Value in range 0-1 where 1 denotes identical direction.
   * @throws Error If vector dimensions mismatch.
   */
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embedding dimensions must match');
    }

    const dotProduct = embedding1.reduce((sum, a, i) => sum + a * embedding2[i], 0);
    const magnitude1 = Math.sqrt(embedding1.reduce((sum, a) => sum + a * a, 0));
    const magnitude2 = Math.sqrt(embedding2.reduce((sum, a) => sum + a * a, 0));

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * Return the *top-K* most similar candidates to a given query embedding.
   *
   * @param queryEmbedding – The reference vector.
   * @param candidateEmbeddings – Array of `{ id, embedding }` pairs.
   * @param topK – Number of results to return (default 5).
   * @returns Sorted array of `{ id, similarity }`.
   */
  findMostSimilar(
    queryEmbedding: number[],
    candidateEmbeddings: Array<{ id: string; embedding: number[] }>,
    topK: number = 3
  ): Array<{ id: string; similarity: number }> {
    const similarities = candidateEmbeddings.map(candidate => ({
      id: candidate.id,
      similarity: this.calculateSimilarity(queryEmbedding, candidate.embedding),
    }));

    return similarities.sort((a, b) => b.similarity - a.similarity).slice(0, topK);
  }

  /**
   * Inspect runtime stats: embedding API call count, cache size and active organisation.
   */
  getStats() {
    return {
      embeddingCallCount: this.embeddingCallCount,
      cacheSize: this.embeddingCache.size,
      organizationId: this.organizationId,
    };
  }

  /**
   * Get current embedding call count for metrics
   * (Compatibility method from agents' embedding service)
   */
  getEmbeddingCallCount(): number {
    return this.embeddingCallCount;
  }

  /**
   * Reset embedding call count (useful for testing)
   * (Compatibility method from agents' embedding service)
   */
  resetEmbeddingCallCount(): void {
    this.embeddingCallCount = 0;
  }

  /**
   * Purge cache entries older than the configured TTL (30 minutes by default).
   * Safe to call periodically (e.g., via `setInterval`).
   */
  clearExpiredCache(): void {
    const now = new Date();
    for (const [key, cache] of this.embeddingCache.entries()) {
      if (now.getTime() - cache.timestamp.getTime() > this.CACHE_TTL_MS) {
        this.embeddingCache.delete(key);
      }
    }
  }

  /**
   * Remove **all** cached embeddings – either globally or scoped to a single organisation.
   *
   * @param organizationId – Optional tenant identifier. When omitted the entire cache is flushed.
   */
  clearCache(organizationId?: string): void {
    if (organizationId) {
      for (const [key, cache] of this.embeddingCache.entries()) {
        if (cache.organizationId === organizationId) {
          this.embeddingCache.delete(key);
        }
      }
    } else {
      this.embeddingCache.clear();
    }
  }

  /**
   * Clean up expired embedding caches
   * (Compatibility method from agents' embedding service)
   */
  cleanupExpiredCaches(): void {
    this.clearExpiredCache();
  }

  /* ------------------------------ PRIVATE HELPERS ------------------------------ */

  /**
   * Create a deterministic cache key unique to the organisation namespace.
   */
  private generateCacheKey(text: string): string {
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `${this.organizationId}:${hash}`;
  }

  /**
   * Retrieve an embedding from the local cache if available and not expired.
   */
  private getCachedEmbedding(cacheKey: string): number[] | null {
    const cached = this.embeddingCache.get(cacheKey);
    if (!cached) {
      return null;
    }

    // Check if cache is expired
    const now = new Date();
    if (now.getTime() - cached.timestamp.getTime() > this.CACHE_TTL_MS) {
      this.embeddingCache.delete(cacheKey);
      return null;
    }

    return cached.embeddings.get(cacheKey) || null;
  }

  /**
   * Store (or update) an embedding in the cache with the current timestamp.
   */
  private setCachedEmbedding(cacheKey: string, embedding: number[]): void {
    const cache = this.embeddingCache.get(cacheKey) || {
      embeddings: new Map(),
      timestamp: new Date(),
      organizationId: this.organizationId,
    };

    cache.embeddings.set(cacheKey, embedding);
    cache.timestamp = new Date();
    this.embeddingCache.set(cacheKey, cache);
  }

  /**
   * Get cached embeddings for an organization
   * (Compatibility method from agents' embedding service)
   */
  private getCachedEmbeddings(organizationId: string): Map<string, number[]> | null {
    const cached = this.embeddingCache.get(organizationId);

    if (!cached) {
      return null;
    }

    // Check if cache is expired
    const isExpired = Date.now() - cached.timestamp.getTime() > this.CACHE_TTL_MS;
    if (isExpired) {
      this.embeddingCache.delete(organizationId);
      return null;
    }

    return cached.embeddings;
  }

  /**
   * Update embedding cache with new embeddings
   * (Compatibility method from agents' embedding service)
   */
  private updateEmbeddingCache(organizationId: string, newEmbeddings: Map<string, number[]>): void {
    let cache = this.embeddingCache.get(organizationId);

    if (!cache) {
      // Create new cache
      cache = {
        embeddings: new Map(),
        timestamp: new Date(),
        organizationId,
      };
      this.embeddingCache.set(organizationId, cache);
    }

    // Add new embeddings to existing cache
    for (const [docId, embedding] of newEmbeddings) {
      cache.embeddings.set(docId, embedding);
    }

    // Update timestamp
    cache.timestamp = new Date();

    // Cleanup if cache gets too large
    if (this.embeddingCache.size > 10) {
      this.cleanupExpiredCaches();
    }
  }
}
