import { sql } from 'drizzle-orm';
import { MCPToolsDependencies, ToolCategory } from '../types';
import { MCPSession } from '../types/session';
import { AsyncTaskManager } from '../task-manager';
import { QueryValidator } from '../services/query-validator';
import { DocumentProcessor } from '../services/document-processor';
import * as schemas from '../schemas';

// Export new MCP architecture
export * from '../core';
export * from './database';

// Tool input types
interface QueryDatabaseInput {
  query: string;
}

interface GetContextInput {
  key?: string;
}

interface SetContextInput {
  key: string;
  data: any;
}

interface AnalyzeDataInput {
  query: string;
  analysis_type: 'security' | 'performance' | 'trends';
  options?: {
    time_range?: string;
    filters?: Record<string, any>;
  };
}

interface SystemStatusInput {
  component?: 'all' | 'redis' | 'database' | 'sessions';
}

interface GetTaskStatusInput {
  task_id: string;
}

interface CancelTaskInput {
  task_id: string;
}

interface ListTasksInput {
  status?: 'all' | 'active' | 'completed' | 'failed';
  limit?: number;
}

interface GetDocumentInput {
  file_id: string;
}

interface GetAllDocumentsInput {
  limit?: number;
  offset?: number;
  includeFullContent?: boolean;
}

export class MCPTools {
  private taskManager: AsyncTaskManager;
  private documentProcessor: DocumentProcessor;

  /**
   * Instantiate a new `MCPTools` utility wrapper.
   *
   * This constructor wires together helper services that are reused by the
   * individual tool methods:
   *
   * • `AsyncTaskManager` – orchestrates long-running, cancellable operations and
   *   collects basic task-level telemetry.
   * • `DocumentProcessor` – transforms raw database rows into the public API
   *   document representation.
   *
   * No network or database calls are executed at construction time, making the
   * instantiation side-effect free except for the allocation of in-memory
   * helpers.
   *
   * @param deps - Injectable runtime dependencies such as database adapters,
   *               cache clients, and an optional logger implementation.
   */
  constructor(private deps: MCPToolsDependencies) {
    this.taskManager = new AsyncTaskManager(deps.cache, deps.logger);
    this.documentProcessor = new DocumentProcessor();
  }

  /**
   * Execute a **read-only** SQL query scoped to the current organization.
   *
   * The method first validates the incoming SQL text with
   * {@link QueryValidator.validateQuery} to ensure mutating statements are not
   * executed. It then runs the query against a tenant-isolated connection and
   * returns the result set as a prettified JSON string.
   *
   * @param session - The active {@link MCPSession} carrying tenant context.
   * @param input - Contains the raw SQL `query` string to execute.
   * @returns A JSON string with the shape `{ success, rowCount, data }`.
   * @throws {Error} If validation fails or the database rejects the query.
   *
   * @example
   * ```ts
   * const resultJson = await tools.queryDatabase(session, {
   *   query: 'SELECT id, name FROM file LIMIT 10;',
   * });
   * console.log(JSON.parse(resultJson));
   * ```
   */
  async queryDatabase(session: MCPSession, input: QueryDatabaseInput): Promise<string> {
    const { query } = input;

    // Validate query safety
    QueryValidator.validateQuery(query);

    try {
      // Use existing database infrastructure with tenant isolation
      const db = await this.deps.database.withTenant(session.organization_id);

      // Execute the query with tenant isolation
      const results = await db.execute(sql.raw(query));

      return JSON.stringify(
        {
          success: true,
          rowCount: results.rows.length,
          data: results.rows,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
      this.deps.logger?.error(error as Error, `Database query failed for session ${session.id}`);
      throw new Error(`Database query failed: ${errorMessage}`);
    }
  }

  /**
   * Retrieve contextual data stored for the provided session.
   *
   * When the optional `key` is omitted the entire context namespace is
   * returned. Values are deserialized from Redis *as-is* and therefore may be
   * of any JSON-serialisable shape.
   *
   * @param session - Current {@link MCPSession} identifier.
   * @param input - Optional `key` to narrow the lookup.
   * @returns A JSON string describing the retrieved context map.
   * @throws {Error} When Redis operations fail.
   */
  async getContext(session: MCPSession, input: GetContextInput): Promise<string> {
    const { key } = input;

    try {
      let context;
      if (key) {
        const contextKey = `mcp:context:${session.id}:${key}`;
        const data = await this.deps.cache.get(contextKey);
        context = data ? JSON.parse(data) : null;
      } else {
        // Get all context for session
        const pattern = `mcp:context:${session.id}:*`;
        const keys = await this.deps.cache.keys(pattern);
        context = {} as Record<string, any>;

        for (const redisKey of keys) {
          const data = await this.deps.cache.get(redisKey);
          const contextKey = redisKey.split(':').pop();
          if (contextKey && data) {
            context[contextKey] = JSON.parse(data);
          }
        }
      }

      return JSON.stringify(
        {
          success: true,
          key: key || 'all',
          data: context,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown context error';
      this.deps.logger?.error(
        error as Error,
        `Failed to retrieve context for session ${session.id}`
      );
      throw new Error(`Failed to retrieve context: ${errorMessage}`);
    }
  }

  /**
   * Persist an arbitrary JSON value in the session context namespace.
   *
   * The key/value pair is stored in Redis with a configurable TTL (defaults to
   * one hour via the `MCP_CONTEXT_TTL` environment variable).
   *
   * @param session - The caller's {@link MCPSession}.
   * @param input.key - Context entry identifier.
   * @param input.data - Serializable data to cache.
   * @returns A confirmation JSON string containing the expiry information.
   * @throws {Error} When Redis storage fails.
   */
  async setContext(session: MCPSession, input: SetContextInput): Promise<string> {
    const { key, data } = input;

    try {
      const contextKey = `mcp:context:${session.id}:${key}`;
      const ttl = parseInt(process.env.MCP_CONTEXT_TTL || '3600'); // 1 hour default
      await this.deps.cache.setex(contextKey, ttl, JSON.stringify(data));

      this.deps.logger?.debug(`Set context ${key} for session ${session.id}`);
      return JSON.stringify(
        {
          success: true,
          message: `Context '${key}' updated successfully`,
          key,
          expires_in: `${ttl} seconds`,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown context error';
      this.deps.logger?.error(error as Error, `Failed to set context for session ${session.id}`);
      throw new Error(`Failed to set context: ${errorMessage}`);
    }
  }

  /**
   * Kick-off an asynchronous data-analysis pipeline.
   *
   * A new task is registered in {@link AsyncTaskManager} and processed on the
   * event loop without blocking the caller. Progress can be inspected through
   * {@link getTaskStatus} and cancelled via {@link cancelTask}.
   *
   * @param session - Current {@link MCPSession}.
   * @param input.query - The domain-specific query text to analyse.
   * @param input.analysis_type - Desired analysis flavour (`security`,
   *        `performance`, or `trends`).
   * @param input.options - Additional time-range and filter directives.
   * @returns A JSON string with the newly created `task_id`.
   * @throws {Error} When task creation fails.
   */
  async analyzeData(session: MCPSession, input: AnalyzeDataInput): Promise<string> {
    const { query, analysis_type, options = {} } = input;

    // Validate query safety
    QueryValidator.validateQuery(query);

    try {
      // Create async task using task manager
      const taskId = await this.taskManager.createTask(session.id, 'analyze_data', {
        query,
        analysis_type,
        options,
      });

      // Start async processing
      this.processAnalysisAsync(session, taskId, query, analysis_type, options);

      return JSON.stringify(
        {
          success: true,
          task_id: taskId,
          status: 'processing',
          message: 'Analysis started. Use get_task_status to check progress.',
          estimated_completion: new Date(Date.now() + 30000).toISOString(), // 30 seconds
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown analysis error';
      this.deps.logger?.error(error as Error, `Data analysis failed for session ${session.id}`);
      throw new Error(`Data analysis failed: ${errorMessage}`);
    }
  }

  /**
   * Gather a holistic health snapshot of internal sub-systems.
   *
   * Depending on the requested `component`, the method performs lightweight
   * health probes (e.g. `PING` for Redis, `SELECT 1` for Postgres) and returns
   * timing information useful for operational dashboards.
   *
   * @param session - Current {@link MCPSession} (used for namespace metrics).
   * @param input.component - Limits the check to a single subsystem.
   * @returns A JSON string with the aggregated health report.
   * @throws {Error} If a probe fails unexpectedly.
   */
  async systemStatus(session: MCPSession, input: SystemStatusInput): Promise<string> {
    const { component = 'all' } = input;

    try {
      const status: Record<string, any> = {
        timestamp: new Date().toISOString(),
        session_id: session.id,
        requested_component: component,
      };

      if (component === 'all' || component === 'redis') {
        try {
          const pingResult = await this.deps.cache.ping();
          const keyCount = await this.deps.cache.dbsize();
          const info = await this.deps.cache.info('memory');

          status.redis = {
            connected: pingResult === 'PONG',
            key_count: keyCount,
            memory_info: this.parseRedisInfo(info),
            response_time: Date.now(),
          };
        } catch (error) {
          status.redis = {
            connected: false,
            error: error instanceof Error ? error.message : 'Unknown Redis error',
          };
        }
      }

      if (component === 'all' || component === 'database') {
        try {
          const startTime = Date.now();
          const db = await this.deps.database.withTenant(session.organization_id);
          await db.execute(sql.raw('SELECT 1'));

          status.database = {
            connected: true,
            response_time: Date.now() - startTime,
            organization_id: session.organization_id,
          };
        } catch (error) {
          status.database = {
            connected: false,
            error: error instanceof Error ? error.message : 'Unknown database error',
          };
        }
      }

      if (component === 'all' || component === 'sessions') {
        const sessionKeys = await this.deps.cache.keys('mcp:session:*');
        const contextKeys = await this.deps.cache.keys(`mcp:context:${session.id}:*`);

        status.sessions = {
          active_sessions: sessionKeys.length,
          current_session_contexts: contextKeys.length,
          session_id: session.id,
        };
      }

      return JSON.stringify(
        {
          success: true,
          system_status: status,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown system error';
      this.deps.logger?.error(
        error as Error,
        `System status check failed for session ${session.id}`
      );
      throw new Error(`System status check failed: ${errorMessage}`);
    }
  }

  /**
   * Inspect the current status of a previously launched async task.
   *
   * @param session - The session that owns the task.
   * @param input.task_id - Identifier returned by {@link analyzeData} (or any
   *        other async tool).
   * @returns JSON string detailing progress, current step, and cancelability.
   * @throws {Error} When the task lookup fails.
   */
  async getTaskStatus(session: MCPSession, input: GetTaskStatusInput): Promise<string> {
    const { task_id } = input;

    try {
      const taskStatus = await this.taskManager.getTaskStatus(session.id, task_id);

      if (!taskStatus) {
        return JSON.stringify(
          {
            success: false,
            error: `Task ${task_id} not found`,
          },
          null,
          2
        );
      }

      return JSON.stringify(
        {
          success: true,
          task: taskStatus.task,
          can_cancel: taskStatus.can_cancel,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.deps.logger?.error(
        error as Error,
        `Failed to get task status for session ${session.id}`
      );
      throw new Error(`Failed to get task status: ${errorMessage}`);
    }
  }

  /**
   * Attempt to cancel a running asynchronous task.
   *
   * Cancellation is *best-effort*: a task that has already completed will not
   * be affected.
   *
   * @param session - Owner of the task.
   * @param input.task_id - Identifier of the task to cancel.
   * @returns Confirmation JSON including `success` flag and message.
   * @throws {Error} When task cancellation fails ‑ for instance due to storage
   *         outages.
   */
  async cancelTask(session: MCPSession, input: CancelTaskInput): Promise<string> {
    const { task_id } = input;

    try {
      const cancelled = await this.taskManager.cancelTask(session.id, task_id);

      if (!cancelled) {
        return JSON.stringify(
          {
            success: false,
            message: `Task ${task_id} not found or cannot be cancelled (already completed/failed)`,
          },
          null,
          2
        );
      }

      return JSON.stringify(
        {
          success: true,
          message: `Task ${task_id} cancelled successfully`,
          task_id,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.deps.logger?.error(error as Error, `Failed to cancel task for session ${session.id}`);
      throw new Error(`Failed to cancel task: ${errorMessage}`);
    }
  }

  /**
   * List tasks belonging to the current session with optional status filters.
   *
   * @param session - Active {@link MCPSession}.
   * @param input.status - Filter (`all`, `active`, `completed`, `failed`).
   * @param input.limit - Maximum number of records to return.
   * @returns JSON array of task metadata accompanied by aggregate metrics.
   * @throws {Error} On backend retrieval problems.
   */
  async listTasks(session: MCPSession, input: ListTasksInput): Promise<string> {
    const { status = 'all', limit = 50 } = input;

    try {
      let tasks = await this.taskManager.listTasksForSession(session.id);

      // Filter by status if specified
      if (status !== 'all') {
        if (status === 'active') {
          tasks = tasks.filter(task => ['pending', 'processing'].includes(task.status));
        } else {
          tasks = tasks.filter(task => task.status === status);
        }
      }

      // Apply limit
      tasks = tasks.slice(0, limit);

      // Get task metrics for summary
      const metrics = await this.taskManager.getTaskMetrics();

      return JSON.stringify(
        {
          success: true,
          tasks,
          summary: {
            total_returned: tasks.length,
            filters_applied: { status, limit },
            session_metrics: {
              total_tasks: metrics.total_tasks,
              active_tasks: metrics.active_tasks,
              completed_tasks: metrics.completed_tasks,
              failed_tasks: metrics.failed_tasks,
            },
          },
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.deps.logger?.error(error as Error, `Failed to list tasks for session ${session.id}`);
      throw new Error(`Failed to list tasks: ${errorMessage}`);
    }
  }

  /**
   * Fetch a single document by its file identifier.
   *
   * The raw database row is transformed with {@link DocumentProcessor} into a
   * schema suitable for public consumption (e.g. binary buffers are converted
   * into base-64 strings, text content is normalised, etc.).
   *
   * @param session - The tenant-scoped session.
   * @param input.file_id - Database primary key of the file.
   * @returns JSON string with the processed document object.
   * @throws {Error} When the document cannot be found or a DB error occurs.
   */
  async getDocument(session: MCPSession, input: GetDocumentInput): Promise<string> {
    const { file_id } = input;

    try {
      const db = await this.deps.database.withTenant(session.organization_id);

      const query = sql`
        SELECT id, name, document_type, created_at, updated_at, 
               category_id, content, buffer_file, access_level, file_type
        FROM file 
        WHERE id = ${file_id} AND organization_id = ${session.organization_id}
      `;

      const result = await db.execute(query);

      if (result.rows.length === 0) {
        return JSON.stringify(
          {
            success: false,
            error: 'Document not found',
            file_id,
          },
          null,
          2
        );
      }

      const document = await this.documentProcessor.processDocumentForAPI(result.rows[0]);

      this.deps.logger?.info(`Document retrieved: ${file_id} for session ${session.id}`);

      return JSON.stringify(
        {
          success: true,
          document,
        },
        null,
        2
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
      this.deps.logger?.error(
        error as Error,
        `Failed to retrieve document ${file_id} for session ${session.id}`
      );
      throw new Error(`Failed to retrieve document: ${errorMessage}`);
    }
  }

  /**
   * Return a paginated collection of documents for the organisation.
   *
   * When `includeFullContent` is `false`, the heavy `fullContent` field is
   * stripped to keep payload sizes small for list-style views.
   *
   * Side-effects: extensive debug-level logging to aid troubleshooting in
   * lower environments.
   *
   * @param session - Caller session.
   * @param input.limit - Page size (defaults to 50).
   * @param input.offset - Pagination offset.
   * @param input.includeFullContent - Include full text/binary payloads.
   * @returns JSON string that includes `documents` and `pagination` metadata.
   * @throws {Error} When database access or document processing fails.
   */
  async getAllDocuments(session: MCPSession, input: GetAllDocumentsInput): Promise<string> {
    const { limit = 50, offset = 0, includeFullContent = true } = input;
    console.log(
      `[DEBUG] getAllDocuments: Called with limit=${limit}, offset=${offset}, includeFullContent=${includeFullContent} for org=${session.organization_id}`
    );

    try {
      console.log(
        `[DEBUG] getAllDocuments: Attempting to get database connection for tenant ${session.organization_id}`
      );
      const db = await this.deps.database.withTenant(session.organization_id);
      console.log(`[DEBUG] getAllDocuments: Database connection result:`, {
        hasDb: !!db,
        dbType: typeof db,
        dbConstructorName: db?.constructor?.name,
        hasExecuteMethod: !!db?.execute,
      });

      // Get total count first
      console.log(
        `[DEBUG] getAllDocuments: Executing count query for org: ${session.organization_id}`
      );
      const countQuery = sql`
        SELECT COUNT(*) as total 
        FROM file 
        WHERE organization_id = ${session.organization_id}
      `;

      console.log(`[DEBUG] getAllDocuments: About to execute count query`);
      const countResult = await db.execute(countQuery);
      console.log(`[DEBUG] getAllDocuments: Count query result:`, {
        hasResult: !!countResult,
        hasRows: !!countResult?.rows,
        rowsLength: countResult?.rows?.length,
        firstRow: countResult?.rows?.[0],
      });

      const totalCount = countResult.rows[0]?.total || 0;
      console.log(`[DEBUG] getAllDocuments: Found ${totalCount} total documents in database`);

      // Get documents with pagination
      console.log(
        `[DEBUG] getAllDocuments: Executing documents query with limit=${limit}, offset=${offset}`
      );
      const documentsQuery = sql`
        SELECT id, name, document_type, created_at, updated_at, 
               category_id, content, buffer_file, access_level, file_type
        FROM file 
        WHERE organization_id = ${session.organization_id}
        AND content IS NOT NULL
        ORDER BY created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      console.log(`[DEBUG] getAllDocuments: About to execute documents query: ${documentsQuery}`);
      const result = await db.execute(documentsQuery);
      console.log(`[DEBUG] getAllDocuments: Documents query result:`, {
        hasResult: !!result,
        hasRows: !!result?.rows,
        rowsLength: result?.rows?.length,
        resultType: typeof result,
      });
      console.log(`[DEBUG] getAllDocuments: Query returned ${result.rows.length} raw rows`);

      // Log first 100 chars of each raw row before processing
      // result.rows.forEach((row: any, index: number) => {
      //   const content = row.content || '';
      //   const bufferFile = row.buffer_file || Buffer.from([]);
      //   console.log(
      //     `[DEBUG] getAllDocuments: Raw Row ${index + 1} - ID: ${row.id}, Name: ${row.name}, FileType: ${row.file_type}`
      //   );
      //   console.log(
      //     `[DEBUG] getAllDocuments: Raw Row ${index + 1} - Content preview: "${String(content).substring(0, 100)}${String(content).length > 100 ? '...' : ''}"`
      //   );
      //   console.log(
      //     `[DEBUG] getAllDocuments: Raw Row ${index + 1} - Buffer file preview: "${String(bufferFile).substring(0, 100)}${String(bufferFile).length > 100 ? '...' : ''}"`
      //   );
      // });

      const documents = await Promise.all(
        result.rows.map(async (row: Record<string, any>) => {
          const processedDoc = await this.documentProcessor.processDocumentForAPI(row);
          // For list endpoint, exclude fullContent to avoid huge payloads (unless explicitly requested)
          return includeFullContent ? processedDoc : this.createListSafeDocument(processedDoc);
        })
      );

      console.log(
        `[DEBUG] getAllDocuments: After processing, returning ${documents.length} documents`
      );

      // Log first 100 chars of each processed document
      documents.forEach((doc: any, index: number) => {
        const preview = doc.contentPreview || '';
        console.log(
          `[DEBUG] getAllDocuments: Processed Doc ${index + 1} - ID: ${doc.id}, Name: ${doc.name}`
        );
        console.log(
          `[DEBUG] getAllDocuments: Processed Doc ${index + 1} - Content preview: "${String(preview).substring(0, 100)}${String(preview).length > 100 ? '...' : ''}"`
        );
      });

      this.deps.logger?.info(
        `Retrieved ${documents.length} documents for session ${session.id} (limit: ${limit}, offset: ${offset})`
      );

      const responseData = {
        success: true,
        documents,
        pagination: {
          total: totalCount,
          limit,
          offset,
          returned: documents.length,
          hasMore: offset + documents.length < totalCount,
        },
      };

      console.log(
        `[DEBUG] getAllDocuments: Returning response with success=true, ${documents.length} documents`
      );
      return JSON.stringify(responseData, null, 3);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
      console.error(`[DEBUG] getAllDocuments: Error occurred:`, error);
      console.error(
        `[DEBUG] getAllDocuments: Error stack:`,
        error instanceof Error ? error.stack : 'No stack trace'
      );
      this.deps.logger?.error(
        error as Error,
        `Failed to retrieve documents for session ${session.id}`
      );
      throw new Error(`Failed to retrieve documents: ${errorMessage}`);
    }
  }

  /**
   * Internal helper that drives the mock analysis pipeline in a separate tick.
   *
   * The method updates task progress in five discrete steps and respects
   * cancellation requests.
   *
   * @param session - Task owner.
   * @param taskId - Identifier managed by {@link AsyncTaskManager}.
   * @param query - Original analysis query.
   * @param analysisType - One of the supported analysis flavours.
   * @param _options - Currently unused arbitrary options object.
   * @throws {Error} Never re-throws – errors are trapped and marked as `failed`
   *         on the task entity itself.
   */
  private async processAnalysisAsync(
    session: MCPSession,
    taskId: string,
    query: string,
    analysisType: string,
    _options: any
  ): Promise<void> {
    try {
      // Mark task as processing
      await this.taskManager.updateTask(session.id, taskId, {
        status: 'processing',
        current_step: 'Starting analysis...',
      });

      // Simulate processing steps
      const steps = [
        { progress: 10, status: 'Validating query...' },
        { progress: 30, status: 'Fetching data...' },
        { progress: 60, status: 'Performing analysis...' },
        { progress: 90, status: 'Generating insights...' },
        { progress: 100, status: 'Complete' },
      ];

      for (const step of steps) {
        // Check if task was cancelled
        const task = await this.taskManager.getTask(session.id, taskId);
        if (task?.status === 'cancelled') {
          return; // Exit early if cancelled
        }

        // Update progress
        const updates: any = {
          progress: step.progress,
          current_step: step.status,
        };

        if (step.progress === 100) {
          updates.status = 'completed';
          updates.result = this.generateMockAnalysisResults(query, analysisType);
        }

        await this.taskManager.updateTask(session.id, taskId, updates);

        // Simulate processing time (except for the last step)
        if (step.progress < 100) {
          await new Promise(resolve => setTimeout(resolve, 6000)); // 6 seconds per step
        }
      }
    } catch (error) {
      // Mark as failed
      await this.taskManager.updateTask(session.id, taskId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * Generate pseudo-random analysis results for demonstration purposes only.
   *
   * @param query - Original query string.
   * @param analysisType - Analysis flavour requested.
   * @returns An object whose shape depends on `analysisType`.
   */
  private generateMockAnalysisResults(query: string, analysisType: string): any {
    const baseResults = {
      query_analyzed: query,
      analysis_type: analysisType,
      execution_time: '30.2 seconds',
      data_points_analyzed: Math.floor(Math.random() * 10000) + 1000,
    };

    switch (analysisType) {
      case 'security':
        return {
          ...baseResults,
          security_insights: {
            threat_level: 'low',
            anomalies_detected: Math.floor(Math.random() * 5),
            recommendations: [
              'Enable additional monitoring for unusual access patterns',
              'Consider implementing rate limiting for sensitive operations',
              'Review user permissions for elevated access',
            ],
            risk_score: Math.floor(Math.random() * 30) + 10, // 10-40
          },
        };

      case 'performance':
        return {
          ...baseResults,
          performance_insights: {
            avg_response_time: `${(Math.random() * 500 + 100).toFixed(2)}ms`,
            throughput: `${Math.floor(Math.random() * 1000 + 500)} requests/min`,
            bottlenecks: ['Database query optimization needed', 'Consider index optimization'],
            efficiency_score: Math.floor(Math.random() * 30) + 70, // 70-100
          },
        };

      case 'trends':
        return {
          ...baseResults,
          trend_insights: {
            growth_rate: `${(Math.random() * 20 + 5).toFixed(1)}% monthly`,
            seasonal_patterns: 'Higher activity during business hours',
            predictions: {
              next_month: `${Math.floor(Math.random() * 20 + 80)}% increase expected`,
              peak_times: ['9:00-11:00 AM', '2:00-4:00 PM'],
            },
            trend_score: Math.floor(Math.random() * 40) + 60, // 60-100
          },
        };

      default:
        return baseResults;
    }
  }

  /**
   * Parse the output of the `INFO MEMORY` Redis command into a key/value map.
   *
   * @param info - Raw multiline string as returned by Redis.
   * @returns A map where each memory metric is addressed by its original key.
   */
  private parseRedisInfo(info: string): Record<string, string> {
    const parsed: Record<string, string> = {};
    const lines = info.split('\r\n');

    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        parsed[key] = value;
      }
    }

    return parsed;
  }

  /**
   * Create a lightweight document representation by omitting the `fullContent`
   * field.
   *
   * @param processedDoc - Document object produced by
   *        {@link DocumentProcessor}.
   * @returns A shallow copy **without** the heavy `fullContent` property.
   */
  private createListSafeDocument(processedDoc: Record<string, any>): Record<string, any> {
    // Create a copy without fullContent for list endpoints to avoid huge payloads
    const { fullContent, ...listSafeDoc } = processedDoc;
    return listSafeDoc;
  }
}

// Enhanced tool definitions with categories and async support
export const TOOL_DEFINITIONS = [
  {
    name: 'query_database',
    description: 'Execute read-only SQL queries on organization data',
    category: ToolCategory.DATABASE,
    inputSchema: schemas.QUERY_DATABASE_SCHEMA,
    async: false,
    estimatedDuration: 1000, // milliseconds
  },
  {
    name: 'get_document',
    description: 'Retrieve a specific document by file ID',
    category: ToolCategory.DATABASE,
    inputSchema: schemas.GET_DOCUMENT_SCHEMA,
    async: false,
    estimatedDuration: 800,
  },
  {
    name: 'get_all_documents',
    description:
      'Retrieve all documents for the organization with optional pagination and full content',
    category: ToolCategory.DATABASE,
    inputSchema: schemas.GET_ALL_DOCUMENTS_SCHEMA,
    async: false,
    estimatedDuration: 1200,
  },
  {
    name: 'get_context',
    description: 'Retrieve session context data',
    category: ToolCategory.CONTEXT,
    inputSchema: schemas.GET_CONTEXT_SCHEMA,
    async: false,
    estimatedDuration: 500,
  },
  {
    name: 'set_context',
    description: 'Store data in session context',
    category: ToolCategory.CONTEXT,
    inputSchema: schemas.SET_CONTEXT_SCHEMA,
    async: false,
    estimatedDuration: 500,
  },
  {
    name: 'analyze_data',
    description: 'Perform advanced data analysis with security, performance, or trend insights',
    category: ToolCategory.ANALYSIS,
    inputSchema: schemas.ANALYZE_DATA_SCHEMA,
    async: true,
    estimatedDuration: 30000, // 30 seconds for analysis
  },
  {
    name: 'system_status',
    description: 'Get comprehensive system status and health information',
    category: ToolCategory.SYSTEM,
    inputSchema: schemas.SYSTEM_STATUS_SCHEMA,
    async: false,
    estimatedDuration: 2000,
  },
  {
    name: 'get_task_status',
    description: 'Check the status and progress of an async task',
    category: ToolCategory.ASYNC,
    inputSchema: schemas.GET_TASK_STATUS_SCHEMA,
    async: false,
    estimatedDuration: 300,
  },
  {
    name: 'cancel_task',
    description: 'Cancel a running async task',
    category: ToolCategory.ASYNC,
    inputSchema: schemas.CANCEL_TASK_SCHEMA,
    async: false,
    estimatedDuration: 500,
  },
  {
    name: 'list_tasks',
    description: 'List async tasks for the current session',
    category: ToolCategory.ASYNC,
    inputSchema: schemas.LIST_TASKS_SCHEMA,
    async: false,
    estimatedDuration: 800,
  },
];

// Tool categories grouping for organized display
export const TOOLS_BY_CATEGORY = TOOL_DEFINITIONS.reduce(
  (acc, tool) => {
    if (!acc[tool.category]) {
      acc[tool.category] = [];
    }
    acc[tool.category].push(tool);
    return acc;
  },
  {} as Record<string, typeof TOOL_DEFINITIONS>
);
