import { MC<PERSON><PERSON><PERSON><PERSON><PERSON>ult, ToolExecutionContext } from '../../core/tool-handler';
import { AbstractDatabaseTool } from './abstract-database-tool';
import { EnhancedDatabaseProvider } from './enhanced-database-provider';
import { DatabaseSchemaAdapter } from './database-schema-adapter';
/**
 * Get All Internal Documents Tool
 * Retrieves documents from the internal database with different schema
 * Following Model Context Protocol specification
 *
 * This tool adapts the internal database schema to match the external database format
 * for backward compatibility with existing agents.
 */
export class GetAllInternalDocumentsTool extends AbstractDatabaseTool {
  private enhancedProvider?: EnhancedDatabaseProvider;

  constructor() {
    super(
      'get_all_internal_documents',
      'Retrieve paginated list of documents from the internal database with different schema',
      {
        type: 'object',
        properties: {
          limit: {
            type: 'number',
            description: 'Maximum number of documents to return',
            default: 50,
            minimum: 1,
            maximum: 200,
          },
          offset: {
            type: 'number',
            description: 'Number of documents to skip for pagination',
            default: 0,
            minimum: 0,
          },
          filter: {
            type: 'object',
            description: 'Optional filters for document search',
            properties: {
              ids: {
                type: 'array',
                items: { type: 'string' },
                description: 'Filter by a list of document IDs',
              },
              documentType: {
                type: 'string',
                description: 'Filter by document type (maps to internal schema)',
              },
              fileType: {
                type: 'string',
                description: 'Filter by file type (maps to internal schema)',
              },
              createdAfter: {
                type: 'string',
                format: 'date-time',
                description: 'Filter documents created after this date',
              },
              createdBefore: {
                type: 'string',
                format: 'date-time',
                description: 'Filter documents created before this date',
              },
              category: {
                type: 'string',
                description: 'Filter by document category (internal schema specific)',
              },
              status: {
                type: 'string',
                enum: ['active', 'archived', 'deleted'],
                description: 'Filter by document status (internal schema specific)',
              },
            },
          },
          includeIdsOnly: {
            type: 'boolean',
            description:
              'If true, returns only the ID and last update timestamp of matching documents',
            default: false,
          },
        },
      }
    );
  }

  // Override the setDatabaseProvider method to handle enhanced provider
  setDatabaseProvider(provider: any): void {
    console.log('[GetAllInternalDocumentsTool] setDatabaseProvider called', {
      hasProvider: !!provider,
      providerType: typeof provider,
      hasExecuteQueryInternal: provider && typeof provider.executeQueryInternal === 'function',
      providerKeys: provider ? Object.keys(provider) : [],
    });

    super.setDatabaseProvider(provider);
    if (provider && typeof provider.executeQueryInternal === 'function') {
      this.enhancedProvider = provider as EnhancedDatabaseProvider;
      console.log('[GetAllInternalDocumentsTool] Enhanced provider set successfully');
    } else {
      console.log(
        '[GetAllInternalDocumentsTool] Provider does not have executeQueryInternal method'
      );
    }
  }

  async execute(args: Record<string, any>, context: ToolExecutionContext): Promise<MCPToolResult> {
    const { limit = 50, offset = 0, filter, includeIdsOnly = false } = args;
    const safeLimit = Math.min(limit, 200);
    const safeOffset = Math.max(offset, 0);

    try {
      // Get organization ID
      const organizationId = await this.getOrganizationId(context);

      // Log the execution parameters
      console.log(`[GetAllInternalDocumentsTool] execute called`, {
        organizationId,
        limit: safeLimit,
        offset: safeOffset,
        includeIdsOnly,
        hasFilter: !!filter,
      });

      // Build filter conditions for internal database schema using adapter
      const filterConditions = DatabaseSchemaAdapter.buildInternalFilterConditions(
        filter,
        organizationId,
        DatabaseSchemaAdapter.escapeSqlString
      );

      console.log(`[GetAllInternalDocumentsTool] Filter conditions built`);

      // Handle the case where only IDs and timestamps are requested
      if (includeIdsOnly) {
        console.log(`[GetAllInternalDocumentsTool] Building ID-only query`);
        const idQuery = DatabaseSchemaAdapter.buildInternalQuery(
          filterConditions,
          safeLimit,
          safeOffset,
          true
        );
        console.log(`[GetAllInternalDocumentsTool] ID query built:`, idQuery);

        console.log(`[GetAllInternalDocumentsTool] About to execute query`);
        const idResult = await this.executeQueryInternal(idQuery, organizationId);
        console.log(`[GetAllInternalDocumentsTool] ID query result:`, {
          rowCount: idResult.rowCount,
          rowsLength: idResult.rows.length,
          resultType: typeof idResult,
          resultKeys: Object.keys(idResult || {}),
        });

        const documents = idResult.rows.map((row: any) => ({
          id: row.id,
          updatedAt: row.updated_at,
        }));

        console.log(`[GetAllInternalDocumentsTool] Returning ID documents:`, {
          documentCount: documents.length,
        });

        return {
          content: [{ type: 'text', text: JSON.stringify({ success: true, documents }) }],
          isError: false,
        };
      }

      // Test with a simple query first
      console.log(`[GetAllInternalDocumentsTool] Testing simple query first`);
      const simpleTestQuery = `SELECT COUNT(*) AS total FROM documents d WHERE d.organization_id = '${organizationId}'`;
      try {
        const simpleResult = await this.executeQueryInternal(simpleTestQuery, organizationId);
        console.log(
          `[GetAllInternalDocumentsTool] Simple query succeeded: ${simpleResult.rows[0]?.total || 0} documents`
        );
      } catch (error) {
        console.error(
          `[GetAllInternalDocumentsTool] Simple query failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
        throw error;
      }

      // Test query to see all documents regardless of status
      const allDocsQuery = `SELECT id, name, status, created_at FROM documents d WHERE d.organization_id = '${organizationId}' LIMIT 5`;
      try {
        const allDocsResult = await this.executeQueryInternal(allDocsQuery, organizationId);
        console.log(
          `[GetAllInternalDocumentsTool] All docs query result: ${allDocsResult.rows.length} documents found`
        );
        if (allDocsResult.rows.length > 0) {
          console.log(
            `[GetAllInternalDocumentsTool] Sample documents:`,
            allDocsResult.rows.map((row: any) => ({
              id: row.id,
              name: row.name,
              status: row.status,
            }))
          );
        }
      } catch (error) {
        console.error(
          `[GetAllInternalDocumentsTool] All docs query failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }

      // Total count query for internal database
      const countQuery = `SELECT COUNT(*) AS total FROM documents d WHERE ${filterConditions}`;
      const countResult = await this.executeQueryInternal(countQuery, organizationId);
      const total = countResult.rows[0]?.total || 0;

      // Documents query with pagination for internal database using adapter
      const documentsQuery = DatabaseSchemaAdapter.buildInternalQuery(
        filterConditions,
        safeLimit,
        safeOffset,
        false
      );
      const docsResult = await this.executeQueryInternal(documentsQuery, organizationId);

      const documents = docsResult.rows.map((row: any) => {
        const isStringContent = typeof row.content === 'string';
        const fileType: string = (row.file_type || '').toString().toLowerCase();

        // Normalizers / classifiers
        const isImageType = (t: string) =>
          t.startsWith('image/') ||
          ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp', 'tiff'].includes(t);
        const isExtractableTextType = (t: string) =>
          t.startsWith('text/') ||
          ['application/json', 'application/xml', 'text/xml', 'text/html', 'text/csv'].includes(
            t
          ) ||
          ['pdf', 'docx', 'xlsx', 'xls', 'txt', 'md', 'html', 'json', 'csv', 'xml'].includes(t);
        const isBase64String = (s: string) => /^[A-Za-z0-9+/\r\n]*={0,2}$/.test(s);

        // Initial values from DB row
        let content = isStringContent ? row.content : undefined;
        let buffer_file = row.buffer_file;

        // Exclude images from text processing early by clearing content and buffer_file
        if (isImageType(fileType)) {
          return {
            id: row.id,
            name: row.name,
            documentType: row.document_type,
            fileType: row.file_type,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            content: undefined,
            buffer_file: undefined,
            category: row.category,
            tags: row.tags,
            status: row.status,
            uploadedBy: row.uploaded_by,
            projectId: row.project_id,
            fileId: row.file_id,
            uploadDate: row.upload_date,
            size: row.size,
          };
        }

        // If `content` is base64 and type looks like extractable text, move to buffer_file
        if (content && isBase64String(content) && isExtractableTextType(fileType)) {
          console.log(
            `[GetAllInternalDocumentsTool] Detected base64 text content for document ${row.id}, routing to extractor via buffer_file`
          );
          buffer_file = content;
          content = undefined; // Force downstream extractor path
        }

        // Existing binary heuristic remains for legacy cases
        if (content && this.isBase64BinaryContent(content, row.file_type)) {
          console.log(
            `[GetAllInternalDocumentsTool] Detected base64 binary content for document ${row.id}, moving to buffer_file`
          );
          buffer_file = content;
          content = undefined;
        }

        return {
          id: row.id,
          name: row.name,
          documentType: row.document_type,
          fileType: row.file_type,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          content,
          buffer_file,
          category: row.category,
          tags: row.tags,
          status: row.status,
          uploadedBy: row.uploaded_by,
          projectId: row.project_id,
          fileId: row.file_id,
          uploadDate: row.upload_date,
          size: row.size,
        };
      });

      const result = {
        success: true,
        documents,
        pagination: {
          total,
          limit: safeLimit,
          offset: safeOffset,
          returned: documents.length,
          hasMore: safeOffset + documents.length < total,
        },
        // Metadata about the source database
        source: {
          database: 'internal',
          schema: 'documents',
          hasRLS: this.enhancedProvider?.getDatabaseConfig('internal').hasRLS || false,
          schemaInfo: DatabaseSchemaAdapter.getSchemaInfo('internal'),
        },
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(result) }],
        isError: false,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';

      const errorResult = {
        success: false,
        error: errorMessage,
        documents: [],
        pagination: {
          total: 0,
          limit: safeLimit,
          offset: safeOffset,
          returned: 0,
          hasMore: false,
        },
        source: {
          database: 'internal',
          schema: 'documents',
          error: true,
        },
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(errorResult) }],
        isError: true,
      };
    }
  }

  /**
   * Execute query on internal database using enhanced provider
   */
  private async executeQueryInternal(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }> {
    if (this.enhancedProvider) {
      return this.enhancedProvider.executeQueryInternal(query, organizationId);
    }

    // Fallback to regular provider if enhanced provider is not available
    return this.executeQuery(query, organizationId);
  }

  /**
   * Helper to check if content is likely base64-encoded binary data.
   * This is a heuristic and might need adjustment based on actual data.
   */
  private isBase64BinaryContent(content: string, fileType: string): boolean {
    // Common binary file types that might be base64-encoded
    const binaryFileTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ];

    // Check if the content looks like base64-encoded binary data
    // Base64 strings typically contain only A-Z, a-z, 0-9, +, /, and = for padding
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;

    // Check if content starts with common binary file signatures when decoded
    const isBase64Format = base64Pattern.test(content);

    if (isBase64Format && content.length > 100) {
      try {
        // Try to decode a small portion to check for binary signatures
        const decoded = Buffer.from(content.substring(0, 100), 'base64');

        // Check for common binary file signatures
        const pdfSignature = decoded.slice(0, 4).toString('hex') === '25504446'; // %PDF
        const pngSignature = decoded.slice(0, 8).toString('hex') === '89504e470d0a1a0a'; // PNG
        const jpegSignature = decoded.slice(0, 2).toString('hex') === 'ffd8'; // JPEG
        const zipSignature = decoded.slice(0, 4).toString('hex') === '504b0304'; // ZIP (DOCX, PPTX, etc.)

        if (pdfSignature || pngSignature || jpegSignature || zipSignature) {
          return true;
        }
      } catch (error) {
        // If decoding fails, it's probably not base64
        return false;
      }
    }

    // If file type is binary but content doesn't match base64 pattern, it might still be binary
    if (binaryFileTypes.includes(fileType)) {
      return true;
    }

    return false;
  }
}
