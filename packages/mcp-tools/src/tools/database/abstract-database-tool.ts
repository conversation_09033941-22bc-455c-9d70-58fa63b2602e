import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolExecutionContext } from '../../core/tool-handler';

/**
 * Database Provider Interface
 * Allows dependency injection of database connections
 */
export interface DatabaseProvider {
  executeQuery(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }>;
  validateQuery(query: string): Promise<boolean>;
}

/**
 * Session Provider Interface
 * Allows dependency injection of session management
 */
export interface SessionProvider {
  getOrganizationId(sessionId: string): Promise<string | null>;
  getUserId(sessionId: string): Promise<string | null>;
}

/**
 * Abstract Database Tool
 * Base class for database-related MCP tools with dependency injection
 */
export abstract class AbstractDatabaseTool implements MCPToolHandler {
  protected databaseProvider?: DatabaseProvider;
  protected sessionProvider?: SessionProvider;

  constructor(
    protected toolName: string,
    protected toolDescription: string,
    protected schema: Record<string, any>
  ) {}

  get name(): string {
    return this.toolName;
  }

  get description(): string {
    return this.toolDescription;
  }

  get inputSchema(): Record<string, any> {
    return this.schema;
  }

  // Dependency injection methods
  setDatabaseProvider(provider: DatabaseProvider): void {
    this.databaseProvider = provider;
  }

  setSessionProvider(provider: SessionProvider): void {
    this.sessionProvider = provider;
  }

  // Abstract method to be implemented by concrete tools
  abstract execute(
    args: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<MCPToolResult>;

  // Helper methods for common database operations
  protected async getOrganizationId(context: ToolExecutionContext): Promise<string> {
    if (context.organizationId) {
      return context.organizationId;
    }

    if (!this.sessionProvider) {
      throw new Error('Session provider not configured');
    }

    const orgId = await this.sessionProvider.getOrganizationId(context.sessionId);
    if (!orgId) {
      throw new Error('Organization not found for session');
    }

    return orgId;
  }

  protected async validateQuery(query: string): Promise<boolean> {
    if (!this.databaseProvider) {
      throw new Error('Database provider not configured');
    }

    // Use the injected database provider's validation
    return this.databaseProvider.validateQuery(query);
  }

  protected async executeQuery(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }> {
    if (!this.databaseProvider) {
      throw new Error('Database provider not configured');
    }

    return this.databaseProvider.executeQuery(query, organizationId);
  }

  protected createErrorResult(message: string): MCPToolResult {
    return {
      content: [
        {
          type: 'text',
          text: `Error: ${message}`,
        },
      ],
      isError: true,
    };
  }

  protected createSuccessResult(data: any): MCPToolResult {
    return {
      content: [
        {
          type: 'text',
          text: typeof data === 'string' ? data : JSON.stringify(data, null, 2),
        },
      ],
      isError: false,
    };
  }
}
