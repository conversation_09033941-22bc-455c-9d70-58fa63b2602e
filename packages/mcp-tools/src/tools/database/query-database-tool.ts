import { MCPToolResult, ToolExecutionContext } from '../../core/tool-handler';
import { AbstractDatabaseTool } from './abstract-database-tool';

/**
 * Query Database Tool
 * Executes safe SQL queries against tenant databases
 * Following Model Context Protocol specification
 */
export class QueryDatabaseTool extends AbstractDatabaseTool {
  constructor() {
    super(
      'query_database',
      'Execute safe SQL queries against the tenant database. Only SELECT statements are allowed for security.',
      {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'SQL query to execute (SELECT statements only)',
            default: 'SELECT 1',
          },
          timeout: {
            type: 'number',
            description: 'Query timeout in milliseconds',
            default: 30000,
          },
        },
        required: [],
      }
    );
  }

  async execute(args: Record<string, any>, context: ToolExecutionContext): Promise<MCPToolResult> {
    const query = args.query || 'SELECT 1';
    const timeout = args.timeout || 30000;

    try {
      // Validate query safety
      const isValid = await this.validateQuery(query);
      if (!isValid) {
        const errorResult = {
          success: false,
          error:
            'Query contains potentially unsafe operations. Only SELECT statements are allowed.',
          query_attempted: query,
        };
        return {
          content: [{ type: 'text', text: JSON.stringify(errorResult) }],
          isError: true,
        };
      }

      // Get organization ID
      const organizationId = await this.getOrganizationId(context);

      // Execute query with timeout and timing
      const startTime = Date.now();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), timeout);
      });

      const queryPromise = this.executeQuery(query, organizationId);
      const result = (await Promise.race([queryPromise, timeoutPromise])) as {
        rows: any[];
        rowCount: number;
        fields?: any[];
      };
      const executionTime = Date.now() - startTime;

      // Format result following agents project structure
      const queryResult = {
        success: true,
        rowCount: result.rowCount || result.rows?.length || 0,
        query_executed: query,
        execution_time: executionTime,
        data: result.rows || [],
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(queryResult) }],
        isError: false,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';

      const errorResult = {
        success: false,
        error: errorMessage,
        query_attempted: query,
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(errorResult) }],
        isError: true,
      };
    }
  }
}
