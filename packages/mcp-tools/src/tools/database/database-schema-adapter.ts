/**
 * Database Schema Adapter
 * Handles schema differences between internal and external databases
 * Provides unified interface for document operations across different schemas
 */

export interface DocumentSchema {
  id: string;
  name: string;
  documentType: string;
  fileType?: string;
  createdAt: Date;
  updatedAt: Date;
  content?: string;
  bufferFile?: Buffer;
  organizationId: string;
  // Additional fields that may vary between schemas
  [key: string]: any;
}

export interface ExternalDocumentSchema extends DocumentSchema {
  // External database specific fields
  fileType: string;
  content?: string;
  bufferFile?: Buffer;
}

export interface InternalDocumentSchema extends DocumentSchema {
  // Internal database specific fields
  type: string; // Maps to documentType
  category?: string;
  tags?: any;
  status: 'active' | 'archived' | 'deleted';
  uploadedBy: string;
  projectId?: string;
  fileId: string;
  uploadDate: Date;
  size: number;
}

/**
 * Schema adapter for converting between different database schemas
 */
export class DatabaseSchemaAdapter {
  /**
   * Convert internal database schema to unified document format
   */
  static adaptInternalToUnified(internalDoc: InternalDocumentSchema): DocumentSchema {
    return {
      id: internalDoc.id,
      name: internalDoc.name,
      documentType: internalDoc.type, // Map 'type' to 'documentType'
      fileType: undefined, // Will be populated from file_metadata join
      createdAt: internalDoc.createdAt,
      updatedAt: internalDoc.updatedAt,
      content: undefined, // Will be populated from file_metadata join
      bufferFile: undefined, // Will be populated from file_metadata join
      organizationId: internalDoc.organizationId,
      // Additional fields for backward compatibility
      category: internalDoc.category,
      tags: internalDoc.tags,
      status: internalDoc.status,
      uploadedBy: internalDoc.uploadedBy,
      projectId: internalDoc.projectId,
      fileId: internalDoc.fileId,
      uploadDate: internalDoc.uploadDate,
      size: internalDoc.size,
    };
  }

  /**
   * Convert external database schema to unified document format
   */
  static adaptExternalToUnified(externalDoc: ExternalDocumentSchema): DocumentSchema {
    return {
      id: externalDoc.id,
      name: externalDoc.name,
      documentType: externalDoc.documentType,
      fileType: externalDoc.fileType,
      createdAt: externalDoc.createdAt,
      updatedAt: externalDoc.updatedAt,
      content: externalDoc.content,
      bufferFile: externalDoc.bufferFile,
      organizationId: externalDoc.organizationId,
    };
  }

  /**
   * Build SQL query for internal database with proper joins
   */
  static buildInternalQuery(
    baseConditions: string,
    limit: number,
    offset: number,
    includeIdsOnly: boolean = false
  ): string {
    if (includeIdsOnly) {
      return `
        SELECT d.id, d.updated_at 
        FROM documents d
        WHERE ${baseConditions} 
        ORDER BY d.created_at DESC
      `;
    }

    return `
      SELECT 
        d.id, 
        d.name, 
        d.type as document_type, 
        d.created_at, 
        d.updated_at, 
        d.category,
        d.tags,
        d.status,
        d.uploaded_by,
        d.project_id,
        d.file_id,
        d.upload_date,
        d.size,
        fm.mime_type as file_type,
        fc.content,
        fm.storage_key as buffer_file
      FROM documents d
      LEFT JOIN file_metadata fm ON d.file_id = fm.id
      LEFT JOIN file_content fc ON d.file_id = fc.id
      WHERE ${baseConditions}
      ORDER BY d.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;
  }

  /**
   * Build SQL query for external database
   */
  static buildExternalQuery(
    baseConditions: string,
    limit: number,
    offset: number,
    includeIdsOnly: boolean = false
  ): string {
    if (includeIdsOnly) {
      return `
        SELECT id, updated_at 
        FROM file 
        WHERE ${baseConditions} 
        ORDER BY created_at DESC
      `;
    }

    return `
      SELECT 
        id, 
        name, 
        document_type, 
        created_at, 
        updated_at, 
        file_type,
        content,
        buffer_file,
        organization_id
      FROM file 
      WHERE ${baseConditions}
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;
  }

  /**
   * Build filter conditions for internal database
   */
  static buildInternalFilterConditions(
    filter: any,
    orgId: string,
    escapeFunction: (str: string) => string
  ): string {
    const conditions = [`d.organization_id = '${escapeFunction(orgId)}'`];

    // Always filter for active documents only
    conditions.push(`d.status = 'active'`);

    if (filter) {
      if (filter.ids && Array.isArray(filter.ids) && filter.ids.length > 0) {
        const safeIds = filter.ids.map((id: string) => `'${escapeFunction(id)}'`).join(', ');
        conditions.push(`d.id IN (${safeIds})`);
      }
      if (filter.documentType) {
        conditions.push(`d.type = '${escapeFunction(filter.documentType)}'`);
      }
      if (filter.fileType) {
        conditions.push(`fm.mime_type = '${escapeFunction(filter.fileType)}'`);
      }
      if (filter.createdAfter) {
        conditions.push(`d.created_at >= '${escapeFunction(filter.createdAfter)}'`);
      }
      if (filter.createdBefore) {
        conditions.push(`d.created_at <= '${escapeFunction(filter.createdBefore)}'`);
      }
      if (filter.category) {
        conditions.push(`d.category = '${escapeFunction(filter.category)}'`);
      }
      // Note: We don't allow status override since we always want active documents
      // if (filter.status) {
      //   conditions.push(`d.status = '${escapeFunction(filter.status)}'`);
      // }
    }

    return conditions.join(' AND ');
  }

  /**
   * Build filter conditions for external database
   */
  static buildExternalFilterConditions(
    filter: any,
    orgId: string,
    escapeFunction: (str: string) => string
  ): string {
    const conditions = [`organization_id = '${escapeFunction(orgId)}'`];

    if (filter) {
      if (filter.ids && Array.isArray(filter.ids) && filter.ids.length > 0) {
        const safeIds = filter.ids.map((id: string) => `'${escapeFunction(id)}'`).join(', ');
        conditions.push(`id IN (${safeIds})`);
      }
      if (filter.documentType) {
        conditions.push(`document_type = '${escapeFunction(filter.documentType)}'`);
      }
      if (filter.fileType) {
        conditions.push(`file_type = '${escapeFunction(filter.fileType)}'`);
      }
      if (filter.createdAfter) {
        conditions.push(`created_at >= '${escapeFunction(filter.createdAfter)}'`);
      }
      if (filter.createdBefore) {
        conditions.push(`created_at <= '${escapeFunction(filter.createdBefore)}'`);
      }
    }

    return conditions.join(' AND ');
  }

  /**
   * Escape SQL strings to prevent injection
   */
  static escapeSqlString(str: string): string {
    return str.replace(/'/g, "''");
  }

  /**
   * Get schema information for a specific database
   */
  static getSchemaInfo(schema: 'internal' | 'external'): {
    tableName: string;
    hasRLS: boolean;
    fields: string[];
    joins?: string[];
  } {
    if (schema === 'internal') {
      return {
        tableName: 'documents',
        hasRLS: process.env.INTERNAL_DB_HAS_RLS === 'true',
        fields: [
          'id',
          'name',
          'type',
          'created_at',
          'updated_at',
          'category',
          'tags',
          'status',
          'uploaded_by',
          'project_id',
          'file_id',
          'upload_date',
          'size',
          'organization_id',
        ],
        joins: ['LEFT JOIN file_metadata fm ON d.file_id = fm.id'],
      };
    }

    return {
      tableName: 'file',
      hasRLS: true,
      fields: [
        'id',
        'name',
        'document_type',
        'created_at',
        'updated_at',
        'file_type',
        'content',
        'buffer_file',
        'organization_id',
      ],
    };
  }
}
