import { MCPToolResult, ToolExecutionContext } from '../../core/tool-handler';
import { AbstractDatabaseTool } from './abstract-database-tool';

/**
 * Get All Documents Tool
 * Retrieves documents from tenant databases with pagination
 * Following Model Context Protocol specification
 */
export class GetAllDocumentsTool extends AbstractDatabaseTool {
  constructor() {
    super('get_all_documents', 'Retrieve paginated list of documents from the tenant database', {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Maximum number of documents to return',
          default: 50,
          minimum: 1,
          maximum: 200,
        },
        offset: {
          type: 'number',
          description: 'Number of documents to skip for pagination',
          default: 0,
          minimum: 0,
        },
        filter: {
          type: 'object',
          description: 'Optional filters for document search',
          properties: {
            ids: {
              type: 'array',
              items: { type: 'string' },
              description: 'Filter by a list of document IDs',
            },
            documentType: {
              type: 'string',
              description: 'Filter by document type',
            },
            fileType: {
              type: 'string',
              description: 'Filter by file type',
            },
            createdAfter: {
              type: 'string',
              format: 'date-time',
              description: 'Filter documents created after this date',
            },
            createdBefore: {
              type: 'string',
              format: 'date-time',
              description: 'Filter documents created before this date',
            },
          },
        },
        includeIdsOnly: {
          type: 'boolean',
          description:
            'If true, returns only the ID and last update timestamp of matching documents',
          default: false,
        },
      },
    });
  }

  async execute(args: Record<string, any>, context: ToolExecutionContext): Promise<MCPToolResult> {
    const { limit = 50, offset = 0, filter, includeIdsOnly = false } = args;
    const safeLimit = Math.min(limit, 200);
    const safeOffset = Math.max(offset, 0);

    try {
      // Get organization ID
      const organizationId = await this.getOrganizationId(context);

      // Build filter conditions
      const filterConditions = this.buildFilterConditions(filter, organizationId);

      // Handle the case where only IDs and timestamps are requested
      if (includeIdsOnly) {
        const idQuery = `SELECT id, updated_at FROM file WHERE ${filterConditions} ORDER BY created_at DESC`;
        const idResult = await this.executeQuery(idQuery, organizationId);
        const documents = idResult.rows.map((row: any) => ({
          id: row.id,
          updatedAt: row.updated_at,
        }));
        return {
          content: [{ type: 'text', text: JSON.stringify({ success: true, documents }) }],
          isError: false,
        };
      }

      // Total count query
      const countQuery = `SELECT COUNT(*) AS total FROM file WHERE ${filterConditions}`;
      const countResult = await this.executeQuery(countQuery, organizationId);
      const total = countResult.rows[0]?.total || 0;

      // Documents query with pagination
      const documentsQuery = `
        SELECT id, name, document_type, created_at, updated_at, file_type, content, buffer_file
        FROM file
        WHERE ${filterConditions}
        ORDER BY created_at DESC
        LIMIT ${safeLimit} OFFSET ${safeOffset}
      `;
      const docsResult = await this.executeQuery(documentsQuery, organizationId);

      const documents = docsResult.rows.map((row: any) => {
        const isStringContent = typeof row.content === 'string';

        return {
          id: row.id,
          name: row.name,
          documentType: row.document_type,
          fileType: row.file_type,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          // Include full textual content when stored in the content column
          content: isStringContent ? row.content : undefined,
          // Binary uploads are passed through for downstream handling
          buffer_file: row.buffer_file,
        };
      });

      const result = {
        success: true,
        documents,
        pagination: {
          total,
          limit: safeLimit,
          offset: safeOffset,
          returned: documents.length,
          hasMore: safeOffset + documents.length < total,
        },
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(result) }],
        isError: false,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';

      const errorResult = {
        success: false,
        error: errorMessage,
        documents: [],
        pagination: {
          total: 0,
          limit: safeLimit,
          offset: safeOffset,
          returned: 0,
          hasMore: false,
        },
      };

      return {
        content: [{ type: 'text', text: JSON.stringify(errorResult) }],
        isError: true,
      };
    }
  }

  /**
   * Build SQL filter conditions based on provided filters
   */
  private buildFilterConditions(filter: any, orgId: string): string {
    const conditions = [`organization_id = '${this.escapeSqlString(orgId)}'`];

    if (filter) {
      if (filter.ids && Array.isArray(filter.ids) && filter.ids.length > 0) {
        // Securely build an IN clause for IDs
        const safeIds = filter.ids.map((id: string) => `'${this.escapeSqlString(id)}'`).join(', ');
        conditions.push(`id IN (${safeIds})`);
      }
      if (filter.documentType) {
        conditions.push(`document_type = '${this.escapeSqlString(filter.documentType)}'`);
      }
      if (filter.fileType) {
        conditions.push(`file_type = '${this.escapeSqlString(filter.fileType)}'`);
      }
      if (filter.createdAfter) {
        conditions.push(`created_at >= '${this.escapeSqlString(filter.createdAfter)}'`);
      }
      if (filter.createdBefore) {
        conditions.push(`created_at <= '${this.escapeSqlString(filter.createdBefore)}'`);
      }
    }

    return conditions.join(' AND ');
  }

  /**
   * Escape SQL strings to prevent injection
   */
  private escapeSqlString(str: string): string {
    return str.replace(/'/g, "''");
  }
}
