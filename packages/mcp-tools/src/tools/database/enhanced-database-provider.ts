import { DatabaseProvider } from './abstract-database-tool';

/**
 * Enhanced Database Provider Interface
 * Extends the base DatabaseProvider to support multiple database connections
 */
export interface EnhancedDatabaseProvider extends DatabaseProvider {
  executeQueryInternal(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }>;
  validateQuery(query: string): Promise<boolean>;
  getDatabaseConfig(schema: 'internal' | 'external'): {
    connectionString: string;
    hasRLS: boolean;
    schema: 'internal' | 'external';
  };
}

/**
 * Enhanced Database Provider Implementation
 * Supports both internal and external database connections
 */
export class EnhancedDatabaseProviderImpl implements EnhancedDatabaseProvider {
  constructor(
    private dbProvider: {
      dbWithTenant(orgId: string): Promise<any>;
      dbInternalWithTenant(orgId: string): Promise<any>;
      log: any; // Ensure log is part of the constructor's dbProvider type
      redis?: any; // Add Redis client support
    }
  ) {}

  /**
   * Expose the log property to satisfy DatabaseProvider interface
   */
  get log() {
    // Added log getter
    return this.dbProvider.log;
  }

  /**
   * Expose the redis property to satisfy DatabaseProvider interface
   */
  get redis() {
    // Added redis getter
    return this.dbProvider.redis;
  }

  /**
   * Expose dbWithTenant method to satisfy DatabaseProvider interface
   */
  async dbWithTenant(orgId: string): Promise<any> {
    return this.dbProvider.dbWithTenant(orgId);
  }

  /**
   * Execute query on external database (current implementation)
   */
  async executeQuery(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }> {
    try {
      const db = await this.dbProvider.dbWithTenant(organizationId);

      // Try multiple execution methods to match the agents project approach
      let result: any = null;
      try {
        // Try the standard Drizzle execute method first
        result = await db.execute(query);
      } catch (error) {
        // If that fails, try calling it directly as a function
        if (typeof db === 'function') {
          result = await db(query);
        } else {
          this.dbProvider.log.error(
            error as Error,
            `External database execution failed for org ${organizationId}`
          );
          throw new Error(
            `External database execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      // Ensure result is not undefined
      if (!result) {
        throw new Error('Database execution returned no result');
      }

      return {
        rows: result.rows || [],
        rowCount: result.rowCount || (result.rows ? result.rows.length : 0),
        fields: result.fields || undefined,
      };
    } catch (error) {
      this.dbProvider.log.error(
        error as Error,
        `External database query failed for org ${organizationId}`
      );
      throw error;
    }
  }

  /**
   * Execute query on internal database (new implementation)
   */
  async executeQueryInternal(
    query: string,
    organizationId: string
  ): Promise<{
    rows: any[];
    rowCount: number;
    fields?: any[];
  }> {
    try {
      const db = await this.dbProvider.dbInternalWithTenant(organizationId);

      // Try multiple execution methods to match the agents project approach
      let result: any = null;
      try {
        // Try the standard Drizzle execute method first
        result = await db.execute(query);
      } catch (error) {
        // If that fails, try calling it directly as a function
        if (typeof db === 'function') {
          result = await db(query);
        } else {
          this.dbProvider.log.error(
            error as Error,
            `Internal database execution failed for org ${organizationId}`
          );
          throw new Error(
            `Internal database execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      // Ensure result is not undefined
      if (!result) {
        throw new Error('Database execution returned no result');
      }

      return {
        rows: result.rows || [],
        rowCount: result.rowCount || (result.rows ? result.rows.length : 0),
        fields: result.fields || undefined,
      };
    } catch (error) {
      this.dbProvider.log.error(
        error as Error,
        `Internal database query failed for org ${organizationId}`
      );
      throw error;
    }
  }

  /**
   * Performs a lightweight static analysis to verify that the supplied SQL is a
   * safe, read-only `SELECT` statement and does not contain dangerous keywords.
   */
  async validateQuery(query: string): Promise<boolean> {
    const upperQuery = query.toUpperCase().trim();

    // Check for dangerous keywords
    const dangerousKeywords = [
      'DROP',
      'DELETE',
      'UPDATE',
      'INSERT',
      'ALTER',
      'CREATE',
      'TRUNCATE',
      'GRANT',
      'REVOKE',
      'EXECUTE',
      'EXEC',
      'EXECUTE IMMEDIATE',
    ];

    const hasDangerousKeywords = dangerousKeywords.some(keyword => upperQuery.includes(keyword));

    // Check if it's a SELECT query
    const isSelectQuery = upperQuery.startsWith('SELECT');

    // Additional safety checks
    const hasMultipleStatements = upperQuery.includes(';') && upperQuery.split(';').length > 2;
    const hasComments = upperQuery.includes('--') || upperQuery.includes('/*');

    return isSelectQuery && !hasDangerousKeywords && !hasMultipleStatements && !hasComments;
  }

  /**
   * Get database configuration for a specific schema
   */
  getDatabaseConfig(schema: 'internal' | 'external'): {
    connectionString: string;
    hasRLS: boolean;
    schema: 'internal' | 'external';
  } {
    if (schema === 'internal') {
      return {
        connectionString:
          process.env.DATABASE_URL_INTERNAL || process.env.DATABASE_URL_ROOT_USER || '',
        hasRLS: process.env.INTERNAL_DB_HAS_RLS === 'true',
        schema: 'internal',
      };
    }
    return {
      connectionString: process.env.DATABASE_URL_ROOT_USER || '',
      hasRLS: true, // External database always has RLS
      schema: 'external',
    };
  }
}
