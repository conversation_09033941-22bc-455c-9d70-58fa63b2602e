// Core shared types are sourced from @anter/shared-services to avoid duplication
export type {
  DatabaseAdapter,
  CacheAdapter,
  LoggerAdapter,
  MCPToolsDependencies,
  MCPSession,
  AsyncTask,
  TaskStatusResponse,
  TaskMetrics,
} from '@anter/shared-services';

// Tool categories
export enum ToolCategory {
  DATABASE = 'database',
  CONTEXT = 'context',
  SYSTEM = 'system',
  ANALYSIS = 'analysis',
  ASYNC = 'async',
}
