export interface PromptMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
}

export interface PromptConfig {
  temperature?: number;
  includeConversationHistory?: boolean;
  responseFormat?: 'natural' | 'structured' | 'json' | undefined;
  model?: string;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  [key: string]: any; // Allow for additional config properties
}

export interface Prompt {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
  projectId?: string;
  createdBy: string;
  prompt: string | PromptMessage[]; // Can be text (string) or chat (PromptMessage[])
  name: string;
  version: number;
  type: 'text' | 'chat';
  config: PromptConfig;
  tags: string[];
  labels: string[];
  commitMessage?: string;
}

export interface PromptDependency {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
  projectId?: string;
  parentId: string;
  childName: string;
  childLabel?: string;
  childVersion?: number;
}

export interface PromptQuery {
  organizationId?: string;
  projectId?: string;
  name?: string;
  version?: number;
  type?: 'text' | 'chat';
  tags?: string[];
  labels?: string[];
  createdBy?: string;
}

export interface PromptCreateRequest {
  organizationId: string;
  projectId?: string;
  createdBy: string;
  prompt: string | PromptMessage[];
  name: string;
  version: number;
  type: 'text' | 'chat';
  config?: PromptConfig;
  tags?: string[];
  labels?: string[];
  commitMessage?: string;
}

export interface PromptUpdateRequest {
  prompt?: string | PromptMessage[];
  config?: PromptConfig;
  tags?: string[];
  labels?: string[];
  commitMessage?: string;
}

export interface PromptSearchResult {
  prompt: Prompt;
  score?: number;
  matchedFields?: string[];
}

export interface PromptManagerConfig {
  defaultOrganizationId?: string;
  defaultProjectId?: string;
  cacheEnabled?: boolean;
  cacheTTL?: number;
  maxCacheSize?: number;
  enableVersioning?: boolean;
  autoIncrementVersion?: boolean;
  storageType?: 'memory' | 'langfuse' | 'hybrid';
  langfuseConfig?: {
    publicKey?: string;
    secretKey?: string;
    baseUrl?: string;
  };
}

export interface PromptValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PromptTemplate {
  name: string;
  description: string;
  type: 'text' | 'chat';
  prompt: string | PromptMessage[];
  config: PromptConfig;
  tags: string[];
  labels: string[];
}

export interface PromptRenderingContext {
  variables: Record<string, any>;
  conversationHistory?: PromptMessage[];
  metadata?: Record<string, any>;
}

export interface PromptRenderingResult {
  renderedPrompt: string | PromptMessage[];
  usedVariables: string[];
  missingVariables: string[];
  warnings: string[];
}

export interface DependencyValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface DependencyResolutionResult {
  resolvedPrompts: Prompt[];
  unresolvedDependencies: PromptDependency[];
  circularDependencies: string[];
}

export interface PromptGraph {
  nodes: Array<{ id: string; name: string; version: number; type: string }>;
  edges: Array<{ from: string; to: string; label: string }>;
}
