// Tool input schemas using plain objects (avoiding complex AJV typing)
export const QUERY_DATABASE_SCHEMA = {
  type: 'object',
  properties: {
    query: {
      type: 'string',
      description: 'SQL query to execute (SELECT statements only)',
      minLength: 1,
    },
  },
  required: ['query'],
  additionalProperties: false,
} as const;

export const GET_CONTEXT_SCHEMA = {
  type: 'object',
  properties: {
    key: {
      type: 'string',
      description: 'Context key to retrieve (optional - if not provided, returns all context)',
    },
  },
  required: [],
  additionalProperties: false,
} as const;

export const SET_CONTEXT_SCHEMA = {
  type: 'object',
  properties: {
    key: {
      type: 'string',
      description: 'Context key',
      minLength: 1,
    },
    data: {
      description: 'Data to store',
    },
  },
  required: ['key', 'data'],
  additionalProperties: false,
} as const;

export const ANALYZE_DATA_SCHEMA = {
  type: 'object',
  properties: {
    query: {
      type: 'string',
      description: 'SQL query for data to analyze',
      minLength: 1,
    },
    analysis_type: {
      type: 'string',
      enum: ['security', 'performance', 'trends'],
      description: 'Type of analysis to perform',
    },
    options: {
      type: 'object',
      properties: {
        time_range: {
          type: 'string',
          description: 'Time range for analysis (e.g., "24h", "7d", "30d")',
        },
        filters: {
          type: 'object',
          description: 'Additional filters for analysis',
        },
      },
      additionalProperties: false,
    },
  },
  required: ['query', 'analysis_type'],
  additionalProperties: false,
} as const;

export const SYSTEM_STATUS_SCHEMA = {
  type: 'object',
  properties: {
    component: {
      type: 'string',
      enum: ['all', 'redis', 'database', 'sessions'],
      description: 'System component to check (default: all)',
    },
  },
  required: [],
  additionalProperties: false,
} as const;

export const GET_TASK_STATUS_SCHEMA = {
  type: 'object',
  properties: {
    task_id: {
      type: 'string',
      description: 'Task ID to check status for',
      minLength: 1,
    },
  },
  required: ['task_id'],
  additionalProperties: false,
} as const;

export const CANCEL_TASK_SCHEMA = {
  type: 'object',
  properties: {
    task_id: {
      type: 'string',
      description: 'Task ID to cancel',
      minLength: 1,
    },
  },
  required: ['task_id'],
  additionalProperties: false,
} as const;

export const LIST_TASKS_SCHEMA = {
  type: 'object',
  properties: {
    status: {
      type: 'string',
      enum: ['all', 'active', 'completed', 'failed'],
      description: 'Filter tasks by status (default: all)',
    },
    limit: {
      type: 'number',
      minimum: 1,
      maximum: 100,
      description: 'Maximum number of tasks to return (default: 50)',
    },
  },
  required: [],
  additionalProperties: false,
} as const;

export const GET_DOCUMENT_SCHEMA = {
  type: 'object',
  properties: {
    file_id: {
      type: 'string',
      description: 'Document/file ID to retrieve',
      minLength: 1,
    },
  },
  required: ['file_id'],
  additionalProperties: false,
} as const;

export const GET_ALL_DOCUMENTS_SCHEMA = {
  type: 'object',
  properties: {
    limit: {
      type: 'number',
      minimum: 1,
      maximum: 100,
      description: 'Maximum number of documents to return (default: 50)',
    },
    offset: {
      type: 'number',
      minimum: 0,
      description: 'Number of documents to skip (default: 0)',
    },
    includeFullContent: {
      type: 'boolean',
      description: 'Include full content in response (default: false, only previews)',
    },
  },
  required: [],
  additionalProperties: false,
} as const;
