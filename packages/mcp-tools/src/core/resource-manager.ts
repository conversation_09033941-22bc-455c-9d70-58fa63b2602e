// Removed SDK import to avoid dependency issues; using local types instead.

// TODO: Move to a separate file
class DocumentService {
  getDocumentCount(): any {
    throw new Error('Method not implemented.');
  }
  getCategories(): any {
    throw new Error('Method not implemented.');
  }
  async getDocument(_uri: string): Promise<any> {
    return null;
  }
}
// TODO: Move to a separate file
class EmbeddingService {
  getModelInfo(): any {
    throw new Error('Method not implemented.');
  }
  getDimensions(): any {
    throw new Error('Method not implemented.');
  }
}
/**
 * `ResourceManager` exposes virtual resources (documents, embeddings, metrics)
 * to external clients via the MCP server. Resources are identified by custom
 * URIs and can be listed, read, and subscribed to.
 */
export class ResourceManager {
  private resources: Map<string, ResourceDefinition> = new Map();
  private subscribers: Map<string, Set<string>> = new Map();
  public readonly pageSize = 50;

  /**
   * @param documentService - Service for document metadata/stats.
   * @param embeddingService - Service exposing embedding model info.
   */
  constructor(
    private documentService: DocumentService,
    private embeddingService: EmbeddingService
  ) {
    this.initializeResources();
  }

  private initializeResources(): void {
    // Register built-in resources
    this.registerResource({
      uri: 'askinfosec://documents',
      name: 'Document Collection',
      description: 'Access to organization documents and knowledge base',
      mimeType: 'application/json',
      handler: this.handleDocumentResource.bind(this),
    });

    this.registerResource({
      uri: 'askinfosec://embeddings',
      name: 'Embedding Service',
      description: 'Vector embeddings for semantic search and similarity',
      mimeType: 'application/json',
      handler: this.handleEmbeddingResource.bind(this),
    });

    this.registerResource({
      uri: 'askinfosec://metrics',
      name: 'System Metrics',
      description: 'Performance and usage metrics for the system',
      mimeType: 'application/json',
      handler: this.handleMetricsResource.bind(this),
    });
  }

  /** Returns a paginated list of available resources. */
  async listResources(cursor?: string): Promise<ResourceDefinition[]> {
    const allResources = Array.from(this.resources.values());

    // Sort by priority and usage
    allResources.sort((a, b) => a.name.localeCompare(b.name));

    // Handle pagination
    const startIndex = cursor ? this.decodeCursor(cursor) : 0;
    const endIndex = Math.min(startIndex + this.pageSize, allResources.length);

    return allResources.slice(startIndex, endIndex);
  }

  /** Reads a resource and returns serialised content. */
  async readResource(uri: string): Promise<ResourceContent> {
    const resource = this.resources.get(uri);
    if (!resource) {
      throw new Error(`Resource not found: ${uri}`);
    }

    try {
      const content = await resource.handler();

      return {
        mimeType: resource.mimeType,
        content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to read resource ${uri}: ${error.message}`);
      }
      throw new Error(`Failed to read resource ${uri}: Unknown error`);
    }
  }

  /** Subscribes a client to resource updates (no-op placeholder). */
  async subscribeToResource(uri: string, subscriberId: string): Promise<void> {
    if (!this.resources.has(uri)) {
      throw new Error(`Resource not found: ${uri}`);
    }

    if (!this.subscribers.has(uri)) {
      this.subscribers.set(uri, new Set());
    }

    this.subscribers.get(uri)!.add(subscriberId);
  }

  /** Unsubscribes a client from resource updates. */
  async unsubscribeFromResource(uri: string, subscriberId: string): Promise<void> {
    const subscribers = this.subscribers.get(uri);
    if (subscribers) {
      subscribers.delete(subscriberId);
    }
  }

  private async handleDocumentResource(): Promise<any> {
    // This would typically be called with organization/user context
    // For MCP compliance demo, return sample structure
    return {
      type: 'document_collection',
      description: 'Organization document collection',
      totalDocuments: await this.documentService.getDocumentCount(),
      categories: await this.documentService.getCategories(),
      lastUpdated: new Date().toISOString(),
      accessMethods: [
        {
          method: 'get-all-documents-tool',
          description: 'Retrieve all documents for an organization',
        },
        {
          method: 'query-database-tool',
          description: 'Search documents with SQL-like queries',
        },
      ],
    };
  }

  private async handleEmbeddingResource(): Promise<any> {
    return {
      type: 'embedding_service',
      description: 'Vector embedding service for semantic search',
      model: this.embeddingService.getModelInfo(),
      dimensions: this.embeddingService.getDimensions(),
      supportedFormats: ['text', 'document'],
      performance: {
        averageProcessingTime: '150ms',
        maxBatchSize: 100,
      },
    };
  }

  private async handleMetricsResource(): Promise<any> {
    return {
      type: 'system_metrics',
      description: 'Real-time system performance metrics',
      metrics: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        toolCallsToday: 0, // This would come from actual metrics service
        averageResponseTime: 0,
        errorRate: 0,
      },
      timestamp: new Date().toISOString(),
    };
  }

  private registerResource(resource: ResourceDefinition): void {
    this.resources.set(resource.uri, resource);
  }

  getNextCursor(resources: ResourceDefinition[]): string {
    return Buffer.from(resources.length.toString()).toString('base64');
  }

  private decodeCursor(cursor: string): number {
    try {
      return parseInt(Buffer.from(cursor, 'base64').toString(), 10) || 0;
    } catch {
      return 0;
    }
  }
}

interface ResourceDefinition {
  uri: string;
  name: string;
  description: string;
  mimeType: string;
  handler: () => Promise<any>;
}

interface ResourceContent {
  mimeType: string;
  content: string;
}
