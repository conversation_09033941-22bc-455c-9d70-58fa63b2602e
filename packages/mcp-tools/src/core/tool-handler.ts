/**
 * Core MCP Tool Handler Interface
 * Following Model Context Protocol specification with dependency injection
 */

// JSON-RPC 2.0 base types
export interface JsonRpcRequest {
  jsonrpc: '2.0';
  id: string | number;
  method: string;
  params?: any;
}

export interface JsonRpcResponse {
  jsonrpc: '2.0';
  id: string | number;
  result?: any;
  error?: JsonRpcError;
}

export interface JsonRpcError {
  code: number;
  message: string;
  data?: any;
}

/**
 * JSON-RPC 2.0 request sent to the MCP server for tool invocation.
 */
export interface MCPToolCallRequest extends JsonRpcRequest {
  method: 'tools/call';
  params: {
    name: string;
    arguments?: Record<string, any>;
  };
}

/**
 * Standardised structure returned from tool execution; may contain multiple
 * content blocks (text, images, resource links, etc.).
 */
export interface MCPToolResult {
  content: Array<{
    type: 'text' | 'image' | 'audio' | 'resource_link' | 'resource';
    text?: string;
    data?: string;
    url?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

// Core tool handler interface
export interface MCPToolHandler {
  readonly name: string;
  readonly description: string;
  readonly inputSchema: Record<string, any>;

  execute(args: Record<string, any>, context: ToolExecutionContext): Promise<MCPToolResult>;
}

/**
 * Runtime context supplied to each tool executor, providing session and tenant
 * information as well as arbitrary metadata.
 */
export interface ToolExecutionContext {
  sessionId: string;
  organizationId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

/**
 * Registry abstraction used by bridges/servers to manage tool handlers.
 */
export interface MCPToolRegistry {
  registerTool(tool: MCPToolHandler): void;
  getTool(name: string): MCPToolHandler | undefined;
  getAllTools(): MCPToolHandler[];
  listTools(): Array<{
    name: string;
    description: string;
    inputSchema: Record<string, any>;
  }>;
}
