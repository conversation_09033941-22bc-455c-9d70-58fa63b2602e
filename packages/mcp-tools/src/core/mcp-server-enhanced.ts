import {
  InitializeRequest,
  // InitializeResponse,
  ListToolsRequest,
  CallToolRequest,
  ListResourcesRequest,
  ReadResourceRequest,
  ServerCapabilities,
  // LogLevel,
  // MCPServer,
  // MCPServerOptions,
  // CallToolResponse,
  // ListToolsResponse,
  // ListResourcesResponse,
  // ReadResourceResponse,
} from '@modelcontextprotocol/sdk/types';
import { EnhancedToolRegistry } from './enhanced-tool-registry';
import { ResourceManager } from './resource-manager';

// TODO: Move to a separate file
class CapabilityManager {
  negotiate(_clientCapabilities: any, serverCapabilities: ServerCapabilities): ServerCapabilities {
    return serverCapabilities;
  }
}

// TODO: Move to a separate file
class MCPLogger {
  private level: string;
  constructor(logLevel: string) {
    this.level = logLevel;
  }
  info(_message: string, ..._args: any[]) {
    if (this.level === 'info') {
      console.log(_message, ..._args);
    }
  }
  error(_message: string, ..._args: any[]) {
    if (this.level === 'error') {
      console.error(_message, ..._args);
    }
  }
  debug(_message: string, ..._args: any[]) {
    if (this.level === 'debug') {
      console.log(_message, ..._args);
    }
  }
}

interface MCPServerOptions {
  logLevel: string;
}

interface InitializeResponse {
  protocolVersion: string;
  capabilities: ServerCapabilities;
  serverInfo: {
    name: string;
    version: string;
  };
}

interface ListToolsResponse {
  tools: Tool[];
  nextCursor: string | undefined;
}

interface Tool {
  name: string;
  description: string;
  inputSchema: object;
}

interface Content {
  type: string;
  text: string;
}

interface CallToolResponse {
  content: Content[];
}

interface ListResourcesResponse {
  resources: Resource[];
  nextCursor: string | undefined;
}

interface Resource {
  uri: string;
  name: string;
  description: string;
  mimeType: string;
}

interface ReadResourceResponse {
  contents: Content[];
}

interface MCPServer {
  handleInitialize(request: InitializeRequest): Promise<InitializeResponse>;
  handleListTools(request: ListToolsRequest): Promise<ListToolsResponse>;
  handleCallTool(request: CallToolRequest): Promise<CallToolResponse>;
  handleListResources(request: ListResourcesRequest): Promise<ListResourcesResponse>;
  handleReadResource(request: ReadResourceRequest): Promise<ReadResourceResponse>;
}

export class EnhancedMCPServer implements MCPServer {
  private toolRegistry: EnhancedToolRegistry;
  private resourceManager: ResourceManager;
  private capabilityManager: CapabilityManager;
  private logger: MCPLogger;

  constructor(options: MCPServerOptions) {
    this.toolRegistry = new EnhancedToolRegistry();
    this.resourceManager = new ResourceManager(
      new (class DocumentService {
        getDocumentCount() {}
        getCategories() {}
        async getDocument(_uri: string) {
          return null;
        }
      })(),
      new (class EmbeddingService {
        getModelInfo() {}
        getDimensions() {}
      })()
    );
    this.capabilityManager = new CapabilityManager();
    this.logger = new MCPLogger(options.logLevel || 'info');
  }

  async handleInitialize(request: InitializeRequest): Promise<InitializeResponse> {
    this.logger.info('Initializing MCP server', {
      client: request.params.clientInfo,
      protocolVersion: request.params.protocolVersion,
    });

    // Validate protocol version compatibility
    const supportedVersions = ['1.0.0', '0.1.0', '0.0.1'];
    if (!supportedVersions.includes(request.params.protocolVersion)) {
      throw new Error(`Unsupported protocol version: ${request.params.protocolVersion}`);
    }

    // Negotiate capabilities
    const negotiatedCapabilities = this.capabilityManager.negotiate(
      request.params.capabilities,
      this.getSupportedCapabilities()
    );

    return {
      protocolVersion: '0.0.1',
      capabilities: negotiatedCapabilities,
      serverInfo: {
        name: 'Anter MCP Server',
        version: '0.0.1',
      },
    };
  }

  async handleListTools(request: ListToolsRequest): Promise<ListToolsResponse> {
    const tools = await this.toolRegistry.listTools(request.params?.cursor);

    return {
      tools: tools.map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
      })),
      nextCursor:
        tools.length >= this.toolRegistry.pageSize
          ? this.toolRegistry.getNextCursor(tools)
          : undefined,
    };
  }

  async handleCallTool(request: CallToolRequest): Promise<CallToolResponse> {
    const toolName = request.params.name;
    const args = request.params.arguments || {};

    this.logger.debug('Calling tool', { toolName, arguments: args });

    try {
      // Validate tool exists
      const tool = await this.toolRegistry.getTool(toolName);
      if (!tool) {
        throw new Error(`Tool not found: ${toolName}`);
      }

      // Execute tool with timeout and monitoring
      const startTime = Date.now();
      const result = await this.executeToolWithTimeout(tool, args);
      const executionTime = Date.now() - startTime;

      // Log execution metrics
      this.logger.info('Tool executed successfully', {
        toolName,
        executionTime,
        resultSize: JSON.stringify(result).length,
      });

      return {
        content: [
          {
            type: 'text',
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error('Tool execution failed', {
          toolName,
          error: error.message,
          stack: error.stack,
        });
        throw new Error(`Tool execution failed: ${error.message}`);
      }
      throw new Error(`Tool execution failed: Unknown error`);
    }
  }

  async handleListResources(request: ListResourcesRequest): Promise<ListResourcesResponse> {
    const resources = await this.resourceManager.listResources(request.params?.cursor);

    return {
      resources: resources.map(resource => ({
        uri: resource.uri,
        name: resource.name,
        description: resource.description,
        mimeType: resource.mimeType,
      })),
      nextCursor:
        resources.length >= this.resourceManager.pageSize
          ? this.resourceManager.getNextCursor(resources)
          : undefined,
    };
  }

  async handleReadResource(request: ReadResourceRequest): Promise<ReadResourceResponse> {
    const uri = request.params.uri;

    this.logger.debug('Reading resource', { uri });

    try {
      const resource = await this.resourceManager.readResource(uri);

      return {
        contents: [
          {
            type: 'text',
            text: resource.content,
          },
        ],
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error('Resource read failed', {
          uri,
          error: error.message,
        });
        throw new Error(`Failed to read resource: ${error.message}`);
      }
      throw new Error(`Failed to read resource: Unknown error`);
    }
  }

  private getSupportedCapabilities(): ServerCapabilities {
    return {
      tools: {
        listChanged: true,
      },
      resources: {
        subscribe: true,
        listChanged: true,
      },
      prompts: {
        listChanged: true,
      },
      logging: {
        level: 'debug' as MCPLogger['level'],
      },
    };
  }

  private async executeToolWithTimeout(
    tool: any,
    args: Record<string, any>,
    timeoutMs: number = 30000
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      tool
        .execute(args)
        .then((result: any) => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch((error: any) => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }
}
