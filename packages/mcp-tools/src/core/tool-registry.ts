import { MCP<PERSON><PERSON><PERSON><PERSON><PERSON>, MCPToolRegistry } from './tool-handler';

/**
 * MCP Tool Registry Implementation
 * Manages tool registration and discovery following MCP specification
 */
export class DefaultMCPToolRegistry implements MCPToolRegistry {
  private tools = new Map<string, MCPToolHandler>();

  /** Registers a new tool ensuring unique names. */
  registerTool(tool: MCPToolHandler): void {
    if (this.tools.has(tool.name)) {
      throw new Error(`Tool '${tool.name}' is already registered`);
    }
    this.tools.set(tool.name, tool);
  }

  /** Retrieves a tool instance by name. */
  getTool(name: string): MCPToolHandler | undefined {
    return this.tools.get(name);
  }

  /** Returns all registered tool instances. */
  getAllTools(): MCPToolHandler[] {
    return Array.from(this.tools.values());
  }

  /** Lists tools in a lightweight form suitable for external consumers. */
  listTools(): Array<{
    name: string;
    description: string;
    inputSchema: Record<string, any>;
  }> {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
    }));
  }

  /** Removes all registered tools (primarily for testing). */
  clear(): void {
    this.tools.clear();
  }

  /** Returns the total number of registered tools. */
  size(): number {
    return this.tools.size;
  }
}
