import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  IEmbeddingIndex,
  RedisEmbeddingIndex,
  InMemoryEmbeddingIndex,
  EmbeddingIndexFactory,
  EmbeddingIndexOptions,
} from '../../src/services/embedding-index';
import { SemanticSearchServiceEnhanced } from '../../src/services/semantic-search-service-enhanced';
import { ContentHashService } from '../../src/services/content-hash.service';
import { EmbeddingService } from '../../src/services/embedding-service';
import { ProcessedDocument } from '../../src/services/document-processor';

// Mock Redis client for testing
interface MockRedisClient {
  data: Map<string, Map<string, string>>;
  pipelineCommands: Array<{ command: string; args: any[] }>;
  hset(key: string, field: string, value: string): Promise<number>;
  hget(key: string, field: string): Promise<string | null>;
  hdel(key: string, field: string): Promise<number>;
  hgetall(key: string): Promise<Record<string, string>>;
  del(key: string): Promise<number>;
  keys(pattern: string): Promise<string[]>;
  expire(key: string, seconds: number): Promise<number>;
  ping(): Promise<string>;
  pipeline(): MockPipeline;
  flushall(): void;
}

interface MockPipeline {
  commands: Array<{ command: string; args: any[] }>;
  hset(key: string, field: string, value: string): MockPipeline;
  expire(key: string, seconds: number): MockPipeline;
  exec(): Promise<any[]>;
}

class MockRedis implements MockRedisClient {
  data = new Map<string, Map<string, string>>();
  pipelineCommands: Array<{ command: string; args: any[] }> = [];

  async hset(key: string, field: string, value: string): Promise<number> {
    if (!this.data.has(key)) {
      this.data.set(key, new Map());
    }
    this.data.get(key)!.set(field, value);
    return 1;
  }

  async hget(key: string, field: string): Promise<string | null> {
    const hash = this.data.get(key);
    return hash ? hash.get(field) || null : null;
  }

  async hdel(key: string, field: string): Promise<number> {
    const hash = this.data.get(key);
    if (hash && hash.has(field)) {
      hash.delete(field);
      return 1;
    }
    return 0;
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    const hash = this.data.get(key);
    if (!hash) return {};

    const result: Record<string, string> = {};
    for (const [field, value] of hash.entries()) {
      result[field] = value;
    }
    return result;
  }

  async del(key: string): Promise<number> {
    return this.data.delete(key) ? 1 : 0;
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return Array.from(this.data.keys()).filter(key => regex.test(key));
  }

  async expire(key: string, seconds: number): Promise<number> {
    // In a real implementation, this would set a TTL
    // For testing, we'll just return success
    return 1;
  }

  async ping(): Promise<string> {
    return 'PONG';
  }

  pipeline(): MockPipeline {
    return new MockPipelineImpl(this);
  }

  flushall(): void {
    this.data.clear();
  }
}

class MockPipelineImpl implements MockPipeline {
  commands: Array<{ command: string; args: any[] }> = [];

  constructor(private redis: MockRedis) {}

  hset(key: string, field: string, value: string): MockPipeline {
    this.commands.push({ command: 'hset', args: [key, field, value] });
    return this;
  }

  expire(key: string, seconds: number): MockPipeline {
    this.commands.push({ command: 'expire', args: [key, seconds] });
    return this;
  }

  async exec(): Promise<any[]> {
    const results: any[] = [];
    for (const cmd of this.commands) {
      if (cmd.command === 'hset') {
        const result = await this.redis.hset(cmd.args[0], cmd.args[1], cmd.args[2]);
        results.push(result);
      } else if (cmd.command === 'expire') {
        const result = await this.redis.expire(cmd.args[0], cmd.args[1]);
        results.push(result);
      }
    }
    return results;
  }
}

describe('Redis Integration Tests', () => {
  const testOptions: EmbeddingIndexOptions = {
    organizationId: 'test-org',
    ttlHours: 24,
    batchSize: 10,
  };

  const testEmbedding = [0.1, 0.2, 0.3, 0.4, 0.5];
  const testContent = 'This is test content for Redis integration';
  const testMetadata = { type: 'document', category: 'test' };

  describe('RedisEmbeddingIndex', () => {
    let redis: MockRedis;
    let index: IEmbeddingIndex;

    beforeEach(() => {
      redis = new MockRedis();
      index = new RedisEmbeddingIndex(redis as any, testOptions);
    });

    afterEach(() => {
      redis.flushall();
    });

    it('should store and retrieve documents', async () => {
      await index.store('doc1', testEmbedding, testContent, testMetadata);

      const retrieved = await index.retrieve('doc1');
      expect(retrieved).toBeDefined();
      expect(retrieved!.embedding).toEqual(testEmbedding);
      expect(retrieved!.content).toBe(testContent);
      expect(retrieved!.metadata).toEqual(testMetadata);
    });

    it('should return null for non-existent documents', async () => {
      const retrieved = await index.retrieve('non-existent');
      expect(retrieved).toBeNull();
    });

    it('should search for similar documents', async () => {
      await index.store('doc1', [0.1, 0.2, 0.3], 'Content 1');
      await index.store('doc2', [0.2, 0.3, 0.4], 'Content 2');
      await index.store('doc3', [0.9, 0.8, 0.7], 'Content 3');

      const results = await index.searchSimilar([0.1, 0.2, 0.3], 2, 0.5);
      expect(results.length).toBe(2);
      expect(results[0].documentId).toBe('doc1'); // Most similar
      expect(results[0].similarity).toBeCloseTo(1.0, 2);
    });

    it('should filter search results by metadata', async () => {
      await index.store('doc1', testEmbedding, 'Content 1', { type: 'policy' });
      await index.store('doc2', testEmbedding, 'Content 2', { type: 'report' });

      const results = await index.searchSimilar(testEmbedding, 10, 0.0, { type: 'policy' });
      expect(results.length).toBe(1);
      expect(results[0].documentId).toBe('doc1');
    });

    it('should remove documents', async () => {
      await index.store('doc1', testEmbedding, testContent);

      const removed = await index.remove('doc1');
      expect(removed).toBe(true);

      const retrieved = await index.retrieve('doc1');
      expect(retrieved).toBeNull();
    });

    it('should batch store documents using pipeline', async () => {
      const documents = [
        { documentId: 'doc1', embedding: [0.1, 0.2], content: 'Content 1' },
        { documentId: 'doc2', embedding: [0.3, 0.4], content: 'Content 2' },
      ];

      await index.batchStore(documents);

      const doc1 = await index.retrieve('doc1');
      const doc2 = await index.retrieve('doc2');

      expect(doc1).toBeDefined();
      expect(doc2).toBeDefined();
      expect(doc1!.content).toBe('Content 1');
      expect(doc2!.content).toBe('Content 2');
    });

    it('should return correct stats', async () => {
      await index.store('doc1', testEmbedding, testContent);
      await index.store('doc2', testEmbedding, testContent);

      const stats = await index.getStats();
      expect(stats.totalDocuments).toBe(2);
      expect(stats.organizationCounts[testOptions.organizationId]).toBe(2);
    });

    it('should pass health check', async () => {
      const isHealthy = await index.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it('should clear organization documents', async () => {
      await index.store('doc1', testEmbedding, testContent);
      await index.store('doc2', testEmbedding, testContent);

      await index.clearOrganization(testOptions.organizationId);

      const doc1 = await index.retrieve('doc1');
      const doc2 = await index.retrieve('doc2');

      expect(doc1).toBeNull();
      expect(doc2).toBeNull();
    });
  });

  describe('EmbeddingIndexFactory', () => {
    let redis: MockRedis;

    beforeEach(() => {
      redis = new MockRedis();

      // Mock environment variables for testing
      process.env.REDIS_INDEX_ENABLED = 'true';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.SEARCH_FALLBACK_STRATEGY = 'in_memory';
    });

    afterEach(() => {
      delete process.env.REDIS_INDEX_ENABLED;
      delete process.env.REDIS_URL;
      delete process.env.SEARCH_FALLBACK_STRATEGY;
    });

    it('should create Redis index when available', async () => {
      const index = await EmbeddingIndexFactory.createIndex({
        organizationId: 'test-org',
        redisClient: redis as any,
      });

      expect(index).toBeInstanceOf(RedisEmbeddingIndex);
    });

    it('should create in-memory index when Redis not available', async () => {
      const index = await EmbeddingIndexFactory.createIndex({
        organizationId: 'test-org',
        // No Redis client provided
      });

      expect(index).toBeInstanceOf(InMemoryEmbeddingIndex);
    });

    it('should force in-memory index when requested', async () => {
      const index = await EmbeddingIndexFactory.createIndex({
        organizationId: 'test-org',
        redisClient: redis as any,
        forceInMemory: true,
      });

      expect(index).toBeInstanceOf(InMemoryEmbeddingIndex);
    });

    it('should check Redis availability', async () => {
      const isAvailable = await EmbeddingIndexFactory.isRedisAvailable(redis as any);
      expect(isAvailable).toBe(true);

      const isNotAvailable = await EmbeddingIndexFactory.isRedisAvailable();
      expect(isNotAvailable).toBe(false);
    });

    it('should detect mock Redis client and use in-memory index', async () => {
      // Create a mock Redis client with the isRedisMocked property
      const mockRedisClient = {
        ...redis,
        isRedisMocked: true,
      };

      const index = await EmbeddingIndexFactory.createIndex({
        organizationId: 'test-org',
        redisClient: mockRedisClient as any,
      });

      expect(index).toBeInstanceOf(InMemoryEmbeddingIndex);
    });

    it('should return false for mock Redis client in isRedisAvailable', async () => {
      // Create a mock Redis client with the isRedisMocked property
      const mockRedisClient = {
        ...redis,
        isRedisMocked: true,
      };

      const isAvailable = await EmbeddingIndexFactory.isRedisAvailable(mockRedisClient as any);
      expect(isAvailable).toBe(false);
    });
  });

  describe('SemanticSearchServiceEnhanced Integration', () => {
    let redis: MockRedis;
    let searchService: SemanticSearchServiceEnhanced;
    let embeddingService: EmbeddingService;

    beforeEach(() => {
      redis = new MockRedis();
      embeddingService = new EmbeddingService();
      searchService = new SemanticSearchServiceEnhanced(embeddingService);

      // Mock environment variables
      process.env.REDIS_INDEX_ENABLED = 'true';
      process.env.REDIS_URL = 'redis://localhost:6379';
    });

    afterEach(() => {
      redis.flushall();
      delete process.env.REDIS_INDEX_ENABLED;
      delete process.env.REDIS_URL;
    });

    it('should perform incremental indexing', async () => {
      const documents: ProcessedDocument[] = [
        {
          id: 'doc1',
          name: 'Document 1',
          content: 'This is the first document content',
          metadata: { type: 'policy' },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'doc2',
          name: 'Document 2',
          content: 'This is the second document content',
          metadata: { type: 'report' },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const result = await searchService.indexDocumentsIncremental(documents, 'test-org', {
        useRedis: true,
        redisClient: redis as any,
      });

      expect(result.indexed).toBe(2);
      expect(result.skipped).toBe(0);
      expect(result.changedDocuments).toHaveLength(2);
    });

    it('should search with Redis backend', async () => {
      const documents: ProcessedDocument[] = [
        {
          id: 'doc1',
          name: 'Security Policy',
          content: 'This document outlines security policies and procedures',
          metadata: { type: 'policy' },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      await searchService.indexDocuments(documents, 'test-org', {
        useRedis: true,
        redisClient: redis as any,
      });

      const results = await searchService.search('security policy', {
        organizationId: 'test-org',
        useRedis: true,
        redisClient: redis as any,
        topK: 3,
        threshold: 0.1,
      });

      expect(results).toHaveLength(1);
      expect(results[0].documentId).toBe('doc1');
    });

    it('should handle large batch operations', async () => {
      // Generate a large set of test documents
      const documents: ProcessedDocument[] = [];
      for (let i = 0; i < 100; i++) {
        documents.push({
          id: `doc${i}`,
          name: `Document ${i}`,
          content: `This is document number ${i} with some test content about ${i % 10 === 0 ? 'security' : 'general'} topics`,
          metadata: { type: i % 2 === 0 ? 'policy' : 'report', index: i },
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      const start = Date.now();
      await searchService.indexDocuments(documents, 'test-org', {
        useRedis: true,
        redisClient: redis as any,
      });
      const indexTime = Date.now() - start;

      // Search should be fast
      const searchStart = Date.now();
      const results = await searchService.search('security', {
        organizationId: 'test-org',
        useRedis: true,
        redisClient: redis as any,
        topK: 3,
        threshold: 0.1,
      });
      const searchTime = Date.now() - searchStart;

      expect(results.length).toBeGreaterThan(0);
      expect(indexTime).toBeLessThan(30000); // Should complete within 30 seconds
      expect(searchTime).toBeLessThan(5000); // Should search within 5 seconds
    });
  });

  describe('Performance Benchmarks', () => {
    let redis: MockRedis;
    let index: IEmbeddingIndex;

    beforeEach(() => {
      redis = new MockRedis();
      index = new RedisEmbeddingIndex(redis as any, testOptions);
    });

    afterEach(() => {
      redis.flushall();
    });

    it('should handle batch operations efficiently', async () => {
      const documents = Array.from({ length: 50 }, (_, i) => ({
        documentId: `doc${i}`,
        embedding: Array.from({ length: 384 }, () => Math.random()),
        content: `Document ${i} content with various topics and information`,
        metadata: { type: i % 3 === 0 ? 'policy' : 'report', index: i },
      }));

      const start = Date.now();
      await index.batchStore(documents);
      const batchTime = Date.now() - start;

      // Verify all documents were stored
      const stats = await index.getStats();
      expect(stats.totalDocuments).toBe(50);
      expect(batchTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Test search performance
      const searchStart = Date.now();
      const results = await index.searchSimilar(
        Array.from({ length: 384 }, () => Math.random()),
        10,
        0.1
      );
      const searchTime = Date.now() - searchStart;

      expect(results.length).toBeGreaterThan(0);
      expect(searchTime).toBeLessThan(1000); // Should search within 1 second
    });
  });
});
