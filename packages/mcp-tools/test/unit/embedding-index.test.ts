import { describe, it, expect, beforeEach } from 'vitest';
import {
  IEmbeddingIndex,
  InMemoryEmbeddingIndex,
  EmbeddingIndexOptions,
} from '../../src/services/embedding-index';

describe('EmbeddingIndex', () => {
  const testOptions: EmbeddingIndexOptions = {
    organizationId: 'test-org',
    ttlHours: 24,
    batchSize: 10,
  };

  const testEmbedding = [0.1, 0.2, 0.3, 0.4, 0.5];
  const testContent = 'This is test content';
  const testMetadata = { type: 'document', category: 'test' };

  describe('InMemoryEmbeddingIndex', () => {
    let index: IEmbeddingIndex;

    beforeEach(() => {
      index = new InMemoryEmbeddingIndex(testOptions);
    });

    it('should store and retrieve documents', async () => {
      await index.store('doc1', testEmbedding, testContent, testMetadata);

      const retrieved = await index.retrieve('doc1');
      expect(retrieved).toBeDefined();
      expect(retrieved!.embedding).toEqual(testEmbedding);
      expect(retrieved!.content).toBe(testContent);
      expect(retrieved!.metadata).toEqual(testMetadata);
    });

    it('should return null for non-existent documents', async () => {
      const retrieved = await index.retrieve('non-existent');
      expect(retrieved).toBeNull();
    });

    it('should search for similar documents', async () => {
      await index.store('doc1', [0.1, 0.2, 0.3], 'Content 1');
      await index.store('doc2', [0.2, 0.3, 0.4], 'Content 2');
      await index.store('doc3', [0.9, 0.8, 0.7], 'Content 3');

      const results = await index.searchSimilar([0.1, 0.2, 0.3], 2, 0.5);
      expect(results.length).toBe(2);
      expect(results[0].documentId).toBe('doc1'); // Most similar
      expect(results[0].similarity).toBeCloseTo(1.0, 2);
    });

    it('should filter search results by metadata', async () => {
      await index.store('doc1', testEmbedding, 'Content 1', { type: 'policy' });
      await index.store('doc2', testEmbedding, 'Content 2', { type: 'report' });

      const results = await index.searchSimilar(testEmbedding, 10, 0.0, { type: 'policy' });
      expect(results.length).toBe(1);
      expect(results[0].documentId).toBe('doc1');
    });

    it('should remove documents', async () => {
      await index.store('doc1', testEmbedding, testContent);

      const removed = await index.remove('doc1');
      expect(removed).toBe(true);

      const retrieved = await index.retrieve('doc1');
      expect(retrieved).toBeNull();
    });

    it('should batch store documents', async () => {
      const documents = [
        { documentId: 'doc1', embedding: [0.1, 0.2], content: 'Content 1' },
        { documentId: 'doc2', embedding: [0.3, 0.4], content: 'Content 2' },
      ];

      await index.batchStore(documents);

      const doc1 = await index.retrieve('doc1');
      const doc2 = await index.retrieve('doc2');

      expect(doc1).toBeDefined();
      expect(doc2).toBeDefined();
      expect(doc1!.content).toBe('Content 1');
      expect(doc2!.content).toBe('Content 2');
    });

    it('should return correct stats', async () => {
      await index.store('doc1', testEmbedding, testContent);
      await index.store('doc2', testEmbedding, testContent);

      const stats = await index.getStats();
      expect(stats.totalDocuments).toBe(2);
      expect(stats.organizationCounts[testOptions.organizationId]).toBe(2);
    });

    it('should pass health check', async () => {
      const isHealthy = await index.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it('should clear organization documents', async () => {
      await index.store('doc1', testEmbedding, testContent);
      await index.store('doc2', testEmbedding, testContent);

      await index.clearOrganization(testOptions.organizationId);

      const doc1 = await index.retrieve('doc1');
      const doc2 = await index.retrieve('doc2');

      expect(doc1).toBeNull();
      expect(doc2).toBeNull();
    });
  });
});
