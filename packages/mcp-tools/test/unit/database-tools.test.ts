import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  DefaultMCPToolRegistry,
  QueryDatabaseTool,
  GetAllDocumentsTool,
  DatabaseProvider,
  SessionProvider,
  ToolExecutionContext,
} from '../../src';

// Mock database provider
const mockDatabaseProvider: DatabaseProvider = {
  async executeQuery(query: string, organizationId: string) {
    if (query.includes('COUNT(*)')) {
      return { rows: [{ total: 5 }], rowCount: 1 };
    }
    if (query.includes('SELECT id, name')) {
      return {
        rows: [
          {
            id: '1',
            name: 'Test Document 1',
            document_type: 'pdf',
            file_type: 'application/pdf',
            created_at: '2025-01-27T10:00:00Z',
            updated_at: '2025-01-27T10:00:00Z',
            content: 'Test content 1',
            buffer_file: null,
          },
          {
            id: '2',
            name: 'Test Document 2',
            document_type: 'docx',
            file_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            created_at: '2025-01-26T10:00:00Z',
            updated_at: '2025-01-26T10:00:00Z',
            content: null,
            buffer_file: { data: 'binary data' },
          },
        ],
        rowCount: 2,
      };
    }
    return {
      rows: [
        { id: 1, name: 'Test Record', created_at: '2025-01-27' },
        { id: 2, name: 'Another Record', created_at: '2025-01-26' },
      ],
      rowCount: 2,
    };
  },

  async validateQuery(query: string) {
    const upperQuery = query.toUpperCase().trim();
    const dangerousKeywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE'];
    const hasDangerousKeywords = dangerousKeywords.some(keyword => upperQuery.includes(keyword));
    const isSelectQuery = upperQuery.startsWith('SELECT');
    return isSelectQuery && !hasDangerousKeywords;
  },
};

// Mock session provider
const mockSessionProvider: SessionProvider = {
  async getOrganizationId(sessionId: string) {
    return sessionId === 'valid-session' ? 'test-org-123' : null;
  },

  async getUserId(sessionId: string) {
    return sessionId === 'valid-session' ? 'test-user-456' : null;
  },
};

// Test context
const validContext: ToolExecutionContext = {
  sessionId: 'valid-session',
  organizationId: 'test-org-123',
  userId: 'test-user-456',
};

describe('MCP Tools Registry', () => {
  let registry: DefaultMCPToolRegistry;

  beforeEach(() => {
    registry = new DefaultMCPToolRegistry();
  });

  it('should register tools successfully', () => {
    const queryTool = new QueryDatabaseTool();
    const docsTool = new GetAllDocumentsTool();

    registry.registerTool(queryTool);
    registry.registerTool(docsTool);

    expect(registry.size()).toBe(2);
    expect(registry.getTool('query_database')).toBe(queryTool);
    expect(registry.getTool('get_all_documents')).toBe(docsTool);
  });

  it('should prevent duplicate tool registration', () => {
    const queryTool1 = new QueryDatabaseTool();
    const queryTool2 = new QueryDatabaseTool();

    registry.registerTool(queryTool1);

    expect(() => registry.registerTool(queryTool2)).toThrow(
      "Tool 'query_database' is already registered"
    );
  });

  it('should list all registered tools', () => {
    const queryTool = new QueryDatabaseTool();
    const docsTool = new GetAllDocumentsTool();

    registry.registerTool(queryTool);
    registry.registerTool(docsTool);

    const tools = registry.listTools();
    expect(tools).toHaveLength(2);
    expect(tools.find(t => t.name === 'query_database')).toBeDefined();
    expect(tools.find(t => t.name === 'get_all_documents')).toBeDefined();
  });
});

describe('QueryDatabaseTool', () => {
  let queryTool: QueryDatabaseTool;

  beforeEach(() => {
    queryTool = new QueryDatabaseTool();
    queryTool.setDatabaseProvider(mockDatabaseProvider);
    queryTool.setSessionProvider(mockSessionProvider);
  });

  it('should have correct tool metadata', () => {
    expect(queryTool.name).toBe('query_database');
    expect(queryTool.description).toContain('safe SQL queries');
    expect(queryTool.inputSchema.type).toBe('object');
    expect(queryTool.inputSchema.properties.query).toBeDefined();
  });

  it('should execute valid SELECT query successfully', async () => {
    const result = await queryTool.execute({ query: 'SELECT * FROM test_table' }, validContext);

    expect(result.isError).toBe(false);
    expect(result.content).toHaveLength(1);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(true);
    expect(content.query_executed).toBe('SELECT * FROM test_table');
    expect(content.execution_time).toBeGreaterThanOrEqual(0);
    expect(content.rowCount).toBe(2);
    expect(content.data).toHaveLength(2);
  });

  it('should reject unsafe queries', async () => {
    const result = await queryTool.execute({ query: 'DROP TABLE users' }, validContext);

    expect(result.isError).toBe(true);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(false);
    expect(content.error).toContain('potentially unsafe operations');
    expect(content.query_attempted).toBe('DROP TABLE users');
  });

  it('should use default query when none provided', async () => {
    const result = await queryTool.execute({}, validContext);

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(true);
    expect(content.query_executed).toBe('SELECT 1');
  });

  it('should handle database errors gracefully', async () => {
    const errorProvider: DatabaseProvider = {
      ...mockDatabaseProvider,
      async executeQuery() {
        throw new Error('Database connection failed');
      },
    };

    queryTool.setDatabaseProvider(errorProvider);

    const result = await queryTool.execute({ query: 'SELECT * FROM test_table' }, validContext);

    expect(result.isError).toBe(true);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(false);
    expect(content.error).toContain('Database connection failed');
  });

  it('should handle query timeout', async () => {
    const slowProvider: DatabaseProvider = {
      ...mockDatabaseProvider,
      async executeQuery() {
        await new Promise(resolve => setTimeout(resolve, 100));
        return { rows: [], rowCount: 0 };
      },
    };

    queryTool.setDatabaseProvider(slowProvider);

    const result = await queryTool.execute(
      { query: 'SELECT * FROM test_table', timeout: 50 },
      validContext
    );

    expect(result.isError).toBe(true);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(false);
    expect(content.error).toContain('Query timeout');
  });
});

describe('GetAllDocumentsTool', () => {
  let docsTool: GetAllDocumentsTool;

  beforeEach(() => {
    docsTool = new GetAllDocumentsTool();
    docsTool.setDatabaseProvider(mockDatabaseProvider);
    docsTool.setSessionProvider(mockSessionProvider);
  });

  it('should have correct tool metadata', () => {
    expect(docsTool.name).toBe('get_all_documents');
    expect(docsTool.description).toContain('paginated list of documents');
    expect(docsTool.inputSchema.type).toBe('object');
    expect(docsTool.inputSchema.properties.limit).toBeDefined();
    expect(docsTool.inputSchema.properties.offset).toBeDefined();
    expect(docsTool.inputSchema.properties.filter).toBeDefined();
  });

  it('should retrieve documents with default pagination', async () => {
    const result = await docsTool.execute({}, validContext);

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(true);
    expect(content.documents).toHaveLength(2);
    expect(content.pagination.total).toBe(5);
    expect(content.pagination.limit).toBe(50);
    expect(content.pagination.offset).toBe(0);
    expect(content.pagination.returned).toBe(2);
    expect(content.pagination.hasMore).toBe(true);
  });

  it('should apply custom pagination', async () => {
    const result = await docsTool.execute({ limit: 10, offset: 5 }, validContext);

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    expect(content.pagination.limit).toBe(10);
    expect(content.pagination.offset).toBe(5);
  });

  it('should enforce maximum limit', async () => {
    const result = await docsTool.execute({ limit: 1000 }, validContext);

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    expect(content.pagination.limit).toBe(200); // Should be capped at 200
  });

  it('should handle document filtering', async () => {
    const result = await docsTool.execute(
      {
        filter: {
          documentType: 'pdf',
          fileType: 'application/pdf',
          createdAfter: '2025-01-01T00:00:00Z',
        },
      },
      validContext
    );

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(true);
    expect(content.documents).toBeDefined();
  });

  it('should handle database errors gracefully', async () => {
    const errorProvider: DatabaseProvider = {
      ...mockDatabaseProvider,
      async executeQuery() {
        throw new Error('Database connection failed');
      },
    };

    docsTool.setDatabaseProvider(errorProvider);

    const result = await docsTool.execute({}, validContext);

    expect(result.isError).toBe(true);

    const content = JSON.parse(result.content[0].text!);
    expect(content.success).toBe(false);
    expect(content.error).toContain('Database connection failed');
    expect(content.documents).toEqual([]);
    expect(content.pagination.total).toBe(0);
  });

  it('should process documents with mixed content types', async () => {
    const result = await docsTool.execute({}, validContext);

    expect(result.isError).toBe(false);

    const content = JSON.parse(result.content[0].text!);
    const docs = content.documents;

    // First document has string content
    expect(docs[0].content).toBe('Test content 1');
    expect(docs[0].buffer_file).toBeNull();

    // Second document has binary content
    expect(docs[1].content).toBeUndefined();
    expect(docs[1].buffer_file).toEqual({ data: 'binary data' });
  });
});

describe('Tool Integration', () => {
  it('should work with dependency injection pattern', async () => {
    const registry = new DefaultMCPToolRegistry();

    const queryTool = new QueryDatabaseTool();
    queryTool.setDatabaseProvider(mockDatabaseProvider);
    queryTool.setSessionProvider(mockSessionProvider);

    const docsTool = new GetAllDocumentsTool();
    docsTool.setDatabaseProvider(mockDatabaseProvider);
    docsTool.setSessionProvider(mockSessionProvider);

    registry.registerTool(queryTool);
    registry.registerTool(docsTool);

    // Test query tool through registry
    const queryResult = await queryTool.execute(
      { query: 'SELECT COUNT(*) FROM test' },
      validContext
    );
    expect(queryResult.isError).toBe(false);

    // Test docs tool through registry
    const docsResult = await docsTool.execute({}, validContext);
    expect(docsResult.isError).toBe(false);
  });

  it('should handle missing providers gracefully', async () => {
    const queryTool = new QueryDatabaseTool();
    // Don't set providers

    const result = await queryTool.execute({ query: 'SELECT * FROM test' }, validContext);

    expect(result.isError).toBe(true);

    const content = JSON.parse(result.content[0].text!);
    expect(content.error).toContain('provider not configured');
  });
});
