import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { PromptManager } from '../../src/services/prompt/prompt-manager';
import { InMemoryPromptStorage } from '../../src/services/prompt/storage/in-memory-prompt-storage';
import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptRenderingContext,
} from '../../src/types/prompt';

describe('PromptManager', () => {
  let promptManager: PromptManager;
  let storage: InMemoryPromptStorage;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    storage = new InMemoryPromptStorage(
      {
        dataFilePath: '/tmp/test-prompts.json',
        autoSave: false,
      },
      mockLogger
    );

    promptManager = new PromptManager(
      {
        defaultProjectId: 'test-project',
        cacheEnabled: false,
      },
      storage,
      mockLogger
    );
  });

  afterEach(async () => {
    await promptManager.close();
  });

  describe('createPrompt', () => {
    it('should create a new text prompt', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'This is a test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        tags: ['test'],
        labels: ['production'],
      };

      const result = await promptManager.createPrompt(request);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.name).toBe('test-prompt');
      expect(result.type).toBe('text');
      expect(result.prompt).toBe('This is a test prompt');
      expect(result.projectId).toBe('test-project');
      expect(result.createdBy).toBe('test-user');
      expect(result.tags).toEqual(['test']);
      expect(result.labels).toEqual(['production']);
    });

    it('should create a new chat prompt', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: [
          { role: 'system', content: 'You are a helpful assistant' },
          { role: 'user', content: '{{userMessage}}' },
        ],
        name: 'test-chat-prompt',
        version: 1,
        type: 'chat',
      };

      const result = await promptManager.createPrompt(request);

      expect(result).toBeDefined();
      expect(result.type).toBe('chat');
      expect(Array.isArray(result.prompt)).toBe(true);
      expect((result.prompt as any[]).length).toBe(2);
    });

    it('should auto-increment version when enabled', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'auto-version-prompt',
        type: 'text',
      };

      const result1 = await promptManager.createPrompt(request);
      const result2 = await promptManager.createPrompt(request);

      expect(result1.version).toBe(1);
      expect(result2.version).toBe(2);
    });

    it('should validate required fields', async () => {
      const invalidRequest = {
        projectId: '',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text' as const,
      };

      await expect(promptManager.createPrompt(invalidRequest as any)).rejects.toThrow();
    });
  });

  describe('getPrompt', () => {
    it('should retrieve a prompt by ID', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Test prompt content',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await promptManager.createPrompt(request);
      const retrieved = await promptManager.getPrompt(created.id);

      expect(retrieved).toBeDefined();
      expect(retrieved?.id).toBe(created.id);
      expect(retrieved?.prompt).toBe('Test prompt content');
    });

    it('should return null for non-existent prompt', async () => {
      const result = await promptManager.getPrompt('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('getPromptByName', () => {
    it('should retrieve a prompt by name and version', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Test prompt content',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      await promptManager.createPrompt(request);
      const retrieved = await promptManager.getPromptByName('test-project', 'test-prompt', 1);

      expect(retrieved).toBeDefined();
      expect(retrieved?.name).toBe('test-prompt');
      expect(retrieved?.version).toBe(1);
    });

    it('should retrieve the latest version when no version specified', async () => {
      const request1: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Version 1 content',
        name: 'version-test-prompt',
        version: 1,
        type: 'text',
      };

      const request2: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Version 2 content',
        name: 'version-test-prompt',
        version: 2,
        type: 'text',
      };

      await promptManager.createPrompt(request1);
      await promptManager.createPrompt(request2);

      const latest = await promptManager.getPromptByName('test-project', 'version-test-prompt');
      expect(latest).toBeDefined();
      expect(latest?.version).toBe(2);
      expect(latest?.prompt).toBe('Version 2 content');
    });
  });

  describe('updatePrompt', () => {
    it('should update an existing prompt', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Original content',
        name: 'update-test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await promptManager.createPrompt(request);

      const updateRequest: PromptUpdateRequest = {
        prompt: 'Updated content',
        tags: ['updated'],
      };

      const updated = await promptManager.updatePrompt(created.id, updateRequest);

      expect(updated).toBeDefined();
      expect(updated?.prompt).toBe('Updated content');
      expect(updated?.tags).toEqual(['updated']);
      expect(updated?.updatedAt.getTime()).toBeGreaterThan(created.updatedAt.getTime());
    });

    it('should return null for non-existent prompt', async () => {
      const updateRequest: PromptUpdateRequest = {
        prompt: 'Updated content',
      };

      const result = await promptManager.updatePrompt('non-existent-id', updateRequest);
      expect(result).toBeNull();
    });
  });

  describe('deletePrompt', () => {
    it('should delete an existing prompt', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Test prompt content',
        name: 'delete-test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await promptManager.createPrompt(request);
      const deleted = await promptManager.deletePrompt(created.id);

      expect(deleted).toBe(true);

      const retrieved = await promptManager.getPrompt(created.id);
      expect(retrieved).toBeNull();
    });

    it('should return false for non-existent prompt', async () => {
      const result = await promptManager.deletePrompt('non-existent-id');
      expect(result).toBe(false);
    });
  });

  describe('searchPrompts', () => {
    beforeEach(async () => {
      const prompts = [
        {
          projectId: 'test-project',
          createdBy: 'test-user',
          prompt: 'Prompt 1',
          name: 'prompt-1',
          version: 1,
          type: 'text' as const,
          tags: ['tag1', 'tag2'],
          labels: ['production'],
        },
        {
          projectId: 'test-project',
          createdBy: 'test-user',
          prompt: 'Prompt 2',
          name: 'prompt-2',
          version: 1,
          type: 'chat' as const,
          tags: ['tag2', 'tag3'],
          labels: ['staging'],
        },
        {
          projectId: 'other-project',
          createdBy: 'test-user',
          prompt: 'Prompt 3',
          name: 'prompt-3',
          version: 1,
          type: 'text' as const,
          tags: ['tag1'],
          labels: ['production'],
        },
      ];

      for (const prompt of prompts) {
        await promptManager.createPrompt(prompt);
      }
    });

    it('should search by project ID', async () => {
      const query: PromptQuery = { projectId: 'test-project' };
      const results = await promptManager.searchPrompts(query);

      expect(results).toHaveLength(2);
      expect(results.every(p => p.projectId === 'test-project')).toBe(true);
    });

    it('should search by type', async () => {
      const query: PromptQuery = { type: 'chat' };
      const results = await promptManager.searchPrompts(query);

      expect(results).toHaveLength(1);
      expect(results[0].type).toBe('chat');
    });

    it('should search by tags', async () => {
      const query: PromptQuery = { tags: ['tag1'] };
      const results = await promptManager.searchPrompts(query);

      expect(results).toHaveLength(2);
      expect(results.every(p => p.tags.includes('tag1'))).toBe(true);
    });

    it('should search by labels', async () => {
      const query: PromptQuery = { labels: ['production'] };
      const results = await promptManager.searchPrompts(query);

      expect(results).toHaveLength(2);
      expect(results.every(p => p.labels.includes('production'))).toBe(true);
    });

    it('should combine multiple search criteria', async () => {
      const query: PromptQuery = {
        projectId: 'test-project',
        type: 'text',
        tags: ['tag1'],
      };
      const results = await promptManager.searchPrompts(query);

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('prompt-1');
    });
  });

  describe('renderPrompt', () => {
    it('should render a text prompt with variables', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, welcome to {{service}}!',
        name: 'greeting-prompt',
        version: 1,
        type: 'text',
      };

      const created = await promptManager.createPrompt(request);

      const context: PromptRenderingContext = {
        variables: {
          name: 'John',
          service: 'Anter',
        },
      };

      const result = await promptManager.renderPrompt(created.id, context);

      expect(result.renderedPrompt).toBe('Hello John, welcome to Anter!');
      expect(result.usedVariables).toEqual(['name', 'service']);
      expect(result.missingVariables).toEqual([]);
    });

    it('should render a chat prompt with variables', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: [
          { role: 'system', content: 'You are {{assistantName}}' },
          { role: 'user', content: '{{userMessage}}' },
        ],
        name: 'chat-prompt',
        version: 1,
        type: 'chat',
      };

      const created = await promptManager.createPrompt(request);

      const context: PromptRenderingContext = {
        variables: {
          assistantName: 'Anter Assistant',
          userMessage: 'Hello there!',
        },
      };

      const result = await promptManager.renderPrompt(created.id, context);

      expect(Array.isArray(result.renderedPrompt)).toBe(true);
      const messages = result.renderedPrompt as any[];
      expect(messages[0].content).toBe('You are Anter Assistant');
      expect(messages[1].content).toBe('Hello there!');
    });

    it('should handle missing variables', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, your age is {{age}}',
        name: 'missing-vars-prompt',
        version: 1,
        type: 'text',
      };

      const created = await promptManager.createPrompt(request);

      const context: PromptRenderingContext = {
        variables: {
          name: 'John',
        },
      };

      const result = await promptManager.renderPrompt(created.id, context);

      expect(result.renderedPrompt).toBe('Hello John, your age is {{age}}');
      expect(result.usedVariables).toEqual(['name']);
      expect(result.missingVariables).toEqual(['age']);
    });
  });

  describe('validatePrompt', () => {
    it('should validate a valid prompt', async () => {
      const prompt: Prompt = {
        id: 'test-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Valid prompt content',
        name: 'valid-prompt',
        version: 1,
        type: 'text',
        isActive: true,
        config: {},
        tags: [],
        labels: [],
      };

      const result = await promptManager.validatePrompt(prompt);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect validation errors', async () => {
      const invalidPrompt: Prompt = {
        id: 'test-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        projectId: '',
        createdBy: '',
        prompt: '',
        name: '',
        version: 0,
        type: 'text',
        isActive: true,
        config: {},
        tags: [],
        labels: [],
      };

      const result = await promptManager.validatePrompt(invalidPrompt);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', async () => {
      const request: PromptCreateRequest = {
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'stats-test-prompt',
        version: 1,
        type: 'text',
      };

      await promptManager.createPrompt(request);

      const stats = await promptManager.getStats();

      expect(stats.totalPrompts).toBe(1);
      expect(stats.totalDependencies).toBe(0);
      expect(stats.projects).toContain('test-project');
    });
  });
});
