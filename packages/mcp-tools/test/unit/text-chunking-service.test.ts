import { TextChunkingService, ChunkingOptions } from '../../src/services/text-chunking-service';
import { ProcessedDocument } from '../../src/services/document-processor';

describe('TextChunkingService', () => {
  let service: TextChunkingService;

  beforeEach(() => {
    service = new TextChunkingService();
  });

  describe('estimateTokenCount', () => {
    it('should estimate token count correctly', () => {
      const text = 'This is a test document with some content.';
      const tokenCount = service.estimateTokenCount(text);

      // Should be roughly 1/4 of character count
      expect(tokenCount).toBeGreaterThan(0);
      expect(tokenCount).toBeLessThanOrEqual(text.length);
    });

    it('should return 0 for empty text', () => {
      expect(service.estimateTokenCount('')).toBe(0);
      expect(service.estimateTokenCount(null as any)).toBe(0);
      expect(service.estimateTokenCount(undefined as any)).toBe(0);
    });
  });

  describe('needsChunking', () => {
    it('should return false for small documents', () => {
      const smallText = 'This is a small document.';
      expect(service.needsChunking(smallText)).toBe(false);
    });

    it('should return true for large documents', () => {
      // Create a large document that exceeds token limit
      const largeText = 'This is a large document. '.repeat(10000);
      expect(service.needsChunking(largeText)).toBe(true);
    });
  });

  describe('getChunkingStats', () => {
    it('should provide accurate chunking statistics', () => {
      const text = 'This is a test document. '.repeat(1000);
      const stats = service.getChunkingStats(text);

      expect(stats.estimatedTokens).toBeGreaterThan(0);
      expect(stats.needsChunking).toBeDefined();
      expect(stats.estimatedChunks).toBeGreaterThan(0);
      expect(stats.maxTokens).toBeDefined();
    });
  });

  describe('chunkDocument', () => {
    it('should return single chunk for small documents', async () => {
      const document: ProcessedDocument = {
        id: 'test-doc-1',
        name: 'Test Document',
        content: 'This is a small test document.',
        metadata: { type: 'test' },
      };

      const result = await service.chunkDocument(document);

      expect(result.documentId).toBe('test-doc-1');
      expect(result.chunks).toHaveLength(1);
      expect(result.totalChunks).toBe(1);
      expect(result.chunks[0].chunkNumber).toBe(1);
      expect(result.chunks[0].content).toBe(document.content);
      expect(result.chunks[0].isChunked).toBeUndefined(); // Not chunked
    });

    it('should chunk large documents into multiple pieces', async () => {
      const largeContent = 'This is a large document. '.repeat(5000);
      const document: ProcessedDocument = {
        id: 'test-doc-2',
        name: 'Large Test Document',
        content: largeContent,
        metadata: { type: 'test' },
      };

      const result = await service.chunkDocument(document);

      expect(result.documentId).toBe('test-doc-2');
      expect(result.totalChunks).toBeGreaterThan(1);
      expect(result.chunks).toHaveLength(result.totalChunks);

      // Verify chunk properties
      result.chunks.forEach((chunk, index) => {
        expect(chunk.chunkNumber).toBe(index + 1);
        expect(chunk.totalChunks).toBe(result.totalChunks);
        expect(chunk.documentId).toBe('test-doc-2');
        expect(chunk.content.length).toBeGreaterThan(0);
        expect(chunk.metadata?.isChunked).toBe(true);
        expect(chunk.metadata?.chunkNumber).toBe(index + 1);
        expect(chunk.metadata?.totalChunks).toBe(result.totalChunks);
      });
    });

    it('should preserve paragraph boundaries when possible', async () => {
      const content = `
        First paragraph with some content.

        Second paragraph with different content.

        Third paragraph with more content.
      `.repeat(1000);

      const document: ProcessedDocument = {
        id: 'test-doc-3',
        name: 'Paragraph Test Document',
        content,
        metadata: { type: 'test' },
      };

      const result = await service.chunkDocument(document, { preserveParagraphs: true });

      expect(result.totalChunks).toBeGreaterThan(1);

      // Check that chunks don't break mid-paragraph
      result.chunks.forEach(chunk => {
        const lines = chunk.content.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);

        // Should have complete paragraphs
        expect(nonEmptyLines.length).toBeGreaterThan(0);
      });
    });

    it('should handle documents with no content', async () => {
      const document: ProcessedDocument = {
        id: 'test-doc-4',
        name: 'Empty Document',
        content: '',
        metadata: { type: 'test' },
      };

      const result = await service.chunkDocument(document);

      expect(result.documentId).toBe('test-doc-4');
      expect(result.chunks).toHaveLength(0);
      expect(result.totalChunks).toBe(0);
    });
  });

  describe('chunkDocuments', () => {
    it('should chunk multiple documents', async () => {
      const documents: ProcessedDocument[] = [
        {
          id: 'doc-1',
          name: 'Small Document',
          content: 'Small content.',
          metadata: { type: 'small' },
        },
        {
          id: 'doc-2',
          name: 'Large Document',
          content: 'Large content. '.repeat(5000),
          metadata: { type: 'large' },
        },
      ];

      const results = await service.chunkDocuments(documents);

      expect(results).toHaveLength(2);
      expect(results[0].totalChunks).toBe(1); // Small document
      expect(results[1].totalChunks).toBeGreaterThan(1); // Large document
    });
  });

  describe('reconstructDocument', () => {
    it('should reconstruct document from chunks', () => {
      const chunks = [
        {
          id: 'doc-1::1',
          content: 'This is the first part of the document. ',
          chunkNumber: 1,
          totalChunks: 2,
          documentId: 'doc-1',
          metadata: { chunkNumber: 1, totalChunks: 2 },
        },
        {
          id: 'doc-1::2',
          content: 'This is the second part of the document.',
          chunkNumber: 2,
          totalChunks: 2,
          documentId: 'doc-1',
          metadata: { chunkNumber: 2, totalChunks: 2 },
        },
      ];

      const reconstructed = service.reconstructDocument(chunks);

      expect(reconstructed).toContain('This is the first part of the document');
      expect(reconstructed).toContain('This is the second part of the document');
    });

    it('should handle overlapping chunks correctly', () => {
      const chunks = [
        {
          id: 'doc-1::1',
          content: 'This is the first part with overlap.',
          chunkNumber: 1,
          totalChunks: 2,
          documentId: 'doc-1',
          metadata: { chunkNumber: 1, totalChunks: 2 },
          startToken: 0,
          endToken: 100,
        },
        {
          id: 'doc-1::2',
          content: 'overlap. This is the second part.',
          chunkNumber: 2,
          totalChunks: 2,
          documentId: 'doc-1',
          metadata: { chunkNumber: 2, totalChunks: 2 },
          startToken: 80,
          endToken: 150,
        },
      ];

      const reconstructed = service.reconstructDocument(chunks);

      // Should not have duplicate "overlap" text
      const overlapCount = (reconstructed.match(/overlap/g) || []).length;
      expect(overlapCount).toBeLessThanOrEqual(2);
    });
  });

  describe('Redis key generation', () => {
    it('should generate correct chunk keys', () => {
      const orgId = '2dd032ac-2dd0-32ac-2dd0-32ac2dd032ac';
      const docId = '4aec1471-4aec-1471-4aec-14714aec1471';

      const key1 = service.generateChunkKey(orgId, docId, 1);
      const key2 = service.generateChunkKey(orgId, docId, 2);

      expect(key1).toBe(`embeddings:org-${orgId}:doc-${docId}::1`);
      expect(key2).toBe(`embeddings:org-${orgId}:doc-${docId}::2`);
    });

    it('should parse chunk keys correctly', () => {
      const key = 'embeddings:test-org:doc-4aec1471-4aec-1471-4aec-14714aec1471::1';
      const parsed = service.parseChunkKey(key);

      expect(parsed).toEqual({
        organizationId: 'test-org',
        documentId: '4aec1471-4aec-1471-4aec-14714aec1471',
        chunkNumber: 1,
      });
    });

    it('should generate document chunk pattern', () => {
      const orgId = 'test-org';
      const docId = 'test-doc';
      const pattern = service.generateDocumentChunkPattern(orgId, docId);

      expect(pattern).toBe(`embeddings:org-${orgId}:doc-${docId}::*`);
    });
  });

  describe('chunking options', () => {
    it('should respect custom chunking options', async () => {
      const largeContent = 'This is a test sentence. '.repeat(1000);
      const document: ProcessedDocument = {
        id: 'test-doc-options',
        name: 'Options Test Document',
        content: largeContent,
        metadata: { type: 'test' },
      };

      const options: ChunkingOptions = {
        maxTokens: 1000,
        overlapTokens: 50,
        preserveParagraphs: false,
        preserveSentences: true,
        minChunkSize: 200,
      };

      const result = await service.chunkDocument(document, options);

      expect(result.totalChunks).toBeGreaterThan(1);

      // Check that chunks respect the minimum size
      result.chunks.forEach(chunk => {
        const tokenCount = service.estimateTokenCount(chunk.content);
        expect(tokenCount).toBeGreaterThanOrEqual(options.minChunkSize!);
      });
    });
  });
});
