import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  DocumentCacheService,
  DocumentFetcher,
  DocumentMetadata,
} from '../../src/services/document-cache.service';
import { ProcessedDocument } from '../../src/services/document-processor';

describe('DocumentCacheService - Cache TTL with Database Check', () => {
  let cacheService: DocumentCacheService;
  let mockDocumentFetcher: DocumentFetcher;
  let mockRedisClient: any;

  const mockProcessedDocument: ProcessedDocument = {
    id: 'doc1',
    name: 'Test Document',
    content: 'Test content',
    metadata: { title: 'Test Document' },
    updatedAt: new Date().toISOString(),
  };

  const mockDocumentMetadata: DocumentMetadata = {
    id: 'doc1',
    updatedAt: new Date().toISOString(),
  };

  beforeEach(() => {
    mockDocumentFetcher = {
      fetchDocumentIds: vi.fn(),
      fetchDocumentsByIds: vi.fn(),
    };

    mockRedisClient = {
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
    };

    cacheService = new DocumentCacheService(mockDocumentFetcher, mockRedisClient);
  });

  describe('Memory cache hit scenarios', () => {
    it('should return from memory cache when no new documents are detected', async () => {
      // Setup memory cache with existing document
      const cachedData = {
        version: 2,
        documents: [mockProcessedDocument],
      };
      (cacheService as any).memoryCache.set('org1', cachedData);

      // Mock database to return same document metadata
      (mockDocumentFetcher.fetchDocumentIds as any).mockResolvedValue([mockDocumentMetadata]);

      const result = await cacheService.getDocuments('org1', 'user1', 'trace1');

      expect(result.source).toBe('memory');
      expect(result.documents).toEqual([mockProcessedDocument]);
      expect(result.cacheStats.docsFromMemory).toBe(1);
      expect(result.cacheStats.docsFromRedis).toBe(0);
      expect(result.cacheStats.docsFromDatabase).toBe(0);
      expect(mockDocumentFetcher.fetchDocumentIds).toHaveBeenCalledWith('org1', 'user1', 'trace1');
      expect(mockDocumentFetcher.fetchDocumentsByIds).not.toHaveBeenCalled();
    });

    it('should fetch new documents and update cache when new documents are detected', async () => {
      // Setup memory cache with existing document
      const cachedData = {
        version: 2,
        documents: [mockProcessedDocument],
      };
      (cacheService as any).memoryCache.set('org1', cachedData);

      // Mock database to return additional document metadata
      const newDocumentMetadata: DocumentMetadata = {
        id: 'doc2',
        updatedAt: new Date().toISOString(),
      };
      const newProcessedDocument: ProcessedDocument = {
        id: 'doc2',
        name: 'New Document',
        content: 'New content',
        metadata: { title: 'New Document' },
        updatedAt: new Date().toISOString(),
      };

      (mockDocumentFetcher.fetchDocumentIds as any).mockResolvedValue([
        mockDocumentMetadata,
        newDocumentMetadata,
      ]);
      (mockDocumentFetcher.fetchDocumentsByIds as any).mockResolvedValue([newProcessedDocument]);

      const result = await cacheService.getDocuments('org1', 'user1', 'trace1');

      expect(result.source).toBe('database');
      expect(result.documents).toHaveLength(2);
      expect(result.documents).toContain(mockProcessedDocument);
      expect(result.documents).toContain(newProcessedDocument);
      expect(result.cacheStats.docsFromMemory).toBe(0);
      expect(result.cacheStats.docsFromRedis).toBe(1);
      expect(result.cacheStats.docsFromDatabase).toBe(1);
      expect(mockDocumentFetcher.fetchDocumentIds).toHaveBeenCalledWith('org1', 'user1', 'trace1');
      expect(mockDocumentFetcher.fetchDocumentsByIds).toHaveBeenCalledWith(
        'org1',
        'user1',
        ['doc2'],
        'trace1'
      );
      expect(mockRedisClient.set).toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      // Setup memory cache
      const cachedData = {
        version: 2,
        documents: [mockProcessedDocument],
      };
      (cacheService as any).memoryCache.set('org1', cachedData);

      // Mock database error
      (mockDocumentFetcher.fetchDocumentIds as any).mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(cacheService.getDocuments('org1', 'user1', 'trace1')).rejects.toThrow(
        'Database connection failed'
      );
      expect(mockDocumentFetcher.fetchDocumentIds).toHaveBeenCalledWith('org1', 'user1', 'trace1');
    });
  });

  describe('Memory cache miss scenarios', () => {
    it('should fall back to Redis and database when memory cache misses', async () => {
      // No memory cache
      (mockRedisClient.get as any).mockResolvedValue(null);
      (mockDocumentFetcher.fetchDocumentIds as any).mockResolvedValue([mockDocumentMetadata]);
      (mockDocumentFetcher.fetchDocumentsByIds as any).mockResolvedValue([mockProcessedDocument]);

      const result = await cacheService.getDocuments('org1', 'user1', 'trace1');

      expect(result.source).toBe('database');
      expect(result.documents).toEqual([mockProcessedDocument]);
      expect(result.cacheStats.docsFromMemory).toBe(0);
      expect(result.cacheStats.docsFromRedis).toBe(0);
      expect(result.cacheStats.docsFromDatabase).toBe(1);
    });
  });
});
