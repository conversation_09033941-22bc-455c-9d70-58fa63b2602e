import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  GetAllDocumentsTool,
  DatabaseProvider,
  SessionProvider,
  ToolExecutionContext,
} from '../../src';
import { DocumentProcessor } from '../../src/utils/document-processor';

describe('Document Processing', () => {
  let documentProcessor: DocumentProcessor;

  beforeEach(() => {
    documentProcessor = new DocumentProcessor();
  });

  describe('processDocumentContent', () => {
    it('should expose full content for internal platform documents', async () => {
      const row = {
        id: 'doc-1',
        name: 'Internal Document',
        document_type: 'internal',
        created_at: new Date(),
        updated_at: new Date(),
        category_id: 'cat-1',
        access_level: 'public',
        file_type: 'md',
        content: 'This is a long internal document content that exceeds 1000 characters. '.repeat(
          20
        ),
        buffer_file: null,
      };

      const result = await documentProcessor.processDocumentForAPI(row);

      expect(result.fullContent).toBe(row.content);
      expect(result.contentPreview).toBe(row.content.substring(0, 1000) + '...');
      expect(result.hasContent).toBe(true);
      expect(result.hasBufferFile).toBe(false);
    });

    it('should expose full content for text file uploads', async () => {
      const textContent = 'This is uploaded text file content that is quite long. '.repeat(30);
      const buffer = Buffer.from(textContent, 'utf-8');

      const row = {
        id: 'doc-2',
        name: 'Uploaded Text File',
        document_type: 'upload',
        created_at: new Date(),
        updated_at: new Date(),
        category_id: 'cat-1',
        access_level: 'public',
        file_type: 'txt',
        content: null,
        buffer_file: buffer,
      };

      const result = await documentProcessor.processDocumentForAPI(row);

      expect(result.fullContent).toBe(textContent);
      expect(result.contentPreview).toBe(textContent.substring(0, 1000) + '...');
      expect(result.hasContent).toBe(false);
      expect(result.hasBufferFile).toBe(true);
    });

    it('should attempt text extraction for binary files and fallback to placeholder', async () => {
      const binaryBuffer = Buffer.from('fake-pdf-content', 'binary');

      const row = {
        id: 'doc-3',
        name: 'Binary PDF File',
        document_type: 'upload',
        created_at: new Date(),
        updated_at: new Date(),
        category_id: 'cat-1',
        access_level: 'public',
        file_type: 'pdf',
        content: null,
        buffer_file: binaryBuffer,
      };

      const result = await documentProcessor.processDocumentForAPI(row);

      // PDF extraction should fail with fake content, so it should fallback to placeholder
      expect(result.contentPreview).toBe('[Binary file: pdf]');
      expect(result.contentType).toBe('application/pdf');
      expect(result.contentEncoding).toBe('base64');
    });

    it('should handle short content without truncation', async () => {
      const shortContent = 'Short content';

      const row = {
        id: 'doc-4',
        name: 'Short Document',
        document_type: 'internal',
        created_at: new Date(),
        updated_at: new Date(),
        category_id: 'cat-1',
        access_level: 'public',
        file_type: 'md',
        content: shortContent,
        buffer_file: null,
      };

      const result = await documentProcessor.processDocumentForAPI(row);

      expect(result.fullContent).toBe(shortContent);
      expect(result.contentPreview).toBe(shortContent);
    });

    it('should handle markdown files as text', async () => {
      const markdownContent = '# Title\n\nThis is markdown content.';
      const buffer = Buffer.from(markdownContent, 'utf-8');

      const row = {
        id: 'doc-5',
        name: 'Markdown File',
        document_type: 'upload',
        created_at: new Date(),
        updated_at: new Date(),
        category_id: 'cat-1',
        access_level: 'public',
        file_type: 'md',
        content: null,
        buffer_file: buffer,
      };

      const result = await documentProcessor.processDocumentForAPI(row);

      expect(result.fullContent).toBe(markdownContent);
      expect(result.contentPreview).toBe(markdownContent);
    });
  });

  // Note: createListSafeDocument is tested as part of GetAllDocumentsTool integration
});

describe('GetAllDocumentsTool - Parameter Handling', () => {
  let docsTool: GetAllDocumentsTool;
  let mockDatabaseProvider: DatabaseProvider;
  let mockSessionProvider: SessionProvider;

  beforeEach(() => {
    mockDatabaseProvider = {
      async executeQuery(query: string, organizationId: string) {
        if (query.includes('COUNT(*)')) {
          return { rows: [{ total: 10 }], rowCount: 1 };
        }
        return {
          rows: [
            {
              id: '1',
              name: 'Test Document',
              document_type: 'pdf',
              file_type: 'application/pdf',
              created_at: '2025-01-27T10:00:00Z',
              updated_at: '2025-01-27T10:00:00Z',
              content: 'Test content',
              buffer_file: null,
            },
          ],
          rowCount: 1,
        };
      },
      async validateQuery() {
        return true;
      },
    };

    mockSessionProvider = {
      async getOrganizationId() {
        return 'test-org';
      },
      async getUserId() {
        return 'test-user';
      },
    };

    docsTool = new GetAllDocumentsTool();
    docsTool.setDatabaseProvider(mockDatabaseProvider);
    docsTool.setSessionProvider(mockSessionProvider);
  });

  describe('includeFullContent parameter handling', () => {
    it('should handle includeFullContent parameter correctly', async () => {
      const context: ToolExecutionContext = {
        sessionId: 'test-session',
        organizationId: 'test-org',
        userId: 'test-user',
      };

      // Test with includeFullContent: true
      const resultWithFullContent = await docsTool.execute(
        {
          limit: 10,
          offset: 0,
          includeFullContent: true,
        },
        context
      );

      expect(resultWithFullContent.isError).toBe(false);
      const contentWith = JSON.parse(resultWithFullContent.content[0].text!);
      expect(contentWith.success).toBe(true);

      // Test with includeFullContent: false
      const resultWithoutFullContent = await docsTool.execute(
        {
          limit: 10,
          offset: 0,
          includeFullContent: false,
        },
        context
      );

      expect(resultWithoutFullContent.isError).toBe(false);
      const contentWithout = JSON.parse(resultWithoutFullContent.content[0].text!);
      expect(contentWithout.success).toBe(true);
    });
  });
});
