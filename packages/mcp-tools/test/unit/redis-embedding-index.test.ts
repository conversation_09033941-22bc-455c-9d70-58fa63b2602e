import { describe, it, expect, beforeEach, vi } from 'vitest';
import { RedisEmbeddingIndex } from '../../src/services/embedding-index/redis-embedding-index';

describe('RedisEmbeddingIndex', () => {
  let mockRedis: any;
  let index: RedisEmbeddingIndex;

  beforeEach(() => {
    mockRedis = {
      hset: vi.fn().mockResolvedValue(1),
      hget: vi.fn().mockResolvedValue(null),
      hdel: vi.fn().mockResolvedValue(1),
      hgetall: vi.fn().mockResolvedValue({}),
      del: vi.fn().mockResolvedValue(1),
      keys: vi.fn().mockResolvedValue([]),
      expire: vi.fn().mockResolvedValue(1),
      ping: vi.fn().mockResolvedValue('PONG'),
      pipeline: vi.fn().mockReturnValue({
        hset: vi.fn().mockReturnThis(),
        expire: vi.fn().mockReturnThis(),
        exec: vi.fn().mockResolvedValue([
          [null, 1],
          [null, 1],
        ]),
      }),
    };

    index = new RedisEmbeddingIndex(mockRedis, {
      organizationId: 'org-abc',
      ttlHours: 24,
    });
  });

  describe('batchStore', () => {
    it('should store documents with correct Redis keys', async () => {
      const documents = [
        {
          documentId: 'doc-123',
          embedding: [0.1, 0.2, 0.3],
          content: 'Test content 1',
          metadata: { test: true },
        },
        {
          documentId: 'embeddings:org-abc:doc-456::1',
          embedding: [0.4, 0.5, 0.6],
          content: 'Test content 2',
          metadata: { test: true, isChunked: true },
        },
      ];

      await index.batchStore(documents);

      // Verify that the correct keys were used
      const pipeline = mockRedis.pipeline();
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-123',
        'data',
        expect.stringContaining('doc-123')
      );
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-456::1',
        'data',
        expect.stringContaining('embeddings:org-abc:doc-456::1')
      );
    });

    it('should handle chunk keys correctly without adding duplicate prefixes', async () => {
      const documents = [
        {
          documentId: 'embeddings:test-org:doc-30cc00b1-30cc-00b1-30cc-00b130cc00b1::1',
          embedding: [0.1, 0.2, 0.3],
          content: 'Test chunk content',
          metadata: { isChunked: true },
        },
      ];

      await index.batchStore(documents);

      // Verify that the key is used as-is without adding the keyPrefix
      const pipeline = mockRedis.pipeline();
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:test-org:doc-30cc00b1-30cc-00b1-30cc-00b130cc00b1::1',
        'data',
        expect.stringContaining('embeddings:test-org:doc-30cc00b1-30cc-00b1-30cc-00b130cc00b1::1')
      );
    });

    it('should handle mixed document types correctly', async () => {
      const documents = [
        {
          documentId: 'doc-regular',
          embedding: [0.1, 0.2, 0.3],
          content: 'Regular document',
          metadata: { type: 'regular' },
        },
        {
          documentId: 'embeddings:org-abc:doc-chunked::1',
          embedding: [0.4, 0.5, 0.6],
          content: 'Chunked document part 1',
          metadata: { type: 'chunked', chunkNumber: 1 },
        },
        {
          documentId: 'embeddings:org-abc:doc-chunked::2',
          embedding: [0.7, 0.8, 0.9],
          content: 'Chunked document part 2',
          metadata: { type: 'chunked', chunkNumber: 2 },
        },
      ];

      await index.batchStore(documents);

      const pipeline = mockRedis.pipeline();
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-regular',
        'data',
        expect.stringContaining('doc-regular')
      );
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-chunked::1',
        'data',
        expect.stringContaining('embeddings:org-abc:doc-chunked::1')
      );
      expect(pipeline.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-chunked::2',
        'data',
        expect.stringContaining('embeddings:org-abc:doc-chunked::2')
      );
    });
  });

  describe('store', () => {
    it('should store a document with correct key', async () => {
      await index.store('doc-123', [0.1, 0.2, 0.3], 'Test content', { test: true });

      expect(mockRedis.hset).toHaveBeenCalledWith(
        'embeddings:org-abc:doc-123',
        'data',
        expect.stringContaining('doc-123')
      );
    });

    it('should store a chunk document with correct key', async () => {
      const chunkKey = 'embeddings:org-abc:doc-456::1';
      await index.store(chunkKey, [0.1, 0.2, 0.3], 'Test chunk content', { isChunked: true });

      expect(mockRedis.hset).toHaveBeenCalledWith(
        chunkKey,
        'data',
        expect.stringContaining(chunkKey)
      );
    });
  });

  describe('retrieve', () => {
    it('should retrieve a document with correct key', async () => {
      mockRedis.hget.mockResolvedValue(
        JSON.stringify({
          documentId: 'doc-123',
          embedding: [0.1, 0.2, 0.3],
          content: 'Test content',
          metadata: { test: true },
          timestamp: Date.now(),
          organizationId: 'org-abc',
        })
      );

      const result = await index.retrieve('doc-123');

      expect(mockRedis.hget).toHaveBeenCalledWith('embeddings:org-abc:doc-123', 'data');
      expect(result).toEqual({
        embedding: [0.1, 0.2, 0.3],
        content: 'Test content',
        metadata: { test: true },
      });
    });

    it('should retrieve a chunk document with correct key', async () => {
      const chunkKey = 'embeddings:org-abc:doc-456::1';
      mockRedis.hget.mockResolvedValue(
        JSON.stringify({
          documentId: chunkKey,
          embedding: [0.1, 0.2, 0.3],
          content: 'Test chunk content',
          metadata: { isChunked: true },
          timestamp: Date.now(),
          organizationId: 'org-abc',
        })
      );

      const result = await index.retrieve(chunkKey);

      expect(mockRedis.hget).toHaveBeenCalledWith(chunkKey, 'data');
      expect(result).toEqual({
        embedding: [0.1, 0.2, 0.3],
        content: 'Test chunk content',
        metadata: { isChunked: true },
      });
    });
  });
});
