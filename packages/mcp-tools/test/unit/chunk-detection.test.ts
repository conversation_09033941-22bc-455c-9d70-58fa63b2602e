import { TextChunkingService } from '../../src/services/text-chunking-service';
import { ProcessedDocument } from '../../src/services/document-processor';

describe('Chunk Detection', () => {
  let service: TextChunkingService;

  beforeEach(() => {
    service = new TextChunkingService();
  });

  describe('isAlreadyChunked', () => {
    it('should detect chunked document IDs', () => {
      expect(service.isAlreadyChunked('doc-123::1')).toBe(true);
      expect(service.isAlreadyChunked('doc-72890305-7289-0305-7289-030572890305::1')).toBe(true);
      expect(
        service.isAlreadyChunked('embeddings:test-org:doc-4aec1471-4aec-1471-4aec-14714aec1471::1')
      ).toBe(true);
    });

    it('should not detect regular document IDs as chunked', () => {
      expect(service.isAlreadyChunked('doc-123')).toBe(false);
      expect(service.isAlreadyChunked('document-456')).toBe(false);
      expect(service.isAlreadyChunked('')).toBe(false);
    });
  });

  describe('extractOriginalDocumentId', () => {
    it('should extract original document ID from chunk ID', () => {
      expect(service.extractOriginalDocumentId('doc-123::1')).toBe('doc-123');
      expect(service.extractOriginalDocumentId('doc-72890305-7289-0305-7289-030572890305::1')).toBe(
        'doc-72890305-7289-0305-7289-030572890305'
      );
      expect(
        service.extractOriginalDocumentId(
          'embeddings:test-org:doc-4aec1471-4aec-1471-4aec-14714aec1471::1'
        )
      ).toBe('embeddings:test-org:doc-4aec1471-4aec-1471-4aec-14714aec1471');
    });

    it('should return null for non-chunked document IDs', () => {
      expect(service.extractOriginalDocumentId('doc-123')).toBe(null);
      expect(service.extractOriginalDocumentId('document-456')).toBe(null);
      expect(service.extractOriginalDocumentId('')).toBe(null);
    });
  });

  describe('chunkDocument with already chunked document', () => {
    it('should handle already chunked document IDs correctly', async () => {
      const alreadyChunkedDoc: ProcessedDocument = {
        id: 'doc-72890305-7289-0305-7289-030572890305::1',
        name: 'Already Chunked Document',
        content: 'This is content from an already chunked document.',
        metadata: { type: 'chunk' },
      };

      const result = await service.chunkDocument(alreadyChunkedDoc);

      // Should treat it as a single chunk since it's already chunked
      expect(result.documentId).toBe('doc-72890305-7289-0305-7289-030572890305::1');
      expect(result.chunks).toHaveLength(1);
      expect(result.totalChunks).toBe(1);
      expect(result.chunks[0].id).toBe('doc-72890305-7289-0305-7289-030572890305::1::1');
      expect(result.chunks[0].content).toBe(alreadyChunkedDoc.content);
    });
  });
});
