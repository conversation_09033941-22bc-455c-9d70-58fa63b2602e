import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { InMemoryPromptStorage } from '../../src/services/prompt/storage/in-memory-prompt-storage';
import {
  Prompt,
  PromptCreateRequest,
  PromptUpdateRequest,
  PromptQuery,
  PromptDependency,
} from '../../src/types/prompt';
import * as fs from 'fs';
import * as path from 'path';

describe('InMemoryPromptStorage', () => {
  let storage: InMemoryPromptStorage;
  let mockLogger: any;
  const testDataPath = '/tmp/test-prompts.json';

  beforeEach(() => {
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    storage = new InMemoryPromptStorage(
      {
        dataFilePath: testDataPath,
        autoSave: false,
      },
      mockLogger
    );

    // Clean up test file if it exists
    if (fs.existsSync(testDataPath)) {
      fs.unlinkSync(testDataPath);
    }
  });

  afterEach(async () => {
    await storage.close();
    // Clean up test file
    if (fs.existsSync(testDataPath)) {
      fs.unlinkSync(testDataPath);
    }
  });

  describe('createPrompt', () => {
    it('should create a new prompt', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        projectId: 'test-project',
        createdBy: 'test-user',
        prompt: 'This is a test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        tags: ['test'],
        labels: ['production'],
      };

      const result = await storage.createPrompt(request);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.name).toBe('test-prompt');
      expect(result.type).toBe('text');
      expect(result.prompt).toBe('This is a test prompt');
      expect(result.organizationId).toBe('test-org');
      expect(result.projectId).toBe('test-project');
      expect(result.createdBy).toBe('test-user');
      expect(result.tags).toEqual(['test']);
      expect(result.labels).toEqual(['production']);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should create a chat prompt', async () => {
      const messages = [
        { role: 'system' as const, content: 'You are a helpful assistant' },
        { role: 'user' as const, content: 'Hello' },
      ];

      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: messages,
        name: 'test-chat-prompt',
        version: 1,
        type: 'chat',
      };

      const result = await storage.createPrompt(request);

      expect(result.type).toBe('chat');
      expect(Array.isArray(result.prompt)).toBe(true);
      expect((result.prompt as any[]).length).toBe(2);
    });
  });

  describe('getPrompt', () => {
    it('should retrieve a prompt by ID', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await storage.createPrompt(request);
      const retrieved = await storage.getPrompt(created.id);

      expect(retrieved).toBeDefined();
      expect(retrieved?.id).toBe(created.id);
      expect(retrieved?.name).toBe('test-prompt');
    });

    it('should return null for non-existent prompt', async () => {
      const result = await storage.getPrompt('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('getPromptByName', () => {
    it('should retrieve a prompt by name and version', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      await storage.createPrompt(request);
      const retrieved = await storage.getPromptByName('test-org', 'test-prompt', 1);

      expect(retrieved).toBeDefined();
      expect(retrieved?.name).toBe('test-prompt');
      expect(retrieved?.version).toBe(1);
    });

    it('should return null for non-existent prompt', async () => {
      const result = await storage.getPromptByName('test-org', 'non-existent', 1);
      expect(result).toBeNull();
    });
  });

  describe('getLatestPrompt', () => {
    it('should retrieve the latest version of a prompt', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Test prompt v1',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      const request2: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Test prompt v2',
        name: 'test-prompt',
        version: 2,
        type: 'text',
      };

      await storage.createPrompt(request1);
      await storage.createPrompt(request2);

      const latest = await storage.getLatestPrompt('test-org', 'test-prompt');

      expect(latest).toBeDefined();
      expect(latest?.version).toBe(2);
      expect(latest?.prompt).toBe('Test prompt v2');
    });
  });

  describe('updatePrompt', () => {
    it('should update an existing prompt', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Original prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await storage.createPrompt(request);

      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1));

      const updateRequest: PromptUpdateRequest = {
        prompt: 'Updated prompt',
        tags: ['updated'],
      };

      const updated = await storage.updatePrompt(created.id, updateRequest);

      expect(updated).toBeDefined();
      expect(updated?.prompt).toBe('Updated prompt');
      expect(updated?.tags).toEqual(['updated']);
      expect(updated?.updatedAt.getTime()).toBeGreaterThan(created.updatedAt.getTime());
    });

    it('should return null for non-existent prompt', async () => {
      const updateRequest: PromptUpdateRequest = {
        prompt: 'Updated prompt',
      };

      const result = await storage.updatePrompt('non-existent-id', updateRequest);
      expect(result).toBeNull();
    });
  });

  describe('deletePrompt', () => {
    it('should delete an existing prompt', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Test prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
      };

      const created = await storage.createPrompt(request);
      const deleted = await storage.deletePrompt(created.id);

      expect(deleted).toBe(true);

      const retrieved = await storage.getPrompt(created.id);
      expect(retrieved).toBeNull();
    });

    it('should return false for non-existent prompt', async () => {
      const result = await storage.deletePrompt('non-existent-id');
      expect(result).toBe(false);
    });
  });

  describe('searchPrompts', () => {
    it('should search prompts by organization', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Prompt 1',
        name: 'prompt1',
        version: 1,
        type: 'text',
      };

      const request2: PromptCreateRequest = {
        organizationId: 'org2',
        createdBy: 'test-user',
        prompt: 'Prompt 2',
        name: 'prompt2',
        version: 1,
        type: 'text',
      };

      await storage.createPrompt(request1);
      await storage.createPrompt(request2);

      const results = await storage.searchPrompts({ organizationId: 'org1' });

      expect(results).toHaveLength(1);
      expect(results[0].organizationId).toBe('org1');
    });

    it('should search prompts by type', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Text prompt',
        name: 'text-prompt',
        version: 1,
        type: 'text',
      };

      const request2: PromptCreateRequest = {
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: [{ role: 'user', content: 'Chat prompt' }],
        name: 'chat-prompt',
        version: 1,
        type: 'chat',
      };

      await storage.createPrompt(request1);
      await storage.createPrompt(request2);

      const results = await storage.searchPrompts({ type: 'text' });

      expect(results).toHaveLength(1);
      expect(results[0].type).toBe('text');
    });
  });

  describe('dependencies', () => {
    beforeEach(async () => {
      // Create test prompts for dependency testing
      await storage.createPrompt({
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Parent prompt',
        name: 'parent-prompt',
        version: 1,
        type: 'text',
      });

      await storage.createPrompt({
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Child prompt',
        name: 'child-prompt',
        version: 1,
        type: 'text',
      });

      await storage.createPrompt({
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Grandchild prompt',
        name: 'grandchild-prompt',
        version: 1,
        type: 'text',
      });
    });

    it('should create and retrieve dependencies', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);
      const childPrompt = await storage.getPromptByName('test-org', 'child-prompt', 1);

      const dependency = {
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childLabel: 'child-label',
        childVersion: 1,
      };

      const created = await storage.createDependency(dependency);

      expect(created).toBeDefined();
      expect(created.parentId).toBe(parentPrompt!.id);
      expect(created.childName).toBe('child-prompt');

      const retrieved = await storage.getDependencies(parentPrompt!.id);
      expect(retrieved).toHaveLength(1);
      expect(retrieved[0].id).toBe(created.id);
    });

    it('should validate dependencies before creation', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);

      const validDependency = {
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childVersion: 1,
      };

      const validation = await storage.validateDependency(validDependency);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);

      const invalidDependency = {
        organizationId: 'test-org',
        parentId: 'non-existent-id',
        childName: 'child-prompt',
        childVersion: 1,
      };

      const invalidValidation = await storage.validateDependency(invalidDependency);
      expect(invalidValidation.isValid).toBe(false);
      expect(invalidValidation.errors).toHaveLength(1);
      expect(invalidValidation.errors[0]).toContain('does not exist');
    });

    it('should detect circular dependencies', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);
      const childPrompt = await storage.getPromptByName('test-org', 'child-prompt', 1);
      const grandchildPrompt = await storage.getPromptByName('test-org', 'grandchild-prompt', 1);

      // Create dependency chain: parent -> child -> grandchild
      await storage.createDependency({
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childVersion: 1,
      });

      await storage.createDependency({
        organizationId: 'test-org',
        parentId: childPrompt!.id,
        childName: 'grandchild-prompt',
        childVersion: 1,
      });

      // Try to create circular dependency: grandchild -> parent
      const circularDependency = {
        organizationId: 'test-org',
        parentId: grandchildPrompt!.id,
        childName: 'parent-prompt',
        childVersion: 1,
      };

      await expect(storage.createDependency(circularDependency)).rejects.toThrow(
        'Circular dependency detected'
      );
    });

    it('should resolve dependencies correctly', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);
      const childPrompt = await storage.getPromptByName('test-org', 'child-prompt', 1);
      const grandchildPrompt = await storage.getPromptByName('test-org', 'grandchild-prompt', 1);

      // Create dependency chain: parent -> child -> grandchild
      await storage.createDependency({
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childVersion: 1,
      });

      await storage.createDependency({
        organizationId: 'test-org',
        parentId: childPrompt!.id,
        childName: 'grandchild-prompt',
        childVersion: 1,
      });

      const resolution = await storage.resolveDependencies(parentPrompt!.id);

      expect(resolution.resolvedPrompts).toHaveLength(3);
      expect(resolution.unresolvedDependencies).toHaveLength(0);
      expect(resolution.circularDependencies).toHaveLength(0);

      // Check that prompts are in dependency order
      const promptIds = resolution.resolvedPrompts.map(p => p.id);
      expect(promptIds).toContain(parentPrompt!.id);
      expect(promptIds).toContain(childPrompt!.id);
      expect(promptIds).toContain(grandchildPrompt!.id);
    });

    it('should generate prompt graph for visualization', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);
      const childPrompt = await storage.getPromptByName('test-org', 'child-prompt', 1);

      await storage.createDependency({
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childVersion: 1,
      });

      const graph = await storage.getPromptGraph(parentPrompt!.id);

      expect(graph.nodes).toHaveLength(2);
      expect(graph.edges).toHaveLength(1);

      const parentNode = graph.nodes.find(n => n.id === parentPrompt!.id);
      const childNode = graph.nodes.find(n => n.id === childPrompt!.id);

      expect(parentNode).toBeDefined();
      expect(childNode).toBeDefined();
      expect(graph.edges[0].from).toBe(parentPrompt!.id);
      expect(graph.edges[0].to).toBe(childPrompt!.id);
      expect(graph.edges[0].label).toBe('child-prompt v1');
    });

    it('should delete dependencies', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);

      const dependency = {
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
      };

      const created = await storage.createDependency(dependency);
      const deleted = await storage.deleteDependency(created.id);

      expect(deleted).toBe(true);

      const retrieved = await storage.getDependencies(parentPrompt!.id);
      expect(retrieved).toHaveLength(0);
    });

    it('should cascade delete dependencies when prompt is deleted', async () => {
      const parentPrompt = await storage.getPromptByName('test-org', 'parent-prompt', 1);
      const childPrompt = await storage.getPromptByName('test-org', 'child-prompt', 1);

      await storage.createDependency({
        organizationId: 'test-org',
        parentId: parentPrompt!.id,
        childName: 'child-prompt',
        childVersion: 1,
      });

      // Verify dependency exists
      const depsBefore = await storage.getDependencies(parentPrompt!.id);
      expect(depsBefore).toHaveLength(1);

      // Delete parent prompt
      const deleted = await storage.deletePrompt(parentPrompt!.id);
      expect(deleted).toBe(true);

      // Verify dependency is cascade deleted
      const depsAfter = await storage.getDependencies(parentPrompt!.id);
      expect(depsAfter).toHaveLength(0);

      // Verify child prompt still exists
      const childStillExists = await storage.getPrompt(childPrompt!.id);
      expect(childStillExists).toBeDefined();
    });
  });

  describe('label-based functionality', () => {
    it('should get active prompts using production label', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Production prompt',
        name: 'prod-prompt',
        version: 1,
        type: 'text',
        labels: ['production'],
      };

      const request2: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Staging prompt',
        name: 'staging-prompt',
        version: 1,
        type: 'text',
        labels: ['staging'],
      };

      await storage.createPrompt(request1);
      await storage.createPrompt(request2);

      const activePrompts = await storage.getActivePrompts('org1');

      expect(activePrompts).toHaveLength(1);
      expect(activePrompts[0].name).toBe('prod-prompt');
      expect(activePrompts[0].labels).toContain('production');
    });

    it('should get prompts by specific labels', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Production prompt',
        name: 'prod-prompt',
        version: 1,
        type: 'text',
        labels: ['production', 'latest'],
      };

      const request2: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Staging prompt',
        name: 'staging-prompt',
        version: 1,
        type: 'text',
        labels: ['staging', 'latest'],
      };

      await storage.createPrompt(request1);
      await storage.createPrompt(request2);

      const latestPrompts = await storage.getPromptsByLabels(['latest'], 'org1');
      expect(latestPrompts).toHaveLength(2);

      const productionPrompts = await storage.getPromptsByLabels(['production'], 'org1');
      expect(productionPrompts).toHaveLength(1);
      expect(productionPrompts[0].name).toBe('prod-prompt');
    });

    it('should get production prompt by name', async () => {
      const request: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Production prompt',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        labels: ['production'],
      };

      await storage.createPrompt(request);

      const productionPrompt = await storage.getProductionPrompt('org1', 'test-prompt');
      expect(productionPrompt).toBeDefined();
      expect(productionPrompt!.name).toBe('test-prompt');
      expect(productionPrompt!.labels).toContain('production');
    });

    it('should return null for non-existent production prompt', async () => {
      const productionPrompt = await storage.getProductionPrompt('org1', 'non-existent');
      expect(productionPrompt).toBeNull();
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', async () => {
      const request1: PromptCreateRequest = {
        organizationId: 'org1',
        createdBy: 'test-user',
        prompt: 'Prompt 1',
        name: 'prompt1',
        version: 1,
        type: 'text',
      };

      const request2: PromptCreateRequest = {
        organizationId: 'org2',
        createdBy: 'test-user',
        prompt: 'Prompt 2',
        name: 'prompt2',
        version: 1,
        type: 'text',
      };

      const prompt1 = await storage.createPrompt(request1);
      const prompt2 = await storage.createPrompt(request2);

      const dependency = {
        organizationId: 'org1',
        parentId: prompt1.id,
        childName: 'child',
      };

      await storage.createDependency(dependency);

      const stats = await storage.getStats();

      expect(stats.totalPrompts).toBe(2);
      expect(stats.totalDependencies).toBe(1);
      expect(stats.organizations).toEqual(['org1', 'org2']);
    });
  });
});
