import { describe, it, expect, beforeEach } from 'vitest';
import { FileTextExtractor, extractText } from '../../src/utils/file-text-extractor';
import * as fs from 'fs';
import * as path from 'path';

describe('FileTextExtractor', () => {
  describe('isSupportedFileType', () => {
    it('should return true for supported file types', () => {
      const supportedTypes = ['pdf', 'docx', 'xlsx', 'txt', 'md', 'html', 'json', 'csv', 'xml'];

      supportedTypes.forEach(type => {
        expect(FileTextExtractor.isSupportedFileType(type)).toBe(true);
        expect(FileTextExtractor.isSupportedFileType(type.toUpperCase())).toBe(true);
      });
    });

    it('should return false for unsupported file types', () => {
      const unsupportedTypes = ['exe', 'bin', 'jpg', 'png', 'gif', 'mp4', 'unknown'];

      unsupportedTypes.forEach(type => {
        expect(FileTextExtractor.isSupportedFileType(type)).toBe(false);
      });
    });
  });

  describe('extractText', () => {
    it('should extract text from plain text files', async () => {
      const textContent = 'Hello, this is a test document with some content.';
      const buffer = Buffer.from(textContent, 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'txt');

      expect(result.text).toBe(textContent);
      expect(result.metadata?.extractionMethod).toBe('buffer-to-string');
      expect(result.metadata?.chars).toBe(textContent.length);
      expect(result.metadata?.words).toBeGreaterThan(0);
    });

    it('should extract text from markdown files', async () => {
      const markdownContent = '# Title\n\nThis is **bold** text and *italic* text.';
      const buffer = Buffer.from(markdownContent, 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'md');

      expect(result.text).toBe(markdownContent);
      expect(result.metadata?.extractionMethod).toBe('buffer-to-string');
    });

    it('should extract text from JSON files', async () => {
      const jsonContent = '{"name": "test", "description": "A test JSON file"}';
      const buffer = Buffer.from(jsonContent, 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'json');

      expect(result.text).toBe(jsonContent);
      expect(result.metadata?.extractionMethod).toBe('buffer-to-string');
    });

    it('should extract text from CSV files', async () => {
      const csvContent = 'Name,Age,City\nJohn,25,New York\nJane,30,Los Angeles';
      const buffer = Buffer.from(csvContent, 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'csv');

      expect(result.text).toBe(csvContent);
      expect(result.metadata?.extractionMethod).toBe('buffer-to-string');
    });

    it('should handle empty files gracefully', async () => {
      const buffer = Buffer.from('', 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'txt');

      expect(result.text).toBe('');
      expect(result.metadata?.chars).toBe(0);
      expect(result.metadata?.words).toBe(0);
    });

    it('should reject files that are too large', async () => {
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024); // 11MB

      await expect(
        FileTextExtractor.extractText(largeBuffer, 'txt', { maxSizeBytes: 10 * 1024 * 1024 })
      ).rejects.toThrow('File size');
    });

    it('should reject unsupported file types', async () => {
      const buffer = Buffer.from('test content', 'utf-8');

      await expect(FileTextExtractor.extractText(buffer, 'unsupported')).rejects.toThrow(
        'Unsupported file type'
      );
    });

    it('should handle extraction timeout for binary files', async () => {
      // Create a large buffer that might cause timeout in PDF processing
      const largeBuffer = Buffer.alloc(1024 * 1024, 'fake pdf content'); // 1MB of fake PDF

      // Test timeout with PDF extraction which is more likely to timeout
      await expect(
        FileTextExtractor.extractText(largeBuffer, 'pdf', { timeout: 10 }) // 10ms timeout
      ).rejects.toThrow();
    }, 15000); // Allow up to 15 seconds for this test

    it('should handle different encodings', async () => {
      const content = 'Test content with special chars: é, ñ, ü';
      const buffer = Buffer.from(content, 'utf-8');

      const result = await FileTextExtractor.extractText(buffer, 'txt', { encoding: 'utf-8' });

      expect(result.text).toBe(content);
    });
  });

  describe('PDF extraction', () => {
    it('should attempt to extract text from PDF files', async () => {
      // Create a minimal PDF buffer (this might not work with pdf-parse, but we test the flow)
      const pdfContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT /F1 12 Tf 100 700 Td (Test PDF) Tj ET
endstream endobj
trailer<</Size 5/Root 1 0 R>>
%%EOF`;

      const buffer = Buffer.from(pdfContent, 'binary');

      try {
        const result = await FileTextExtractor.extractText(buffer, 'pdf');
        // If successful, check the result
        expect(result.metadata?.extractionMethod).toBe('pdf-parse');
        expect(typeof result.text).toBe('string');
      } catch (error) {
        // PDF parsing might fail with our minimal PDF, which is expected
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toContain('PDF extraction failed');
      }
    });
  });

  describe('DOCX extraction', () => {
    it('should handle DOCX extraction attempts', async () => {
      // Create a minimal buffer (not a real DOCX, but test the error handling)
      const buffer = Buffer.from('fake docx content', 'utf-8');

      try {
        const result = await FileTextExtractor.extractText(buffer, 'docx');
        expect(result.metadata?.extractionMethod).toBe('mammoth');
      } catch (error) {
        // Expected to fail with fake content
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toContain('DOCX extraction failed');
      }
    });
  });

  describe('XLSX extraction', () => {
    it('should handle XLSX extraction attempts', async () => {
      // Create a minimal buffer (not a real XLSX, but test the error handling)
      const buffer = Buffer.from('fake xlsx content', 'utf-8');

      try {
        const result = await FileTextExtractor.extractText(buffer, 'xlsx');
        expect(result.metadata?.extractionMethod).toBe('xlsx');
      } catch (error) {
        // Expected to fail with fake content
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toContain('Excel extraction failed');
      }
    });
  });

  describe('sanitizeText', () => {
    it('should remove control characters', () => {
      const dirtyText = 'Hello\x00\x01\x02World\x7F\x80Test';
      const cleaned = FileTextExtractor.sanitizeText(dirtyText);

      expect(cleaned).toBe('Hello World Test');
    });

    it('should preserve newlines and tabs', () => {
      const textWithWhitespace = 'Hello\nWorld\tTest\r\nMore';
      const cleaned = FileTextExtractor.sanitizeText(textWithWhitespace);

      expect(cleaned).toContain('Hello');
      expect(cleaned).toContain('World');
      expect(cleaned).toContain('Test');
    });

    it('should normalize excessive whitespace', () => {
      const textWithSpaces = 'Hello     World\n\n\nTest';
      const cleaned = FileTextExtractor.sanitizeText(textWithSpaces);

      expect(cleaned).toBe('Hello World Test');
    });

    it('should trim leading and trailing whitespace', () => {
      const textWithPadding = '   Hello World   ';
      const cleaned = FileTextExtractor.sanitizeText(textWithPadding);

      expect(cleaned).toBe('Hello World');
    });
  });

  describe('getExtractionStats', () => {
    it('should return correct statistics', () => {
      const result = {
        text: 'Hello world this is a test',
        metadata: {
          extractionMethod: 'buffer-to-string',
          chars: 26,
          words: 6,
        },
      };

      const stats = FileTextExtractor.getExtractionStats(result);

      expect(stats.textLength).toBe(26);
      expect(stats.wordCount).toBe(6);
      expect(stats.extractionMethod).toBe('buffer-to-string');
      expect(stats.hasWarnings).toBe(false);
    });

    it('should handle missing metadata', () => {
      const result = {
        text: 'Hello world',
        metadata: {
          extractionMethod: 'unknown',
        },
      };

      const stats = FileTextExtractor.getExtractionStats(result);

      expect(stats.textLength).toBe(11);
      expect(stats.wordCount).toBe(0); // No words metadata
      expect(stats.extractionMethod).toBe('unknown');
      expect(stats.hasWarnings).toBe(false);
    });

    it('should detect warnings', () => {
      const result = {
        text: 'Hello world',
        metadata: {
          extractionMethod: 'pdf-parse',
          warnings: ['Some warning'],
        },
      };

      const stats = FileTextExtractor.getExtractionStats(result);

      expect(stats.hasWarnings).toBe(true);
    });
  });

  describe('extractText convenience function', () => {
    it('should extract and sanitize text', async () => {
      const dirtyText = 'Hello\x00World   with   spaces\n\n';
      const buffer = Buffer.from(dirtyText, 'utf-8');

      const result = await extractText(buffer, 'txt');

      expect(result).toBe('Hello World with spaces');
    });
  });
});
