import {
  RedisKeyMonitor,
  KeyStructureAlert,
  MonitoringConfig,
} from '../../src/services/redis-key-monitor';
import { RedisClient } from '../../src/services/embedding-index/redis-embedding-index';

// Mock Redis client
const createMockRedisClient = (): RedisClient => ({
  hset: jest.fn(),
  hget: jest.fn(),
  hdel: jest.fn(),
  hgetall: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  expire: jest.fn(),
  ping: jest.fn(),
  pipeline: jest.fn(),
  exec: jest.fn(),
  exists: jest.fn(),
  sadd: jest.fn(),
  ttl: jest.fn(),
});

describe('RedisKeyMonitor', () => {
  let monitor: RedisKeyMonitor;
  let mockRedis: RedisClient;
  let config: MonitoringConfig;

  beforeEach(() => {
    mockRedis = createMockRedisClient();
    config = {
      checkIntervalMs: 1000,
      alertThreshold: 5,
      organizations: ['test-org'],
      enableRealTimeAlerts: true,
    };
    monitor = new RedisKeyMonitor(mockRedis, config);
  });

  afterEach(() => {
    monitor.stopMonitoring();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create monitor with correct configuration', () => {
      expect(monitor).toBeInstanceOf(RedisKeyMonitor);
      expect(monitor.getStatus().config).toEqual(config);
      expect(monitor.getStatus().isMonitoring).toBe(false);
    });
  });

  describe('startMonitoring', () => {
    it('should start monitoring and set up interval', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');
      (mockRedis.keys as jest.Mock).mockResolvedValue([]);

      await monitor.startMonitoring();

      expect(monitor.getStatus().isMonitoring).toBe(true);
    });

    it('should not start monitoring if already active', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');
      (mockRedis.keys as jest.Mock).mockResolvedValue([]);

      await monitor.startMonitoring();
      await monitor.startMonitoring(); // Second call

      expect(monitor.getStatus().isMonitoring).toBe(true);
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring and clear interval', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');
      (mockRedis.keys as jest.Mock).mockResolvedValue([]);

      await monitor.startMonitoring();
      monitor.stopMonitoring();

      expect(monitor.getStatus().isMonitoring).toBe(false);
    });

    it('should handle stopping when not monitoring', () => {
      expect(() => monitor.stopMonitoring()).not.toThrow();
    });
  });

  describe('performKeyStructureCheck', () => {
    it('should detect incorrect keys and create alerts', async () => {
      const incorrectKeys = [
        'doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26::1',
        'doc-5d32ac26-5d32-ac26-5d32-ac265d32ac26::2',
      ];
      const correctKeys = ['embeddings:test-org:doc-7f36e93e-7f36-e93e-7f36-e93e7f36e93e::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys]) // doc-*::*
        .mockResolvedValueOnce(correctKeys); // embeddings:org-*

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts).toHaveLength(1);
      expect(alerts[0].severity).toBe('warning');
      expect(alerts[0].details.incorrectKeys).toEqual(incorrectKeys);
      expect(alerts[0].details.totalIncorrectKeys).toBe(2);
      expect(alerts[0].details.totalCorrectKeys).toBe(1);
    });

    it('should not create alerts when no incorrect keys found', async () => {
      const correctKeys = ['embeddings:test-org:doc-7f36e93e-7f36-e93e-7f36-e93e7f36e93e::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce(correctKeys) // doc-*::*
        .mockResolvedValueOnce(correctKeys); // embeddings:org-*

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts).toHaveLength(0);
    });

    it('should handle Redis errors gracefully', async () => {
      (mockRedis.keys as jest.Mock).mockRejectedValue(new Error('Redis connection failed'));

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts).toHaveLength(1);
      expect(alerts[0].severity).toBe('error');
      expect(alerts[0].message).toContain('Failed to check key structure');
    });
  });

  describe('alert severity determination', () => {
    it('should return critical for high incorrect key count', async () => {
      const incorrectKeys = Array.from({ length: 10 }, (_, i) => `doc-test-${i}::1`);
      const correctKeys = ['embeddings:test-org:doc-test::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts[0].severity).toBe('critical');
    });

    it('should return error for high percentage of incorrect keys', async () => {
      const incorrectKeys = ['doc-test-1::1', 'doc-test-2::1', 'doc-test-3::1'];
      const correctKeys = ['embeddings:test-org:doc-test::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts[0].severity).toBe('error');
    });

    it('should return warning for low percentage of incorrect keys', async () => {
      const incorrectKeys = ['doc-test-1::1'];
      const correctKeys = [
        'embeddings:test-org:doc-test-1::1',
        'embeddings:test-org:doc-test-2::1',
        'embeddings:test-org:doc-test-3::1',
        'embeddings:test-org:doc-test-4::1',
        'embeddings:test-org:doc-test-5::1',
      ];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      const alerts = await monitor.performKeyStructureCheck();

      expect(alerts[0].severity).toBe('warning');
    });
  });

  describe('alert callbacks', () => {
    it('should trigger alert callbacks when alerts are generated', async () => {
      const mockCallback = jest.fn();
      monitor.onAlert(mockCallback);

      const incorrectKeys = ['doc-test-1::1'];
      const correctKeys = ['embeddings:test-org:doc-test::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      await monitor.performKeyStructureCheck();

      expect(mockCallback).toHaveBeenCalledTimes(1);
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          severity: 'warning',
          details: expect.objectContaining({
            incorrectKeys,
            totalIncorrectKeys: 1,
          }),
        })
      );
    });

    it('should handle callback errors gracefully', async () => {
      const mockCallback = jest.fn().mockImplementation(() => {
        throw new Error('Callback error');
      });
      monitor.onAlert(mockCallback);

      const incorrectKeys = ['doc-test-1::1'];
      const correctKeys = ['embeddings:test-org:doc-test::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      // Should not throw
      await expect(monitor.performKeyStructureCheck()).resolves.toBeDefined();
    });
  });

  describe('getStatistics', () => {
    it('should return correct statistics for organizations', async () => {
      const incorrectKeys = ['doc-test-1::1', 'doc-test-2::1'];
      const correctKeys = [
        'embeddings:test-org:doc-test-1::1',
        'embeddings:test-org:doc-test-2::1',
        'embeddings:test-org:doc-test-3::1',
      ];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      const stats = await monitor.getStatistics();

      expect(stats.organizations['test-org']).toEqual({
        totalKeys: 5,
        correctKeys: 3,
        incorrectKeys: 2,
        incorrectPercentage: 40,
      });

      expect(stats.summary).toEqual({
        totalOrganizations: 1,
        totalKeys: 5,
        totalCorrectKeys: 3,
        totalIncorrectKeys: 2,
        overallIncorrectPercentage: 40,
      });
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status when monitoring is active and no issues', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');
      (mockRedis.keys as jest.Mock).mockResolvedValue([]);

      await monitor.startMonitoring();
      const health = await monitor.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.message).toBe('Redis key monitor is healthy');
    });

    it('should return unhealthy status when monitoring is not active', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');

      const health = await monitor.healthCheck();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toBe('Monitoring is not active');
    });

    it('should return unhealthy status when Redis connection fails', async () => {
      (mockRedis.ping as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      const health = await monitor.healthCheck();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toContain('Health check failed');
    });

    it('should return unhealthy status when critical alerts are detected', async () => {
      (mockRedis.ping as jest.Mock).mockResolvedValue('PONG');
      const incorrectKeys = Array.from({ length: 10 }, (_, i) => `doc-test-${i}::1`);
      const correctKeys = ['embeddings:org-2dd032ac-2dd0-32ac-2dd0-32ac2dd032ac:doc-test::1'];

      (mockRedis.keys as jest.Mock)
        .mockResolvedValueOnce([...incorrectKeys, ...correctKeys])
        .mockResolvedValueOnce(correctKeys);

      await monitor.startMonitoring();
      const health = await monitor.healthCheck();

      expect(health.status).toBe('unhealthy');
      expect(health.message).toBe('Critical key structure issues detected');
    });
  });

  describe('removeAlertCallback', () => {
    it('should remove alert callback', () => {
      const mockCallback = jest.fn();
      monitor.onAlert(mockCallback);
      monitor.removeAlertCallback(mockCallback);

      // Verify callback is removed by checking internal state
      // Note: This is testing implementation details, but necessary for coverage
      expect(monitor).toBeDefined();
    });
  });
});
