import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PromptRenderer } from '../../src/services/prompt/prompt-renderer';
import { Prompt, PromptMessage, PromptRenderingContext } from '../../src/types/prompt';

describe('PromptRenderer', () => {
  let renderer: PromptRenderer;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    renderer = new PromptRenderer(mockLogger);
  });

  describe('renderPrompt', () => {
    it('should render text prompts with variables', () => {
      const prompt: Prompt = {
        id: 'test-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, welcome to {{platform}}!',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        config: {},
        tags: [],
        labels: [],
      };

      const context: PromptRenderingContext = {
        variables: {
          name: '<PERSON>',
          platform: 'Anter AI',
        },
      };

      const result = renderer.renderPrompt(prompt, context);

      expect(result.renderedPrompt).toBe('Hello John, welcome to Anter AI!');
      expect(result.usedVariables).toEqual(['name', 'platform']);
      expect(result.missingVariables).toEqual([]);
    });

    it('should handle missing variables', () => {
      const prompt: Prompt = {
        id: 'test-2',
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, your age is {{age}}',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        config: {},
        tags: [],
        labels: [],
      };

      const context: PromptRenderingContext = {
        variables: {
          name: 'John',
        },
      };

      const result = renderer.renderPrompt(prompt, context);

      expect(result.renderedPrompt).toBe('Hello John, your age is {{age}}');
      expect(result.usedVariables).toEqual(['name']);
      expect(result.missingVariables).toEqual(['age']);
    });
  });

  describe('extractVariables', () => {
    it('should extract variables from text prompt', () => {
      const prompt: Prompt = {
        id: 'test-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, welcome to {{platform}}!',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        config: {},
        tags: [],
        labels: [],
      };

      const variables = renderer.extractVariables(prompt);

      expect(variables).toEqual(['name', 'platform']);
    });
  });

  describe('validateContext', () => {
    it('should validate complete context', () => {
      const prompt: Prompt = {
        id: 'test-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationId: 'test-org',
        createdBy: 'test-user',
        prompt: 'Hello {{name}}, welcome to {{platform}}!',
        name: 'test-prompt',
        version: 1,
        type: 'text',
        config: {},
        tags: [],
        labels: [],
      };

      const context: PromptRenderingContext = {
        variables: {
          name: 'John',
          platform: 'Anter AI',
        },
      };

      const result = renderer.validateContext(prompt, context);

      expect(result.isValid).toBe(true);
      expect(result.missingVariables).toEqual([]);
    });
  });
});
