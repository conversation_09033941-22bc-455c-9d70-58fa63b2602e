# Text Chunking Implementation for Large Documents

## Overview

This implementation provides intelligent text chunking for large documents that exceed the token limits of the `text-embedding-3-large` model. The solution ensures semantic coherence while maintaining optimal performance for embedding generation and storage.

## Key Features

### 1. Intelligent Text Chunking

- **Token-aware splitting**: Respects the `text-embedding-3-large` token limits (8,000 tokens default)
- **Semantic boundary preservation**: Avoids breaking mid-sentence or mid-paragraph
- **Overlap strategies**: Prevents loss of context at chunk boundaries
- **Configurable options**: Customizable chunk size, overlap, and preservation rules

### 2. Redis Key Pattern Implementation

- **Incremental numbering**: `embeddings:org-{orgId}:doc-{docId}::{chunkNumber}`
- **Example keys**:
  - `embeddings:org-{organization-id}:doc-{document-id}::1`
  - `embeddings:org-{organization-id}:doc-{document-id}::2`
  - `embeddings:org-{organization-id}:doc-{document-id}::3`

### 3. Enhanced Service Integration

- **Backward compatibility**: Works with existing single-chunk documents
- **Automatic chunking detection**: Only chunks documents that exceed token limits
- **Aggregated search results**: Combines chunks during search operations
- **Comprehensive logging**: Detailed logging for debugging and monitoring

## Architecture

### Core Components

1. **TextChunkingService** (`text-chunking-service.ts`)

   - Handles document chunking logic
   - Manages token estimation and boundary detection
   - Provides Redis key generation utilities

2. **Enhanced SemanticSearchService** (`semantic-search-service-enhanced.ts`)

   - Integrates chunking into indexing process
   - Handles chunk aggregation during search
   - Maintains backward compatibility

3. **Enhanced RedisEmbeddingIndex** (`redis-embedding-index.ts`)
   - Supports chunked document keys
   - Handles both regular and chunked document patterns
   - Optimized batch operations

### Data Flow

```
Document Input → Token Estimation → Chunking Decision → Chunk Generation → Embedding Creation → Redis Storage
                                                                    ↓
Search Query → Embedding Search → Chunk Retrieval → Content Aggregation → Result Ranking → Final Results
```

## Implementation Details

### Token Estimation

```typescript
// Conservative estimate for text-embedding-3-large
private readonly TOKENS_PER_CHAR = 0.25; // 1 token ≈ 4 characters

estimateTokenCount(text: string): number {
  return Math.ceil(text.length * this.TOKENS_PER_CHAR);
}
```

### Chunking Strategy

1. **Paragraph-level splitting**: Preserves paragraph boundaries when possible
2. **Sentence-level splitting**: Falls back to sentence boundaries for large paragraphs
3. **Overlap management**: Implements configurable overlap to maintain context
4. **Minimum chunk size**: Ensures chunks are meaningful and not too small

### Redis Key Management

```typescript
// Generate chunk key
generateChunkKey(organizationId: string, documentId: string, chunkNumber: number): string {
  return `embeddings:org-${organizationId}:doc-${documentId}::${chunkNumber}`;
}

// Parse chunk key
parseChunkKey(key: string): {
  organizationId: string;
  documentId: string;
  chunkNumber: number;
} | null;
```

## Usage Examples

### Basic Chunking

```typescript
import { TextChunkingService } from './text-chunking-service';

const chunkingService = new TextChunkingService();

const document = {
  id: 'doc-123',
  name: 'Large Document',
  content: 'Very large document content...',
  metadata: { type: 'policy' },
};

const chunkedDoc = await chunkingService.chunkDocument(document);
console.log(`Document split into ${chunkedDoc.totalChunks} chunks`);
```

### Custom Chunking Options

```typescript
const options = {
  maxTokens: 6000, // Custom token limit
  overlapTokens: 300, // Overlap between chunks
  preserveParagraphs: true, // Preserve paragraph boundaries
  preserveSentences: true, // Preserve sentence boundaries
  minChunkSize: 500, // Minimum tokens per chunk
};

const chunkedDoc = await chunkingService.chunkDocument(document, options);
```

### Enhanced Indexing

```typescript
import { SemanticSearchServiceEnhanced } from './semantic-search-service-enhanced';

const searchService = new SemanticSearchServiceEnhanced();

// Automatically handles chunking for large documents
const result = await searchService.indexDocumentsIncremental(documents, orgId, {
  maxTokens: 8000,
  overlapTokens: 200,
  useRedis: true,
});

console.log(`Indexed ${result.indexed} documents, created ${result.totalChunks} chunks`);
```

### Enhanced Search

```typescript
// Search automatically aggregates chunked documents
const results = await searchService.search(query, {
  organizationId: orgId,
  topK: 5,
  threshold: 0.7,
});

// Results include aggregated content from chunks
results.forEach(result => {
  console.log(`Document: ${result.documentId}`);
  console.log(`Content: ${result.content.substring(0, 200)}...`);
  console.log(`Is chunked: ${result.metadata?.isChunked}`);
  console.log(`Total chunks: ${result.metadata?.totalChunks}`);
});
```

## Configuration Options

### ChunkingOptions Interface

```typescript
interface ChunkingOptions {
  maxTokens?: number; // Maximum tokens per chunk (default: 8000)
  overlapTokens?: number; // Overlap tokens between chunks (default: 200)
  preserveParagraphs?: boolean; // Preserve paragraph boundaries (default: true)
  preserveSentences?: boolean; // Preserve sentence boundaries (default: true)
  minChunkSize?: number; // Minimum tokens per chunk (default: 100)
}
```

### Default Values

- **maxTokens**: 8000 (conservative limit for text-embedding-3-large)
- **overlapTokens**: 200 (prevents context loss)
- **preserveParagraphs**: true (maintains document structure)
- **preserveSentences**: true (ensures semantic coherence)
- **minChunkSize**: 100 (ensures meaningful chunks)

## Performance Considerations

### Optimization Strategies

1. **Batch Processing**: Process multiple documents and chunks in batches
2. **Embedding Caching**: Cache embeddings to avoid regeneration
3. **Redis Pipeline**: Use Redis pipeline for batch operations
4. **Lazy Loading**: Only chunk documents when necessary

### Memory Management

- **Streaming**: Process large documents in streams
- **Garbage Collection**: Clear temporary data after processing
- **Cache Limits**: Implement LRU cache with size limits

### Search Performance

- **Chunk Aggregation**: Efficiently combine chunks during search
- **Similarity Weighting**: Weight chunk similarities appropriately
- **Result Deduplication**: Avoid duplicate results from overlapping chunks

## Error Handling

### Common Scenarios

1. **Token Limit Exceeded**: Graceful fallback to smaller chunks
2. **Redis Connection Issues**: Fallback to in-memory storage
3. **Embedding Generation Failures**: Retry with smaller chunks
4. **Chunk Reconstruction Errors**: Fallback to concatenation

### Monitoring and Logging

```typescript
// Comprehensive logging for debugging
logger.info('[SemanticSearch] Document chunking stats', {
  organizationId,
  documentId: doc.id,
  estimatedTokens: chunkingStats.estimatedTokens,
  needsChunking: chunkingStats.needsChunking,
  estimatedChunks: chunkingStats.estimatedChunks,
});
```

## Testing

### Unit Tests

Comprehensive test suite covering:

- Token estimation accuracy
- Chunking boundary preservation
- Redis key generation and parsing
- Document reconstruction
- Error handling scenarios

### Integration Tests

- End-to-end chunking and search workflows
- Performance benchmarks
- Memory usage validation
- Redis integration testing

## Migration Guide

### From Single-Chunk to Multi-Chunk

1. **Backward Compatibility**: Existing documents continue to work
2. **Gradual Migration**: Only new large documents are chunked
3. **Data Consistency**: Chunked documents maintain original content integrity
4. **Search Compatibility**: Search results are automatically aggregated

### Redis Data Migration

```typescript
// Existing keys remain unchanged
// New chunked documents use new key pattern
// Both patterns are supported in search operations
```

## Troubleshooting

### Common Issues

1. **Chunks too small**: Adjust `minChunkSize` option
2. **Poor search results**: Increase `overlapTokens` for better context
3. **Memory issues**: Reduce `maxTokens` or implement streaming
4. **Redis performance**: Use pipeline operations for batch processing

### Debug Information

```typescript
// Enable debug logging
const chunkingStats = service.getChunkingStats(content, options);
console.log('Chunking statistics:', chunkingStats);

// Check chunk reconstruction
const reconstructed = service.reconstructDocument(chunks);
console.log('Reconstruction successful:', reconstructed.length === originalContent.length);
```

## Future Enhancements

### Planned Features

1. **Adaptive Chunking**: Dynamic chunk size based on content complexity
2. **Semantic Chunking**: AI-powered content boundary detection
3. **Compression**: Optimize storage for large document collections
4. **Distributed Processing**: Support for distributed chunking across nodes

### Performance Improvements

1. **Parallel Processing**: Concurrent chunking and embedding generation
2. **Streaming APIs**: Real-time chunking for large documents
3. **Caching Strategies**: Multi-level caching for frequently accessed chunks
4. **Index Optimization**: Vector index optimization for chunked documents
