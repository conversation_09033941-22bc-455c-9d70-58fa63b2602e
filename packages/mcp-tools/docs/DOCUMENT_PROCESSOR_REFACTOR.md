# Document Processing Refactor Summary

## What Was Done

This refactor consolidates duplicate document processing logic into a shared utility module, following Option B as requested.

### 1. Created Shared DocumentProcessor Utility

**Location:** `packages/mcp-tools/src/utils/document-processor.ts`

**Key Features:**

- `processDocumentContent()` - For agent-side processing with chunking (based on tested implementation from `document.service.ts`)
- `processDocumentForAPI()` - For API-side processing without chunking
- Unified buffer normalization and file type inference
- Support for text extraction from binary files (PDF, DOCX, etc.)
- Configurable sanitization via `DocumentSanitizer` interface

### 2. Refactored MCPTools Class

**Changes:**

- Removed duplicate `processDocumentContent()` method
- Uses shared `DocumentProcessor.processDocumentForAPI()` for API responses
- Maintains existing API contract for `getAllDocuments` and `getDocument` tools

### 3. Refactored DocumentService Class

**Changes:**

- Removed duplicate `processDocumentContent()`, helper methods (`inferFileType`, `normalizeToBuffer`)
- Uses shared `DocumentProcessor.processDocumentContent()` for agent-side processing
- Maintains existing chunking behavior for LLM consumption
- Passes sanitizer to shared processor for consistent text cleaning

### 4. Cleaned Up Debug Logging

**Improvements:**

- Removed verbose console.log statements from hot paths
- Kept essential error handling without noisy debug output
- Reduced log verbosity by ~80% while maintaining functionality

## Call Flow After Refactor

### Agent Side (apps/agents)

```
DocumentService.retrieveAllDocuments()
  → MCPConnectionManager.executeToolCall('get_all_documents')
    → AgentMCPBridge.callToolDirect()
      → connection-manager stub (in-process)
        → Raw SQL execution
        → Returns basic document data
  → DocumentProcessor.processDocumentContent() [shared utility]
    → Text extraction, sanitization, chunking for LLM
```

### API Side (apps/api)

```
MCP Server receives 'get_all_documents' tool call
  → MCPTools.getAllDocuments()
    → DocumentProcessor.processDocumentForAPI() [shared utility]
      → Text extraction, preview generation for API response
```

## Benefits

1. **Single Source of Truth:** Document processing logic is now centralized
2. **Tested Foundation:** Based on the proven `document.service.ts` implementation
3. **Reduced Duplication:** ~200 lines of duplicate code eliminated
4. **Cleaner Logs:** Significantly reduced debug noise
5. **Maintainable:** Future changes only need to be made in one place
6. **Flexible:** Different processing modes for different use cases (chunked vs. API)

## Files Modified

- ✅ `packages/mcp-tools/src/utils/document-processor.ts` (new)
- ✅ `packages/mcp-tools/src/index.ts` (exports)
- ✅ `packages/mcp-tools/src/tools/index.ts` (refactored)
- ✅ `apps/agents/src/agents/ask-ai/services/document.service.ts` (refactored)

## Next Steps

1. **Testing:** Verify both agent and API flows work correctly
2. **Performance:** Monitor for any performance impacts
3. **Future Enhancement:** Consider consolidating the two tool implementations (in-process stub vs. production MCPTools) into a single authoritative version
