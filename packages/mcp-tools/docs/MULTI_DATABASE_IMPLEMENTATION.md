# Multi-Database Implementation Guide

## Overview

This implementation provides support for multiple database connections with different schemas while maintaining backward compatibility with existing agents. The system supports both internal and external databases with conditional Row Level Security (RLS) support.

## Architecture

### Database Connection Manager

The `DatabaseConnectionManager` class manages multiple database connections:

- **External Database**: Current main database with RLS enabled
- **Internal Database**: New database with different schema, RLS support configurable

### Key Components

1. **Enhanced Database Provider** (`EnhancedDatabaseProviderImpl`)

   - Supports both internal and external database operations
   - Provides unified interface for database queries
   - Handles RLS configuration per database

2. **Database Schema Adapter** (`DatabaseSchemaAdapter`)

   - Converts between different database schemas
   - Provides unified document format for backward compatibility
   - Handles SQL query generation for different schemas

3. **Internal Database Tool** (`GetAllInternalDatabaseTool`)
   - MCP tool for retrieving documents from internal database
   - Maintains same response format as external database tool
   - Supports all filtering and pagination features

## Database Schemas

### External Database Schema (Current)

```sql
CREATE TABLE file (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  document_type TEXT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  file_type TEXT,
  content TEXT,
  buffer_file BYTEA,
  organization_id TEXT NOT NULL
);
```

### Internal Database Schema (New)

```sql
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  size INTEGER NOT NULL,
  upload_date TIMESTAMP NOT NULL DEFAULT NOW(),
  category TEXT,
  tags JSONB,
  uploaded_by TEXT NOT NULL,
  organization_id TEXT NOT NULL,
  project_id TEXT,
  file_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE file_metadata (
  id TEXT PRIMARY KEY,
  file_type TEXT,
  content TEXT,
  buffer_file BYTEA
);
```

## Environment Configuration

Add the following environment variables:

```bash
# Internal database connection (optional, defaults to external)
DATABASE_URL_INTERNAL=postgresql://user:pass@host:port/internal_db

# RLS support for internal database (default: false)
INTERNAL_DB_HAS_RLS=false
```

## Usage

### Fastify Integration

The enhanced database connection manager is automatically registered with Fastify:

```typescript
// External database (current)
const db = await fastify.dbWithTenant(organizationId);
const dbBypass = await fastify.dbBypassRLS();

// Internal database (new)
const dbInternal = await fastify.dbInternalWithTenant(organizationId);
const dbInternalBypass = await fastify.dbInternalBypassRLS();
```

### MCP Tool Usage

The internal database tool is available as an MCP tool:

```json
{
  "method": "tools/call",
  "params": {
    "name": "get_all_internal_database",
    "arguments": {
      "limit": 50,
      "offset": 0,
      "filter": {
        "documentType": "pdf",
        "category": "security",
        "status": "active"
      }
    }
  }
}
```

### Response Format

Both internal and external database tools return the same format for backward compatibility:

```json
{
  "success": true,
  "documents": [
    {
      "id": "doc_123",
      "name": "Security Policy.pdf",
      "documentType": "pdf",
      "fileType": "application/pdf",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "content": "Document content...",
      "buffer_file": null,
      "category": "security",
      "tags": ["policy", "security"],
      "status": "active"
    }
  ],
  "pagination": {
    "total": 100,
    "limit": 50,
    "offset": 0,
    "returned": 50,
    "hasMore": true
  },
  "source": {
    "database": "internal",
    "schema": "documents",
    "hasRLS": false
  }
}
```

## RLS Support

### Current Implementation

- External database: RLS always enabled
- Internal database: RLS configurable via `INTERNAL_DB_HAS_RLS` environment variable

### Future RLS Implementation

When RLS is enabled for the internal database, the system will:

1. Set PostgreSQL session variables for tenant isolation
2. Apply RLS policies automatically
3. Support bypass operations for administrative tasks

### RLS Configuration Example

```sql
-- Enable RLS on internal database
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY documents_organization_policy ON documents
  FOR ALL USING (organization_id = current_setting('app.current_organization_id')::text);
```

## Security Features

1. **SQL Injection Prevention**

   - All user inputs are properly escaped
   - Parameterized queries where possible
   - Input validation and sanitization

2. **Tenant Isolation**

   - Organization-based filtering
   - RLS support for additional security layer
   - Session-based context management

3. **Query Validation**
   - Read-only operations enforced
   - Dangerous keywords blocked
   - Multiple statement execution prevented

## Migration Guide

### From Single Database to Multi-Database

1. **Update Environment Variables**

   ```bash
   # Add internal database configuration
   DATABASE_URL_INTERNAL=postgresql://user:pass@host:port/internal_db
   INTERNAL_DB_HAS_RLS=false
   ```

2. **Update Fastify Plugin**

   ```typescript
   // Replace old drizzle plugin
   import enhancedDrizzlePlugin from './lib/drizzle/connection-manager';
   await fastify.register(enhancedDrizzlePlugin);
   ```

3. **Update Database Provider**
   ```typescript
   // Use enhanced database provider
   const enhancedProvider = new EnhancedDatabaseProviderImpl({
     dbWithTenant: orgId => fastify.dbWithTenant(orgId),
     dbInternalWithTenant: orgId => fastify.dbInternalWithTenant(orgId),
     log: fastify.log,
   });
   ```

### Schema Migration

1. **Create Internal Database Schema**

   ```sql
   -- Run the internal database migration
   -- This creates the documents and file_metadata tables
   ```

2. **Data Migration (if needed)**
   ```sql
   -- Example: Migrate data from external to internal database
   INSERT INTO internal_db.documents (
     id, name, type, organization_id, created_at, updated_at
   )
   SELECT
     id, name, document_type, organization_id, created_at, updated_at
   FROM external_db.file;
   ```

## Testing

### Unit Tests

```bash
# Test database tools
pnpm test packages/mcp-tools/src/tools/database/

# Test schema adapter
pnpm test packages/mcp-tools/src/tools/database/database-schema-adapter.test.ts
```

### Integration Tests

```bash
# Test multi-database functionality
pnpm test apps/api/test/integration/multi-database.test.ts
```

### Manual Testing

1. Set up both database connections
2. Test internal database tool via MCP
3. Verify response format compatibility
4. Test RLS functionality (when enabled)

## Monitoring and Observability

### Database Metrics

- Connection pool status
- Query performance
- RLS policy effectiveness
- Error rates per database

### Logging

```typescript
// Database connection logs
console.log(`Database Connection Manager initialized:
  - External DB: ${externalHasRLS ? 'RLS Enabled' : 'RLS Disabled'}
  - Internal DB: ${internalHasRLS ? 'RLS Enabled' : 'RLS Disabled'}
`);
```

### Health Checks

```typescript
// Health check endpoint
app.get('/health/databases', async (req, reply) => {
  const externalHealth = await checkDatabaseHealth('external');
  const internalHealth = await checkDatabaseHealth('internal');

  return {
    external: externalHealth,
    internal: internalHealth,
    timestamp: new Date().toISOString(),
  };
});
```

## Troubleshooting

### Common Issues

1. **Connection Errors**

   - Verify `DATABASE_URL_INTERNAL` is correct
   - Check network connectivity
   - Ensure database exists and is accessible

2. **Schema Mismatches**

   - Verify internal database schema matches expected structure
   - Check table and column names
   - Ensure proper joins between tables

3. **RLS Issues**

   - Verify RLS policies are correctly configured
   - Check session variable settings
   - Ensure organization context is properly set

4. **Performance Issues**
   - Monitor query execution plans
   - Check index usage
   - Verify connection pool settings

### Debug Mode

```typescript
// Enable debug logging
const debugMode = process.env.DATABASE_DEBUG === 'true';

if (debugMode) {
  console.log('Database query:', query);
  console.log('Database result:', result);
}
```

## Future Enhancements

1. **Dynamic Schema Discovery**

   - Automatically detect database schemas
   - Generate adapters dynamically
   - Support schema versioning

2. **Advanced RLS Features**

   - Role-based access control
   - Time-based policies
   - Data masking

3. **Performance Optimizations**

   - Connection pooling improvements
   - Query caching
   - Read replicas support

4. **Monitoring Enhancements**
   - Real-time performance metrics
   - Automated alerting
   - Capacity planning tools
