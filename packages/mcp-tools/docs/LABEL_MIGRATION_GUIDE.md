# Label-Based State Management Migration Guide

## Overview

This guide helps you migrate from the deprecated `isActive` field to the new label-based state management system, following <PERSON><PERSON>'s approach for more flexible prompt state management.

## Why This Change?

The `isActive` field was deprecated because it was too limiting:

- **Single State**: Only allowed "active" or "inactive"
- **No Environment Support**: Couldn't distinguish between production, staging, development
- **No Version States**: Couldn't mark versions as "latest", "deprecated", etc.
- **Hard to Extend**: Adding new states required schema changes

The new label-based system provides:

- **Multiple States**: A prompt can have multiple labels simultaneously
- **Environment Support**: Clear separation between production, staging, development
- **Version States**: Support for "latest", "deprecated", "experimental"
- **Flexible**: Easy to add new labels without schema changes

## Migration Steps

### 1. Update Type Definitions

**Before:**

```typescript
interface Prompt {
  id: string;
  name: string;
  version: number;
  isActive?: boolean; // ❌ Deprecated
  labels: string[];
}
```

**After:**

```typescript
interface Prompt {
  id: string;
  name: string;
  version: number;
  labels: string[]; // ✅ Use labels for state management
}
```

### 2. Update Prompt Creation

**Before:**

```typescript
const prompt = await storage.createPrompt({
  organizationId: 'org1',
  createdBy: 'user1',
  prompt: 'Hello world',
  name: 'greeting',
  version: 1,
  type: 'text',
  isActive: true, // ❌ Deprecated
  labels: ['production'],
});
```

**After:**

```typescript
const prompt = await storage.createPrompt({
  organizationId: 'org1',
  createdBy: 'user1',
  prompt: 'Hello world',
  name: 'greeting',
  version: 1,
  type: 'text',
  labels: ['production', 'latest'], // ✅ Use labels for state
});
```

### 3. Update Prompt Activation

**Before:**

```typescript
// Activate a prompt
await storage.updatePrompt(promptId, { isActive: true });

// Deactivate a prompt
await storage.updatePrompt(promptId, { isActive: false });
```

**After:**

```typescript
// Activate a prompt (add production label)
await storage.updatePrompt(promptId, {
  labels: [...existingLabels, 'production'],
});

// Deactivate a prompt (remove production label)
await storage.updatePrompt(promptId, {
  labels: existingLabels.filter(label => label !== 'production'),
});
```

### 4. Update Prompt Queries

**Before:**

```typescript
// Get active prompts
const activePrompts = await storage.searchPrompts({ isActive: true });

// Get inactive prompts
const inactivePrompts = await storage.searchPrompts({ isActive: false });
```

**After:**

```typescript
// Get production prompts (equivalent to active)
const productionPrompts = await storage.getActivePrompts(organizationId);

// Get prompts by specific labels
const latestPrompts = await storage.getPromptsByLabels(['latest'], organizationId);
const stagingPrompts = await storage.getPromptsByLabels(['staging'], organizationId);

// Get prompts with multiple labels
const productionLatest = await storage.getPromptsByLabels(['production', 'latest'], organizationId);
```

### 5. Update Prompt Retrieval

**Before:**

```typescript
// Get the active prompt for a name
const activePrompt = await storage.searchPrompts({
  name: 'greeting',
  isActive: true,
})[0];
```

**After:**

```typescript
// Get the production prompt for a name
const productionPrompt = await storage.getProductionPrompt(organizationId, 'greeting');
```

## Label Conventions

### Environment Labels

- `"production"` - Live/production environment
- `"staging"` - Staging environment
- `"development"` - Development environment
- `"testing"` - Testing environment

### State Labels

- `"latest"` - Most recent version
- `"deprecated"` - Deprecated version
- `"experimental"` - Experimental feature
- `"archived"` - Archived version
- `"draft"` - Work in progress

### Custom Labels

You can create custom labels for your specific needs:

- `"feature-flag-enabled"` - Feature flag is enabled
- `"a-b-test"` - Part of A/B testing
- `"high-priority"` - High priority prompt
- `"legacy"` - Legacy system prompt

## Migration Examples

### Example 1: Simple Migration

**Before:**

```typescript
// Create and activate a prompt
const prompt = await storage.createPrompt({
  organizationId: 'org1',
  createdBy: 'user1',
  prompt: 'Hello world',
  name: 'greeting',
  version: 1,
  type: 'text',
  isActive: true,
  labels: [],
});

// Get active prompts
const activePrompts = await storage.searchPrompts({ isActive: true });
```

**After:**

```typescript
// Create and activate a prompt
const prompt = await storage.createPrompt({
  organizationId: 'org1',
  createdBy: 'user1',
  prompt: 'Hello world',
  name: 'greeting',
  version: 1,
  type: 'text',
  labels: ['production', 'latest'],
});

// Get production prompts
const productionPrompts = await storage.getActivePrompts('org1');
```

### Example 2: Multi-Environment Setup

**Before:**

```typescript
// Could only have one active prompt per name
const prodPrompt = await storage.createPrompt({
  name: 'greeting',
  isActive: true,
  // ... other fields
});

const stagingPrompt = await storage.createPrompt({
  name: 'greeting',
  isActive: false, // Had to be inactive
  // ... other fields
});
```

**After:**

```typescript
// Can have multiple prompts with different environment labels
const prodPrompt = await storage.createPrompt({
  name: 'greeting',
  labels: ['production', 'latest'],
  // ... other fields
});

const stagingPrompt = await storage.createPrompt({
  name: 'greeting',
  labels: ['staging', 'latest'],
  // ... other fields
});

// Query by environment
const prodPrompts = await storage.getPromptsByLabels(['production'], 'org1');
const stagingPrompts = await storage.getPromptsByLabels(['staging'], 'org1');
```

### Example 3: Version Management

**Before:**

```typescript
// Hard to manage version states
const v1Prompt = await storage.createPrompt({
  name: 'greeting',
  version: 1,
  isActive: false, // Old version
  // ... other fields
});

const v2Prompt = await storage.createPrompt({
  name: 'greeting',
  version: 2,
  isActive: true, // New version
  // ... other fields
});
```

**After:**

```typescript
// Clear version state management
const v1Prompt = await storage.createPrompt({
  name: 'greeting',
  version: 1,
  labels: ['deprecated'], // Old version
  // ... other fields
});

const v2Prompt = await storage.createPrompt({
  name: 'greeting',
  version: 2,
  labels: ['production', 'latest'], // New version
  // ... other fields
});

// Query by version state
const latestPrompts = await storage.getPromptsByLabels(['latest'], 'org1');
const deprecatedPrompts = await storage.getPromptsByLabels(['deprecated'], 'org1');
```

## New Methods Available

### `getPromptsByLabels(labels, organizationId?, projectId?)`

Filter prompts by specific labels.

```typescript
// Get all production prompts
const productionPrompts = await storage.getPromptsByLabels(['production'], 'org1');

// Get prompts with multiple labels
const productionLatest = await storage.getPromptsByLabels(['production', 'latest'], 'org1');

// Get prompts by project
const projectPrompts = await storage.getPromptsByLabels(['staging'], 'org1', 'project1');
```

### `getProductionPrompt(organizationId, name, projectId?)`

Get the production prompt for a specific name.

```typescript
// Get production prompt by name
const productionPrompt = await storage.getProductionPrompt('org1', 'greeting');

// Get production prompt by name and project
const projectPrompt = await storage.getProductionPrompt('org1', 'greeting', 'project1');
```

### Enhanced `getActivePrompts()`

Now filters by "production" label instead of isActive.

```typescript
// Get all production prompts in organization
const activePrompts = await storage.getActivePrompts('org1');

// Get all production prompts in project
const projectActivePrompts = await storage.getActivePrompts('org1', 'project1');
```

## Best Practices

### 1. Use Consistent Label Naming

```typescript
// ✅ Good - Consistent naming
const labels = ['production', 'latest'];

// ❌ Bad - Inconsistent naming
const labels = ['PRODUCTION', 'Latest'];
```

### 2. Combine Labels for Complex States

```typescript
// ✅ Good - Clear state combination
const labels = ['production', 'latest', 'feature-flag-enabled'];

// ❌ Bad - Unclear state
const labels = ['active', 'new'];
```

### 3. Use Labels for Environment Separation

```typescript
// ✅ Good - Clear environment separation
const prodLabels = ['production', 'latest'];
const stagingLabels = ['staging', 'latest'];
const devLabels = ['development', 'experimental'];
```

### 4. Document Custom Labels

```typescript
// ✅ Good - Document custom labels
const labels = [
  'production', // Live environment
  'latest', // Most recent version
  'feature-flag-enabled', // Feature flag is active
  'high-priority', // High priority prompt
];
```

## Troubleshooting

### Common Issues

**Issue**: Getting no results from `getActivePrompts()`
**Solution**: Make sure your prompts have the "production" label

```typescript
// Add production label
await storage.updatePrompt(promptId, {
  labels: [...existingLabels, 'production'],
});
```

**Issue**: Multiple prompts with same name and "production" label
**Solution**: Use `getProductionPrompt()` to get the specific production prompt

```typescript
const productionPrompt = await storage.getProductionPrompt(organizationId, promptName);
```

**Issue**: Need to migrate existing data
**Solution**: Add "production" label to existing active prompts

```typescript
// Migration script
const allPrompts = await storage.getAllPrompts();
for (const prompt of allPrompts) {
  if (prompt.isActive) {
    // If using old data
    await storage.updatePrompt(prompt.id, {
      labels: [...prompt.labels, 'production'],
    });
  }
}
```

## Conclusion

The label-based system provides much more flexibility and power than the old `isActive` field. While the migration requires some code changes, the benefits include:

- **Better Environment Management**: Clear separation between production, staging, development
- **Improved Version Control**: Support for latest, deprecated, experimental states
- **Enhanced Flexibility**: Easy to add new states without schema changes
- **Better Querying**: More powerful filtering and search capabilities

Take your time with the migration and use the new methods to their full potential!
