# @anter/mcp-tools

MCP (Model Context Protocol) tools and task management for the AskInfoSec platform.

## Purpose

This package provides a collection of tools for the Model Context Protocol implementation, including:

- Database query tools with tenant isolation
- Context management for session data
- Advanced data analysis capabilities
- System status monitoring
- Async task management
- SQL query safety validation

## Architecture

The package uses an adapter pattern to decouple from specific frameworks:

```typescript
import { MCPTools, ToolCategory } from '@anter/mcp-tools';

// Create adapters for your environment
const dependencies = {
  database: {
    withTenant: (orgId: string) => yourDb.withTenant(orgId),
    execute: (query: any) => yourDb.execute(query),
  },
  cache: {
    get: (key: string) => yourRedis.get(key),
    set: (key: string, value: string, ttl?: number) =>
      ttl ? yourRedis.setex(key, ttl, value) : yourRedis.set(key, value),
    // ... other cache methods
  },
};

// Initialize tools
const mcpTools = new MCPTools(dependencies);
```

## Available Tools

### Database Tools

- `query_database`: Execute read-only SQL queries with tenant isolation
- `get_document`: Retrieve a specific document by file ID with **full content processing**
- `get_all_documents`: Retrieve all documents for the organization with pagination (optimized for lists)

### Context Tools

- `get_context`: Retrieve session context data
- `set_context`: Store data in session context

### Analysis Tools

- `analyze_data`: Perform security, performance, or trend analysis

### System Tools

- `system_status`: Get comprehensive system health information

### Async Tools

- `get_task_status`: Check async task progress
- `cancel_task`: Cancel running tasks
- `list_tasks`: List session tasks

## Development

1. Install dependencies: `pnpm install`
2. Build: `pnpm build`
3. Run tests: `pnpm test`
4. Watch mode: `pnpm dev`

## Integration

Add to your service's `package.json`:

```json
{
  "dependencies": {
    "@anter/mcp-tools": "workspace:^"
  }
}
```

## Document Content Processing

The package provides intelligent document content processing:

### Full Content Exposure

- **Internal documents**: Complete text from `content` field
- **Text uploads**: Full UTF-8 content from `buffer_file` (txt, md, html, json, csv, xml)
- **Binary files**: Metadata only with extraction placeholders

### API Response Optimization

- `get_document`: Returns `fullContent` for detailed retrieval
- `get_all_documents`: Returns `contentPreview` only to avoid large payloads
- Backward compatible with existing consumers

### Supported Text Formats

- Platform-created documents (markdown, text)
- Uploaded text files: `.txt`, `.md`, `.html`, `.json`, `.csv`, `.xml`
- Binary files: `.pdf`, `.docx`, `.xlsx`, `.pptx` (Phase 2 - extraction planned)

## Migration from Embedded Tools

If migrating from embedded tools in your API:

1. Install this package
2. Create adapter implementations for your framework
3. Replace direct tool imports with the new adapter-based approach
4. Test thoroughly before removing old implementations
