# Migration Guide: Service Consolidation

This guide helps you migrate from individual agent services to the consolidated services in `@anter/mcp-tools`.

## Overview

We've consolidated duplicate and similar services from multiple agents into a single, enhanced package. This provides:

- ✅ **Better Performance**: Optimized implementations with caching and batching
- ✅ **Consistent APIs**: Unified interfaces across all agents
- ✅ **Enhanced Features**: Best features from all implementations combined
- ✅ **Reduced Dependencies**: Single source of truth for common functionality
- ✅ **100% Backward Compatibility**: Existing functionality preserved

## Consolidated Services

### 1. EmbeddingService

**Replaces:**

- `apps/agents/src/agents/ask-ai/services/embedding.service.ts`
- Similar functionality in search agent

**Migration:**

```typescript
// Before
import { EmbeddingService } from '../services/embedding.service';

// After
import { EmbeddingService } from '@anter/mcp-tools';

// Usage remains the same - 100% compatible
const embeddingService = new EmbeddingService();
```

### 2. SemanticSearchService

**Replaces:**

- `apps/agents/src/agents/ask-ai/services/semantic-search.service.ts` (103 lines)
- `apps/agents/src/agents/search/services/semantic-search.service.ts` (468 lines)

**Migration:**

```typescript
// Before
import { SemanticSearchService } from '../services/semantic-search.service';

// After
import { SemanticSearchService } from '@anter/mcp-tools';

// Enhanced features now available:
const searchService = new SemanticSearchService();
await searchService.indexDocuments(documents, organizationId);
const results = await searchService.search(query, {
  topK: 10,
  threshold: 0.7,
  includeHighlights: true,
  rerank: true,
});
```

### 3. DocumentAnalyzerService

**Replaces:**

- `apps/agents/src/agents/ask-ai/services/document-sanitizer.service.ts`
- `apps/agents/src/agents/document/services/document-analysis.service.ts`
- `apps/agents/src/agents/document/services/metadata-extraction.service.ts`
- `apps/agents/src/agents/document/services/content-classification.service.ts`

**Migration:**

```typescript
// Before (multiple imports)
import { DocumentSanitizerService } from '../services/document-sanitizer.service';
import { DocumentAnalysisService } from '../services/document-analysis.service';
import { MetadataExtractionService } from '../services/metadata-extraction.service';

// After (single import)
import { DocumentAnalyzerService } from '@anter/mcp-tools';

const analyzer = new DocumentAnalyzerService();

// All functionality in one service:
const analysis = await analyzer.analyzeDocument(document);
const metadata = analyzer.extractMetadata(document);
const classification = analyzer.classifyContent(content);
const sanitized = analyzer.sanitizeContent(content, options);
```

### 4. MetricsTrackerService

**Replaces:**

- `apps/agents/src/agents/ask-ai/services/metrics.service.ts`
- Similar functionality in other agents

**Migration:**

```typescript
// Before
import { MetricsService } from '../services/metrics.service';

// After
import { MetricsTrackerService } from '@anter/mcp-tools';

const metrics = new MetricsTrackerService();
metrics.recordMetric('search.query', 1, { agent: 'ask-ai' });
metrics.recordPerformance('embedding.generation', duration, true);
```

### 5. VectorOperationsService

**Replaces:**

- `apps/agents/src/agents/search/services/vector-operations.service.ts`

**Migration:**

```typescript
// Before
import { VectorOperationsService } from '../services/vector-operations.service';

// After
import { VectorOperationsService, EmbeddingService } from '@anter/mcp-tools';

// Enhanced features now available:
const embeddingService = new EmbeddingService();
const vectorService = new VectorOperationsService(embeddingService);

// Batch document addition
await vectorService.addDocuments(documents, organizationId);

// Advanced search with organization isolation
const results = await vectorService.findSimilarDocuments(query, {
  organizationId: 'your-org',
  topK: 10,
  threshold: 0.7,
  useEmbeddings: true, // Use real embeddings vs simple vectors
});
```

## Step-by-Step Migration

### Step 1: Update Package Dependencies

Ensure you have the latest `@anter/mcp-tools`:

```bash
cd apps/agents
pnpm install
```

### Step 2: Update Agent Imports

For each agent, update the imports:

**Ask-AI Agent (`apps/agents/src/agents/ask-ai/index.ts`):**

```typescript
// Remove old imports
// import { EmbeddingService } from './services/embedding.service';
// import { SemanticSearchService } from './services/semantic-search.service';
// import { DocumentSanitizerService } from './services/document-sanitizer.service';
// import { MetricsService } from './services/metrics.service';

// Add new imports
import {
  EmbeddingService,
  SemanticSearchService,
  DocumentAnalyzerService,
  MetricsTrackerService,
  VectorOperationsService,
} from '@anter/mcp-tools';

export class AskAIAgent extends AbstractAgent {
  constructor(
    private embeddingService = new EmbeddingService(),
    private searchService = new SemanticSearchService(embeddingService),
    private documentAnalyzer = new DocumentAnalyzerService(),
    private metrics = new MetricsTrackerService()
  ) {
    super();
  }

  // Rest of implementation remains the same
}
```

**Search Agent (`apps/agents/src/agents/search/index.ts`):**

```typescript
// Remove old imports
// import { SemanticSearchService } from './services/semantic-search.service';
// import { QueryExpansionService } from './services/query-expansion.service';

// Add new imports
import { SemanticSearchService, EmbeddingService } from '@anter/mcp-tools';

export class SearchAgent extends AbstractAgent {
  constructor(
    private embeddingService = new EmbeddingService(),
    private searchService = new SemanticSearchService(embeddingService)
  ) {
    super();
  }
}
```

**Document Agent (`apps/agents/src/agents/document/index.ts`):**

```typescript
// Remove old imports
// import { DocumentAnalysisService } from './services/document-analysis.service';
// import { MetadataExtractionService } from './services/metadata-extraction.service';
// import { ContentClassificationService } from './services/content-classification.service';

// Add new imports
import { DocumentAnalyzerService } from '@anter/mcp-tools';

export class DocumentAgent extends AbstractAgent {
  constructor(private documentAnalyzer = new DocumentAnalyzerService()) {
    super();
  }

  async processDocument(document: ProcessedDocument) {
    // All functionality now in one service
    const analysis = await this.documentAnalyzer.analyzeDocument(document);
    const metadata = this.documentAnalyzer.extractMetadata(document);
    const classification = this.documentAnalyzer.classifyContent(document.content);

    return { analysis, metadata, classification };
  }
}
```

### Step 3: Update Method Calls

Most method calls remain the same, but some new enhanced features are available:

**Enhanced Search Options:**

```typescript
// Before (limited options)
const results = await searchService.search(query, topK);

// After (enhanced options)
const results = await searchService.search(query, {
  topK: 10,
  threshold: 0.7,
  includeMetadata: true,
  includeChunks: true,
  includeHighlights: true,
  organizationId: 'your-org',
  filters: { category: 'security' },
  rerank: true,
});
```

**Enhanced Document Analysis:**

```typescript
// Before (separate calls)
const sanitized = sanitizer.sanitize(content);
const analysis = await analyzer.analyze(document);
const metadata = extractor.extract(document);

// After (unified service)
const analyzer = new DocumentAnalyzerService();
const sanitized = analyzer.sanitizeContent(content, {
  removeHtml: true,
  normalizeWhitespace: true,
  maxLength: 5000,
});
const analysis = await analyzer.analyzeDocument(document);
const metadata = analyzer.extractMetadata(document);
```

### Step 4: Remove Old Service Files

After migration and testing, remove the old service files:

```bash
# Remove old ask-ai services
rm apps/agents/src/agents/ask-ai/services/embedding.service.ts
rm apps/agents/src/agents/ask-ai/services/semantic-search.service.ts
rm apps/agents/src/agents/ask-ai/services/document-sanitizer.service.ts
rm apps/agents/src/agents/ask-ai/services/metrics.service.ts

# Remove old search services
rm apps/agents/src/agents/search/services/semantic-search.service.ts

# Remove old document services
rm apps/agents/src/agents/document/services/document-analysis.service.ts
rm apps/agents/src/agents/document/services/metadata-extraction.service.ts
rm apps/agents/src/agents/document/services/content-classification.service.ts
```

### Step 5: Update Tests

Update test imports and ensure all tests pass:

```typescript
// Update test imports
import {
  EmbeddingService,
  SemanticSearchService,
  DocumentAnalyzerService,
  MetricsTrackerService,
} from '@anter/mcp-tools';

// Tests should continue to work with the same APIs
```

## New Features Available

### Enhanced Embedding Service

- **Batch Processing**: `embedTexts()` for multiple texts
- **Caching**: Automatic caching with TTL
- **Similarity Calculations**: Built-in cosine similarity
- **Statistics**: Performance metrics and cache stats

### Enhanced Semantic Search

- **Organization Isolation**: Multi-tenant support
- **Advanced Filtering**: Filter by metadata, tags, time ranges
- **Highlighting**: Automatic query highlighting in results
- **Reranking**: Improved result relevance
- **Chunking**: Automatic content chunking for better search

### Enhanced Document Analyzer

- **Unified Interface**: All document operations in one service
- **Content Classification**: Automatic categorization
- **Quality Assessment**: Document quality scoring
- **Sentiment Analysis**: Basic sentiment detection
- **Metadata Extraction**: Comprehensive metadata extraction

### Enhanced Metrics Tracker

- **Performance Tracking**: Automatic operation timing
- **Aggregations**: Time-based metric aggregations
- **Filtering**: Advanced metric filtering
- **Memory Management**: Automatic cleanup of old metrics

## Troubleshooting

### Common Issues

1. **Import Errors**

   ```
   Module not found: @anter/mcp-tools
   ```

   **Solution:** Run `pnpm install` in the agents directory

2. **Type Errors**

   ```
   Property 'newMethod' does not exist
   ```

   **Solution:** Check the migration guide for updated method names

3. **Runtime Errors**
   ```
   Cannot find module 'html-to-text'
   ```
   **Solution:** This should be automatically resolved by the package dependencies

### Validation

To ensure migration was successful:

1. **Build Check:**

   ```bash
   cd apps/agents
   pnpm build
   ```

2. **Test Check:**

   ```bash
   cd apps/agents
   pnpm test
   ```

3. **Runtime Check:**
   - Start your agents
   - Verify all functionality works as expected
   - Check that performance is maintained or improved

## Benefits After Migration

- **Reduced Bundle Size**: Removed duplicate code
- **Better Performance**: Optimized implementations with caching
- **Enhanced Features**: Access to new capabilities
- **Easier Maintenance**: Single source of truth for common functionality
- **Better Testing**: Comprehensive test coverage in mcp-tools
- **Future-Proof**: Centralized updates and improvements

## Support

If you encounter issues during migration:

1. Check this guide for common solutions
2. Review the API documentation in the source files
3. Run the test suite to validate functionality
4. Create an issue if you find bugs or need assistance

---

**Migration Checklist:**

- [ ] Updated package dependencies
- [ ] Updated imports in all agents
- [ ] Updated method calls (if needed)
- [ ] Removed old service files
- [ ] Updated tests
- [ ] Verified build passes
- [ ] Verified tests pass
- [ ] Tested runtime functionality
