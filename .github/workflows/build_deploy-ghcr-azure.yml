name: Build and Deploy GHCR to Azure

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.CSJ_GHCR_PAT }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=latest
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm manually
        run: |
          corepack enable
          corepack prepare pnpm@8.15.4 --activate

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./apps/api/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Verify Docker image
        env:
          IMAGE_TAG: latest
          APP_PORT: 8000
          HEALTH_ENDPOINT: /
        run: |
          set -e

          IMAGE_NAME="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:$IMAGE_TAG"

          echo "🔄 Pulling image: $IMAGE_NAME"
          docker pull $IMAGE_NAME

          REDIS_URL="${{ secrets.REDIS_URL }}"
          REDIS_URL=${REDIS_URL:-"redis://localhost:6379"}
          BASE_URL="${{ secrets.BASE_URL }}"
          BASE_URL=${BASE_URL:-"http://localhost:8000"}

          echo "🚀 Starting container..."
          CONTAINER_ID=$(docker run -d \
            -p 8000:8000 \
            -e BASE_URL="$BASE_URL" \
            -e DATABASE_URL_ROOT_USER="${{ secrets.DATABASE_URL_ROOT_USER }}" \
            -e DATABASE_URL_RLS_USER="${{ secrets.DATABASE_URL_RLS_USER }}" \
            -e OPENAI_API_KEY="${{ secrets.OPENAI_API_KEY }}" \
            -e JWT_SECRET="${{ secrets.JWT_SECRET }}" \
            -e REFRESH_TOKEN_SECRET="${{ secrets.REFRESH_TOKEN_SECRET }}" \
            -e INTERNAL_API_SECRET="${{ secrets.INTERNAL_API_SECRET }}" \
            -e TOKEN_EXPIRY="${{ secrets.TOKEN_EXPIRY }}" \
            -e REFRESH_TOKEN_EXPIRY="${{ secrets.REFRESH_TOKEN_EXPIRY }}" \
            -e REDIS_URL="$REDIS_URL" \
            -e SKIP_REDIS=true \
            $IMAGE_NAME)

          echo "⏳ Waiting for the container to start..."
          sleep 10
          echo "📦 Container ID: $CONTAINER_ID"
          docker ps -a 

          echo "✅ Container is running with ID: $CONTAINER_ID"

          echo "🩺 Running health check on http://localhost:$APP_PORT$HEALTH_ENDPOINT..."
          HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$APP_PORT$HEALTH_ENDPOINT || echo "Failed")
          if [ "$HEALTH_CHECK" != "200" ]; then
            echo "❌ Health check failed with status: $HEALTH_CHECK"
            docker logs $CONTAINER_ID
            docker stop $CONTAINER_ID
            docker rm $CONTAINER_ID
            exit 1
          fi

          echo "✅ Health check passed with status: $HEALTH_CHECK"
          echo "🧹 Cleaning up..."
          docker stop $CONTAINER_ID
          docker rm $CONTAINER_ID

      - name: Azure login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.CSJ_AZURE_CREDENTIALS }}

      - name: Deploy to Azure Container Apps
        uses: azure/webapps-deploy@v3
        with:
          app-name: askinfosec-api
          images: ghcr.io/${{ env.IMAGE_NAME }}:latest
          publish-profile: ${{ secrets.CSJ_AZURE_PUBLISH_PROFILE }}
