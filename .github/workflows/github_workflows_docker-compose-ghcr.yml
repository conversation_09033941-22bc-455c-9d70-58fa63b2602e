name: <PERSON><PERSON> and Push Docker Compose to GHCR

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Fast-fail step: test tag and push permissions before building
      - name: Test GHCR push permission
        run: |
          echo "FROM scratch" > Dockerfile.test
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:ghcr-test -f Dockerfile.test .
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:ghcr-test
          docker rmi ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:ghcr-test
          rm Dockerfile.test
        # If this step fails, the workflow will stop here

      - name: Extract image name from compose file
        id: meta
        run: |
          IMAGE_TAG=${REGISTRY}/${IMAGE_NAME,,}:latest
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_OUTPUT

      # Add Docker Buildx cache support
      - name: Set up Docker cache
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker Compose services
        run: |
          docker compose build --build-arg BUILDKIT_INLINE_CACHE=1
          docker compose push
        env:
          REGISTRY: ${{ env.REGISTRY }}
          IMAGE_NAME: ${{ env.IMAGE_NAME }}
        # Enable cache usage
        # Buildx automatically uses cache if present in /tmp/.buildx-cache

      - name: Tag and push images to GHCR
        run: |
          images=$(docker compose config | grep 'image:' | awk '{print $2}')
          for image in $images; do
            docker pull $image
            docker tag $image ${{ steps.meta.outputs.IMAGE_TAG }}
            docker push ${{ steps.meta.outputs.IMAGE_TAG }}
          done
