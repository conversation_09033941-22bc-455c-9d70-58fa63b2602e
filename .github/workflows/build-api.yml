name: Build (API)

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: 'The environment to build for'
    outputs:
      image:
        value: ${{ jobs.Build.outputs.image }}

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout
  packages: write # This is required for docker/build-push-action

env:
  REGISTRY: ghcr.io

jobs:
  Build:
    name: Build
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.set-image-output.outputs.image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Build and Push to GitHub Container Registry
        uses: docker/build-push-action@v6
        id: build
        with:
          push: true
          context: .
          file: apps/api/Dockerfile
          tags: ${{ env.REGISTRY }}/${{ github.repository }}/anter-ai:${{ inputs.environment }}.${{ github.sha }},${{ env.REGISTRY }}/${{ github.repository }}/anter-ai:${{ inputs.environment }}.latest

      - name: Set image output
        id: set-image-output
        run: |
          IMAGE_NAME="${{ env.REGISTRY }}/${{ github.repository }}/anter-ai:${{ inputs.environment }}.${{ github.sha }}"
          echo "image=$IMAGE_NAME" >> $GITHUB_OUTPUT
