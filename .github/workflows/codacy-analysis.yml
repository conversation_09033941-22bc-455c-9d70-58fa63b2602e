name: Codacy Security Analysis

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run weekly security scans
    - cron: '0 2 * * 1'

jobs:
  codacy-analysis:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install

      - name: Make Codacy CLI executable
        run: chmod +x .codacy/cli.sh

      - name: Run Codacy ESLint Analysis
        run: .codacy/cli.sh analyze --tool eslint

      - name: Run Codacy Semgrep Analysis
        run: .codacy/cli.sh analyze --tool semgrep

      - name: Run Codacy Trivy Analysis
        run: .codacy/cli.sh analyze --tool trivy
        timeout-minutes: 5

      - name: Run Full Codacy Analysis
        run: .codacy/cli.sh analyze
        timeout-minutes: 10

      - name: Upload analysis results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: codacy-analysis-results
          path: |
            .codacy/logs/
            .trivycache/
          retention-days: 30

  security-scan:
    runs-on: ubuntu-latest
    needs: codacy-analysis

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download analysis results
        uses: actions/download-artifact@v4
        with:
          name: codacy-analysis-results

      - name: Security summary
        run: |
          echo "## Security Analysis Summary" >> $GITHUB_STEP_SUMMARY
          echo "✅ Codacy analysis completed successfully" >> $GITHUB_STEP_SUMMARY
          echo "🔍 Tools used: ESLint, Semgrep, Trivy" >> $GITHUB_STEP_SUMMARY
          echo "📊 Check logs for detailed results" >> $GITHUB_STEP_SUMMARY
