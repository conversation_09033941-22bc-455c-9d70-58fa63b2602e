name: Deploy Teams Bot to Azure

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: infosec-ai/anterai-teams-bot

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.CSJ_GHCR_PAT }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=latest
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./apps/teams/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.CSJ_AZURE_CREDENTIALS }}

      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v3
        with:
          app-name: anterai-teams-apps
          publish-profile: ${{ secrets.CSJ_ANTERAI_TEAMS_APPS_PUBLISH_PROFILE }}
          images: ghcr.io/infosec-ai/anterai-teams-bot:latest
