name: ECS Deployment

on:
  workflow_call:
    inputs:
      image:
        required: true
        type: string
      environment:
        required: true
        type: string
      app:
        description: 'Application to deploy'
        type: string

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  Deploy:
    environment: ${{ inputs.environment }}-${{ inputs.app }}
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ vars.AWS_REGION }}
          role-duration-seconds: 2400
          role-skip-session-tagging: true

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ vars.TASK_FAMILY }} --query taskDefinition > task-definition.json

      - name: Print task definition
        run: |
          cat task-definition.json

      - name: Render Amazon ECS task definition
        id: render-task-definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: ${{ vars.CONTAINER_NAME }}
          image: ${{ inputs.image }}

      - name: Print rendered task definition
        run: |
          cat ${{ steps.render-task-definition.outputs.task-definition }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.render-task-definition.outputs.task-definition }}
          service: ${{ vars.ECS_SERVICE }}
          cluster: ${{ vars.ECS_CLUSTER }}
