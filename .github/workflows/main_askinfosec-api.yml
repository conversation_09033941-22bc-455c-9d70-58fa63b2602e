# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - askinfosec-api

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: pnpm install, build, and test
        run: |
          pnpm install
          NODE_ENV=production pnpm build

      - name: Zip artifact for deployment
        run: zip release.zip ./dist -r package.json package-lock.json

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          retention-days: 1
          name: node-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write
      contents: read
    environment:
      name: Production
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Unzip artifact for deployment
        run: unzip release.zip

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies in deployment environment
        run: pnpm install --prod

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_4E01C643DAE1420599EE42FA2DAEECEA }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_A77AE6366DD041A2A028C4E356656249 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_55688DE023D64966AAB9055D68E5CE8B }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: askinfosec-api
          slot-name: Production
          package: .
