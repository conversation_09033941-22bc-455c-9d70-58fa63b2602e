#!/bin/bash

# Add essential Codacy files for GitHub workflow
echo "Adding essential Codacy files..."

# Main configuration files
git add .codacy/codacy.yaml
git add .codacy/cli.sh
git add .codacy/cli-config.yaml

# Documentation
git add .codacy/README.md
git add .codacy/OWASP_TOP10_REFERENCE.md
git add .codacy/MONOREPO_BEST_PRACTICES.md
git add .codacy/COMMIT_GUIDE.md

# Tool configurations
git add .codacy/tools-configs/semgrep.yaml
git add .codacy/tools-configs/eslint.config.mjs
git add .codacy/tools-configs/trivy.yaml
git add .codacy/tools-configs/lizard.yaml
git add .codacy/tools-configs/languages-config.yaml

# GitHub workflow
git add .github/workflows/codacy-analysis.yml

echo "✅ Essential Codacy files added to staging"
echo "📋 Run 'git status' to verify"
echo "🚀 Run 'git commit -m \"Add Codacy security analysis configuration\"' to commit" 