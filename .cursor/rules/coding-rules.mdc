---
description: 
globs: 
alwaysApply: false
---
# Automated Coding & Testing Guidelines

## 1. Project Overview

**Tech Stack**
- Node.js + TypeScript
- Fastify as the web framework
- Prisma ORM (`src/lib/prisma/prisma.ts`)
- Jest + Supertest for tests (`test/…`)
- Environment via `.env` (`DATABASE_URL_…`)

**Directory Structure**
```plaintext
src/
├── api/                    # API endpoints
│   ├── v1/                # API version 1
│   │   ├── internal/     # Internal API endpoints
│   │   │   ├── routes/  # Route handlers
│   │   │   ├── controllers/ # Business logic
│   │   │   ├── schemas/ # Request/response schemas
│   │   │   └── middleware/ # Internal-specific middleware
│   │   │
│   │   └── external/    # External/Public API endpoints
│   │       ├── routes/  # Route handlers
│   │       ├── controllers/ # Business logic
│   │       ├── schemas/ # Request/response schemas
│   │       └── middleware/ # External-specific middleware
│   │
│   └── v2/              # Future API version
│       ├── internal/
│       └── external/
│
├── core/                # Core application functionality
│   ├── auth/           # Authentication & Authorization
│   │   ├── jwt/       # JWT implementation
│   │   ├── api-key/   # API key management
│   │   └── middleware/ # Auth middleware
│   │
│   ├── security/      # Security features
│   │   ├── rate-limit/ # Rate limiting
│   │   ├── cors/      # CORS configuration
│   │   └── headers/   # Security headers
│   │
│   └── validation/    # Input validation
│
├── services/          # Business logic services
│   ├── internal/     # Internal service implementations
│   └── external/     # External service implementations
│
├── lib/              # Shared utilities
│   ├── logger/      # Logging implementation
│   ├── errors/      # Error handling
│   └── utils/       # Utility functions
│
├── types/           # TypeScript type definitions
│   ├── api/        # API-specific types
│   ├── models/     # Data models
│   └── common/     # Shared types
│
├── config/         # Configuration
│   ├── env/       # Environment configuration
│   └── constants/ # Application constants
│
├── plugins/       # Fastify plugins
│   ├── swagger/  # API documentation
│   ├── database/ # Database connections
│   └── custom/   # Custom plugins
│
└── app.ts        # Application entry point

test/
├── api/                    # API tests
│   ├── v1/                # Version 1 tests
│   │   ├── internal/     # Internal API tests
│   │   │   ├── routes/  # Route tests
│   │   │   ├── controllers/ # Controller tests
│   │   │   ├── schemas/ # Schema validation tests
│   │   │   └── middleware/ # Middleware tests
│   │   │
│   │   └── external/    # External API tests
│   │       ├── routes/  # Route tests
│   │       ├── controllers/ # Controller tests
│   │       ├── schemas/ # Schema validation tests
│   │       └── middleware/ # Middleware tests
│   │
│   └── v2/              # Version 2 tests
│       ├── internal/
│       └── external/
│
├── core/                # Core functionality tests
│   ├── auth/           # Authentication tests
│   │   ├── jwt/       # JWT tests
│   │   ├── api-key/   # API key tests
│   │   └── middleware/ # Auth middleware tests
│   │
│   ├── security/      # Security feature tests
│   │   ├── rate-limit/ # Rate limiting tests
│   │   ├── cors/      # CORS tests
│   │   └── headers/   # Security headers tests
│   │
│   └── validation/    # Validation tests
│
├── services/          # Service tests
│   ├── internal/     # Internal service tests
│   └── external/     # External service tests
│
├── lib/              # Utility tests
│   ├── logger/      # Logger tests
│   ├── errors/      # Error handling tests
│   └── utils/       # Utility function tests
│
├── integration/      # Integration tests
│   ├── api/         # API integration tests
│   ├── auth/        # Authentication integration tests
│   └── database/    # Database integration tests
│
├── e2e/             # End-to-end tests
│   ├── internal/    # Internal API E2E tests
│   └── external/    # External API E2E tests
│
├── fixtures/        # Test fixtures and mocks
│   ├── data/       # Test data
│   └── mocks/      # Mock implementations
│
└── setup/          # Test setup and utilities
    ├── jest/      # Jest configuration
    └── helpers/   # Test helper functions
```

## 2. API Organization Rules

1. **Versioning**
   - All API endpoints must be versioned (v1, v2, etc.)
   - Each version must have separate internal and external endpoints
   - Version deprecation must be documented and communicated

2. **Internal vs External APIs**
   - Internal APIs are for our frontend application
   - External APIs are for partner/client integration
   - Clear separation of concerns between internal and external endpoints
   - Different security requirements for each type

3. **Route Organization**
   - Routes must be organized by version and type (internal/external)
   - Each route must have corresponding controller, schema, and middleware
   - Route handlers should be thin and delegate to controllers

4. **Schema Validation**
   - All requests must be validated using JSON Schema
   - Schemas must be versioned and documented
   - Validation errors must be handled consistently

## 3. Documentation & Style

1. **TSDoc for every function/class**
   ```ts
   /**
    * Fetches a question by ID.
    * @param id  UUID of the question
    * @returns   Question record or null
    * @throws   {NotFoundError} if no record found
    */
   export async function getQuestionById(id: string): Promise<Question | null> { … }
   ```

2. **Single responsibility**
   - **Controllers/routes** only parse HTTP inputs & call services
   - **Services** contain all business logic
   - **DB access** lives in a thin repository layer or directly in services via injected Prisma client

3. **Naming**
   - Files: `foo.ts` / `foo.test.ts`
   - Classes/Functions: `PascalCase` for classes, `camelCase` for functions
   - API endpoints: kebab-case for URLs, camelCase for parameters

4. **Linting & Formatting**
   - ESLint + Prettier must pass on every change
   - No `any`—use precise types
   - Maximum line length: 100 characters

5. **Response Format**
   - All API responses must use snake_case for JSON property names
   - Consistent error response format across all endpoints
   - Version-specific response formats must be documented

## 4. Security Guidelines

1. **Authentication & Authorization**
   - JWT for internal API authentication
   - API keys for external API authentication
   - Role-based access control (RBAC) implementation
   - Regular token/key rotation

2. **Input Validation**
   - All inputs must be validated using JSON Schema
   - Sanitize all user inputs
   - Implement rate limiting for all endpoints
   - CORS configuration for external APIs

3. **Security Headers**
   - Implement security headers using helmet
   - Content Security Policy (CSP) configuration
   - Regular security audits

## 5. Testing Requirements

1. **Unit Testing**
   - Every new or modified function must have unit tests
   - Aim for ≥80% coverage on new modules
   - Mock external dependencies
   - Test files must mirror the source directory structure
   - Test files should be named `*.test.ts` or `*.spec.ts`

2. **Integration Testing**
   - Test database setup with isolated environment
   - Test both internal and external APIs
   - Verify security measures
   - Test version compatibility
   - Use `*.int.test.ts` naming convention

3. **End-to-End Testing**
   - Test complete user flows
   - Use `*.e2e.test.ts` naming convention
   - Test both internal and external API flows
   - Include authentication flows

4. **Test Organization**
   - Mirror the source directory structure in the test directory
   - Group related tests in appropriate subdirectories
   - Use descriptive test names
   - Include setup and teardown procedures

5. **Test Data Management**
   - Use fixtures for test data
   - Implement data factories where appropriate
   - Clean up test data after each test
   - Use separate test databases

6. **Security Testing**
   - Regular security testing
   - Penetration testing for external APIs
   - OWASP Top 10 compliance checks
   - Authentication and authorization tests

## 6. Dependency Management

1. **Dependency Selection Priority**
   - **Priority 1:** Custom Implementation
     - Implement internally if it can be done effectively with minimal code
     - Avoid reinventing the wheel; assess if custom implementation makes sense
     - Consider maintenance burden and security implications
   
   - **Priority 2:** Official Fastify Packages
     - Use official Fastify packages as the first choice for external dependencies
     - Prefer the latest stable versions
     - Verify package maintenance status and community adoption
   
   - **Priority 3:** Third-Party Packages
     - Only consider non-Fastify packages if no suitable Fastify package exists
     - Select packages with:
       - Active maintenance
       - Strong community support
       - Regular security updates
       - Clear documentation
       - Compatible license

2. **Dependency Review Process**
   - Document the rationale for each new dependency
   - Review dependency tree for security vulnerabilities
   - Consider the impact on bundle size and performance
   - Assess long-term maintenance requirements

## 7. Error Handling

1. **Error Types**
   - Use typed errors for different scenarios
   - Consistent error response format
   - Proper error logging

2. **Error Responses**
   ```json
   {
     "error": "Error message",
     "code": "ERROR_CODE",
     "details": {
       "field": "Additional error details"
     }
   }
   ```

3. **Logging**
   - Structured logging for all errors
   - Different log levels for different environments
   - Sensitive data must not be logged

## 8. Performance Guidelines

1. **Response Time**
   - Internal API: < 100ms
   - External API: < 200ms
   - Long-running operations must be async

2. **Caching**
   - Implement caching where appropriate
   - Cache invalidation strategy
   - Cache headers for external APIs

3. **Database**
   - Optimize database queries
   - Use appropriate indexes
   - Regular query performance monitoring

## 9. Deployment & CI/CD

1. **Environment Configuration**
   - Use environment variables for configuration
   - Different configurations for different environments
   - Secure storage of sensitive data

2. **CI/CD Pipeline**
   - Automated testing
   - Security scanning
   - Version management
   - Deployment automation

3. **Monitoring**
   - Application performance monitoring
   - Error tracking
   - Security monitoring
   - Usage analytics

## 10. Module Extraction Criteria

1. **When to extract shared modules (e.g. mcp) into a workspace package**
   - At least **two** deployable applications (e.g. `apps/api`, `apps/agents`, background workers, CLIs) import the same module directly.
   - The module needs to be versioned or released **independently** from the API.
   - You want to run unit tests against the module **without** bootstrapping a Fastify context.
   - Circular dependencies start to appear between `apps/api` and another app because of shared code.
   - The module is **framework-agnostic** (contains no Fastify decorators or reply/request objects).

2. **Extraction checklist**
   - Move code to `packages/<module>` and add a minimal `package.json` with `"type": "module"`.
   - Provide a barrel file (`index.ts`) exposing the public API surface.
   - Update `tsconfig.json` path mappings and the pnpm workspace to reference the new package.
   - Ensure existing tests are moved or duplicated; add package-specific unit tests.
   - Add a CI job that builds and tests the package in isolation.

3. **Post-extraction rules**
   - Treat the package API as **public**: follow semantic versioning and keep a CHANGELOG.
   - Never import from `apps/…` into `packages/…`; the dependency graph must remain acyclic.
   - Keep the package decoupled from framework concerns; framework adapters (e.g. Fastify plugins) live alongside the consuming application, not in the shared package.
