const https = require('https');

// Test the unified prompting system
async function testUnifiedPrompts() {
  console.log('🧪 Testing Unified Prompting System...\n');

  const testQueries = [
    'What are our dental benefits?',
    'How many medical plans do we have?',
    'What is the latest plan year?',
  ];

  for (const query of testQueries) {
    console.log(`📝 Testing query: "${query}"`);

    try {
      const response = await makeRequest(query);
      console.log(`✅ Response received (${response.length} chars)`);
      console.log(`📄 Response preview: ${response.substring(0, 200)}...`);

      // Check for workflow completion messages
      const hasWorkflowMessage =
        response.toLowerCase().includes('workflow') &&
        (response.toLowerCase().includes('complete') ||
          response.toLowerCase().includes('if you have any further questions'));

      if (hasWorkflowMessage) {
        console.log('❌ ISSUE: Response contains workflow completion message');
      } else {
        console.log('✅ SUCCESS: Response is natural, no workflow messages');
      }

      console.log('─'.repeat(80));
    } catch (error) {
      console.error(`❌ Error testing query "${query}":`, error.message);
      console.log('─'.repeat(80));
    }
  }
}

function makeRequest(query) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      agentName: 'ask_ai_v2',
      input: {
        input: query,
        organizationId: 'test-org',
        sessionId: 'test-session',
      },
    });

    const options = {
      hostname: 'localhost',
      port: 8000,
      path: '/v1/external/agent/invoke',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'X-Internal-Secret': 'your-secret-key',
      },
      rejectUnauthorized: process.env.NODE_ENV === 'production', // Only disable in development
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          if (parsed.output && parsed.output.response) {
            resolve(parsed.output.response);
          } else {
            reject(new Error('Invalid response format'));
          }
        } catch (error) {
          reject(new Error('Failed to parse response'));
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// Run the test
testUnifiedPrompts().catch(console.error);
