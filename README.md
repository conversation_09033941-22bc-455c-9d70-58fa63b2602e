# AskInfoSec Workspace

A mono-repo workspace containing all AskInfoSec applications, packages, and tools for information security knowledge management and AI-powered question answering.

## Workspace Structure

This is a mono-repo managed with pnpm workspaces containing:

- **apps/**: Applications (API server, AI agents system)
- **packages/**: Shared packages and libraries
- **tools/**: Development tools and utilities

### Applications

- **apps/api/**: Main API server - A secure multi-tenant API for information security knowledge management and AI-powered question answering
- **apps/agents/**: AI Agents system - Enterprise-grade AI agents with LangChain, OpenAI SDK, and MCP integration

## Features

- **Multi-tenant Architecture**: Secure data isolation between organizations using Row Level Security
- **AI Integration**: OpenAI-powered document analysis, question answering, and intelligent agents
- **Document Processing**: Support for PDF, DOCX, and text files
- **Vector Search**: Semantic search capabilities using embeddings
- **Model Context Protocol (MCP)**: Stateful AI interactions with persistent context
- **AI Agents System**: Specialized agents for various tasks with orchestration and security
- **LangChain OpenAI Integration**: Mature, production-ready OpenAI integration with CommonJS compatibility

## Architecture

### Core Components

- **Fastify Framework**: High-performance web framework
- **Drizzle ORM**: Modern TypeScript ORM with database access and Row Level Security
- **LangChain**: AI integration for document processing and embeddings
- **PostgreSQL**: Primary database with vector search capabilities
- **Redis**: Session management, caching, and MCP context storage
- **AI Agents System**: Modular agent framework with MCP bridge and tool integration
- **LangChain OpenAI Integration**: Production-ready OpenAI chat models with CommonJS compatibility

### Security Features

- **Row Level Security (RLS)**: Database-level tenant isolation
- **Organization-based Access Control**: All data access is scoped to organization ID
- **Environment Variable Protection**: Secure handling of API keys and credentials
- **Agent Security Framework**: Input sanitization, prompt injection prevention, SQL injection protection

### AI Capabilities

- **MCP Integration**: Stateful AI interactions with persistent context and tool calling
- **Agent Orchestration**: Multi-agent coordination and workflow execution
- **LangChain OpenAI Chat**: Mature OpenAI integration with SystemMessage and HumanMessage support
- **Tool Integration**: Database queries, API calls, and external service integration
- **Streaming Support**: Real-time response streaming for enhanced user experience

## Setup

1. Clone the repository
2. Install dependencies for all workspaces:

```bash
$ pnpm install
```

3. Set up the API application:

```bash
# Copy the environment example file
$ cp apps/api/.env.example apps/api/.env

# Update apps/api/.env with your values:
# DATABASE_URL_RLS_USER=postgresql://user:password@localhost:5432/askinfosec
# OPENAI_API_KEY=your_openai_api_key
# REDIS_URL=redis://localhost:6379 (optional, mock fallback available)
```

4. Run database migrations (from API directory):

```bash
$ cd apps/api
$ npx drizzle-kit migrate
```

5. Start Redis (optional - mock fallback available):

```bash
$ docker-compose up redis
```

## Development

### Workspace Commands

Start all applications in development mode:

```bash
$ pnpm dev
```

Build all applications:

```bash
$ pnpm build
```

Run tests for all applications:

```bash
$ pnpm test
```

### API Specific Commands

Start the API development server:

```bash
$ pnpm api:dev
```

Build the API:

```bash
$ pnpm api:build
```

Run API tests:

```bash
$ pnpm api:test
```

### Agents Specific Commands

Start agents in development mode:

```bash
$ pnpm agents:dev
```

Build the agents package:

```bash
$ pnpm agents:build
```

Run agents tests:

```bash
$ pnpm agents:test
```

## Database Management

### Database Schema Management

Generate database schema and migrations:

```bash
$ npx drizzle-kit generate
$ npx drizzle-kit migrate
```

Introspect existing database:

```bash
$ npx drizzle-kit introspect
```

### Row Level Security

This project implements Row Level Security (RLS) at the database level to enforce multi-tenant isolation using Drizzle ORM.

Key components:

- `src/lib/drizzle/drizzle.ts`: Fastify plugin that extends Drizzle with RLS capabilities
- `dbWithTenant(orgId)`: Method to enforce tenant isolation
- `dbBypassRLS()`: Method to bypass RLS for administrative operations

## AI Agents System

The agents system provides enterprise-grade AI capabilities with the following features:

### Core Agents

- **AskAI Agent**: General-purpose AI agent that processes user prompts and executes appropriate tools
- **OpenAI Chat Agent**: Direct integration with OpenAI Agents SDK for conversational AI
- **Echo Agent**: Simple test agent for development and testing

### Agent Features

- **MCP Integration**: Seamless integration with Model Context Protocol for stateful interactions
- **Tool Orchestration**: Automatic tool selection and execution based on user input
- **Database Access**: Secure SQL query execution with tenant isolation
- **Streaming Support**: Real-time response streaming
- **Security Framework**: Multi-layer protection against various attack vectors
- **Performance Monitoring**: Comprehensive metrics and observability

### Usage

```

```
